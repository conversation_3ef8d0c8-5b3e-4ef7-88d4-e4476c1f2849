# Revenova Manual Customer Sync

Manual sync tool for Revenova customer data from Salesforce (Revenova TMS) to the platform database.

## Features

The script supports three sync strategies and automatically determines the best approach based on Redis state:

1. **FULL SYNC** - First-time sync that fetches all customers from Salesforce
2. **INCREMENTAL SYNC** - Fetches only customers modified since the last successful sync
3. **RESUME SYNC** - Resumes a failed sync from where it left off

## Prerequisites

### Required Environment Variables

Create a `.env` file in `scripts/revenova/` with the following variables:

```bash
# Environment
APP_ENV=prod  # or "dev", "staging"

# Database
DB_HOST=drumkit-prod.cluster-xxxxx.us-east-1.rds.amazonaws.com
DB_NAME=beacon
DB_SECRET_ARN=arn:aws:secretsmanager:us-east-1:558920728231:secret:rds!cluster-xxxxx

# Redis (for staging/prod, requires SSH tunnel to access private VPC)
REDIS_URL=redis://localhost:6379

# Encryption
AES_KEY=your-aes-key-here
```

### AWS Credentials

For staging/prod environments, the script fetches database credentials from AWS Secrets Manager. You must have AWS credentials configured.

**Recommended approaches:**

- **aws-vault** (recommended): `aws-vault exec <profile> -- go run ...`
- **AWS SSO**: Run `aws sso login --profile <profile>` first, then `export AWS_PROFILE=<profile>`
- **AWS CLI**: Run `aws configure` to set up credentials

**Required AWS permissions:**
- `secretsmanager:GetSecretValue`
- `rds:DescribeDBProxies`

### SSH Tunnel for Redis (Prod/Staging)

Redis is in a private VPC and requires an SSH tunnel through the Cyclops EC2 instance:

```bash
# In a separate terminal, keep this running
ssh -i ~/.ssh/CyclopsKP.pem \
    -L 6379:beacon-prod-encrypted-xxxxx.kcqbcj.0001.use1.cache.amazonaws.com:6379 \
    ubuntu@<cyclops-public-ip> \
    -N
```

Then use `REDIS_URL=redis://localhost:6379` in your `.env`.

## Usage

### Find Your Integration ID

```sql
SELECT id, name, tenant, service_id 
FROM integrations 
WHERE integration_name = 'revenova';
```

### Dry Run (Recommended First)

Analyze what will happen without executing:

```bash
# From project root
cd /path/to/drumkit

# With aws-vault (for prod/staging)
aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run

# For local development (no aws-vault needed)
go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run
```

**Example dry run output:**

```
═══════════════════════════════════════════════════
🔍 DRY RUN MODE - Analyzing sync strategy...
═══════════════════════════════════════════════════
Integration:  revenova (ID: 4853)
Tenant:       customer.salesforce.com
Environment:  prod

📍 INCREMENTAL SYNC
   - Last successful sync: 2025-11-24 10:15:16 (2h ago)
   - Will fetch customers modified after: 2025-11-24T10:15:16Z

✅ Ready to sync using INCREMENTAL strategy
   Run without --dry-run to execute the sync
═══════════════════════════════════════════════════
```

### Execute Actual Sync

```bash
# With aws-vault (for prod/staging)
aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123

# For local development
go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123
```

## Production Safety

When running against production (`APP_ENV=prod`), the script will prompt for confirmation:

```
⚠️  WARNING: PRODUCTION ENVIRONMENT DETECTED!
═══════════════════════════════════════════════════
Integration:  revenova (ID: 4853)
Service ID:   1750
Tenant:       customer.salesforce.com
═══════════════════════════════════════════════════

Type 'yes' to continue with PRODUCTION sync: 
```

You must type `yes` exactly to proceed.

## Sync Strategies

### FULL SYNC (First Time)

- **When:** No previous sync found in Redis
- **Behavior:** Fetches ALL customers from Salesforce in batches
- **Duration:** Several minutes for large customer lists (e.g., 10,000 customers = ~5-10 minutes)
- **Query:** `SELECT ... FROM Account ORDER BY Id ASC`

### INCREMENTAL SYNC

- **When:** Previous successful sync found
- **Behavior:** Only fetches customers modified since last sync
- **Duration:** Very fast, typically 1-2 seconds
- **Query:** `SELECT ... FROM Account WHERE SystemModstamp > <timestamp> ORDER BY Id ASC`

### RESUME SYNC

- **When:** Previous sync failed partway through
- **Behavior:** Continues from the last successfully processed customer ID
- **Duration:** Depends on how much is left to sync
- **Query:** `SELECT ... FROM Account WHERE Id > '<lastId>' ORDER BY Id ASC`

## Failure Recovery

If a sync fails partway through, simply run the same command again. The script automatically saves progress to Redis and will resume from where it left off.

**Example failure output:**

```
═══════════════════════════════════════════════════
❌ SYNC FAILED
Error:     context deadline exceeded
Duration:  14m 30s
Progress:  8,456 customers synced before failure
═══════════════════════════════════════════════════
💡 The sync will resume from where it left off when you run again
```

**To resume:** Just run the same command - no changes needed.

## Performance

### Full Sync Performance

- **Batch Size:** 200 customers per API call
- **Rate Limit Protection:** 500ms delay between batches
- **Throughput:** ~400 customers/minute

**Estimated Times:**

| Customers | API Calls | Estimated Time |
|-----------|-----------|----------------|
| 1,000     | 5         | ~30 seconds    |
| 5,000     | 25        | ~2.5 minutes   |
| 10,000    | 50        | ~5 minutes     |
| 50,000    | 250       | ~25 minutes    |

### Incremental Sync Performance

- **Throughput:** Typically 1-2 seconds total
- **Only fetches changed records**
- **Recommended:** Run frequently (hourly or daily)

## Troubleshooting

### Error: "Missing required database environment variables"

**Cause:** `DB_HOST` or `DB_NAME` not set in `.env`

**Solution:** Verify your `.env` file exists in `scripts/revenova/` with all required variables.

### Error: "Failed to connect to database: hostname resolving error"

**Cause:** Using RDS Proxy endpoint (private VPC) instead of RDS Cluster endpoint

**Solution:** Use the RDS **Cluster** endpoint (publicly accessible), not the Proxy endpoint:

```bash
# Get the correct endpoint
aws rds describe-db-clusters \
    --db-cluster-identifier drumkit-prod \
    --query 'DBClusters[0].Endpoint' \
    --output text
```

### Error: "Failed to connect to Redis: no such host"

**Cause:** Redis is in private VPC subnets and not publicly accessible

**Solution:** Set up SSH tunnel (see Prerequisites section above)

### Error: "AES key is empty"

**Cause:** `AES_KEY` environment variable not set

**Solution:** Get the AES key from AWS Secrets Manager:

```bash
aws secretsmanager get-secret-value \
    --secret-id beacon-api-iW6tIA \
    --query 'SecretString' \
    --output text | jq -r '.AES_KEY'
```

Add it to your `.env` file.

### Error: "Failed to load integration"

**Cause:** Integration ID doesn't exist or is not a Revenova integration

**Solution:** Verify the integration exists:

```sql
SELECT id, name, integration_name, tenant 
FROM integrations 
WHERE id = 123;
```

Ensure `integration_name = 'revenova'`.

## Command Line Options

```
--integration-id    Required. The Revenova integration ID to sync
--dry-run          Optional. Analyze sync strategy without executing
```

## Examples

### Basic Dry Run

```bash
aws-vault exec patrick -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=4853 --dry-run
```

### Execute Sync

```bash
aws-vault exec patrick -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=4853
```

### Local Development

```bash
# No aws-vault needed for local
go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123
```

## Getting Help

If you see the usage help, you're missing the `--integration-id` flag:

```
Usage: go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=<ID>

Options:
  --integration-id   Required. The Revenova integration ID to sync
  --dry-run          Optional. Analyze what would happen without executing

Examples (with aws-vault for prod/staging):
  aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123
  aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run

Examples (local dev, no aws-vault needed):
  go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run

Notes:
  - Run from the project root directory
  - .env file should be in scripts/revenova/.env
  - For prod/staging: AWS credentials required (use aws-vault, AWS SSO, or aws configure)
```

