package main

import (
	"bufio"
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

// analyzeSyncStrategy checks the Redis state to determine which sync strategy will be used.
//
// It checks for three conditions in priority order:
//  1. Resume cursor exists (indicates a previous failed sync that can be resumed)
//  2. Last sync timestamp exists (indicates incremental sync is possible)
//  3. Neither exists (indicates this is the first sync, requiring a full sync)
//
// The function prints a human-readable analysis of the sync strategy and returns
// a string identifier ("RESUME", "INCREMENTAL", or "FULL") for the strategy.
//
// This is primarily used in dry-run mode to show users what will happen without
// actually executing the sync.
func analyzeSyncStrategy(ctx context.Context, integration models.Integration) string {
	// 1. Check for resume cursor (a previous failure)
	// The "customers" job type stores the cursor in Redis when a sync fails partway through.
	_, resumeCursor, cursorErr := redis.GetIntegrationState(ctx, integration.ID, "customers")

	// 2. Check for the last sync timestamp (previous successful sync)
	// CRITICAL: Must check this BEFORE determining resume strategy to preserve incremental context
	// The "customers-timestamp" job type stores when the last successful sync completed.
	lastSyncTimestamp, _, timestampErr := redis.GetIntegrationState(ctx, integration.ID, "customers-timestamp")

	var hasTimestamp bool
	var parsed time.Time
	if timestampErr == nil && lastSyncTimestamp != "" {
		var parseErr error
		parsed, parseErr = time.Parse(time.RFC3339, lastSyncTimestamp)
		if parseErr == nil {
			hasTimestamp = true
		}
	}

	// 3. Handle resume with proper incremental context
	if cursorErr == nil && resumeCursor != "" {
		if hasTimestamp {
			log.Info(ctx, "📍 INCREMENTAL RESUME SYNC")
			log.Info(ctx, "   - Will continue from failed incremental sync", zap.String("cursor", resumeCursor))
			log.Info(ctx, "   - Only fetching customers modified after",
				zap.String("timestamp", parsed.Format("2006-01-02 15:04:05")))
			return "INCREMENTAL_RESUME"
		}
		log.Info(ctx, "📍 FULL RESUME SYNC")
		log.Info(ctx, "   - Will continue from failed full sync", zap.String("cursor", resumeCursor))
		return "FULL_RESUME"
	}

	// 4. No resume - check for incremental sync
	if hasTimestamp {
		log.Info(ctx, "📍 INCREMENTAL SYNC")
		log.Info(ctx, "   - Last successful sync",
			zap.String("lastSync", parsed.Format("2006-01-02 15:04:05")),
			zap.Duration("ago", time.Since(parsed).Round(time.Second)))
		log.Info(ctx, "   - Will fetch customers modified after", zap.String("timestamp", lastSyncTimestamp))
		return "INCREMENTAL"
	}

	// 5. Default to full sync (no previous state found)
	// This happens on first-time setup or if Redis state was cleared.
	// All customers will be fetched from Salesforce in batches.
	log.Info(ctx, "📍 FULL SYNC (First Time)")
	log.Info(ctx, "   - No previous sync state found. Will fetch ALL customers.")
	log.Info(ctx, "   - Estimated time: Several minutes for large accounts.")
	return "FULL"
}

// initDependencies handles environment loading and establishes connections to required services.
//
// This function performs three critical setup steps:
//  1. Loads environment variables from scripts/revenova/.env file (in dev) or system environment
//  2. Establishes a connection to the PostgreSQL database
//  3. Establishes a connection to Redis (if configured)
//
// The .env file is loaded from scripts/revenova/.env to keep it isolated from other code.
// This prevents accidental usage by other parts of the codebase.
//
// The function fails fast if database credentials are missing or connections fail,
// ensuring we don't start a sync with incomplete infrastructure.
//
// Redis is optional - if not configured, the sync will still work but won't support
// resume or incremental sync features (all syncs will be full syncs).
//
// Returns the determined application environment ("dev", "staging", or "prod") or an error
// if any critical connection fails.
func initDependencies(ctx context.Context) (string, error) {
	// --- 1. Environment Setup ---
	// Load .env file from scripts/revenova/.env for local development.
	// In production, environment variables are typically set by the deployment system.
	appEnv := os.Getenv("APP_ENV")
	if appEnv == "" || appEnv == "dev" {
		log.Info(ctx, "🔧 Loading .env file from scripts/revenova/...")

		// Try multiple paths to find the .env file
		possiblePaths := []string{
			"scripts/revenova/.env",       // From project root
			".env",                        // From script directory
			"../../scripts/revenova/.env", // From other locations
		}

		loaded := false
		for _, envPath := range possiblePaths {
			if err := godotenv.Load(envPath); err == nil {
				log.Info(ctx, "   ✅ Loaded .env", zap.String("path", envPath))
				loaded = true
				break
			}
		}

		if !loaded {
			log.Warn(ctx, "   ⚠️  .env file not found in any expected location")
			log.Warn(ctx, "   Expected locations:")
			log.Warn(ctx, "     - scripts/revenova/.env (from project root)")
			log.Warn(ctx, "     - .env (from current directory)")
			log.Warn(ctx, "   Proceeding with system environment variables...")
		}

		appEnv = os.Getenv("APP_ENV")
		if appEnv == "" {
			appEnv = "dev" // Default to dev if not specified
		}
	}

	// --- 2. Load AES Key for Decryption ---
	// The AES key is needed to decrypt integration credentials stored in the database
	aesKey := os.Getenv("AES_KEY")
	if aesKey == "" {
		return "", errors.New(
			"missing required AES_KEY environment variable (needed to decrypt integration credentials)")
	}

	log.Info(ctx, "🔐 Loading AES encryption key...")
	crypto.AESKey = []byte(aesKey)
	log.Info(ctx, "✅ AES key loaded")

	// --- 3. Database Connection ---
	log.Info(ctx, "🔌 Connecting to database", zap.String("env", appEnv))

	// Check for required DB environment variables
	dbHost := os.Getenv("DB_HOST")
	dbName := os.Getenv("DB_NAME")
	dbSecretARN := os.Getenv("DB_SECRET_ARN")

	if dbHost == "" || dbName == "" {
		return "", errors.New("missing required database environment variables (DB_HOST, DB_NAME)")
	}

	// For staging/prod, DB_SECRET_ARN is required for AWS Secrets Manager
	if (appEnv == "staging" || appEnv == "prod") && dbSecretARN == "" {
		return "", fmt.Errorf("missing required DB_SECRET_ARN for %s environment", appEnv)
	}

	rdsConfig := rds.EnvConfig{
		DBHost:      dbHost,
		DBName:      dbName,
		DBPort:      5432, // PostgreSQL default port
		DBSecretARN: dbSecretARN,
		// Default JSON keys in AWS Secrets Manager for username/password
		DBUser:     "username",
		DBPassword: "password",
	}

	if err := rds.Open(
		ctx,
		rdsConfig,
		rds.WithAppEnv(appEnv),
		rds.WithAWSSecretsManager(appEnv == "staging" || appEnv == "prod"),
		rds.WithApplicationName("revenova-manual-sync"),
	); err != nil {
		return "", fmt.Errorf("❌ Failed to connect to database: %w", err)
	}
	log.Info(ctx, "✅ Database connected")

	// --- 4. Redis Connection ---
	redisURL := os.Getenv("REDIS_URL")
	if redisURL != "" {
		log.Info(ctx, "🔌 Connecting to Redis...")
		if err := redis.Init(ctx, redisURL); err != nil {
			return "", fmt.Errorf("failed to connect to Redis: %w", err)
		}
		log.Info(ctx, "✅ Redis connected")
	} else {
		// This is a warning, not a fatal error, as sync can still run (without state tracking).
		log.Warn(ctx, "⚠️  WARNING: REDIS_URL not configured. Sync state will not be saved (no resume/incremental)")
	}

	return appEnv, nil
}

// run contains the main execution logic and returns an error if something fails.
func run(ctx context.Context) error {
	// --- 1. Argument Parsing ---
	integrationID := flag.Uint("integration-id", 0, "Revenova integration ID to sync")
	dryRun := flag.Bool("dry-run", false, "Analyze sync strategy without executing")
	flag.Parse()

	if *integrationID == 0 {
		log.Error(ctx, "Missing required flag: --integration-id")
		log.Info(ctx, "Usage: go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=<ID>")
		log.Info(ctx, "Options:")
		log.Info(ctx, "  --integration-id   Required. The Revenova integration ID to sync")
		log.Info(ctx, "  --dry-run          Optional. Analyze what would happen without executing")
		log.Info(ctx, "Examples (with aws-vault for prod/staging):")
		//nolint:lll
		log.Info(ctx, "  aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123")
		//nolint:lll
		log.Info(ctx, "  aws-vault exec <profile> -- go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run")
		log.Info(ctx, "Examples (local dev, no aws-vault needed):")
		log.Info(ctx, "  go run scripts/revenova/revenova_manual_customer_sync.go --integration-id=123 --dry-run")
		log.Info(ctx, "Notes:")
		log.Info(ctx, "  - Run from the project root directory")
		log.Info(ctx, "  - .env file should be in scripts/revenova/.env")
		log.Info(ctx, "  - For prod/staging: AWS credentials required (use aws-vault, AWS SSO, or aws configure)")
		return errors.New("missing required flag: --integration-id")
	}

	// --- 2. Initialize Dependencies (Early Check/Fail Fast) ---
	// This replaces steps 2-4 from the previous version.
	appEnv, err := initDependencies(ctx)
	if err != nil {
		log.Warn(ctx, "Hint: Check your environment variables and connection details")
		return err
	}

	log.Info(
		ctx,
		"Starting Revenova customer sync process",
		zap.Uint("integrationID", *integrationID),
		zap.Bool("dryRun", *dryRun),
	)

	// --- 3. Load Integration ---
	integration, err := integrationDB.Get(ctx, *integrationID)
	if err != nil {
		return fmt.Errorf("failed to load integration ID %d: %w", *integrationID, err)
	}
	log.Info(
		ctx,
		"⚡ Loaded Integration",
		zap.String("name", string(integration.Name)),
		zap.String("tenant", integration.Tenant),
	)

	// --- 4. Dry Run Check ---
	if *dryRun {
		log.Info(ctx, "═══════════════════════════════════════════════════")
		log.Info(ctx, "🔍 DRY RUN MODE - Analyzing sync strategy...")
		log.Info(ctx, "═══════════════════════════════════════════════════")
		log.Info(ctx, "Integration",
			zap.String("name", string(integration.Name)),
			zap.Uint("id", integration.ID))
		log.Info(ctx, "Tenant", zap.String("tenant", integration.Tenant))
		log.Info(ctx, "Environment", zap.String("env", appEnv))

		strategy := analyzeSyncStrategy(ctx, integration)

		log.Info(ctx, "✅ Ready to sync", zap.String("strategy", strategy))
		log.Info(ctx, "   Run without --dry-run to execute the sync")
		log.Info(ctx, "═══════════════════════════════════════════════════")
		return nil // Exit successfully after dry run analysis
	}

	// --- 5. Production Safety Check ---
	if appEnv == "prod" {
		log.Warn(ctx, "⚠️  WARNING: PRODUCTION ENVIRONMENT DETECTED!")
		log.Warn(ctx, "═══════════════════════════════════════════════════")
		log.Warn(ctx, "Integration",
			zap.String("name", string(integration.Name)),
			zap.Uint("id", integration.ID))
		log.Warn(ctx, "Service ID", zap.Uint("serviceID", integration.ServiceID))
		log.Warn(ctx, "Tenant", zap.String("tenant", integration.Tenant))
		log.Warn(ctx, "═══════════════════════════════════════════════════")
		//nolint:forbidigo // User input prompt needs to stay on same line
		fmt.Print("\nType 'yes' to continue with PRODUCTION sync: ")

		reader := bufio.NewReader(os.Stdin)
		response, err := reader.ReadString('\n')
		if err != nil {
			return fmt.Errorf("failed to read user input: %w", err)
		}
		response = strings.TrimSpace(strings.ToLower(response))

		if response != "yes" {
			return fmt.Errorf("sync aborted by user (typed '%s', expected 'yes')", response)
		}
		log.Info(ctx, "✅ Production sync confirmed, proceeding...")
	}

	// --- 6. Execution ---
	revenovaClient := revenova.New(ctx, integration)
	startTime := time.Now()

	log.Info(ctx, "🔄 Starting Customer Sync...")
	customers, err := revenovaClient.GetCustomers(ctx)
	elapsed := time.Since(startTime)

	if err != nil {
		log.Error(ctx, "Customer sync FAILED",
			zap.Error(err),
			zap.Duration("elapsed", elapsed),
			zap.Int("syncedBeforeFailure", len(customers)))

		log.Error(ctx, "═══════════════════════════════════════════════════")
		log.Error(ctx, "❌ SYNC FAILED")
		log.Error(ctx, "Error", zap.Error(err))
		log.Error(ctx, "Duration", zap.Duration("elapsed", elapsed.Round(time.Second)))
		log.Error(ctx, "Progress", zap.Int("customersBeforeFailure", len(customers)))
		log.Error(ctx, "═══════════════════════════════════════════════════")
		log.Info(ctx, "💡 The sync will resume from where it left off when you run again")
		return err // Return the execution error
	}

	// --- 7. Summary ---
	rate := float64(len(customers)) / elapsed.Seconds()
	log.Info(ctx, "Customer sync completed successfully!",
		zap.Int("totalCustomers", len(customers)),
		zap.Duration("elapsed", elapsed))

	log.Info(ctx, "═══════════════════════════════════════════════════")
	log.Info(ctx, "🎉 SYNC SUCCESSFUL!")
	log.Info(ctx, "Synced", zap.Int("customers", len(customers)))
	log.Info(ctx, "Duration", zap.Duration("elapsed", elapsed.Round(time.Second)))
	log.Info(ctx, "Rate", zap.Float64("customersPerSecond", rate))
	log.Info(ctx, "═══════════════════════════════════════════════════")
	log.Info(ctx, "💡 Next sync will be fast (incremental)!")

	return nil
}

func main() {
	ctx := context.Background()

	if err := run(ctx); err != nil {
		// Log the fatal error and exit with a non-zero status
		log.Error(ctx, "Script execution aborted", zap.Error(err))
		os.Exit(1)
	}

	// Exit successfully
	os.Exit(0)
}
