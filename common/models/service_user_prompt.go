package models

import (
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

type PromptFeature string
type PromptType string
type CreatedByType string
type ModelName string // Currently only OpenAI Models (see openaiShared.ChatModel)

const (
	// Features
	IsQuoteRequestAttachmentFeature PromptFeature = "is_quote_request_attachment"
	QuoteRequestExtractionFeature   PromptFeature = "quote_request_extraction"

	// Type of prompt for usage in LLM calls
	SystemPromptType    PromptType = "system"
	UserPromptType      PromptType = "user"
	DeveloperPromptType PromptType = "developer"

	// Info on how this prompt was created
	CreatedBySystem    CreatedByType = "system"
	CreatedByUser      CreatedByType = "user"
	CreatedByDeveloper CreatedByType = "developer"

	// Currently open support OpenAI Models...
	ModelNameGPT5Nano  ModelName = "gpt-5-nano" // Current global default
	ModelNameGPT4oMini ModelName = "gpt-4o-mini"
	ModelNameGPT5Mini  ModelName = "gpt-5-mini"
)

// validModelNames is a set of valid OpenAI model names that can be used in custom prompts.
// This prevents invalid model names from being saved and causing runtime failures.
var validModelNames = map[ModelName]bool{
	ModelNameGPT5Nano:  true,
	ModelNameGPT4oMini: true,
	ModelNameGPT5Mini:  true,
}

// IsValidModelName checks if a ModelName is valid and supported.
func IsValidModelName(modelName ModelName) bool {
	return validModelNames[modelName]
}

// ServiceUserPrompt represents a customizable AI prompt with hierarchical resolution support.
// Resolution hierarchy: User > UserGroup > Sender > Domain > Service > Global
type ServiceUserPrompt struct {
	gorm.Model

	// Hierarchical resolution fields
	ServiceID    *uint      `gorm:"index" json:"serviceID"`          // Nullable for global prompts
	Service      *Service   `gorm:"foreignKey:ServiceID" json:"-"`   // Foreign key relationship
	UserID       *uint      `gorm:"index" json:"userID"`             // Nullable, most specific - user level
	User         *User      `gorm:"foreignKey:UserID" json:"-"`      // Foreign key relationship
	UserGroupID  *uint      `gorm:"index" json:"userGroupID"`        // Nullable, user group level
	UserGroup    *UserGroup `gorm:"foreignKey:UserGroupID" json:"-"` // Foreign key relationship
	SenderEmail  *string    `gorm:"index" json:"senderEmail"`        // Nullable, sender-specific
	SenderDomain *string    `gorm:"index" json:"senderDomain"`       // Nullable, domain-level

	// Core prompt fields
	Feature    PromptFeature `json:"feature"`
	Type       PromptType    `json:"type"`
	PromptText string        `gorm:"type:text;not null" json:"promptText"`
	ModelName  *ModelName    `gorm:"type:text" json:"modelName"` // Nullable, can be set to use specific model for feat

	PreviousPromptText *string `gorm:"type:text" json:"previousPromptText"` // For rollback support
	IsActive           bool    `gorm:"default:true;not null" json:"isActive"`

	Notes         *string `gorm:"type:text" json:"notes"`         // For notes written intended to be user-facing
	InternalNotes *string `gorm:"type:text" json:"internalNotes"` // For internal notes written by Drumkit team

	CreatedBy       CreatedByType `gorm:"default:developer;not null" json:"createdBy"`
	CreatedByUserID *uint         `gorm:"index" json:"createdByUserId"` // Nullable, user ID of the creator
	CreatedByUser   *User         `gorm:"foreignKey:CreatedByUserID" json:"-"`

	// TODO: Audit history of prompt (this would come into play when Drumkit portal supports prompt creation, editing,
	//       and disabling)
	//       Audit history would include the user ID of the actor, the action taken, and the timestamp of the action.
}

// Validate ensures the model follows business rules for hierarchical resolution
func (p *ServiceUserPrompt) Validate() error {
	// Required fields
	if p.Feature == "" {
		return errors.New("feature is required")
	}
	if p.Type == "" {
		return errors.New("type is required")
	}
	if p.PromptText == "" {
		return errors.New("prompt_text is required")
	}

	// Normalize email and domain to lowercase
	// If normalization results in empty string, set to nil to prevent orphan records
	if p.SenderEmail != nil {
		normalized := strings.ToLower(strings.TrimSpace(*p.SenderEmail))
		if normalized == "" {
			p.SenderEmail = nil
		} else {
			p.SenderEmail = &normalized
		}
	}
	if p.SenderDomain != nil {
		normalized := strings.ToLower(strings.TrimSpace(*p.SenderDomain))
		if normalized == "" {
			p.SenderDomain = nil
		} else {
			p.SenderDomain = &normalized
		}
	}

	// Special-case for better error messaging/stability:
	// If both sender_domain and sender_email are provided, treat it as a domain-level conflict.
	if p.SenderDomain != nil && p.SenderEmail != nil {
		return errors.New("domain-specific prompts should not have sender_email")
	}

	// Hierarchical resolution rules
	// User-specific prompts must have UserID and ServiceID
	if p.UserID != nil && p.ServiceID == nil {
		return errors.New("user-specific prompts require service_id")
	}
	// User-specific prompts cannot have UserGroupID
	if p.UserID != nil && p.UserGroupID != nil {
		return errors.New("user-specific prompts cannot have user_group_id")
	}
	if p.UserID != nil && (p.SenderEmail != nil || p.SenderDomain != nil) {
		return errors.New("user-specific prompts cannot have sender_email or sender_domain")
	}

	// UserGroup-specific prompts must have UserGroupID and ServiceID
	if p.UserGroupID != nil && p.ServiceID == nil {
		return errors.New("user-group-specific prompts require service_id")
	}
	if p.UserGroupID != nil && (p.SenderEmail != nil || p.SenderDomain != nil) {
		return errors.New("user-group-specific prompts cannot have sender_email or sender_domain")
	}

	// Sender-specific prompts must have ServiceID and SenderEmail
	if p.SenderEmail != nil && p.ServiceID == nil {
		return errors.New("sender-specific prompts require service_id")
	}
	if p.SenderEmail != nil && (p.UserID != nil || p.UserGroupID != nil || p.SenderDomain != nil) {
		return errors.New("sender-specific prompts cannot have user_id, user_group_id, or sender_domain")
	}

	// Domain-specific prompts must have ServiceID and SenderDomain, but no SenderEmail
	if p.SenderDomain != nil && p.ServiceID == nil {
		return errors.New("domain-specific prompts require service_id")
	}
	if p.SenderDomain != nil && (p.UserID != nil || p.UserGroupID != nil) {
		return errors.New("domain-specific prompts cannot have user_id or user_group_id")
	}

	// Enforce "exactly one resolution level" invariant:
	// Global (no service_id) OR Service-only OR exactly one of:
	// user_id / user_group_id / sender_email / sender_domain (all require service_id).
	// Note: We do this *after* specific conflict checks above, so errors remain informative/stable.
	setCount := 0
	if p.UserID != nil {
		setCount++
	}
	if p.UserGroupID != nil {
		setCount++
	}
	if p.SenderEmail != nil {
		setCount++
	}
	if p.SenderDomain != nil {
		setCount++
	}
	if setCount > 1 {
		return errors.New("prompt resolution must be exactly one of user, user_group, sender, or domain")
	}

	// Validate model name if provided
	if p.ModelName != nil {
		if !IsValidModelName(*p.ModelName) {
			return fmt.Errorf(
				"invalid model_name: %s. Valid models are: %s, %s, %s",
				*p.ModelName,
				ModelNameGPT5Nano,
				ModelNameGPT4oMini,
				ModelNameGPT5Mini,
			)
		}
	}

	return nil
}

// BeforeSave validates the model before saving
func (p *ServiceUserPrompt) BeforeSave(_ *gorm.DB) error {
	return p.Validate()
}

// BeforeCreate validates the model before creating
func (p *ServiceUserPrompt) BeforeCreate(_ *gorm.DB) error {
	return p.Validate()
}

// BeforeUpdate validates the model before updating
func (p *ServiceUserPrompt) BeforeUpdate(_ *gorm.DB) error {
	return p.Validate()
}

// GetResolutionLevel returns a string describing the resolution level for logging
func (p *ServiceUserPrompt) GetResolutionLevel() string {
	if p.IsUserSpecific() {
		return "user"
	}
	if p.IsUserGroupSpecific() {
		return "user_group"
	}
	if p.IsSenderSpecific() {
		return "sender"
	}
	if p.IsDomainSpecific() {
		return "domain"
	}
	if p.IsServiceSpecific() {
		return "service"
	}
	return "global"
}

// IsUserSpecific returns true if this is a user-specific prompt
func (p *ServiceUserPrompt) IsUserSpecific() bool {
	return p.ServiceID != nil && p.UserID != nil
}

// IsUserGroupSpecific returns true if this is a user-group-specific prompt
func (p *ServiceUserPrompt) IsUserGroupSpecific() bool {
	return p.ServiceID != nil && p.UserGroupID != nil && p.UserID == nil
}

// IsSenderSpecific returns true if this is a sender-specific prompt
func (p *ServiceUserPrompt) IsSenderSpecific() bool {
	return p.ServiceID != nil && p.SenderEmail != nil
}

// IsDomainSpecific returns true if this is a domain-specific prompt
func (p *ServiceUserPrompt) IsDomainSpecific() bool {
	return p.ServiceID != nil && p.SenderDomain != nil && p.SenderEmail == nil
}

// IsServiceSpecific returns true if this is a service-specific prompt
func (p *ServiceUserPrompt) IsServiceSpecific() bool {
	return p.ServiceID != nil && p.UserID == nil && p.UserGroupID == nil &&
		p.SenderEmail == nil && p.SenderDomain == nil
}

// IsGlobal returns true if this is a global prompt
func (p *ServiceUserPrompt) IsGlobal() bool {
	return p.ServiceID == nil
}
