package models

import (
	"gorm.io/gorm"
)

// ProcessedAttachment stores the result of converting document attachments to markdown.
// Supports PDF, DOCX, XLSX, and XLS files.
// This prevents re-processing the same attachment across different pipelines
// (e.g., classification, load building, quote request).
//
// Design notes:
//   - Deduplication: Unique constraint on (service_id, rfc_message_id, original_file_name)
//     ensures we only process the same attachment once, even when multiple users receive
//     the same email. RFCMessageID is the same for all recipients of the same email.
//   - markdown_content is stored as compressed BYTEA (expect gzip/similar compression).
//   - processing_status tracks the state: pending → processing → success|failed.
//   - ThreadID is indexed for efficient thread-based queries but NOT used for deduplication
//     (it's user-specific and differs per recipient).
type ProcessedAttachment struct {
	gorm.Model

	// Foreign Keys
	// EmailID: The email that triggered processing (for reference/association)
	// NOTE: Due to deduplication by RFCMessageID + OriginalFileName, one ProcessedAttachment
	// can conceptually serve multiple emails (same attachment, different recipients).
	// Query by RFCMessageID + OriginalFileName, not EmailID, to find processed attachments.
	// IMPORTANT: No foreign key constraint - ProcessedAttachment is shared across recipients, so deleting
	// one Email should not delete the shared ProcessedAttachment that other recipients still need.
	// EmailID is kept as an informational reference only, without database-level foreign key constraint.
	EmailID   uint    `gorm:"index"` // Reference to the email that triggered processing
	ThreadID  string  `gorm:"index"` // For efficient thread-based retrieval
	ServiceID uint    `gorm:"uniqueIndex:idx_processed_attachment_dedup,priority:1;index"`
	Service   Service `gorm:"constraint:OnDelete:CASCADE"`

	// Provider Identifiers
	AttachmentExternalID string `gorm:"index"` // Provider-specific attachment ID (differs per user)
	MessageExternalID    string `gorm:"index"` // Provider-specific message ID (differs per user)

	// Metadata
	OriginalFileName string `gorm:"uniqueIndex:idx_processed_attachment_dedup,priority:3"`
	// RFCMessageID: RFC Message-ID header (same for all recipients of the same email)
	RFCMessageID string `gorm:"uniqueIndex:idx_processed_attachment_dedup,priority:2;index"`
	// S3URL: Where the file is stored (may differ per user, but content is the same)
	S3URL string `gorm:"index"`
	// MimeType: MIME type of the attachment
	// (e.g., application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document)
	MimeType string `gorm:"index"`

	// Core Data: Compressed Markdown Content
	MarkdownContent []byte `gorm:"type:bytea"`

	// Processing State
	ProcessingStatus string `gorm:"index"` // Current state: pending, processing, success, failed
	ErrorMessage     string `gorm:"type:text"`

	// Braintrust Integration
	BraintrustLogID *string // Optional: ID from Braintrust for tracking LLM operations
}

// TableName explicitly sets the database table name.
func (ProcessedAttachment) TableName() string {
	return "processed_attachments"
}
