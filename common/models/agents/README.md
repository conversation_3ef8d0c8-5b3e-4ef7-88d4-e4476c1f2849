# Quick Quote Agents

# ADR

https://www.notion.so/drumkitai/Quick-Quote-Agent-v1-Auto-Draft-Replies-2c72b16b087a800b8db5ea98deb118dd#2ca2b16b087a8022a9efc5da7e7e80cf

# Database Design

`QuickQuoteAgent` and `QuickQuoteAgentTask` defines configuration & actions of QQ agent/workflow,
while `QuickQuoteAgentInvocation` and `QuickQuoteAgentTaskInvocation` are actual executions of said agent.

## Tasks as JSONB vs. Separate Tables

To future proof metrics queries, `QuickQuoteAgentTask` and `QuickQuoteAgentTaskInvocation` have a 1-or-many relationship to their respective parents.

## Diagram

### Abridged

_Some JSONB structures/table relationships excluded for simplicity_

```
erDiagram
    Service ||--o{ QuickQuoteAgent : "has many"
    Service ||--o{ User : "has many"
    Service ||--o{ UserGroup : "has many"
    Service ||--o{ QuoteRequest : "has many"
    Service ||--o{ Integration : "has many"

    QuickQuoteAgent ||--|| QuickQuoteAgentConfig : "embeds"
    QuickQuoteAgent }o--o{ User : "many-to-many (quick_quote_agent_users)"
    QuickQuoteAgent }o--o{ UserGroup : "many-to-many (quick_quote_agent_groups)"
    QuickQuoteAgent ||--o{ QuickQuoteAgentInvocation : "has many"
    QuickQuoteAgent ||--o{ AgentTaskConfig : "has many"

    QuickQuoteAgentInvocation ||--|| QuickQuoteAgent : "belongs to"
    QuickQuoteAgentInvocation ||--|| QuoteRequest : "belongs to"
    QuickQuoteAgentInvocation ||--o{ AgentTaskInvocation : "has many"

    AgentTaskConfig ||--o{ AgentTaskInvocation : "has many"
    AgentTaskConfig ||--|| QuickQuoteAgent : "belongs to"

    AgentTaskInvocation ||--|| QuickQuoteAgentInvocation : "belongs to"
    AgentTaskInvocation ||--|| AgentTaskConfig : "belongs to"

    User }o--o{ UserGroup : "many-to-many (user_group_users)"
    User ||--|| Service : "belongs to"
    User ||--o{ QuoteRequest : "has many"

    UserGroup ||--|| Service : "belongs to"

    QuoteRequest ||--|| User : "belongs to"
    QuoteRequest ||--|| Service : "belongs to"
    QuoteRequest ||--|| Email : "belongs to"

    Service {
        uint ID
        string Name
        string SCAC
    }

    QuickQuoteAgent {
        uint ID
        uint ServiceID
        bool IsServiceWide
    }

    QuickQuoteAgentConfig {
        string[] EmailIncludeList
        string[] EmailExcludeList
        string TransportTypeOverride
        string QuoteSelectionStrategy
        AgentPricingOverride PricingOverride
        float64 FuelSurchargePerMileUSDOverride
        string QuoteSourceOverride
        int MaxRetriesAllowed
    }

    AgentTaskConfig {
        uint ID
        uint QuickQuoteAgentID
        timestamp CreatedAt
        timestamp UpdatedAt
        timestamp DeletedAt
        int Step
        string Action
        int MaxRetriesAllowed
    }

    QuickQuoteAgentInvocation {
        uint ID
        uint QuickQuoteAgentID
        uint QuoteRequestID
        timestamp StartedAt
        timestamp CompletedAt
        int DurationSeconds
        string Status
        string UserResponseStatus
        string UserFeedback
        string Error
        string BraintrustTraceID
        string LambdaRequestID
    }

    AgentTaskInvocation {
        uint ID
        uint QuickQuoteAgentInvocationID
        uint AgentTaskConfigID
        timestamp CreatedAt
        timestamp UpdatedAt
        timestamp DeletedAt
        int Step
        string Action
        timestamp StartedAt
        timestamp CompletedAt
        int DurationSeconds
        string Status
        int Retries
        string Error
        string BraintrustSpanID
        TaskInvocationMetadata Metadata
    }

    User {
        uint ID
        uint ServiceID
        string Name
        string EmailAddress
    }

    UserGroup {
        uint ID
        uint ServiceID
        string Name
    }

    QuoteRequest {
        uint ID
        uint UserID
        uint ServiceID
        uint EmailID
    }

    Email {
        uint ID
        uint UserID
        uint ServiceID
        string ExternalID
    }

    Integration {
        uint ID
        uint ServiceID
        string Name
    }
```

### Detailed

```mermaid
erDiagram
    Service ||--o{ QuickQuoteAgent : "has many"
    Service ||--o{ User : "has many"
    Service ||--o{ UserGroup : "has many"
    Service ||--o{ QuoteRequest : "has many"
    Service ||--o{ EmailTemplate : "has many"
    Service ||--o{ Integration : "has many"

    QuickQuoteAgent ||--|| QuickQuoteAgentConfig : "embeds"
    QuickQuoteAgent }o--o{ User : "many-to-many (quick_quote_agent_users)"
    QuickQuoteAgent }o--o{ UserGroup : "many-to-many (quick_quote_agent_groups)"
    QuickQuoteAgent ||--o{ QuickQuoteAgentInvocation : "has many"
    QuickQuoteAgent ||--o{ AgentTaskConfig : "has many"

    QuickQuoteAgentInvocation ||--|| QuickQuoteAgent : "belongs to"
    QuickQuoteAgentInvocation ||--|| QuoteRequest : "belongs to"
    QuickQuoteAgentInvocation ||--o{ AgentTaskInvocation : "has many"

    AgentTaskConfig ||--o{ AgentTaskInvocation : "has many"
    AgentTaskConfig ||--|| QuickQuoteAgent : "belongs to"
    AgentTaskConfig ||--o| EmailTask : "optional (JSONB)"
    AgentTaskConfig ||--o| NotifyUserTask : "optional (JSONB)"
    AgentTaskConfig ||--o| SubmitQuoteToPortalTask : "optional (JSONB)"
    AgentTaskConfig ||--o| SubmitQuoteToTMSTask : "optional (JSONB)"

    AgentTaskInvocation ||--|| QuickQuoteAgentInvocation : "belongs to"
    AgentTaskInvocation ||--|| AgentTaskConfig : "belongs to"
    AgentTaskInvocation ||--o| PricingLookupTaskInvocation : "optional (JSONB)"
    AgentTaskInvocation ||--o| DraftEmailTaskInvocation : "optional (JSONB)"

    EmailTask }o--o| EmailTemplate : "optional reference"

    SubmitQuoteToPortalTask }o--|| Integration : "references"
    SubmitQuoteToTMSTask }o--|| Integration : "references"

    User }o--o{ UserGroup : "many-to-many (user_group_users)"
    User ||--|| Service : "belongs to"
    User ||--o{ QuoteRequest : "has many"
    User ||--o{ EmailTemplate : "has many"

    UserGroup ||--|| Service : "belongs to"
    UserGroup ||--o{ EmailTemplate : "has many"

    QuoteRequest ||--|| User : "belongs to"
    QuoteRequest ||--|| Service : "belongs to"
    QuoteRequest ||--|| Email : "belongs to"

    EmailTemplate ||--o| Service : "belongs to (optional)"
    EmailTemplate ||--o| User : "belongs to (optional)"
    EmailTemplate ||--o| UserGroup : "belongs to (optional)"

    Service {
        uint ID PK
        string Name
        string SCAC
        string Nickname
    }

    QuickQuoteAgent {
        uint ID PK
        uint ServiceID FK
        bool IsServiceWide
        QuickQuoteAgentConfig Config "embedded"
    }

    QuickQuoteAgentConfig {
        string[] EmailIncludeList
        string[] EmailExcludeList
        string TransportTypeOverride
        string QuoteSelectionStrategy
        AgentPricingOverride PricingOverride
        float64 FuelSurchargePerMileUSDOverride
        string QuoteSourceOverride
        ValueUnit MinQuoteValue
        ValueUnit MaxQuoteValue
        int MaxRetriesAllowed
    }

    AgentTaskConfig {
        uint ID PK
        uint QuickQuoteAgentID FK "implied"
        timestamp CreatedAt
        timestamp UpdatedAt
        timestamp DeletedAt
        int Step
        string Action "enum: QuickQuoteAgentAction"
        int MaxRetriesAllowed
        EmailTask DraftEmailTask "JSONB, optional"
        NotifyUserTask NotifyUserTask "JSONB, optional"
        SubmitQuoteToPortalTask SubmitQuoteTask "JSONB, optional"
        SubmitQuoteToTMSTask SubmitQuoteToTMSTask "JSONB, optional"
    }

    QuickQuoteAgentInvocation {
        uint ID PK
        uint QuickQuoteAgentID FK
        uint QuoteRequestID FK
        timestamp StartedAt
        timestamp CompletedAt
        int DurationSeconds
        string Status "enum: AgentInvocationStatus"
        string UserResponseStatus "enum: AgentUserResponseStatus"
        string UserFeedback
        string Error
        string BraintrustTraceID
        string LambdaRequestID
    }

    AgentTaskInvocation {
        uint ID PK
        uint QuickQuoteAgentInvocationID FK "implied"
        uint AgentTaskConfigID FK
        timestamp CreatedAt
        timestamp UpdatedAt
        timestamp DeletedAt
        int Step
        string Action "enum: QuickQuoteAgentAction"
        timestamp StartedAt
        timestamp CompletedAt
        int DurationSeconds
        string Status "enum: AgentInvocationStatus"
        int Retries
        string Error
        string BraintrustSpanID
        TaskInvocationMetadata Metadata "JSONB"
    }

    EmailTask {
        uint EmailTemplateID "optional FK"
    }

    TaskInvocationMetadata {
        PricingLookupTaskInvocation PricingLookupTaskInvocation "JSONB, optional"
        DraftEmailTaskInvocation DraftEmailTaskInvocation "JSONB, optional"
    }

    PricingLookupTaskInvocation {
        string SelectedQuoteSource
        float64 OriginalRate
        float64 FinalRate
        float64 MarginPercent "deprecated, use MarginType/MarginValue/PricingFormula"
        models.MarginType MarginType
        float64 MarginValue
        PricingFormula PricingFormula
        float64 Distance
        int NumQuotesReceived
        int NumQuoteErrors
    }

    DraftEmailTaskInvocation {
        uint GeneratedEmailID "optional"
    }

    NotifyUserTask {
        string Channel
        string MessageTemplate
        string FrequencyType
        string FrequencyUnit
        int FrequencyThreshold
    }

    SubmitQuoteToPortalTask {
        uint IntegrationID FK
        string URL
    }

    SubmitQuoteToTMSTask {
        uint IntegrationID FK
    }

    User {
        uint ID PK
        uint ServiceID FK
        string Name
        string EmailAddress
        string Role
    }

    UserGroup {
        uint ID PK
        uint ServiceID FK
        string Name
    }

    QuoteRequest {
        uint ID PK
        uint UserID FK
        uint ServiceID FK
        uint EmailID FK
        string Status
        QuoteLoadInfo SuggestedRequest "JSONB"
        QuoteLoadInfo AppliedRequest "JSONB"
    }

    EmailTemplate {
        uint ID PK
        uint ServiceID FK "optional"
        uint UserID FK "optional"
        uint UserGroupID FK "optional"
        string Name
        string Subject
        string Body
        string TemplateType
    }

    Email {
        uint ID PK
        uint UserID FK
        uint ServiceID FK
        string ExternalID
        string ThreadID
    }

    Integration {
        uint ID PK
        uint ServiceID FK
        string Name
        string Type
    }
```
