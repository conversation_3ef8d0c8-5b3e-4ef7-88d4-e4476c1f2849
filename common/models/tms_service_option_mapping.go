package models

import (
	"gorm.io/gorm"
)

// TMSServiceOptionMapping stores service options and their mappings to LoadMode enum
// for each TMS integration. This allows per-integration customization of service option mappings.
type TMSServiceOptionMapping struct {
	gorm.Model

	TMSIntegrationID uint        `gorm:"not null;uniqueIndex:idx_tms_service_option_mapping,priority:1;index" json:"tmsIntegrationId"` //nolint:lll // GORM tag is long but necessary
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`

	// 3G TMS service option fields
	ServiceOptionID              int    `gorm:"not null;uniqueIndex:idx_tms_service_option_mapping,priority:2" json:"serviceOptionId"` //nolint:lll // GORM tag is long but necessary
	ServiceOptionName            string `gorm:"type:text;not null" json:"serviceOptionName"`
	Description                  string `gorm:"type:text" json:"description"`
	ServiceOptionTransportModeID *int   `gorm:"type:integer" json:"serviceOptionTransportModeId"`
	TransportModeID              *int   `gorm:"type:integer" json:"transportModeId"`
	TransportModeNamesString     string `gorm:"type:text" json:"transportModeNamesString"`

	// Mapped load mode (our enum)
	MappedLoadMode LoadMode `gorm:"type:text" json:"mappedLoadMode"`
}

// TableName specifies the table name for GORM
func (TMSServiceOptionMapping) TableName() string {
	return "tms_service_option"
}
