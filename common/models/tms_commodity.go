package models

import (
	"gorm.io/gorm"
)

// Unique indices created on rds/migrate.go due to composite unique index on TMS ID and nested External TMS ID.
type TMSCommodity struct {
	gorm.Model
	// Foreign keys
	TMSIntegrationID uint `json:"tmsIntegrationId,omitempty"`
	//nolint:revive // validate:"-" is required to skip validation of nested structs
	TMSIntegration Integration `gorm:"foreignKey:TMSIntegrationID" json:"-" validate:"-"`
	// Optional: Some TMS systems (like GlobalTranz) have customer-specific commodities
	// Nullable to support TMS systems that don't have customer-specific commodities
	TMSCustomerID *uint `gorm:"default:NULL" json:"tmsCustomerId,omitempty"`
	//nolint:revive // validate:"-" is required to skip validation of nested structs
	TMSCustomer TMSCustomer `gorm:"foreignKey:TMSCustomerID" json:"-" validate:"-"`

	// External TMS ID (product ID from GlobalTranz)
	ExternalTMSID string `json:"externalTMSID"`

	// Basic commodity information
	Commodity            string `json:"commodity,omitempty"`            // Basic description from List Product API
	CommodityDescription string `json:"commodityDescription,omitempty"` // Full description from Product Details API

	// Product details
	ProductDefaultDetailsID int `json:"productDefaultDetailsId,omitempty"`

	// Weight information
	WeightAmount *float64 `json:"weightAmount,omitempty"` // From weight.amount
	WeightUnit   *string  `json:"weightUnit,omitempty"`   // From weight.unit
	WeightUnitID *int     `json:"weightUnitId,omitempty"` // From weight.unitId (1 = lbs, etc.)

	// Dimensions
	Length    *float64 `json:"length,omitempty"`
	Width     *float64 `json:"width,omitempty"`
	Height    *float64 `json:"height,omitempty"`
	DimUnit   *string  `json:"dimUnit,omitempty"`   // From dim.unit
	DimUnitID *int     `json:"dimUnitId,omitempty"` // From dim.unitId (0 = Inches, etc.)

	// Handling and packaging
	HandlingUnitTypeID *int `json:"handlingUnitTypeId,omitempty"`
	HandlingUnitCount  *int `json:"handlingUnitCount,omitempty"` // Pallet count
	PieceCount         int  `json:"pieceCount,omitempty"`

	// Freight classification
	FreightClassID *int   `json:"freightClassId,omitempty"` // From API (int)
	FreightClass   string `json:"freightClass,omitempty"`   // String representation if needed
	NMFC           string `json:"nmfc,omitempty"`           // From List Product API
	NMFCNumber     string `json:"nmfcNumber,omitempty"`     // From Product Details API

	// Hazmat information
	HazardousMaterial            bool    `json:"hazardousMaterial,omitempty"`  // From hazmat.flag
	HazmatClass                  *string `json:"hazmatClass,omitempty"`        // From hazmat.class
	HazmatClassID                int     `json:"hazmatClassId,omitempty"`      // From hazmat.classId
	HazmatGroup                  *string `json:"hazmatGroup,omitempty"`        // From hazmat.group
	HazmatGroupID                *int    `json:"hazmatGroupId,omitempty"`      // From hazmat.groupId
	HazmatCode                   *string `json:"hazmatCode,omitempty"`         // From hazmat.code
	HazmatPrefixID               int     `json:"hazmatPrefixId,omitempty"`     // From hazmat.hazmatPrefixId
	HazmatChemicalName           *string `json:"hazmatChemicalName,omitempty"` // From hazmat.chemicalName
	HazmatEmergencyContactNumber *string `json:"hazmatEmergencyContactNumber,omitempty"`

	// Additional properties
	Stackable   bool     `json:"stackable,omitempty"`
	CarrierType int      `json:"carrierType,omitempty"`
	Density     *float64 `json:"density,omitempty"` // Calculated field (lb/cu ft)

	// Optimization flag: tracks if full product details have been fetched
	HasDetailsFetched bool `json:"hasDetailsFetched,omitempty"`

	// Full product details stored as JSON (optional, for caching full details)
	ProductDetailsJSON *string `gorm:"type:JSONB" json:"productDetailsJson,omitempty"`
}
