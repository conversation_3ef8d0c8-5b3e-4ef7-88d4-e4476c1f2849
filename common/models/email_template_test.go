package models

import (
	"reflect"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetTemplateVariables(t *testing.T) {
	tests := []struct {
		name         string
		templateType EmailTemplateType
		expected     []string
	}{
		{
			name:         "AppointmentSchedulingRequestHTML",
			templateType: AppointmentSchedulingRequestHTML,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.CaseCount}}",
				"{{.Company}}",
				"{{.ConsigneeRefNumber}}",
				"{{.ContactName}}",
				"{{.ContactPhone}}",
				"{{.CustomerRefNumber}}",
				"{{.DateRequested}}",
				"{{.DryChilledOrFrozen}}",
				"{{.Integration}}",
				"{{.LTLorTL}}",
				"{{.LoadExternalID}}",
				"{{.Notes}}",
				"{{.PONumber}}",
				"{{.PalletCount}}",
				"{{.PickupName}}",
				"{{.PickupRefNumber}}",
				"{{.PreferredDate}}",
				"{{.PreferredTime}}",
				"{{.RequestedTime}}",
				"{{.StopType}}",
				"{{.StopTypeCapitalized}}",
				"{{.TimeRequested}}",
				"{{.TrailerType}}",
				"{{.WarehouseName}}",
			},
		},
		{
			name:         "CarrierQuoteByLocation",
			templateType: CarrierQuoteByLocation,
			expected: []string{
				"{{.DeliveryEndTime}}",
				"{{.DeliveryLocation}}",
				"{{.DeliveryStartTime}}",
				"{{.ItemDescription}}",
				"{{.PickupEndTime}}",
				"{{.PickupLocation}}",
				"{{.PickupStartTime}}",
				"{{.TransportType}}",
			},
		},
		{
			name:         "CarrierQuoteByGroup",
			templateType: CarrierQuoteByGroup,
			expected: []string{
				"{{.DeliveryEndTime}}",
				"{{.DeliveryLocation}}",
				"{{.DeliveryStartTime}}",
				"{{.ItemDescription}}",
				"{{.PickupEndTime}}",
				"{{.PickupLocation}}",
				"{{.PickupStartTime}}",
				"{{.TransportType}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierDispatch",
			templateType: TrackAndTraceCarrierDispatch,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierPickup - embedded fields promoted",
			templateType: TrackAndTraceCarrierPickup,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.PickupAddress}}",
				"{{.PickupAppointment}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierAfterPickup - embedded fields promoted",
			templateType: TrackAndTraceCarrierAfterPickup,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.PickupAddress}}",
				"{{.PickupAppointment}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierInTransit - embedded fields promoted",
			templateType: TrackAndTraceCarrierInTransit,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.DropoffAddress}}",
				"{{.DropoffAppointment}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierDropoff - embedded fields promoted",
			templateType: TrackAndTraceCarrierDropoff,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.DropoffAddress}}",
				"{{.DropoffAppointment}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierAfterDropoff - embedded fields promoted",
			templateType: TrackAndTraceCarrierAfterDropoff,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.DropoffAddress}}",
				"{{.DropoffAppointment}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
		{
			name:         "TrackAndTraceCarrierPODCollection - embedded fields promoted",
			templateType: TrackAndTraceCarrierPODCollection,
			expected: []string{
				"{{.AdditionalReferences}}",
				"{{.DriverName}}",
				"{{.FreightTrackingID}}",
				"{{.FromCity}}",
				"{{.FromState}}",
				"{{.PONumbers}}",
				"{{.ToCity}}",
				"{{.ToState}}",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			variables := GetTemplateVariables(tt.templateType)

			// Sort both slices for comparison
			sort.Strings(variables)
			sort.Strings(tt.expected)

			assert.Equal(t, tt.expected, variables,
				"Template variables mismatch for %s", tt.templateType)
		})
	}
}

func TestGetTemplateVariables_UnknownType(t *testing.T) {
	variables := GetTemplateVariables("unknown_template_type")
	assert.Empty(t, variables, "Unknown template type should return empty slice")
}

func TestExtractFieldVariables_EmbeddedStructs(t *testing.T) {
	// Test that embedded structs have their fields promoted
	pickupType := reflect.TypeOf(PickupTemplateData{})
	variables := extractFieldVariables(pickupType, "")

	// Should contain promoted fields from BaseTemplateData
	assert.Contains(t, variables, "{{.FreightTrackingID}}")
	assert.Contains(t, variables, "{{.PONumbers}}")
	assert.Contains(t, variables, "{{.FromCity}}")
	assert.Contains(t, variables, "{{.FromState}}")
	assert.Contains(t, variables, "{{.ToCity}}")
	assert.Contains(t, variables, "{{.ToState}}")
	assert.Contains(t, variables, "{{.DriverName}}")
	assert.Contains(t, variables, "{{.AdditionalReferences}}")

	// Should contain fields from PickupTemplateData itself
	assert.Contains(t, variables, "{{.PickupAddress}}")
	assert.Contains(t, variables, "{{.PickupAppointment}}")

	// Should NOT contain the embedded struct name in the path
	assert.NotContains(t, variables, "{{.BaseTemplateData.FreightTrackingID}}")
	assert.NotContains(t, variables, "{{.BaseTemplateData.PONumbers}}")
}

func TestExtractFieldVariables_NestedNonEmbeddedStructs(t *testing.T) {
	// Test struct with non-embedded nested struct
	type Address struct {
		Street string
		City   string
	}
	type Person struct {
		Name    string
		Address Address
	}

	personType := reflect.TypeOf(Person{})
	variables := extractFieldVariables(personType, "")

	// Should contain top-level fields
	assert.Contains(t, variables, "{{.Name}}")

	// Should contain nested struct fields with full path
	assert.Contains(t, variables, "{{.Address.Street}}")
	assert.Contains(t, variables, "{{.Address.City}}")

	// Should NOT contain the struct itself as a variable (only its fields)
	assert.NotContains(t, variables, "{{.Address}}")
}

func TestGetStructTypeForTemplate(t *testing.T) {
	tests := []struct {
		name         string
		templateType EmailTemplateType
		expectedType reflect.Type
	}{
		{
			name:         "AppointmentSchedulingRequestHTML",
			templateType: AppointmentSchedulingRequestHTML,
			expectedType: reflect.TypeOf(AppointmentRequestData{}),
		},
		{
			name:         "CarrierQuoteByLocation",
			templateType: CarrierQuoteByLocation,
			expectedType: reflect.TypeOf(CarrierQuoteTemplateData{}),
		},
		{
			name:         "CarrierQuoteByGroup",
			templateType: CarrierQuoteByGroup,
			expectedType: reflect.TypeOf(CarrierQuoteTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierDispatch",
			templateType: TrackAndTraceCarrierDispatch,
			expectedType: reflect.TypeOf(BaseTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierPickup",
			templateType: TrackAndTraceCarrierPickup,
			expectedType: reflect.TypeOf(PickupTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierAfterPickup",
			templateType: TrackAndTraceCarrierAfterPickup,
			expectedType: reflect.TypeOf(PickupTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierInTransit",
			templateType: TrackAndTraceCarrierInTransit,
			expectedType: reflect.TypeOf(DropoffTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierDropoff",
			templateType: TrackAndTraceCarrierDropoff,
			expectedType: reflect.TypeOf(DropoffTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierAfterDropoff",
			templateType: TrackAndTraceCarrierAfterDropoff,
			expectedType: reflect.TypeOf(DropoffTemplateData{}),
		},
		{
			name:         "TrackAndTraceCarrierPODCollection",
			templateType: TrackAndTraceCarrierPODCollection,
			expectedType: reflect.TypeOf(PODCollectionTemplateData{}),
		},
		{
			name:         "Unknown template type",
			templateType: "unknown",
			expectedType: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			structType := getStructTypeForTemplate(tt.templateType)
			if tt.expectedType == nil {
				require.Nil(t, structType)
			} else {
				require.NotNil(t, structType)
				assert.Equal(t, tt.expectedType, structType)
			}
		})
	}
}

func TestTemplateVariablesMatchExistingTemplates(t *testing.T) {
	// This test ensures that the variables we return are actually used in the templates
	// and vice versa - that templates only use variables that are available

	tests := []struct {
		templateType EmailTemplateType
		template     EmailTemplate
	}{
		{TrackAndTraceCarrierPickup, GenericEmailTemplates[TrackAndTraceCarrierPickup]},
		{TrackAndTraceCarrierAfterPickup, GenericEmailTemplates[TrackAndTraceCarrierAfterPickup]},
		{TrackAndTraceCarrierInTransit, GenericEmailTemplates[TrackAndTraceCarrierInTransit]},
		{TrackAndTraceCarrierDropoff, GenericEmailTemplates[TrackAndTraceCarrierDropoff]},
		{TrackAndTraceCarrierAfterDropoff, GenericEmailTemplates[TrackAndTraceCarrierAfterDropoff]},
		{TrackAndTraceCarrierPODCollection, GenericEmailTemplates[TrackAndTraceCarrierPODCollection]},
	}

	for _, tt := range tests {
		t.Run(string(tt.templateType), func(t *testing.T) {
			variables := GetTemplateVariables(tt.templateType)

			// Verify that embedded struct fields are promoted (not prefixed with BaseTemplateData)
			for _, v := range variables {
				assert.NotContains(t, v, "BaseTemplateData",
					"Variable should not contain BaseTemplateData prefix: %s", v)
			}

			// For templates that use embedded structs, verify the canonical forms are available
			if tt.templateType == TrackAndTraceCarrierPickup ||
				tt.templateType == TrackAndTraceCarrierAfterPickup {
				assert.Contains(t, variables, "{{.FreightTrackingID}}",
					"Pickup templates should have promoted FreightTrackingID field")
				assert.Contains(t, variables, "{{.PONumbers}}",
					"Pickup templates should have promoted PONumbers field")
			}

			if tt.templateType == TrackAndTraceCarrierInTransit ||
				tt.templateType == TrackAndTraceCarrierDropoff ||
				tt.templateType == TrackAndTraceCarrierAfterDropoff {
				assert.Contains(t, variables, "{{.FreightTrackingID}}",
					"Dropoff templates should have promoted FreightTrackingID field")
				assert.Contains(t, variables, "{{.PONumbers}}",
					"Dropoff templates should have promoted PONumbers field")
			}

			if tt.templateType == TrackAndTraceCarrierPODCollection {
				assert.Contains(t, variables, "{{.FreightTrackingID}}",
					"POD Collection template should have promoted FreightTrackingID field")
			}
		})
	}
}

// TestEmbeddedStructFieldPromotion specifically tests the bug fix for embedded struct field paths.
// Before the fix, PickupTemplateData would generate {{.BaseTemplateData.FreightTrackingID}}
// instead of the canonical {{.FreightTrackingID}} that Go templates use for embedded fields.
func TestEmbeddedStructFieldPromotion(t *testing.T) {
	t.Run("PickupTemplateData promotes BaseTemplateData fields", func(t *testing.T) {
		variables := GetTemplateVariables(TrackAndTraceCarrierPickup)

		// These fields should be promoted from BaseTemplateData
		promotedFields := []string{
			"{{.FreightTrackingID}}",
			"{{.PONumbers}}",
			"{{.FromCity}}",
			"{{.FromState}}",
			"{{.ToCity}}",
			"{{.ToState}}",
			"{{.DriverName}}",
			"{{.AdditionalReferences}}",
		}

		for _, field := range promotedFields {
			assert.Contains(t, variables, field,
				"Field %s should be promoted from embedded BaseTemplateData", field)
		}

		// These are the wrong paths that the bug would have generated
		wrongPaths := []string{
			"{{.BaseTemplateData.FreightTrackingID}}",
			"{{.BaseTemplateData.PONumbers}}",
			"{{.BaseTemplateData.FromCity}}",
			"{{.BaseTemplateData.FromState}}",
			"{{.BaseTemplateData.ToCity}}",
			"{{.BaseTemplateData.ToState}}",
			"{{.BaseTemplateData.DriverName}}",
			"{{.BaseTemplateData.AdditionalReferences}}",
		}

		for _, wrongPath := range wrongPaths {
			assert.NotContains(t, variables, wrongPath,
				"Should not generate non-canonical path %s for embedded struct field", wrongPath)
		}
	})

	t.Run("DropoffTemplateData promotes BaseTemplateData fields", func(t *testing.T) {
		variables := GetTemplateVariables(TrackAndTraceCarrierDropoff)

		// These fields should be promoted from BaseTemplateData
		promotedFields := []string{
			"{{.FreightTrackingID}}",
			"{{.PONumbers}}",
			"{{.FromCity}}",
			"{{.FromState}}",
			"{{.ToCity}}",
			"{{.ToState}}",
			"{{.DriverName}}",
			"{{.AdditionalReferences}}",
		}

		for _, field := range promotedFields {
			assert.Contains(t, variables, field,
				"Field %s should be promoted from embedded BaseTemplateData", field)
		}

		// These are the wrong paths that the bug would have generated
		wrongPaths := []string{
			"{{.BaseTemplateData.FreightTrackingID}}",
			"{{.BaseTemplateData.PONumbers}}",
		}

		for _, wrongPath := range wrongPaths {
			assert.NotContains(t, variables, wrongPath,
				"Should not generate non-canonical path %s for embedded struct field", wrongPath)
		}
	})

	t.Run("PODCollectionTemplateData promotes BaseTemplateData fields", func(t *testing.T) {
		variables := GetTemplateVariables(TrackAndTraceCarrierPODCollection)

		// FreightTrackingID should be promoted from BaseTemplateData
		assert.Contains(t, variables, "{{.FreightTrackingID}}",
			"FreightTrackingID should be promoted from embedded BaseTemplateData")

		// This is the wrong path that the bug would have generated
		assert.NotContains(t, variables, "{{.BaseTemplateData.FreightTrackingID}}",
			"Should not generate non-canonical path for embedded struct field")
	})
}

func TestGetCaseCount(t *testing.T) {
	tests := []struct {
		name     string
		specs    Specifications
		expected string
	}{
		{
			name: "Cases with proper casing",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  1160,
					Unit: "Cases",
				},
			},
			expected: "1160",
		},
		{
			name: "Cases lowercase",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  585,
					Unit: "cases",
				},
			},
			expected: "585",
		},
		{
			name: "Case singular",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  1,
					Unit: "Case",
				},
			},
			expected: "1",
		},
		{
			name: "Case singular lowercase",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  24,
					Unit: "case",
				},
			},
			expected: "24",
		},
		{
			name: "Not cases - Pallets",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  50,
					Unit: "Pallets",
				},
			},
			expected: "",
		},
		{
			name: "Not cases - Boxes",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  100,
					Unit: "Boxes",
				},
			},
			expected: "",
		},
		{
			name: "Zero cases",
			specs: Specifications{
				TotalPieces: ValueUnit{
					Val:  0,
					Unit: "Cases",
				},
			},
			expected: "",
		},
		{
			name:     "Empty specifications",
			specs:    Specifications{},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getCaseCount(tt.specs)
			assert.Equal(t, tt.expected, result)
		})
	}
}
