package models

import (
	"gorm.io/gorm"
)

// TMSTransportTypeMapping stores equipment types and their mappings to TransportType enum
// for each TMS integration. This allows per-integration customization of transport type mappings.
type TMSTransportTypeMapping struct {
	gorm.Model

	TMSIntegrationID uint        `gorm:"not null;uniqueIndex:idx_tms_transport_type_mapping,priority:1;index" json:"tmsIntegrationId"` //nolint:lll // GORM tag is long but necessary
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`

	// 3G TMS equipment fields
	EquipmentID   int    `gorm:"not null;uniqueIndex:idx_tms_transport_type_mapping,priority:2" json:"equipmentId"` //nolint:lll // GORM tag is long but necessary
	EquipmentName string `gorm:"type:text;not null" json:"equipmentName"`
	EquipmentCode string `gorm:"type:text" json:"equipmentCode"`
	// dataValue_EquipmentClassification
	EquipmentClassification string `gorm:"type:text" json:"equipmentClassification"` //nolint:lll // Field name is descriptive

	// Mapped transport type (our enum)
	MappedTransportType TransportType `gorm:"type:text" json:"mappedTransportType"`

	// Additional equipment details (for reference)
	WeightMax    string `gorm:"type:text" json:"weightMax"`
	WeightMaxUOM string `gorm:"type:text" json:"weightMaxUOM"`
	VolumeMax    string `gorm:"type:text" json:"volumeMax"`
	VolumeMaxUOM string `gorm:"type:text" json:"volumeMaxUOM"`
	MaxPallets   int    `json:"maxPallets"`
}

// TableName specifies the table name for GORM
func (TMSTransportTypeMapping) TableName() string {
	return "tms_transport_type"
}
