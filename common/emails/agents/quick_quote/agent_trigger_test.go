package quickquoteagent

import (
	"context"
	"testing"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquotedb "github.com/drumkitai/drumkit/common/rds/agents/quick_quote"
)

func TestMatchEmailAddress(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		filters  []string
		expected bool
	}{
		{
			name:     "exact email match",
			email:    "<EMAIL>",
			filters:  []string{"<EMAIL>"},
			expected: true,
		},
		{
			name:     "exact email match case insensitive",
			email:    "<EMAIL>",
			filters:  []string{"<EMAIL>"},
			expected: true,
		},
		{
			name:     "domain match",
			email:    "<EMAIL>",
			filters:  []string{"customer.com"},
			expected: true,
		},
		{
			name:     "domain match case insensitive",
			email:    "<PERSON>@Customer.COM",
			filters:  []string{"customer.com"},
			expected: true,
		},
		{
			name:     "subdomain does not match domain filter",
			email:    "<EMAIL>",
			filters:  []string{"customer.com"},
			expected: false,
		},
		{
			name:     "no match",
			email:    "<EMAIL>",
			filters:  []string{"customer.com"},
			expected: false,
		},
		{
			name:     "empty filters returns false",
			email:    "<EMAIL>",
			filters:  []string{},
			expected: false,
		},
		{
			name:     "whitespace in email and filter",
			email:    "  <EMAIL>  ",
			filters:  []string{"  customer.com  "},
			expected: true,
		},
		{
			name:     "multiple filters - first match",
			email:    "<EMAIL>",
			filters:  []string{"customer.com", "other.com"},
			expected: true,
		},
		{
			name:     "multiple filters - second match",
			email:    "<EMAIL>",
			filters:  []string{"customer.com", "other.com"},
			expected: true,
		},
		{
			name:     "empty filter in list is skipped",
			email:    "<EMAIL>",
			filters:  []string{"", "customer.com"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchEmailAddress(tt.email, tt.filters)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMatchSenderAgainstFilters(t *testing.T) {
	tests := []struct {
		name        string
		sender      string
		includeList []string
		excludeList []string
		expected    bool
	}{
		{
			name:        "empty include list allows all senders",
			sender:      "<EMAIL>",
			includeList: []string{},
			excludeList: []string{},
			expected:    true,
		},
		{
			name:        "sender in include list is allowed",
			sender:      "<EMAIL>",
			includeList: []string{"customer.com"},
			excludeList: []string{},
			expected:    true,
		},
		{
			name:        "sender not in include list is rejected",
			sender:      "<EMAIL>",
			includeList: []string{"customer.com"},
			excludeList: []string{},
			expected:    false,
		},
		{
			name:        "sender in exclude list is rejected even with empty include list",
			sender:      "<EMAIL>",
			includeList: []string{},
			excludeList: []string{"blocked.com"},
			expected:    false,
		},
		{
			name:        "exclude takes precedence over include",
			sender:      "<EMAIL>",
			includeList: []string{"customer.com"},
			excludeList: []string{"<EMAIL>"},
			expected:    false,
		},
		{
			name:        "domain exclude blocks all from domain",
			sender:      "<EMAIL>",
			includeList: []string{},
			excludeList: []string{"blocked.com"},
			expected:    false,
		},
		{
			name:        "exact email exclude only blocks that email",
			sender:      "<EMAIL>",
			includeList: []string{"customer.com"},
			excludeList: []string{"<EMAIL>"},
			expected:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := agentmodels.QuickQuoteAgent{
				Config: agentmodels.QuickQuoteAgentConfig{
					EmailSenderIncludeList: pq.StringArray(tt.includeList),
					EmailSenderExcludeList: pq.StringArray(tt.excludeList),
				},
			}

			result := matchSenderAgainstFilters(tt.sender, agent)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TODO: Multi-stop loads..?
func TestCheckMinimumQuoteRequestInfo(t *testing.T) {
	tests := []struct {
		name     string
		qr       models.QuoteRequest
		expected bool
	}{
		{
			name: "valid with city and state for both locations",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupCity:    "Los Angeles",
					PickupState:   "CA",
					DropoffCity:   "New York",
					DropoffState:  "NY",
				},
			},
			expected: true,
		},
		{
			name: "valid with zip codes only",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "REEFER",
					PickupZip:     "90001",
					DropoffZip:    "10001",
				},
			},
			expected: true,
		},
		{
			name: "valid with mixed - pickup has city/state, dropoff has zip",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "FLATBED",
					PickupCity:    "Los Angeles",
					PickupState:   "CA",
					DropoffZip:    "10001",
				},
			},
			expected: true,
		},
		{
			name: "valid with mixed - pickup has zip, dropoff has city/state",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupZip:     "90001",
					DropoffCity:   "New York",
					DropoffState:  "NY",
				},
			},
			expected: true,
		},
		{
			name: "invalid - missing transport type",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					PickupCity:   "Los Angeles",
					PickupState:  "CA",
					DropoffCity:  "New York",
					DropoffState: "NY",
				},
			},
			expected: false,
		},
		{
			name: "invalid - missing pickup location",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					DropoffCity:   "New York",
					DropoffState:  "NY",
				},
			},
			expected: false,
		},
		{
			name: "invalid - missing dropoff location",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupCity:    "Los Angeles",
					PickupState:   "CA",
				},
			},
			expected: false,
		},
		{
			name: "invalid - pickup has only city (missing state)",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupCity:    "Los Angeles",
					DropoffCity:   "New York",
					DropoffState:  "NY",
				},
			},
			expected: false,
		},
		{
			name: "invalid - pickup has only state (missing city)",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupState:   "CA",
					DropoffCity:   "New York",
					DropoffState:  "NY",
				},
			},
			expected: false,
		},
		{
			name: "invalid - dropoff has only city (missing state)",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
					TransportType: "VAN",
					PickupCity:    "Los Angeles",
					PickupState:   "CA",
					DropoffCity:   "New York",
				},
			},
			expected: false,
		},
		{
			name: "invalid - empty quote request",
			qr: models.QuoteRequest{
				ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkMinimumQuoteRequestInfo(tt.qr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestShouldTriggerQuickQuoteAgent(t *testing.T) {
	ctx := context.Background()

	// Save original DB functions and restore after test
	origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
	origGetServiceWideAgents := dbGetServiceWideAgents
	origHasDuplicateLane := dbHasDuplicateLaneInThreadForUser
	defer func() {
		dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
		dbGetServiceWideAgents = origGetServiceWideAgents
		dbHasDuplicateLaneInThreadForUser = origHasDuplicateLane
	}()

	// Helper to create a service with quick quote enabled
	newServiceWithQQ := func(enabled bool) *models.Service {
		s := &models.Service{}
		s.IsQuickQuoteAgentEnabled = enabled
		return s
	}

	// Helper to create a valid quote request with minimum required fields
	newValidQuoteRequest := func() models.QuoteRequest {
		return models.QuoteRequest{
			ReadOnlySuggestedRequest: models.ReadOnlyQueryFields{
				TransportType: "VAN",
				PickupCity:    "Los Angeles",
				PickupState:   "CA",
				DropoffCity:   "New York",
				DropoffState:  "NY",
			},
		}
	}

	t.Run("returns false when service is nil", func(t *testing.T) {
		email := models.Email{Sender: "<EMAIL>"}
		qr := newValidQuoteRequest()

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, nil)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("returns false when quick quote is not enabled", func(t *testing.T) {
		email := models.Email{Sender: "<EMAIL>"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(false)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("returns false when minimum quote request info not present", func(t *testing.T) {
		email := models.Email{Sender: "<EMAIL>"}
		qr := models.QuoteRequest{} // Empty quote request - missing required fields
		service := newServiceWithQQ(true)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err) // Should not return error, just skip agent trigger
	})

	t.Run("returns false when no agents configured", func(t *testing.T) {
		origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
		origGetServiceWideAgents := dbGetServiceWideAgents
		defer func() {
			dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
			dbGetServiceWideAgents = origGetServiceWideAgents
		}()

		dbGetQuickQuoteAgentsForUser = func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{}, nil
		}
		dbGetServiceWideAgents = func(_ context.Context, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{}, nil
		}

		email := models.Email{UserID: 1, Sender: "<EMAIL>"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(true)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("returns agent when sender matches and no duplicate", func(t *testing.T) {
		origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
		origHasDuplicateLane := dbHasDuplicateLaneInThreadForUser
		defer func() {
			dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
			dbHasDuplicateLaneInThreadForUser = origHasDuplicateLane
		}()

		testAgent := agentmodels.QuickQuoteAgent{
			Config: agentmodels.QuickQuoteAgentConfig{
				EmailSenderIncludeList: pq.StringArray{"customer.com"},
			},
		}
		testAgent.ID = 123

		dbGetQuickQuoteAgentsForUser = func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{testAgent}, nil
		}
		dbHasDuplicateLaneInThreadForUser = func(
			_ context.Context,
			_ string,
			_ uint,
			_ quickquotedb.LaneFields,
		) (bool, error) {
			return false, nil
		}

		email := models.Email{UserID: 1, Sender: "<EMAIL>", ThreadID: "thread-1"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(true)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		require.NotNil(t, agent)
		assert.Equal(t, uint(123), agent.ID)
		assert.True(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("returns false when duplicate lane found in thread for same user", func(t *testing.T) {
		origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
		origHasDuplicateLane := dbHasDuplicateLaneInThreadForUser
		defer func() {
			dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
			dbHasDuplicateLaneInThreadForUser = origHasDuplicateLane
		}()

		testAgent := agentmodels.QuickQuoteAgent{
			Config: agentmodels.QuickQuoteAgentConfig{
				EmailSenderIncludeList: pq.StringArray{"customer.com"},
			},
		}
		testAgent.ID = 123

		dbGetQuickQuoteAgentsForUser = func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{testAgent}, nil
		}
		dbHasDuplicateLaneInThreadForUser = func(
			_ context.Context,
			_ string,
			_ uint,
			_ quickquotedb.LaneFields,
		) (bool, error) {
			return true, nil // Duplicate found for this user
		}

		email := models.Email{UserID: 1, Sender: "<EMAIL>", ThreadID: "thread-1"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(true)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("falls back to service-wide agents when no user agents", func(t *testing.T) {
		origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
		origGetServiceWideAgents := dbGetServiceWideAgents
		origHasDuplicateLane := dbHasDuplicateLaneInThreadForUser
		defer func() {
			dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
			dbGetServiceWideAgents = origGetServiceWideAgents
			dbHasDuplicateLaneInThreadForUser = origHasDuplicateLane
		}()

		serviceWideAgent := agentmodels.QuickQuoteAgent{
			IsServiceWide: true,
			Config: agentmodels.QuickQuoteAgentConfig{
				EmailSenderIncludeList: pq.StringArray{}, // Empty = allow all
			},
		}
		serviceWideAgent.ID = 456

		dbGetQuickQuoteAgentsForUser = func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{}, nil // No user-specific agents
		}
		dbGetServiceWideAgents = func(_ context.Context, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{serviceWideAgent}, nil
		}
		dbHasDuplicateLaneInThreadForUser = func(
			_ context.Context,
			_ string,
			_ uint,
			_ quickquotedb.LaneFields,
		) (bool, error) {
			return false, nil
		}

		email := models.Email{UserID: 1, Sender: "<EMAIL>", ThreadID: "thread-1"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(true)
		service.ID = 1

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		require.NotNil(t, agent)
		assert.Equal(t, uint(456), agent.ID)
		assert.True(t, shouldTrigger)
		assert.NoError(t, err)
	})

	t.Run("skips agent when sender not in include list", func(t *testing.T) {
		origGetAgentsForUser := dbGetQuickQuoteAgentsForUser
		defer func() {
			dbGetQuickQuoteAgentsForUser = origGetAgentsForUser
		}()

		testAgent := agentmodels.QuickQuoteAgent{
			Config: agentmodels.QuickQuoteAgentConfig{
				EmailSenderIncludeList: pq.StringArray{"allowed-customer.com"},
			},
		}
		testAgent.ID = 123

		dbGetQuickQuoteAgentsForUser = func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
			return []agentmodels.QuickQuoteAgent{testAgent}, nil
		}

		email := models.Email{UserID: 1, Sender: "<EMAIL>", ThreadID: "thread-1"}
		qr := newValidQuoteRequest()
		service := newServiceWithQQ(true)

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, qr, service)

		assert.Nil(t, agent)
		assert.False(t, shouldTrigger)
		assert.NoError(t, err)
	})
}
