package quickquoteagent

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteagenttaskinvocationDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/invocation/task"
)

// TaskExecutionContext holds all the context needed for executing agent tasks.
type TaskExecutionContext struct {
	Invocation   agentmodels.QuickQuoteAgentInvocation
	Agent        *agentmodels.QuickQuoteAgent
	Email        models.Email
	QuoteRequest models.QuoteRequest
	User         models.User
	Service      *models.Service

	// Task-specific results - each task stores its own result type
	PricingLookupTaskResult *PricingLookupTaskResult
	DraftEmailTaskResult    *DraftEmailTaskResult
}

// TaskHandler is a function that executes a specific task type.
// Returns task metadata that will be saved to the task invocation record.
type TaskHandler func(
	context.Context,
	*TaskExecutionContext,
	*agentmodels.QuickQuoteAgentTask,
) (*agentmodels.TaskInvocationMetadata, error)

// taskHandlers maps action types to their corresponding handlers.
var taskHandlers = map[agentmodels.QuickQuoteAgentAction]TaskHandler{
	agentmodels.PricingLookupAction:   handlePricingLookup,
	agentmodels.DraftReplyEmailAction: handleDraftReplyEmail,
	agentmodels.AddEmailLabelAction:   handleAddEmailLabel,
	// Future QQ task handlers can be added here
}

// ExecuteAgentTasks orchestrates execution of all tasks for an agent.
// If no tasks are found, it creates default tasks (pricing lookup + draft email) first.
func ExecuteAgentTasks(ctx context.Context, execCtx *TaskExecutionContext) error {
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "quickquoteagent.ExecuteAgentTasks", nil)
	defer func() { metaSpan.End(err) }()

	if execCtx.Agent == nil {
		err = errors.New("agent is required for task execution")
		log.Error(ctx, "missing agent", zap.Error(err))
		return err
	}

	if err = checkAndHandleAgentNoTasks(ctx, execCtx); err != nil {
		return err
	}

	// Sort tasks by step (should already be sorted from DB query)
	sort.Slice(execCtx.Agent.Config.Tasks, func(i, j int) bool {
		return execCtx.Agent.Config.Tasks[i].Step < execCtx.Agent.Config.Tasks[j].Step
	})

	log.Info(
		ctx,
		"Quick Quote Agent: executing agent tasks",
		zap.Uint("agentID", execCtx.Agent.ID),
		zap.Int("numTasks", len(execCtx.Agent.Config.Tasks)),
	)

	// Execute each task in order
	for i := range execCtx.Agent.Config.Tasks {
		task := &execCtx.Agent.Config.Tasks[i]
		if err = executeTask(ctx, execCtx, task); err != nil {
			return fmt.Errorf("task %d (%s) failed: %w", task.Step, task.Action, err)
		}
	}

	return nil
}

// executeTask executes a single task with retry logic and invocation tracking.
func executeTask(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
) error {
	handler := getTaskHandler(task.Action)
	if handler == nil {
		return fmt.Errorf("no handler for action: %s", task.Action)
	}

	log.Info(
		ctx,
		"Quick Quote Agent: starting task execution",
		zap.Uint("taskID", task.ID),
		zap.Int("step", task.Step),
		zap.String("action", string(task.Action)),
		zap.Uint("quickQuoteAgentID", execCtx.Agent.ID),
	)

	taskInvocation, err := startTaskInvocation(
		ctx,
		execCtx.Agent.ID,
		execCtx.Invocation.ID,
		task.ID,
		execCtx.Service.ID,
		execCtx.User.ID,
		execCtx.Email.Account,
		task.Step,
		task.Action,
	)
	if err != nil {
		return fmt.Errorf("failed to create task invocation record: %w", err)
	}

	// Execute with retry logic
	var metadata *agentmodels.TaskInvocationMetadata

	// TODO: Retry on retryable errors with backoff
	for attempt := 0; attempt <= task.MaxRetriesAllowed; attempt++ {
		if attempt > 0 {
			log.Warn(
				ctx,
				"retrying task execution",
				zap.Int("attempt", attempt),
				zap.Int("maxRetries", task.MaxRetriesAllowed),
				zap.String("action", string(task.Action)),
			)

			// Update retry count in DB
			taskInvocation.Retries = attempt
			if updateErr := quickquoteagenttaskinvocationDB.UpdateQuickQuoteAgentTaskInvocationByID(
				ctx,
				taskInvocation.ID,
				&agentmodels.QuickQuoteAgentTaskInvocation{Retries: attempt},
			); updateErr != nil {
				log.Warn(ctx, "failed to update retry count", zap.Error(updateErr))
			}
		}

		// Execute the task handler
		metadata, err = executeTaskHandler(ctx, execCtx, task, handler)
		if err == nil {
			break
		}

		log.WarnNoSentry(
			ctx,
			"task attempt failed",
			zap.Int("attempt", attempt),
			zap.String("action", string(task.Action)),
			zap.Error(err),
		)
	}

	// End task invocation tracking with metadata
	endTaskInvocation(ctx, taskInvocation, err, metadata)

	if err != nil {
		if !task.FailOpen {
			log.Warn(
				ctx,
				"task execution failed",
				zap.String("action", string(task.Action)),
				zap.Error(err),
			)
			return err
		}

		log.WarnNoSentry(
			ctx,
			"task execution failed but fail open is enabled, continuing",
			zap.Uint("taskID", task.ID),
			zap.String("action", string(task.Action)),
			zap.Error(err),
		)

		return nil
	}

	log.Info(
		ctx,
		"task execution completed successfully",
		zap.Uint("taskID", task.ID),
		zap.String("action", string(task.Action)),
	)

	return nil
}

// executeTaskHandler wraps the handler execution.
func executeTaskHandler(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
	handler TaskHandler,
) (*agentmodels.TaskInvocationMetadata, error) {
	// Execute the handler
	return handler(ctx, execCtx, task)
}

// getTaskHandler returns the appropriate handler for a given action type.
func getTaskHandler(action agentmodels.QuickQuoteAgentAction) TaskHandler {
	return taskHandlers[action]
}
