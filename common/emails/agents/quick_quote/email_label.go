package quickquoteagent

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/fn/api/env"
)

func handleAddEmailLabel(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
) (_ *agentmodels.TaskInvocationMetadata, err error) {

	ctx, span := otel.StartSpan(ctx, "agent.handleAddEmailLabel", nil)
	defer func() { span.End(err) }()

	genEmail := execCtx.Invocation.GeneratedEmail
	if genEmail == nil {
		return nil, errors.New("generated email is nil, this should not happen")
	}

	log.Info(
		ctx,
		"executing add email label task",
		zap.Uint("taskID", task.ID),
		zap.Uint("agentID", execCtx.Agent.ID),
		zap.String("emailProvider", string(execCtx.User.EmailProvider)),
		zap.Uint("generatedEmailID", genEmail.ID),
		zap.String("generatedEmailExternalID", genEmail.ExternalID),
	)

	metadata := &agentmodels.TaskInvocationMetadata{}

	if task.Metadata.AddEmailLabelTask == nil {
		return nil, errors.New("add email label task metadata is nil")
	}

	if task.Metadata.AddEmailLabelTask.Label == (models.EmailClientLabel{}) {
		return nil, errors.New("add email label task label is empty")
	}

	switch execCtx.User.EmailProvider {
	case models.OutlookEmailProvider:
		err = addEmailLabelToOutlook(ctx, execCtx, task)
	default:
		err = fmt.Errorf("unsupported email provider for handleAddEmailLabel: %s", execCtx.User.EmailProvider)
		return nil, err
	}

	return metadata, err
}

func addEmailLabelToOutlook(
	ctx context.Context,
	execCtx *TaskExecutionContext,
	task *agentmodels.QuickQuoteAgentTask,
) (err error) {
	ctx, span := otel.StartSpan(ctx, "agent.addEmailLabelToOutlook", nil)
	defer func() { span.End(err) }()

	client, err := msclient.New(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		&execCtx.User,
	)
	if err != nil {
		return fmt.Errorf("failed to create Outlook client: %w", err)
	}

	err = client.AddCategoriesToMessage(
		ctx,
		execCtx.Invocation.GeneratedEmail.ExternalID,
		[]models.EmailClientLabel{task.Metadata.AddEmailLabelTask.Label},
	)
	if err != nil {
		return fmt.Errorf("failed to add categories to Outlook message: %w", err)
	}

	return nil
}
