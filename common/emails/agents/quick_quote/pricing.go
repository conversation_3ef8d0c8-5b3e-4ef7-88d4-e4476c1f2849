package quickquoteagent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
	quoteprivate "github.com/drumkitai/drumkit/fn/api/routes/quote/sidebar"
)

// AgentQuote represents a pricing quote fetched for the agent.
// This is a simplified version of the quote sidebar's Quote struct.
type AgentQuote struct {
	ID       uint                     `json:"id"`
	Source   models.QuoteSource       `json:"source"`
	Type     models.QuoteTypeInSource `json:"type,omitempty"`
	Rates    AgentQuoteRates          `json:"rates"`
	Distance float64                  `json:"distance,omitempty"`
	Metadata map[string]any           `json:"metadata,omitempty"`
}

// AgentQuoteRates contains the rate values for an agent quote.
type AgentQuoteRates struct {
	Target        float64 `json:"target,omitempty"`
	Low           float64 `json:"low,omitempty"`
	High          float64 `json:"high,omitempty"`
	TargetPerMile float64 `json:"targetPerMile,omitempty"`
	LowPerMile    float64 `json:"lowPerMile,omitempty"`
	HighPerMile   float64 `json:"highPerMile,omitempty"`
}

// AgentQuoteError represents an error from a pricing integration.
type AgentQuoteError struct {
	Source models.QuoteSource `json:"source"`
	Error  string             `json:"error"`
}

// PricingLookupInput contains the parameters needed for pricing lookups.
type PricingLookupInput struct {
	Service        models.Service
	User           models.User
	Stops          []models.Stop
	TransportType  models.TransportType
	PickupDate     time.Time
	DeliveryDate   time.Time
	QuoteRequestID uint
	EmailID        uint
	ThreadID       string
}

// PricingLookupResult contains the results of a pricing lookup.
type PricingLookupResult struct {
	Quotes      []AgentQuote
	QuoteErrors []AgentQuoteError
	Distance    float64
}

// GetQuotesForAgent fetches pricing quotes from all available integrations for use by the agent.
// This function reuses the existing getQuickQuotes logic from the quote sidebar to avoid code duplication.
func GetQuotesForAgent(ctx context.Context, input PricingLookupInput) (*PricingLookupResult, error) {
	ctx, span := otel.StartSpan(ctx, "agent.GetQuotesForAgent", nil)
	var err error
	defer func() { span.End(err) }()

	if len(input.Stops) < 2 {
		err = errors.New("at least two stops are required")
		return nil, err
	}

	log.Info(
		ctx,
		"preparing pricing lookup",
		zap.Int("numStops", len(input.Stops)),
		zap.String("transportType", string(input.TransportType)),
		zap.Bool("isMultiStopEnabled", input.Service.IsMultiStopQuickQuoteEnabled),
	)

	// Convert agent input to QuickQuotePrivateBody format
	// We set both NewStops (for multi-stop) and Stops (for legacy compatibility)
	// GetQuickQuotes will use NewStops if multi-stop is enabled, otherwise it falls back to Stops
	body := &quoteprivate.QuickQuotePrivateBody{
		TransportType:        input.TransportType,
		NewStops:             input.Stops,
		PickupDate:           input.PickupDate,
		DeliveryDate:         input.DeliveryDate,
		QuoteRequestID:       input.QuoteRequestID,
		EmailID:              input.EmailID,
		ThreadID:             input.ThreadID,
		SelectedQuickQuoteID: nil,
	}

	// Also populate legacy Stops format for backward compatibility
	// This ensures GetQuickQuotes can validate stops even if multi-stop isn't enabled
	body.Stops = convertToLegacyStops(input.Stops)

	// Create a minimal customer for the quote lookup
	// The actual customer lookup is handled by the quote sidebar's PrepareCustomerAndDates,
	// but since we already have a quote request, we can use an empty customer
	// GetQuickQuotes will handle the case where customer is not fully populated
	customer := models.TMSCustomer{}

	// Call the existing pricing logic
	resp, err := quoteprivate.GetQuickQuotes(ctx, body, input.Service, input.User, customer)
	if err != nil {
		log.Error(ctx, "failed to get quotes from pricing integrations", zap.Error(err))
		return nil, fmt.Errorf("pricing lookup failed: %w", err)
	}

	// Convert quotes from quoteprivate.Quote to AgentQuote
	// TODO: Greenscreens quotes only populate per-mile rates in get_quick_quote_v2.go
	// while DAT quotes populate both total and per-mile rates. This inconsistency should be fixed
	// in get_quick_quote_v2.go to populate all rate fields for consistency across all quote sources.
	agentQuotes := make([]AgentQuote, len(resp.Quotes))
	for i, q := range resp.Quotes {
		agentQuotes[i] = convertQuoteToAgentQuote(q)
	}

	// Convert quote errors
	agentErrors := make([]AgentQuoteError, len(resp.QuoteErrors))
	for i, e := range resp.QuoteErrors {
		agentErrors[i] = AgentQuoteError{
			Source: e.Source,
			Error:  e.Error,
		}
	}

	// Extract distance from first available eligible quote
	var distance float64
	for _, quote := range agentQuotes {
		if quote.Distance > 0 {
			distance = quote.Distance
			break
		}
	}

	return &PricingLookupResult{
		Quotes:      agentQuotes,
		QuoteErrors: agentErrors,
		Distance:    distance,
	}, nil
}

// convertQuoteToAgentQuote converts a quoteprivate.Quote to an AgentQuote.
// This handles the conversion of rate values and includes a workaround for quotes
// that only populate per-mile rates (e.g., Greenscreens quotes).
// If total rates are missing but per-mile rates exist, it calculates total rates from per-mile rates.
func convertQuoteToAgentQuote(q quoteprivate.Quote) AgentQuote {
	rates := AgentQuoteRates{
		Target:        q.Rates.Target,
		Low:           q.Rates.Low,
		High:          q.Rates.High,
		TargetPerMile: q.Rates.TargetPerMile,
		LowPerMile:    q.Rates.LowPerMile,
		HighPerMile:   q.Rates.HighPerMile,
	}

	// Workaround: If total rates are missing but per-mile rates exist, calculate total rates
	// This handles Greenscreens quotes which only populate per-mile rates
	if rates.Target == 0 && rates.TargetPerMile > 0 && q.Distance > 0 {
		rates.Target = rates.TargetPerMile * q.Distance
		// Only calculate Low/High if their per-mile rates are positive to avoid creating zero values
		if rates.LowPerMile > 0 {
			rates.Low = rates.LowPerMile * q.Distance
		}
		if rates.HighPerMile > 0 {
			rates.High = rates.HighPerMile * q.Distance
		}
	}

	return AgentQuote{
		ID:       q.ID,
		Source:   q.Source,
		Type:     q.Type,
		Distance: q.Distance,
		Metadata: q.Metadata,
		Rates:    rates,
	}
}

// convertToLegacyStops converts a slice of models.Stop to the legacy quoteprivate.Stop format.
// It returns only the first and last stops for backward compatibility with GetQuickQuotes.
// Returns an empty slice if there are fewer than 2 stops.
func convertToLegacyStops(stops []models.Stop) []quoteprivate.Stop {
	if len(stops) < 2 {
		return nil
	}

	return []quoteprivate.Stop{
		{
			Order:   stops[0].Order,
			City:    stops[0].Address.City,
			State:   stops[0].Address.State,
			Zip:     stops[0].Address.Zip,
			Country: quote.CountryName(stops[0].Address.Country),
		},
		{
			Order:   stops[len(stops)-1].Order,
			City:    stops[len(stops)-1].Address.City,
			State:   stops[len(stops)-1].Address.State,
			Zip:     stops[len(stops)-1].Address.Zip,
			Country: quote.CountryName(stops[len(stops)-1].Address.Country),
		},
	}
}
