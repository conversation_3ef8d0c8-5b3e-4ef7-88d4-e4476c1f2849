package quickquoteagent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote"
	quickquoteagentinvocationDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/invocation"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

var (
	dbGetQuickQuoteAgentsForUser            = quickquoteDB.GetQuickQuoteAgentsForUser
	dbGetServiceWideAgents                  = quickquoteDB.GetServiceWideAgents
	dbHasDuplicateLaneInThreadForUser       = quickquoteDB.HasDuplicateLaneInThreadForUser
	dbUpdateQuoteRequestWithAgentInvocation = quoteRequestDB.UpdateQuoteRequestWithAgentInvocation
)

// Setter functions for testing
func SetDBGetQuickQuoteAgentsForUser(f func(context.Context, uint, uint) ([]agentmodels.QuickQuoteAgent, error)) {
	dbGetQuickQuoteAgentsForUser = f
}

func SetDBGetServiceWideAgents(f func(context.Context, uint) ([]agentmodels.QuickQuoteAgent, error)) {
	dbGetServiceWideAgents = f
}

func SetDBHasDuplicateLaneInThreadForUser(
	f func(context.Context, string, uint, quickquoteDB.LaneFields) (bool, error),
) {
	dbHasDuplicateLaneInThreadForUser = f
}

func SetDBUpdateQuoteRequestWithAgentInvocation(
	f func(context.Context, uint, uint, uint) error,
) {
	dbUpdateQuoteRequestWithAgentInvocation = f
}

// TriggerQuickQuoteAgent checks if a Quick Quote Agent should be triggered
// for each quote request and updates the quote request with the agent ID if so.
// This is called after quote requests are created and saved to the database.
func TriggerQuickQuoteAgent(
	ctx context.Context,
	email models.Email,
	quoteRequests []models.QuoteRequest,
	service *models.Service,
) (err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "emails.triggerQuickQuoteAgent", nil)
	defer func() { metaSpan.End(err) }()

	if service == nil || !service.IsQuickQuoteAgentEnabled {
		return nil
	}

	var errs []error
	for i := range quoteRequests {
		qr := &quoteRequests[i]

		agent, shouldTrigger, err := ShouldTriggerQuickQuoteAgent(ctx, email, *qr, service)
		if err != nil {
			log.Error(
				ctx,
				"error checking if quick quote agent should trigger",
				zap.Uint("quoteRequestID", qr.ID),
				zap.Error(err),
			)
			errs = append(errs, fmt.Errorf("error triggering quick quote agent for quote request %d: %w", qr.ID, err))
			continue
		}
		if !shouldTrigger || agent == nil {
			// No agent to trigger or agent not found, skip agent invocation on this quote request
			continue
		}

		invocation := agentmodels.QuickQuoteAgentInvocation{
			QuickQuoteAgentID:  agent.ID,
			QuoteRequestID:     qr.ID,
			EmailID:            email.ID,
			ThreadID:           email.ThreadID,
			ServiceID:          service.ID,
			UserID:             email.UserID,
			UserEmailAddress:   email.Account,
			StartedAt:          time.Now(),
			Status:             agentmodels.InProgressInvocation,
			UserResponseStatus: agentmodels.PendingUserResponse,
		}

		if err := quickquoteagentinvocationDB.CreateQuickQuoteAgentInvocation(ctx, &invocation); err != nil {
			log.Error(
				ctx,
				"failed to create agent invocation",
				zap.Uint("quoteRequestID", qr.ID),
				zap.Uint("agentID", agent.ID),
				zap.Error(err),
			)
			errs = append(errs, fmt.Errorf(
				"failed to create agent invocation for quote request %d with agent ID %d: %w",
				qr.ID,
				agent.ID,
				err,
			))
			continue
		}

		if err := dbUpdateQuoteRequestWithAgentInvocation(ctx, qr.ID, agent.ID, invocation.ID); err != nil {
			log.Error(
				ctx,
				"failed to update quote request with agent invocation",
				zap.Uint("quoteRequestID", qr.ID),
				zap.Uint("agentID", agent.ID),
				zap.Uint("invocationId", invocation.ID),
				zap.Error(err),
			)
			errs = append(errs, fmt.Errorf(
				"failed to update quote request %d with agent ID %d and invocation ID %d: %w",
				qr.ID,
				agent.ID,
				invocation.ID,
				err,
			))
			// NOTE: We could update this to fail open, run QQ agent instead of early return
			// and try updating quote request again later.
			// Mark invocation as failed since we couldn't update the quote request
			if endErr := endQuickQuoteAgentInvocation(
				ctx,
				invocation.ID,
				invocation.StartedAt,
				err,
			); endErr != nil {
				log.Warn(ctx, "error updating quick quote agent invocation after failure", zap.Error(endErr))
			}
			continue
		}

		log.Info(
			ctx,
			"quick quote agent triggered for quote request",
			zap.Uint("invocationId", invocation.ID),
			zap.Uint("quoteRequestID", qr.ID),
			zap.Uint("agentID", agent.ID),
			zap.String("sender", email.Sender),
		)

		// Invoke quick quote agent
		agentErr := invokeQuickQuoteAgent(ctx, invocation, agent, email, *qr, service)
		if agentErr != nil {
			log.Error(
				ctx,
				"quick quote agent invocation failed",
				zap.Uint("invocationId", invocation.ID),
				zap.Error(agentErr),
			)
			errs = append(errs, fmt.Errorf(
				"agent invocation failed for quote request %d: %w",
				qr.ID,
				agentErr,
			))
			// Mark invocation as failed
			if endErr := endQuickQuoteAgentInvocation(
				ctx,
				invocation.ID,
				invocation.StartedAt,
				agentErr,
			); endErr != nil {
				log.Warn(ctx, "error updating quick quote agent invocation after failure", zap.Error(endErr))
			}
			continue
		}

		// Mark invocation as successfully completed
		err = endQuickQuoteAgentInvocation(
			ctx,
			invocation.ID,
			invocation.StartedAt,
			nil,
		)
		if err != nil {
			log.Warn(ctx, "error updating quick quote agent invocation after completion", zap.Error(err))
		}
	}

	if len(errs) > 0 {
		err = fmt.Errorf("encountered %d errors while processing quote requests: %v", len(errs), errs)
		return err
	}

	return nil
}

// endQuickQuoteAgentInvocation marks a QQ Agent invocation as complete or failed and updates
// the completion time and duration. If err is not nil, the invocation is marked as failed
// and the error message is stored.
func endQuickQuoteAgentInvocation(
	ctx context.Context,
	invocationID uint,
	startedAt time.Time,
	err error,
) error {
	completedAt := time.Now()
	durationSeconds := int(completedAt.Sub(startedAt).Seconds())

	updates := &agentmodels.QuickQuoteAgentInvocation{
		CompletedAt:     completedAt,
		DurationSeconds: durationSeconds,
	}

	if err != nil {
		updates.Status = agentmodels.FailedInvocation
		errMsg := err.Error()
		updates.Error = &errMsg
		return quickquoteagentinvocationDB.UpdateQuickQuoteAgentInvocationByID(ctx, invocationID, updates)
	}

	updates.Status = agentmodels.CompletedInvocation
	return quickquoteagentinvocationDB.UpdateQuickQuoteAgentInvocationByID(ctx, invocationID, updates)
}

// invokeQuickQuoteAgent runs the quick quote agent workflow via the task orchestrator.
// The orchestrator executes all configured tasks (pricing lookup, draft email, etc.) in order.
func invokeQuickQuoteAgent(
	ctx context.Context,
	invocation agentmodels.QuickQuoteAgentInvocation,
	agent *agentmodels.QuickQuoteAgent,
	email models.Email,
	quoteRequest models.QuoteRequest,
	service *models.Service,
) error {
	ctx, span := otel.StartSpan(ctx, "emails.invokeQuickQuoteAgent", nil)
	var err error
	defer func() { span.End(err) }()

	// Fetch the user
	user, err := userDB.GetByID(ctx, email.UserID)
	if err != nil {
		log.Error(ctx, "failed to get user for agent invocation", zap.Error(err))
		return fmt.Errorf("failed to get user: %w", err)
	}

	execCtx := &TaskExecutionContext{
		Invocation:   invocation,
		Agent:        agent,
		Email:        email,
		QuoteRequest: quoteRequest,
		User:         user,
		Service:      service,
	}

	// Execute all tasks via orchestrator
	err = ExecuteAgentTasks(ctx, execCtx)
	if err != nil {
		log.Error(ctx, "task execution failed in invokeQuickQuoteAgent", zap.Error(err))
		return fmt.Errorf("task execution failed in invokeQuickQuoteAgent: %w", err)
	}

	// Update invocation with generated email ID (if set by tasks)
	if execCtx.Invocation.GeneratedEmailID != nil {
		err = quickquoteagentinvocationDB.UpdateQuickQuoteAgentInvocationByID(
			ctx,
			invocation.ID,
			&agentmodels.QuickQuoteAgentInvocation{
				GeneratedEmailID: execCtx.Invocation.GeneratedEmailID,
			},
		)
		if err != nil {
			log.Warn(ctx, "failed to update invocation with generated email ID", zap.Error(err))
			// Non-fatal - the draft was created, just not linked in invocation
			err = nil // Clear error so span doesn't report failure on success
		}

		log.Info(
			ctx,
			"email draft created",
			zap.Uint("generatedEmailID", *execCtx.Invocation.GeneratedEmailID),
		)
	}

	return nil
}

// ShouldTriggerQuickQuoteAgent determines if a Quick Quote Agent should be triggered
// for the given email and quote request. It returns the matching agent and a boolean
// indicating whether the agent should be triggered.
//
// The function:
// 1. Finds agents associated with the email recipient (user) via association tables
// 2. Falls back to service-wide agents if no user-specific agent is found
// 3. Filters by email sender using include/exclude lists
// 4. Checks for duplicate lanes in the thread to avoid drafting duplicate emails
func ShouldTriggerQuickQuoteAgent(
	ctx context.Context,
	email models.Email,
	quoteRequest models.QuoteRequest,
	service *models.Service,
) (_ *agentmodels.QuickQuoteAgent, _ bool, err error) {

	ctx, span := otel.StartSpan(ctx, "emails.ShouldTriggerQuickQuoteAgent", nil)
	defer func() { span.End(err) }()

	if service == nil || !service.IsQuickQuoteAgentEnabled {
		return nil, false, nil
	}

	if !checkMinimumQuoteRequestInfo(quoteRequest) {
		return nil, false, nil
	}

	// Finds agents for the user (direct user association or via user groups membership)
	agents, err := dbGetQuickQuoteAgentsForUser(ctx, email.UserID, service.ID)
	if err != nil {
		log.Warn(
			ctx,
			"error getting agents for user, falling back to service-wide",
			zap.Uint("userID", email.UserID),
			zap.Error(err),
		)
	}

	// Fallback to service-wide agents if no user-specific agents found
	if len(agents) == 0 {
		log.Info(
			ctx,
			"no user-specific agents found, checking service-wide agents",
			zap.Uint("userID", email.UserID),
		)

		agents, err = dbGetServiceWideAgents(ctx, service.ID)
		if err != nil {
			log.Warn(
				ctx,
				"error getting service-wide agents",
				zap.Uint("serviceID", service.ID),
				zap.Error(err),
			)
			return nil, false, err
		}
	}

	if len(agents) == 0 {
		log.Info(
			ctx, "no quick quote agents configured for user or service",
			zap.Uint("userID", email.UserID),
			zap.Uint("serviceID", service.ID),
		)
		return nil, false, nil
	}

	// Finds the first agent that matches the sender filters
	for i := range agents {
		agent := &agents[i]

		if !matchSenderAgainstFilters(email.Sender, *agent) {
			log.Debug(
				ctx,
				"sender does not match agent filters",
				zap.String("sender", email.Sender),
				zap.Uint("agentID", agent.ID),
			)
			continue
		}

		// Checks for duplicate lane in thread for this specific user
		// This allows different users who receive the same email to each get their own agent invocation
		laneFields := quickquoteDB.NewLaneFieldsFromQuoteRequest(quoteRequest)
		isDuplicate, err := dbHasDuplicateLaneInThreadForUser(ctx, email.ThreadID, email.UserID, laneFields)
		if err != nil {
			log.Warn(
				ctx,
				"error checking for duplicate lane in thread for user",
				zap.String("threadID", email.ThreadID),
				zap.Uint("userID", email.UserID),
				zap.Error(err),
			)
			return nil, false, err
		}

		if isDuplicate {
			log.Info(
				ctx,
				"duplicate lane found in thread, skipping agent trigger",
				zap.String("threadID", email.ThreadID),
				zap.Uint("agentID", agent.ID),
				zap.String("pickupCity", laneFields.PickupCity),
				zap.String("dropoffCity", laneFields.DropoffCity),
			)
			return nil, false, nil
		}

		log.Info(
			ctx,
			"quick quote agent trigger conditions met",
			zap.Uint("agentID", agent.ID),
			zap.String("sender", email.Sender),
			zap.Uint("quoteRequestID", quoteRequest.ID),
		)

		return agent, true, nil
	}

	log.Info(
		ctx,
		"no matching agent found after filter checks",
		zap.String("sender", email.Sender),
		zap.Int("agentsChecked", len(agents)),
	)

	return nil, false, nil
}

// checkMinimumQuoteRequestInfo checks if the minimum quote request info is present to run quick quote agent.
// Fields required are necessary to make a successful 2-stop price lookup via GetQuickQuoteV2().
// TODO: Add support for multi-stop loads?
//
// Required fields:
// - Transport type (required for pricing lookup)
// - Pickup location: Either (city AND state) OR zip code
// - Dropoff location: Either (city AND state) OR zip code
//
// Optional fields (will be defaulted by GetQuickQuoteV2):
// - Customer name (can be empty)
// - Pickup/delivery dates (will default to today+1 and today+2)
func checkMinimumQuoteRequestInfo(quoteRequest models.QuoteRequest) bool {
	req := quoteRequest.ReadOnlySuggestedRequest

	if req.TransportType == "" {
		return false
	}

	// Pickup location: need either (city AND state) OR zip
	hasPickupCityState := req.PickupCity != "" && req.PickupState != ""
	hasPickupZip := req.PickupZip != ""
	if !hasPickupCityState && !hasPickupZip {
		return false
	}

	// Dropoff location: need either (city AND state) OR zip
	hasDropoffCityState := req.DropoffCity != "" && req.DropoffState != ""
	hasDropoffZip := req.DropoffZip != ""
	if !hasDropoffCityState && !hasDropoffZip {
		return false
	}

	return true
}

// matchSenderAgainstFilters checks if the email sender matches the agent's include/exclude lists.
// Returns true if the sender should trigger the agent, false otherwise.
//
// Filter priority:
// 1. If sender is in ExcludeList, reject (return false)
// 2. If IncludeList is empty, allow all senders (return true)
// 3. If IncludeList is non-empty, sender must be in it to proceed
func matchSenderAgainstFilters(sender string, agent agentmodels.QuickQuoteAgent) bool {
	// 1. If sender is in ExcludeList, reject
	if len(agent.Config.EmailSenderExcludeList) > 0 {
		if matchEmailAddress(sender, agent.Config.EmailSenderExcludeList) {
			return false
		}
	}

	// 2. If IncludeList is empty, allow all senders (agent is open to all customers)
	if len(agent.Config.EmailSenderIncludeList) == 0 {
		return true
	}

	// 3. If IncludeList is non-empty, sender must be in it
	return matchEmailAddress(sender, agent.Config.EmailSenderIncludeList)
}

// matchEmailAddress checks if an email address matches any entry in the filters list.
// Supports both exact email matches and domain matches.
//
// Examples:
//   - "<EMAIL>" matches filter "<EMAIL>" (exact match)
//   - "<EMAIL>" matches filter "customer.com" (domain match)
//   - "<EMAIL>" does NOT match filter "customer.com" (subdomain not matched)
func matchEmailAddress(email string, filters []string) bool {
	email = strings.ToLower(strings.TrimSpace(email))

	for _, filter := range filters {
		filter = strings.ToLower(strings.TrimSpace(filter))
		if filter == "" {
			continue
		}

		// Exact email match
		if email == filter {
			return true
		}

		// Domain match (filter = "customer.com", email = "<EMAIL>")
		if strings.HasSuffix(email, "@"+filter) {
			return true
		}
	}

	return false
}
