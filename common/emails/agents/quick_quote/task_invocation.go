package quickquoteagent

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteagenttaskDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/invocation/task"
)

// startTaskInvocation creates and starts a new task invocation record.
func startTaskInvocation(
	ctx context.Context,
	quickQuoteAgentID,
	invocationID,
	agentTaskID,
	serviceID,
	userID uint,
	userEmailAddress string,
	step int,
	action agentmodels.QuickQuoteAgentAction,
) (*agentmodels.QuickQuoteAgentTaskInvocation, error) {

	taskInvocation := &agentmodels.QuickQuoteAgentTaskInvocation{
		QuickQuoteAgentID:           quickQuoteAgentID,
		QuickQuoteAgentInvocationID: invocationID,
		AgentTaskID:                 agentTaskID,
		ServiceID:                   serviceID,
		UserID:                      userID,
		UserEmailAddress:            userEmailAddress,
		Step:                        step,
		Action:                      action,
		StartedAt:                   time.Now(),
		Status:                      agentmodels.InProgressInvocation,
		Retries:                     0,
	}

	if err := quickquoteagenttaskDB.CreateQuickQuoteAgentTaskInvocation(ctx, taskInvocation); err != nil {
		log.Error(
			ctx,
			"failed to create task invocation",
			zap.Uint("invocationID", invocationID),
			zap.String("action", string(action)),
			zap.Error(err),
		)
		return nil, err
	}

	log.Info(
		ctx,
		"started task invocation",
		zap.Uint("taskInvocationID", taskInvocation.ID),
		zap.Uint("invocationID", invocationID),
		zap.String("action", string(action)),
		zap.Int("step", step),
	)

	return taskInvocation, nil
}

// endTaskInvocation marks a task invocation as complete or failed.
func endTaskInvocation(
	ctx context.Context,
	taskInvocation *agentmodels.QuickQuoteAgentTaskInvocation,
	err error,
	metadata *agentmodels.TaskInvocationMetadata,
) {
	if taskInvocation == nil {
		return
	}

	completedAt := time.Now()
	durationSeconds := int(completedAt.Sub(taskInvocation.StartedAt).Seconds())

	updates := &agentmodels.QuickQuoteAgentTaskInvocation{
		CompletedAt:     completedAt,
		DurationSeconds: durationSeconds,
	}

	updates.Status = agentmodels.CompletedInvocation
	if err != nil {
		updates.Status = agentmodels.FailedInvocation
		errMsg := err.Error()
		updates.Error = &errMsg
	}

	if metadata != nil {
		updates.Metadata = *metadata
	}

	if updateErr := quickquoteagenttaskDB.UpdateQuickQuoteAgentTaskInvocationByID(
		ctx,
		taskInvocation.ID,
		updates,
	); updateErr != nil {
		log.Warn(
			ctx,
			"failed to update task invocation",
			zap.Uint("taskInvocationID", taskInvocation.ID),
			zap.Error(updateErr),
		)
	}

	log.Info(
		ctx,
		"completed task invocation",
		zap.Uint("taskInvocationID", taskInvocation.ID),
		zap.String("action", string(taskInvocation.Action)),
		zap.String("status", string(updates.Status)),
		zap.Int("durationSeconds", durationSeconds),
	)
}
