package services

import (
	"context"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

// AttachmentProcessorService handles downloading, converting, and storing markdown content
// for PDF attachments. It ensures each attachment is processed only once and can be reused
// across multiple pipelines (classification, load building, quote request) and
// across users who received the same email.
//
// Key responsibilities:
// - Queue attachments for async processing after email ingestion
// - Track processing status (pending → processing → success|failed)
// - Provide cached markdown to consumers
// - Handle processing failures with error tracking
// - Support status polling for async-aware consumers
type AttachmentProcessorService interface {
	// ProcessEmailAttachments processes all attachments for an email.
	// Called immediately after email ingestion to download, convert, compress, and save markdown.
	// This ensures markdown is available for downstream pipelines.
	//
	// For each attachment:
	//   1. Queues as pending in RDS
	//   2. Downloads from S3
	//   3. Converts PDF to markdown
	//   4. Compresses and saves to RDS
	//
	// Runs synchronously to ensure all markdown is ready before returning.
	// Continues processing even if individual attachments fail.
	//
	// Args:
	//   - emailID: Database ID of email
	//   - threadID: Thread identifier
	//   - serviceID: Customer/service ID for multi-tenancy
	//   - rfcMessageID: RFC Message-ID header (same for all recipients) - used for deduplication
	//   - attachments: List of attachments to process
	//
	// Returns:
	//   - error: Only returned if batch insert fails. Individual attachment failures
	//     are logged but don't cause the method to return an error (fail-open).
	ProcessEmailAttachments(
		ctx context.Context,
		emailID uint,
		threadID string,
		serviceID uint,
		rfcMessageID string,
		attachments []models.Attachment,
	) error

	// GetMarkdownIfReady returns the processed markdown content if available.
	// Non-blocking: returns immediately without waiting.
	// Used by consumers that can gracefully degrade if markdown isn't ready yet.
	//
	// Returns:
	//   - markdown (string): The converted markdown content if found
	//   - err: nil if found, gorm.ErrRecordNotFound if not found/not ready, or other error for database issues
	GetMarkdownIfReady(
		ctx context.Context,
		emailID uint,
		attachmentExternalID string,
	) (markdown string, err error)

	// GetMarkdownOrWait returns the markdown content, waiting up to maxWait for processing.
	// Blocking: polls the database until ready or timeout.
	// Used by consumers that can tolerate short waits for better UX.
	//
	// Args:
	//   - maxWait: Maximum time to wait for processing (e.g., 30 seconds)
	//
	// Returns:
	//   - markdown (string): The converted markdown content if successful
	//   - found (bool): true if ready, false if timeout/failed
	//   - err: Error details if processing failed, or timeout message
	GetMarkdownOrWait(
		ctx context.Context,
		emailID uint,
		attachmentExternalID string,
		maxWait time.Duration,
	) (markdown string, found bool, err error)

	// GetStatus returns the current processing status of an attachment.
	// Used by consumers that want to be "mindful" of processing state.
	//
	// Returns:
	//   - status (string): "pending", "processing", "success", or "failed"
	//   - errorMsg (string): Non-empty if status='failed'
	//   - err: nil if found, gorm.ErrRecordNotFound if not found, or other error for database issues
	GetStatus(
		ctx context.Context,
		emailID uint,
		attachmentExternalID string,
	) (status string, errorMsg string, err error)

	// GetAllMarkdownForEmail returns all processed markdown content for an email.
	// Combines ready and pending attachments.
	// Used by pipelines that need all attachments at once (load building).
	//
	// Returns:
	//   - attachments: Map of attachment_external_id → markdown content
	//   - pendingCount: Number of attachments still processing
	//   - failedCount: Number of attachments that failed
	GetAllMarkdownForEmail(
		ctx context.Context,
		emailID uint,
	) (attachments map[string]string, pendingCount int, failedCount int, err error)
}

// ProcessingStatus constants
const (
	StatusPending    = "pending"
	StatusProcessing = "processing"
	StatusSuccess    = "success"
	StatusFailed     = "failed"
)
