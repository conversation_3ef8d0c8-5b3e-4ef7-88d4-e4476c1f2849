package services

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// MockS3Client is a mock implementation of s3fetcher.Fetcher
type MockS3Client struct {
	FetchObjectFromS3Func func(ctx context.Context, url string) ([]byte, error)
	GetObjectSizeFunc     func(ctx context.Context, url string) (int64, error)
}

func (m *MockS3Client) FetchObjectFromS3(ctx context.Context, url string) ([]byte, error) {
	if m.FetchObjectFromS3Func != nil {
		return m.FetchObjectFromS3Func(ctx, url)
	}
	return []byte("mock pdf data"), nil
}

func (m *MockS3Client) DownloadObjectFromS3ToDirectory(_ context.Context, _ string, _ string) (string, error) {
	return "", nil
}

func (m *MockS3Client) GetObjectSize(ctx context.Context, url string) (int64, error) {
	if m.GetObjectSizeFunc != nil {
		return m.GetObjectSizeFunc(ctx, url)
	}
	// Default: return a reasonable size (1MB) for testing
	return 1024 * 1024, nil
}

// MockOpenAIService is a mock implementation of openai.Service
type MockOpenAIService struct {
	GetResponseFunc func(
		ctx context.Context, email models.Email, attachment models.Attachment,
		projectDetails braintrustsdk.ProjectDetails, opts ...openai.ResponseOptions,
	) (openai.GetResponseOutput, error)
}

func (m *MockOpenAIService) GetResponse(
	ctx context.Context, email models.Email, attachment models.Attachment,
	projectDetails braintrustsdk.ProjectDetails, opts ...openai.ResponseOptions,
) (openai.GetResponseOutput, error) {
	if m.GetResponseFunc != nil {
		return m.GetResponseFunc(ctx, email, attachment, projectDetails, opts...)
	}
	return openai.GetResponseOutput{}, nil
}

func (m *MockOpenAIService) GetEmbedding(_ context.Context, _ string) ([]float64, error) {
	return []float64{}, nil
}

// Test helpers - use public service methods

// Helper function for tests to compress markdown
func compressContent(t *testing.T, markdown string) []byte {
	compressed, err := helpers.CompressGzip(markdown)
	require.NoError(t, err)
	return compressed
}

// setupTestDB creates a PostgreSQL test database (for integration tests)
// Only runs when TEST_LIVE_DB=true is set
func setupTestDB(t *testing.T) context.Context {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return nil
	}

	return rds.SetupTestDB(t)
}

// Integration tests using PostgreSQL (full behavior verification)
// These only run when TEST_LIVE_DB=true is set

func TestGetMarkdownIfReady(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(context.Context) uint // Returns emailID
		attachmentID   string
		expectFound    bool
		expectMarkdown string
		expectErr      bool
	}{
		{
			name: "markdown found and ready",
			setup: func(ctx context.Context) uint {
				// Create email with attachment
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)

				// Create ProcessedAttachment with RFCMessageID + OriginalFileName
				testMarkdown := "# Test Markdown\n\nTest content"
				compressed, err := helpers.CompressGzip(testMarkdown)
				require.NoError(t, err)
				pa := &models.ProcessedAttachment{
					EmailID:              email.ID,
					ThreadID:             "thread-123",
					ServiceID:            1,
					RFCMessageID:         "rfc-msg-123",
					OriginalFileName:     "test.pdf",
					AttachmentExternalID: "att-123",
					MessageExternalID:    "msg-123",
					ProcessingStatus:     StatusSuccess,
					MarkdownContent:      compressed,
				}
				rds.WithContext(ctx).Create(pa)
				return email.ID
			},
			attachmentID:   "att-123",
			expectFound:    true,
			expectMarkdown: "# Test Markdown\n\nTest content",
			expectErr:      false,
		},
		{
			name: "markdown not found",
			setup: func(ctx context.Context) uint {
				// Create email but no ProcessedAttachment
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)
				return email.ID
			},
			attachmentID: "att-123",
			expectFound:  false,
			expectErr:    false,
		},
		{
			name: "markdown pending (not ready)",
			setup: func(ctx context.Context) uint {
				// Create email with attachment
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)

				// Create ProcessedAttachment with pending status
				pa := &models.ProcessedAttachment{
					EmailID:              email.ID,
					ThreadID:             "thread-123",
					ServiceID:            1,
					RFCMessageID:         "rfc-msg-123",
					OriginalFileName:     "test.pdf",
					AttachmentExternalID: "att-123",
					MessageExternalID:    "msg-123",
					ProcessingStatus:     StatusPending,
				}
				rds.WithContext(ctx).Create(pa)
				return email.ID
			},
			attachmentID: "att-123",
			expectFound:  false,
			expectErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := setupTestDB(t)
			var emailID uint
			if tt.setup != nil {
				emailID = tt.setup(ctx)
			}

			service, err := NewAttachmentProcessorService(
				ctx, rds.WithContext(ctx), &MockS3Client{
					GetObjectSizeFunc: func(_ context.Context, _ string) (int64, error) {
						return 1024 * 1024, nil
					},
				}, &MockOpenAIService{},
			)
			require.NoError(t, err)

			markdown, err := service.GetMarkdownIfReady(ctx, emailID, tt.attachmentID)

			if tt.expectErr {
				assert.Error(t, err)
				return
			}

			if tt.expectFound {
				require.NoError(t, err)
				assert.Equal(t, tt.expectMarkdown, markdown)
			} else {
				require.ErrorIs(t, err, gorm.ErrRecordNotFound)
			}
		})
	}
}

func TestGetStatus(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	tests := []struct {
		name           string
		setup          func(context.Context) uint // Returns emailID
		attachmentID   string
		expectStatus   string
		expectErrorMsg string
		expectFound    bool
	}{
		{
			name: "status pending",
			setup: func(ctx context.Context) uint {
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)

				pa := &models.ProcessedAttachment{
					EmailID:              email.ID,
					ThreadID:             "thread-123",
					ServiceID:            1,
					RFCMessageID:         "rfc-msg-123",
					OriginalFileName:     "test.pdf",
					AttachmentExternalID: "att-123",
					MessageExternalID:    "msg-123",
					ProcessingStatus:     StatusPending,
				}
				rds.WithContext(ctx).Create(pa)
				return email.ID
			},
			attachmentID: "att-123",
			expectStatus: StatusPending,
			expectFound:  true,
		},
		{
			name: "status failed with error",
			setup: func(ctx context.Context) uint {
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)

				pa := &models.ProcessedAttachment{
					EmailID:              email.ID,
					ThreadID:             "thread-123",
					ServiceID:            1,
					RFCMessageID:         "rfc-msg-123",
					OriginalFileName:     "test.pdf",
					AttachmentExternalID: "att-123",
					MessageExternalID:    "msg-123",
					ProcessingStatus:     StatusFailed,
					ErrorMessage:         "conversion failed",
				}
				rds.WithContext(ctx).Create(pa)
				return email.ID
			},
			attachmentID:   "att-123",
			expectStatus:   StatusFailed,
			expectErrorMsg: "conversion failed",
			expectFound:    true,
		},
		{
			name: "attachment not found",
			setup: func(ctx context.Context) uint {
				email := &models.Email{
					ServiceID:    1,
					RFCMessageID: "rfc-msg-123",
					Attachments: models.Attachments{
						{
							ExternalID:       "att-123",
							OriginalFileName: "test.pdf",
						},
					},
				}
				rds.WithContext(ctx).Create(email)
				return email.ID
			},
			attachmentID: "att-123",
			expectFound:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := setupTestDB(t)
			var emailID uint
			if tt.setup != nil {
				emailID = tt.setup(ctx)
			}

			service, err := NewAttachmentProcessorService(
				ctx, rds.WithContext(ctx), &MockS3Client{
					GetObjectSizeFunc: func(_ context.Context, _ string) (int64, error) {
						return 1024 * 1024, nil
					},
				}, &MockOpenAIService{},
			)
			require.NoError(t, err)

			status, errorMsg, err := service.GetStatus(ctx, emailID, tt.attachmentID)

			if tt.expectFound {
				require.NoError(t, err)
				assert.Equal(t, tt.expectStatus, status)
				assert.Equal(t, tt.expectErrorMsg, errorMsg)
			} else {
				require.ErrorIs(t, err, gorm.ErrRecordNotFound)
			}
		})
	}
}

func TestGetAllMarkdownForEmail(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := setupTestDB(t)

	// Create email with attachments
	email := &models.Email{
		ServiceID:    1,
		RFCMessageID: "rfc-msg-123",
		Attachments: models.Attachments{
			{
				ExternalID:       "att-1",
				OriginalFileName: "file1.pdf",
			},
			{
				ExternalID:       "att-2",
				OriginalFileName: "file2.pdf",
			},
			{
				ExternalID:       "att-3",
				OriginalFileName: "file3.pdf",
			},
			{
				ExternalID:       "att-4",
				OriginalFileName: "file4.pdf",
			},
		},
	}
	rds.WithContext(ctx).Create(email)

	// Create ProcessedAttachments: 2 success, 1 pending, 1 failed
	processedAttachments := []models.ProcessedAttachment{
		{
			EmailID:              email.ID,
			ThreadID:             "thread-123",
			ServiceID:            1,
			RFCMessageID:         "rfc-msg-123",
			OriginalFileName:     "file1.pdf",
			AttachmentExternalID: "att-1",
			MessageExternalID:    "msg-123",
			ProcessingStatus:     StatusSuccess,
			MarkdownContent:      compressContent(t, "Markdown 1"),
		},
		{
			EmailID:              email.ID,
			ThreadID:             "thread-123",
			ServiceID:            1,
			RFCMessageID:         "rfc-msg-123",
			OriginalFileName:     "file2.pdf",
			AttachmentExternalID: "att-2",
			MessageExternalID:    "msg-123",
			ProcessingStatus:     StatusSuccess,
			MarkdownContent:      compressContent(t, "Markdown 2"),
		},
		{
			EmailID:              email.ID,
			ThreadID:             "thread-123",
			ServiceID:            1,
			RFCMessageID:         "rfc-msg-123",
			OriginalFileName:     "file3.pdf",
			AttachmentExternalID: "att-3",
			MessageExternalID:    "msg-123",
			ProcessingStatus:     StatusPending,
		},
		{
			EmailID:              email.ID,
			ThreadID:             "thread-123",
			ServiceID:            1,
			RFCMessageID:         "rfc-msg-123",
			OriginalFileName:     "file4.pdf",
			AttachmentExternalID: "att-4",
			MessageExternalID:    "msg-123",
			ProcessingStatus:     StatusFailed,
			ErrorMessage:         "failed",
		},
	}

	for i := range processedAttachments {
		rds.WithContext(ctx).Create(&processedAttachments[i])
	}

	service, err := NewAttachmentProcessorService(ctx, rds.WithContext(ctx), &MockS3Client{}, &MockOpenAIService{})
	require.NoError(t, err)

	markdowns, pendingCount, failedCount, err := service.GetAllMarkdownForEmail(ctx, email.ID)

	require.NoError(t, err)
	assert.Equal(t, 2, len(markdowns))
	assert.Equal(t, 1, pendingCount)
	assert.Equal(t, 1, failedCount)
	assert.Equal(t, "Markdown 1", markdowns["att-1"])
	assert.Equal(t, "Markdown 2", markdowns["att-2"])
}

func TestHandleProcessingFailure(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping test: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := setupTestDB(t)

	// Create a pending attachment
	pa := &models.ProcessedAttachment{
		EmailID:              1,
		ThreadID:             "thread-123",
		ServiceID:            1,
		AttachmentExternalID: "att-123",
		MessageExternalID:    "msg-123",
		ProcessingStatus:     StatusProcessing,
	}
	rds.WithContext(ctx).Create(pa)

	service, err := NewAttachmentProcessorService(ctx, rds.WithContext(ctx), &MockS3Client{}, &MockOpenAIService{})
	require.NoError(t, err)

	// Cast to concrete type to access internal method for testing
	impl := service.(*AttachmentProcessorServiceImpl)

	errorMsg := "S3 download failed"
	err = impl.handleProcessingFailure(ctx, pa.ID, errorMsg)

	require.NoError(t, err)

	// Verify status was updated
	var updated models.ProcessedAttachment
	rds.WithContext(ctx).First(&updated, pa.ID)
	assert.Equal(t, StatusFailed, updated.ProcessingStatus)
	assert.Equal(t, errorMsg, updated.ErrorMessage)
}

// Utility tests for compression/decompression (no DB needed)
func TestCompressDecompress(t *testing.T) {
	t.Run("compress and decompress round trip", func(t *testing.T) {
		originalMarkdown := "# Test\n\nThis is a test markdown content with special characters: !@#$%^&*()"

		compressed, err := helpers.CompressGzip(originalMarkdown)
		require.NoError(t, err)
		// Note: gzip adds overhead, so small strings may actually be larger compressed
		require.NotEmpty(t, compressed)

		decompressed, err := helpers.DecompressGzip(compressed)
		require.NoError(t, err)
		assert.Equal(t, originalMarkdown, decompressed)
	})

	t.Run("decompress invalid data", func(t *testing.T) {
		_, err := helpers.DecompressGzip([]byte("not gzip data"))
		assert.Error(t, err)
	})
}

func TestProcessEmailAttachments(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping integration test: run with TEST_LIVE_DB=true to enable")
		return
	}

	tests := []struct {
		name         string
		setup        func(context.Context)
		emailID      uint
		threadID     string
		serviceID    uint
		rfcMessageID string
		attachments  []models.Attachment
		expectErr    bool
	}{
		{
			name:         "successful queue",
			emailID:      1,
			threadID:     "thread-123",
			serviceID:    1,
			rfcMessageID: "rfc-msg-123",
			attachments: []models.Attachment{
				{
					ExternalID:        "att-123",
					MessageExternalID: "msg-123",
					OriginalFileName:  "test.pdf",
					S3URL:             "s3://bucket/test.pdf",
				},
			},
			expectErr: false,
		},
		{
			name:         "missing attachment external id",
			emailID:      1,
			threadID:     "thread-123",
			serviceID:    1,
			rfcMessageID: "rfc-msg-123",
			attachments: []models.Attachment{
				{
					ExternalID:        "",
					MessageExternalID: "msg-123",
					OriginalFileName:  "test.pdf",
				},
			},
			expectErr: false, // Should still queue
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := setupTestDB(t)
			if tt.setup != nil {
				tt.setup(ctx)
			}

			service, err := NewAttachmentProcessorService(
				ctx,
				rds.WithContext(ctx),
				&MockS3Client{
					GetObjectSizeFunc: func(_ context.Context, _ string) (int64, error) {
						return 1024 * 1024, nil
					},
				},
				&MockOpenAIService{},
			)
			require.NoError(t, err)

			err = service.ProcessEmailAttachments(
				ctx,
				tt.emailID,
				tt.threadID,
				tt.serviceID,
				tt.rfcMessageID,
				tt.attachments,
			)

			if tt.expectErr {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
		})
	}
}

func TestProcessEmailAttachmentsIntegration(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping integration test: run with TEST_LIVE_DB=true to enable")
		return
	}

	t.Run("processes attachments successfully", func(t *testing.T) {
		ctx := setupTestDB(t)

		// Create email first
		email := &models.Email{
			ServiceID:    1,
			ThreadID:     "thread-123",
			RFCMessageID: "rfc-msg-123",
			Attachments: models.Attachments{
				{
					ExternalID:        "att-123",
					MessageExternalID: "msg-123",
					OriginalFileName:  "test.pdf",
					S3URL:             "s3://bucket/test.pdf",
				},
			},
		}
		rds.WithContext(ctx).Create(email)

		// Create mock S3 client that returns test PDF data
		mockS3 := &MockS3Client{
			FetchObjectFromS3Func: func(_ context.Context, _ string) ([]byte, error) {
				return []byte("mock pdf data"), nil
			},
		}

		// Create mock OpenAI service
		mockOpenAI := &MockOpenAIService{}

		// Process attachments for email
		service, err := NewAttachmentProcessorService(ctx, rds.WithContext(ctx), mockS3, mockOpenAI)
		require.NoError(t, err)
		err = service.ProcessEmailAttachments(ctx, email.ID, "thread-123", 1, "rfc-msg-123", email.Attachments)

		require.NoError(t, err)
	})

	t.Run("handles missing S3 file", func(t *testing.T) {
		ctx := setupTestDB(t)

		// Create email first
		email := &models.Email{
			ServiceID:    1,
			ThreadID:     "thread-123",
			RFCMessageID: "rfc-msg-456",
			Attachments: models.Attachments{
				{
					ExternalID:        "att-456",
					MessageExternalID: "msg-456",
					OriginalFileName:  "missing.pdf",
					S3URL:             "s3://bucket/missing.pdf",
				},
			},
		}
		rds.WithContext(ctx).Create(email)

		mockS3 := &MockS3Client{
			FetchObjectFromS3Func: func(_ context.Context, _ string) ([]byte, error) {
				return nil, errors.New("S3 file not found")
			},
		}

		service, err := NewAttachmentProcessorService(ctx, rds.WithContext(ctx), mockS3, &MockOpenAIService{})
		require.NoError(t, err)
		err = service.ProcessEmailAttachments(ctx, email.ID, "thread-123", 1, "rfc-msg-456", email.Attachments)

		require.NoError(t, err)
	})
}
