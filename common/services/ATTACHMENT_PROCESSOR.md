# Attachment Processor Service

## Overview

The `AttachmentProcessorService` handles downloading PDF attachments from S3, converting them to markdown, and caching the results in the database. This centralizes attachment processing so multiple pipelines (email classification, load building, quote requests) can reuse the same processed content.

## Key Design Principles

### 1. **Deduplication by Content, Not Email**

Attachments are deduplicated by `RFCMessageID + OriginalFileName`, not by `EmailID`. This means:
- The same PDF sent to multiple recipients is only processed once
- All recipients share the same cached markdown
- Storage is efficient (no duplicate processing)

**Example:** If `invoice.pdf` is sent to 5 people, it's downloaded/converted once and reused 5 times.

### 2. **Synchronous Processing During Ingestion**

Attachments are processed **immediately** after email ingestion, before classification runs. This ensures:
- Markdown is ready when pipelines need it
- No race conditions or polling delays
- Failures are handled gracefully (email processing continues even if attachment processing fails)

### 3. **Status Tracking**

Each attachment goes through a state machine:
- `pending` → `processing` → `success` or `failed`

This allows consumers to:
- Check if content is ready (`GetMarkdownIfReady`)
- Wait for processing (`GetMarkdownOrWait`)
- Handle failures gracefully (`GetStatus`)

### 4. **Compressed Storage**

Markdown is stored compressed (gzip) in the database to save space. The service handles compression/decompression automatically.

## Usage Patterns

### Pattern 1: Email Classification (Non-blocking)

Email classification needs attachment content but can skip if not ready:

```go
markdown, err := attachmentProcessor.GetMarkdownIfReady(ctx, emailID, attachmentExternalID)
if errors.Is(err, gorm.ErrRecordNotFound) {
    // Skip this attachment, continue without it
    continue
}
if err != nil {
    // Database error
    return err
}
// Use markdown for classification
```

### Pattern 2: Load Building (Wait for All)

Load building needs all attachments and can wait a bit:

```go
attachments, pending, failed, err := attachmentProcessor.GetAllMarkdownForEmail(ctx, emailID)
if pending > 0 {
    // Some still processing - wait a bit
    markdown, found, _ := attachmentProcessor.GetMarkdownOrWait(ctx, emailID, attachmentID, 5*time.Second)
    if found {
        attachments[attachmentID] = markdown
    }
}
// Process all attachments
```

### Pattern 3: Status-Aware Processing

Check status before deciding what to do:

```go
status, errorMsg, err := attachmentProcessor.GetStatus(ctx, emailID, attachmentExternalID)
if errors.Is(err, gorm.ErrRecordNotFound) {
    // Attachment not queued yet (shouldn't happen in normal flow)
    return
}
if err != nil {
    // Database error
    return err
}
switch status {
case "success":
    // Use cached markdown
case "pending", "processing":
    // Wait or skip
case "failed":
    // Log error and fall back to old behavior
    log.Warn(ctx, "attachment processing failed", zap.String("error", errorMsg))
}
```

## Method Reference

### `ProcessEmailAttachments`

**When to call:** Automatically called during email ingestion (in `emails.ProcessEmail`).

**What it does:**
1. Queues attachments as `pending` in database
2. Downloads each PDF from S3
3. Converts PDF to markdown using OpenAI vision API
4. Compresses and saves markdown to database
5. Updates status to `success` or `failed`

**Error handling:** Individual attachment failures don't stop the batch. Only database errors cause the method to return an error.

### `GetMarkdownIfReady`

**When to use:** When you need markdown but can skip if not ready (e.g., email classification).

**Returns:**
- `markdown`: The decompressed markdown content if found
- `err`: `nil` if found, `gorm.ErrRecordNotFound` if not found/not ready, or other error for database issues

**Example:**
```go
markdown, err := service.GetMarkdownIfReady(ctx, emailID, "att-123")
if errors.Is(err, gorm.ErrRecordNotFound) {
    // Not ready yet, skip this attachment
    continue
}
if err != nil {
    // Database error
    return err
}
// Use markdown
```

### `GetMarkdownOrWait`

**When to use:** When you can wait a short time for processing (e.g., user-facing operations).

**Parameters:**
- `maxWait`: Maximum time to wait (e.g., `5 * time.Second`)

**Returns:**
- `markdown`: The decompressed markdown if successful
- `found`: `true` if ready, `false` if timeout/failed
- `err`: Error details or timeout message

**Example:**
```go
markdown, found, err := service.GetMarkdownOrWait(ctx, emailID, "att-123", 10*time.Second)
if !found {
    log.Warn(ctx, "attachment not ready after wait", zap.Error(err))
    // Fall back to old behavior
}
```

### `GetStatus`

**When to use:** When you want to check processing state before deciding what to do.

**Returns:**
- `status`: `"pending"`, `"processing"`, `"success"`, or `"failed"`
- `errorMsg`: Non-empty if status is `"failed"`
- `err`: `nil` if found, `gorm.ErrRecordNotFound` if not found, or other error for database issues

### `GetAllMarkdownForEmail`

**When to use:** When you need all attachments for an email at once (e.g., load building pipeline).

**Returns:**
- `attachments`: Map of `attachmentExternalID → markdown`
- `pendingCount`: Number still processing
- `failedCount`: Number that failed

**Note:** Only returns attachments with status `success`. Check `pendingCount` and `failedCount` to see what's missing.

## Common Gotchas

### 1. **Querying by EmailID vs RFCMessageID**

**Wrong:**
```go
// Don't query by EmailID - attachments are deduplicated across emails!
db.Where("email_id = ?", emailID).Find(&attachments)
```

**Right:**
```go
// Query by RFCMessageID + OriginalFileName (the deduplication key)
db.Where("rfc_message_id = ? AND original_file_name = ?", rfcMessageID, filename).First(&attachment)
```

The service methods handle this correctly - you pass `emailID` and `attachmentExternalID`, and it finds the right record.

### 2. **Attachment Not Found**

If `GetMarkdownIfReady` returns `gorm.ErrRecordNotFound`, it could mean:
- Still processing (`pending` or `processing`)
- Processing failed (`failed`)
- Attachment was never queued (shouldn't happen in normal flow)

Use `GetStatus` to distinguish between these cases.

### 3. **Multiple Recipients**

If the same email is sent to multiple recipients:
- Each recipient gets their own `Email` record
- But they share the same `ProcessedAttachment` record (deduplicated by RFCMessageID)
- The `EmailID` in `ProcessedAttachment` points to the first email that triggered processing

This is intentional and efficient - don't try to "fix" it by creating separate records per recipient.

### 4. **S3 URL Format**

The service expects console URLs:
```
https://s3.console.aws.amazon.com/s3/object/bucket-name?region=us-east-1&prefix=path/to/file.pdf
```

These are automatically parsed by `ParseS3URL` in `s3fetcher/util.go`. Don't pass `s3://` URLs directly.

## Integration Points

### Email Ingestion

Called automatically in `emails.ProcessEmail`:
```go
attachmentProcessor.ProcessEmailAttachments(ctx, emailID, threadID, serviceID, rfcMessageID, attachments)
```

### Email Classification

Uses `GetMarkdownIfReady` in `emails.llm_classify.go`:
```go
markdown, err := attachmentProcessor.GetMarkdownIfReady(ctx, emailID, attachment.ExternalID)
if err == nil {
    // Include in classification prompt
}
```

### Load Building Pipeline

Uses `GetAllMarkdownForEmail` in `integrations/llm/extractor/load_pipeline.go`:
```go
attachments, pending, failed, _ := attachmentProcessor.GetAllMarkdownForEmail(ctx, emailID)
// Process all ready attachments
```

### Quote Request Pipeline

Uses `GetMarkdownIfReady` in `integrations/llm/quote_request.go`:
```go
markdown, err := attachmentProcessor.GetMarkdownIfReady(ctx, emailID, attachment.ExternalID)
if err == nil {
    // Extract quote request from markdown
}
```

## Performance Considerations

### Processing Time

- **Small PDFs (< 1MB):** ~2-5 seconds per attachment
- **Large PDFs (5-10MB):** ~10-30 seconds per attachment
- **Very large PDFs (> 10MB):** May timeout or fail

The service processes attachments sequentially by default to avoid memory issues. Large batches may take a while.

### Database Storage

- Markdown is compressed (typically 70-90% reduction)
- A 1MB PDF might produce 200KB of markdown, compressed to ~50KB
- Storage is per unique attachment (deduplicated), not per email

### Caching

Once processed, markdown is cached in the database. Subsequent lookups are fast (< 10ms). The only cost is the initial download/conversion.

## Error Handling

### Processing Failures

If an attachment fails to process:
1. Status is set to `failed`
2. Error message is stored in `ErrorMessage` field
3. Email processing continues (fail-open)
4. Consumers can check status and fall back to old behavior

### Common Failure Reasons

- **S3 download fails:** File doesn't exist, network error, permissions
- **PDF conversion fails:** Invalid PDF, too large, API rate limit
- **Database errors:** Connection issues, constraint violations

All failures are logged with context. Check logs for details.

## Testing

### Unit Tests

Mock the `s3fetcher.Fetcher` and `openai.Service` interfaces:
```go
type MockS3Client struct {
    FetchFunc func(ctx context.Context, s3URL string) ([]byte, error)
}
// Implement s3fetcher.Fetcher interface
```

### Integration Tests

Use `TEST_LIVE_DB=true` to run against a real PostgreSQL database:
```bash
TEST_LIVE_DB=true go test ./common/services -v
```

## Future Improvements

Potential enhancements (not yet implemented):
- Retry mechanism for transient failures
- Async queue system for background processing
- Metrics export (Prometheus/CloudWatch)
- Circuit breaker for S3
- PDF size limits to prevent OOM
- Bulk operations for high-volume ingestion

