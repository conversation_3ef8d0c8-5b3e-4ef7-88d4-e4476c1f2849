package revenova

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

// RefreshToken refreshes the Revenova session token
// Acquires the token mutex to prevent concurrent refresh operations
func (r *<PERSON>enova) RefreshToken(ctx context.Context) error {
	r.tokenMutex.Lock()
	defer r.tokenMutex.Unlock()
	return r.refreshTokenLocked(ctx)
}

// ensureValidToken checks if the token is valid and refreshes it if needed
// Uses mutex to prevent concurrent goroutines from racing to refresh the token
func (r *<PERSON>enova) ensureValidToken(ctx context.Context) error {
	// Quick check without lock first (read-only)
	if !r.tms.NeedsRefresh() {
		return nil
	}

	// Acquire lock to prevent concurrent token refresh
	r.tokenMutex.Lock()
	defer r.tokenMutex.Unlock()

	// Double-check after acquiring lock (another goroutine may have refreshed it)
	if !r.tms.NeedsRefresh() {
		return nil
	}

	log.Info(ctx, "token needs refresh, refreshing now")
	if err := r.refreshTokenLocked(ctx); err != nil {
		return fmt.Errorf("failed to refresh token: %w", err)
	}
	return nil
}

// refreshTokenLocked performs the actual token refresh
// Must be called with tokenMutex already held
func (r *Revenova) refreshTokenLocked(ctx context.Context) error {
	ctx, metaSpan := otel.StartSpan(ctx, "RefreshTokenRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "refreshing Revenova session token")

	// Decrypt the password
	password, err := crypto.DecryptAESGCM(ctx, string(r.tms.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt password: %w", err)
	}

	// Get a new session token with actual expiration time and ServerURL from Salesforce
	newToken, expiresIn, serverURL, err := r.GetToken(ctx, r.tms.Username, password, r.tms.AppID)
	if err != nil {
		return fmt.Errorf("failed to get new session token: %w", err)
	}

	// Extract hostname from ServerURL
	// (e.g., "https://na123.salesforce.com/services/Soap/c/42.0/orgId" -> "na123.salesforce.com")
	serverURLNoProtocol := strings.TrimPrefix(serverURL, "https://")
	serverURLNoProtocol = strings.TrimPrefix(serverURLNoProtocol, "http://")

	// Extract just the hostname (before the first '/')
	tenant := serverURLNoProtocol
	if idx := strings.Index(serverURLNoProtocol, "/"); idx > 0 {
		tenant = serverURLNoProtocol[:idx]
	}

	// Update the token and tenant in the database using actual values from Salesforce API
	r.tms.AccessToken = newToken
	r.tms.AccessTokenExpirationDate = models.NullTime{
		Time:  time.Now().Add(time.Duration(expiresIn) * time.Second), // Use actual Salesforce expiration
		Valid: true,
	}
	r.tms.Tenant = tenant // Update tenant with ServerURL hostname from login response (required for API calls)

	err = integrationDB.Update(ctx, &r.tms)
	if err != nil {
		return fmt.Errorf("failed to update token in database: %w", err)
	}

	log.Info(ctx, "successfully refreshed Revenova session token")
	return nil
}
