package revenova

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	salesforceCustomerQuotePath = "/services/data/v58.0/sobjects/rtms__CustomerQuote__c"
)

// mapRateTypeToSalesforceUnit translates to Salesforce Picklist values
func mapRateTypeToSalesforceUnit(rateType string) string {
	// Normalize string just in case
	switch strings.ToLower(rateType) {
	case "flat", "flatrate":
		return "Fixed Cost"
	case "distance", "mileage", "miles":
		return "Per Mile"
	case "cwt", "hundredweight":
		return "Per Cwt"
	case "tons", "ton":
		return "Per Ton"
	default:
		return "Fixed Cost"
	}
}

// CreateCustomerQuote creates a Customer Quote using the extracted RateDataInfo
func (r *<PERSON><PERSON>va) CreateCustomerQuote(
	ctx context.Context,
	loadID string,
	rateInfo models.RateData,
) error {
	if loadID == "" {
		return nil
	}

	ctx, metaSpan := otel.StartSpan(ctx, "CreateCustomerQuoteRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	var extractedRate float64
	var extractedQty float64
	rateType := rateInfo.CustomerRateType

	// Try to get the per-unit rate, with fallbacks
	hasLineHaulRate := rateInfo.CustomerLineHaulRate != nil && rateInfo.CustomerRateNumUnits > 0
	hasLineHaulCharge := rateInfo.CustomerLineHaulCharge.Val > 0 && rateInfo.CustomerRateNumUnits > 0
	hasTotalCharge := rateInfo.CustomerTotalCharge.Val > 0

	switch {
	case hasLineHaulRate:
		// Use the provided per-unit rate
		extractedRate = float64(*rateInfo.CustomerLineHaulRate)
		extractedQty = float64(rateInfo.CustomerRateNumUnits)
	case hasLineHaulCharge:
		// Calculate per-unit rate from line haul charge and quantity
		extractedRate = float64(rateInfo.CustomerLineHaulCharge.Val) / float64(rateInfo.CustomerRateNumUnits)
		extractedQty = float64(rateInfo.CustomerRateNumUnits)
	case hasTotalCharge:
		// Fall back to total charge as a flat rate
		// Use total charge directly to avoid calculation issues
		extractedRate = float64(rateInfo.CustomerTotalCharge.Val)
		extractedQty = 1.0
		// Override rate type to "Fixed Cost" since we're using total charge
		rateType = "FlatRate"
	default:
		// No valid rate data available
		extractedRate = 0.0
		extractedQty = float64(rateInfo.CustomerRateNumUnits)
	}

	// This prevents under-billing when rate type is missing but quantity is valid
	sfUnit := mapRateTypeToSalesforceUnit(rateType)
	if sfUnit == "Fixed Cost" && extractedQty > 1.0 && extractedRate > 0 {
		// If we have a quantity > 1 and a rate, but type defaults to "Fixed Cost",
		// check if CustomerLineHaulUnit suggests it's actually per-unit
		if rateInfo.CustomerLineHaulUnit != "" {
			// Try to infer from unit field
			unitLower := strings.ToLower(rateInfo.CustomerLineHaulUnit)
			switch {
			case strings.Contains(unitLower, "mile") || strings.Contains(unitLower, "mi"):
				sfUnit = "Per Mile"
			case strings.Contains(unitLower, "cwt") || strings.Contains(unitLower, "hundredweight"):
				sfUnit = "Per Cwt"
			case strings.Contains(unitLower, "ton"):
				sfUnit = "Per Ton"
			// If unit suggests per-unit but we can't determine type, keep as Fixed Cost
			// but log a warning
			default:
				log.Warn(
					ctx,
					"rate type empty but quantity > 1, defaulting to Fixed Cost (may cause under-billing)",
					zap.String("unit", rateInfo.CustomerLineHaulUnit),
					zap.Float64("quantity", extractedQty),
					zap.Float64("rate", extractedRate),
				)
			}
		} else {
			// No unit field, but quantity > 1 suggests per-unit rate
			// Default to "Per Mile" as most common case, but log warning
			log.Warn(
				ctx,
				"rate type empty but quantity > 1, inferring Per Mile (may be incorrect)",
				zap.Float64("quantity", extractedQty),
				zap.Float64("rate", extractedRate),
			)
			sfUnit = "Per Mile"
		}
	}

	sfQty := extractedQty
	sfUnitPrice := extractedRate
	var sfLineHaulCharge float64
	var fuelSurcharge float64
	var fuelSurchargeUnit string
	var fuelSurchargeUnitPrice *float64
	var fuelSurchargeQty = 1.0

	// Check if we're using CustomerTotalCharge (which already includes fuel surcharge)
	usingTotalCharge := hasTotalCharge && !hasLineHaulRate && !hasLineHaulCharge

	// If Flat, the Price IS the Total, and Qty is 1.
	// If Distance/CWT, the Price is Per Unit, so Total = Price * Qty.
	if sfUnit == "Fixed Cost" {
		sfQty = 1.0
		sfUnitPrice = extractedRate
		sfLineHaulCharge = extractedRate
	} else {
		// e.g. $2.00 per mile * 500 miles = $1000 Total
		sfLineHaulCharge = sfUnitPrice * sfQty
	}

	// CustomerRateNumUnits meaning varies by CustomerRateType (could be miles, hours, weight, etc.)
	isDistanceBasedRate := strings.EqualFold(rateType, "distance") ||
		strings.EqualFold(rateType, "mileage") ||
		strings.EqualFold(rateType, "miles") ||
		sfUnit == "Per Mile"

	switch {
	case rateInfo.FSCFlatRate != nil && *rateInfo.FSCFlatRate > 0:
		// Use flat rate fuel surcharge
		fuelSurcharge = float64(*rateInfo.FSCFlatRate)
		fsFlatRate := fuelSurcharge
		fuelSurchargeUnitPrice = &fsFlatRate
		fuelSurchargeQty = 1.0
		fuelSurchargeUnit = "Fixed Cost"

		// If using total charge, subtract fuel surcharge to get line haul
		if usingTotalCharge {
			sfLineHaulCharge -= fuelSurcharge
			if sfLineHaulCharge < 0 {
				sfLineHaulCharge = 0
				log.Warn(
					ctx,
					"fuel surcharge exceeds total charge, setting line haul to 0",
					zap.Float64("totalCharge", extractedRate),
					zap.Float64("fuelSurcharge", fuelSurcharge),
				)
			}
		}
	case rateInfo.FSCPerMile != nil && *rateInfo.FSCPerMile > 0 &&
		isDistanceBasedRate && rateInfo.CustomerRateNumUnits > 0:
		// Calculate fuel surcharge from per-mile rate
		// Only use CustomerRateNumUnits as distance if rate type is distance-based
		fuelSurcharge = float64(*rateInfo.FSCPerMile) * float64(rateInfo.CustomerRateNumUnits)
		fsPerMile := float64(*rateInfo.FSCPerMile)
		fuelSurchargeUnitPrice = &fsPerMile
		fuelSurchargeQty = float64(rateInfo.CustomerRateNumUnits)
		fuelSurchargeUnit = "Per Mile"

		// If using total charge, subtract fuel surcharge to get line haul
		if usingTotalCharge {
			sfLineHaulCharge -= fuelSurcharge
			if sfLineHaulCharge < 0 {
				sfLineHaulCharge = 0
				log.Warn(
					ctx,
					"fuel surcharge exceeds total charge, setting line haul to 0",
					zap.Float64("totalCharge", extractedRate),
					zap.Float64("fuelSurcharge", fuelSurcharge),
				)
			}
		}
	case rateInfo.FSCPercent != nil && *rateInfo.FSCPercent > 0:
		fscPercent := float64(*rateInfo.FSCPercent) / 100.0
		if usingTotalCharge {
			// When using total charge, we need to extract line haul from total
			// CustomerTotalCharge already includes fuel surcharge, so:
			// Total = LineHaul + (LineHaul * FSCPercent/100)
			// Total = LineHaul * (1 + FSCPercent/100)
			// LineHaul = Total / (1 + FSCPercent/100)
			// Note: sfLineHaulCharge is currently set to extractedRate (the total)
			sfLineHaulCharge /= (1.0 + fscPercent)
			// Calculate fuel surcharge as percentage of extracted line haul (not the total)
			fuelSurcharge = sfLineHaulCharge * fscPercent
		} else if sfLineHaulCharge > 0 {
			// Calculate fuel surcharge from percentage of line haul
			fuelSurcharge = sfLineHaulCharge * fscPercent
		}
		// For percentage-based, we'll store it as a flat rate
		if fuelSurcharge > 0 {
			fuelSurchargeUnitPrice = &fuelSurcharge
			fuelSurchargeQty = 1.0
			fuelSurchargeUnit = "Fixed Cost"
		}
	}

	// Calculate total charge: line haul + fuel surcharge
	// Use CustomerTotalCharge if available (it should already include fuel surcharge)
	// Otherwise calculate: line haul + fuel surcharge
	var sfTotalCharge float64
	if hasTotalCharge && rateInfo.CustomerTotalCharge.Val > 0 {
		sfTotalCharge = float64(rateInfo.CustomerTotalCharge.Val)
	} else {
		sfTotalCharge = sfLineHaulCharge + fuelSurcharge
	}

	quoteData := map[string]any{
		"rtms__Load2__c":      loadID,
		"rtms__Status__c":     "Pending",
		"rtms__Quote_Date__c": time.Now().Format("2006-01-02"),

		// Pricing & Totals
		"rtms__Customer_Quote_Total__c": sfTotalCharge,
		// TODO: Spot seems to be the default pricing type always used for customer quotes.
		// Salesforce supported types are: Spot, Contract, and Fixed.
		// We may need to support extracting pricing type from LLM.
		"rtms__Pricing__c": "Spot",

		// Freight / Linehaul Details
		"rtms__Freight_Charge_Unit__c":       sfUnit,
		"rtms__Freight_Charge_Quantity__c":   sfQty,
		"rtms__Freight_Charge_Unit_Price__c": sfUnitPrice,
		"rtms__Freight_Charges__c":           sfLineHaulCharge,
		"rtms__Net_Line_Haul__c":             sfLineHaulCharge,
		"rtms__Transportation_Total__c":      sfTotalCharge,
	}

	// Add fuel surcharge fields if fuel surcharge is present
	if fuelSurcharge > 0 {
		quoteData["rtms__Fuel_Surcharge_Unit__c"] = fuelSurchargeUnit
		quoteData["rtms__Fuel_Surcharge_Quantity__c"] = fuelSurchargeQty
		if fuelSurchargeUnitPrice != nil {
			quoteData["rtms__Fuel_Surcharge_Unit_Price__c"] = float64(*fuelSurchargeUnitPrice)
		}
		quoteData["rtms__Fuel_Surcharge__c"] = fuelSurcharge
	}

	log.Info(
		ctx,
		"Creating Customer Quote for load",
		zap.String("loadID", loadID),
		zap.String("unit", sfUnit),
		zap.Float64("totalCharge", sfTotalCharge),
	)

	var response struct {
		ID      string   `json:"id"`
		Success bool     `json:"success"`
		Errors  []string `json:"errors"`
	}

	err = r.post(ctx, salesforceCustomerQuotePath, nil, quoteData, &response, s3backup.TypeLoads)
	if err != nil {
		log.Error(ctx, "Failed to create Customer Quote", zap.Error(err))
		return fmt.Errorf("failed to create customer quote: %w", err)
	}

	if response.ID == "" {
		err = fmt.Errorf("failed to create customer quote: %v", response.Errors)
		log.Error(ctx, "Failed to create Customer Quote", zap.Error(err))
		return err
	}

	log.Info(
		ctx, "Successfully created Customer Quote",
		zap.String("loadID", loadID),
		zap.String("quoteID", response.ID),
	)

	return nil
}
