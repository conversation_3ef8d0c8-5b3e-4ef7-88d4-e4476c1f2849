package revenova

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestParseDepartureTime(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		timeStr       string
		dateStr       *string
		expectedTime  time.Time
		expectedOk    bool
		expectedError bool
	}{
		{
			name:          "RFC3339 datetime format",
			timeStr:       "2024-01-15T14:30:00Z",
			dateStr:       nil,
			expectedTime:  time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "Valid time with date - normal time",
			timeStr:       "14:30",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "24:00 converts to 00:00 next day",
			timeStr:       "24:00",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "24:00 at end of month rolls to next month",
			timeStr:       "24:00",
			dateStr:       stringPtr("2024-01-31"),
			expectedTime:  time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "24:00 at end of year rolls to next year",
			timeStr:       "24:00",
			dateStr:       stringPtr("2024-12-31"),
			expectedTime:  time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "00:00 stays on same day",
			timeStr:       "00:00",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "23:59 stays on same day",
			timeStr:       "23:59",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Date(2024, 1, 15, 23, 59, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "Invalid time format",
			timeStr:       "25:00",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Time{},
			expectedOk:    false,
			expectedError: false,
		},
		{
			name:          "Missing date returns false",
			timeStr:       "14:30",
			dateStr:       nil,
			expectedTime:  time.Time{},
			expectedOk:    false,
			expectedError: false,
		},
		{
			name:          "24:00 with whitespace",
			timeStr:       "  24:00  ",
			dateStr:       stringPtr("2024-01-15"),
			expectedTime:  time.Date(2024, 1, 16, 0, 0, 0, 0, time.UTC),
			expectedOk:    true,
			expectedError: false,
		},
		{
			name:          "Invalid date format",
			timeStr:       "14:30",
			dateStr:       stringPtr("invalid-date"),
			expectedTime:  time.Time{},
			expectedOk:    false,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, ok := parseDepartureTime(ctx, tt.timeStr, tt.dateStr)
			assert.Equal(t, tt.expectedOk, ok, "parseDepartureTime should return expected ok value")
			if tt.expectedOk {
				assert.Equal(t, tt.expectedTime, result, "parseDepartureTime should return expected time")
			} else {
				assert.True(t, result.IsZero(), "parseDepartureTime should return zero time when ok is false")
			}
		})
	}
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

func TestDrumkitLoadToRevenovaLoad_PONumber(t *testing.T) {
	r := Revenova{}

	tests := []struct {
		name          string
		poNums        string
		expectedPONum *string
	}{
		{
			name:          "PO number is mapped correctly",
			poNums:        "DRUMKIT-TEST-PO-999",
			expectedPONum: stringPtr("DRUMKIT-TEST-PO-999"),
		},
		{
			name:          "Empty PO number results in nil",
			poNums:        "",
			expectedPONum: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			load := models.Load{
				LoadCoreInfo: models.LoadCoreInfo{
					PONums: tt.poNums,
					Pickup: models.Pickup{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Denver",
							State:   "CO",
							Zipcode: "80202",
						},
					},
					Consignee: models.Consignee{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Phoenix",
							State:   "AZ",
							Zipcode: "85001",
						},
					},
				},
			}

			result := r.DrumkitLoadToRevenovaLoad(load)

			if tt.expectedPONum == nil {
				assert.Nil(t, result.Wsl.PoNumber, "PoNumber should be nil")
			} else {
				assert.NotNil(t, result.Wsl.PoNumber, "PoNumber should not be nil")
				assert.Equal(t, *tt.expectedPONum, *result.Wsl.PoNumber, "PoNumber should match")
			}
		})
	}
}

func TestDrumkitLoadToRevenovaLoad_PickupDeliveryNumbers(t *testing.T) {
	r := Revenova{}

	// Note: customer.refNumber is used for BOL, not stop pickup/delivery numbers
	// So we only test pickup.refNumber and consignee.refNumber here
	tests := []struct {
		name                         string
		pickupRefNum                 string
		consigneeRefNum              string
		expectedPickupDeliveryNum    *string
		expectedConsigneeDeliveryNum *string
	}{
		{
			name:                         "Pickup ref number is mapped to pickup stop PickupDeliveryNumber",
			pickupRefNum:                 "PICKUP-REF-123",
			consigneeRefNum:              "",
			expectedPickupDeliveryNum:    stringPtr("PICKUP-REF-123"),
			expectedConsigneeDeliveryNum: nil,
		},
		{
			name:                         "Consignee ref number is mapped to consignee stop PickupDeliveryNumber",
			pickupRefNum:                 "",
			consigneeRefNum:              "CONSIGNEE-REF-789",
			expectedPickupDeliveryNum:    nil,
			expectedConsigneeDeliveryNum: stringPtr("CONSIGNEE-REF-789"),
		},
		{
			name:                         "Both ref numbers are mapped correctly",
			pickupRefNum:                 "PICKUP-REF-123",
			consigneeRefNum:              "CONSIGNEE-REF-789",
			expectedPickupDeliveryNum:    stringPtr("PICKUP-REF-123"),
			expectedConsigneeDeliveryNum: stringPtr("CONSIGNEE-REF-789"),
		},
		{
			name:                         "No ref numbers results in nil",
			pickupRefNum:                 "",
			consigneeRefNum:              "",
			expectedPickupDeliveryNum:    nil,
			expectedConsigneeDeliveryNum: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			load := models.Load{
				LoadCoreInfo: models.LoadCoreInfo{
					Pickup: models.Pickup{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Denver",
							State:   "CO",
							Zipcode: "80202",
						},
						RefNumber: tt.pickupRefNum,
					},
					Consignee: models.Consignee{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Phoenix",
							State:   "AZ",
							Zipcode: "85001",
						},
						RefNumber: tt.consigneeRefNum,
					},
				},
			}

			result := r.DrumkitLoadToRevenovaLoad(load)

			// Check pickup stop PickupDeliveryNumber
			pickupStop := result.Wsl.Stops[0]
			if tt.expectedPickupDeliveryNum == nil {
				assert.Nil(t, pickupStop.PickupDeliveryNumber, "Pickup PickupDeliveryNumber should be nil")
			} else {
				assert.NotNil(t, pickupStop.PickupDeliveryNumber, "Pickup PickupDeliveryNumber should not be nil")
				assert.Equal(
					t,
					*tt.expectedPickupDeliveryNum,
					*pickupStop.PickupDeliveryNumber,
					"Pickup PickupDeliveryNumber should match",
				)
			}

			// Check consignee stop PickupDeliveryNumber
			consigneeStop := result.Wsl.Stops[1]
			if tt.expectedConsigneeDeliveryNum == nil {
				assert.Nil(t, consigneeStop.PickupDeliveryNumber, "Consignee PickupDeliveryNumber should be nil")
			} else {
				assert.NotNil(t, consigneeStop.PickupDeliveryNumber, "Consignee PickupDeliveryNumber should not be nil")
				assert.Equal(
					t,
					*tt.expectedConsigneeDeliveryNum,
					*consigneeStop.PickupDeliveryNumber,
					"Consignee PickupDeliveryNumber should match",
				)
			}
		})
	}
}

func TestDrumkitLoadToRevenovaLoad_BillOfLading(t *testing.T) {
	r := Revenova{}

	// Revenova uses the "Ref #/ BOL" field (customer.refNumber) for Bill of Lading
	// EXCEPT when customer.refNumber starts with "SO" - those go to Bill To Reference instead
	tests := []struct {
		name              string
		customerRefNumber string
		expectedBOL       *string
	}{
		{
			name:              "BOL is mapped from customer.refNumber",
			customerRefNumber: "BOL-12345",
			expectedBOL:       stringPtr("BOL-12345"),
		},
		{
			name:              "Empty customer.refNumber results in nil BOL",
			customerRefNumber: "",
			expectedBOL:       nil,
		},
		{
			name:              "SO-prefixed refNumber is excluded from BOL (goes to Bill To Reference)",
			customerRefNumber: "SO142379",
			expectedBOL:       nil,
		},
		{
			name:              "SO with slash is excluded from BOL",
			customerRefNumber: "SO016641 / SO016644",
			expectedBOL:       nil,
		},
		{
			name:              "Non-SO numeric refNumber maps to BOL",
			customerRefNumber: "12345678",
			expectedBOL:       stringPtr("12345678"),
		},
		{
			name:              "Lowercase 'so' prefix still maps to BOL (case sensitive)",
			customerRefNumber: "so142379",
			expectedBOL:       stringPtr("so142379"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			load := models.Load{
				LoadCoreInfo: models.LoadCoreInfo{
					Customer: models.Customer{
						RefNumber: tt.customerRefNumber,
					},
					Pickup: models.Pickup{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Denver",
							State:   "CO",
							Zipcode: "80202",
						},
					},
					Consignee: models.Consignee{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Phoenix",
							State:   "AZ",
							Zipcode: "85001",
						},
					},
				},
			}

			result := r.DrumkitLoadToRevenovaLoad(load)

			if tt.expectedBOL == nil {
				assert.Nil(t, result.Wsl.BillOfLadingNumber, "BillOfLadingNumber should be nil")
			} else {
				assert.NotNil(t, result.Wsl.BillOfLadingNumber, "BillOfLadingNumber should not be nil")
				assert.Equal(t, *tt.expectedBOL, *result.Wsl.BillOfLadingNumber, "BillOfLadingNumber should match")
			}
		})
	}
}

func TestDrumkitLoadToRevenovaLoad_TenantSpecificBehavior(t *testing.T) {
	tests := []struct {
		name             string
		tenant           string
		mode             models.LoadMode
		expectedModeName string
		expectedCargoVal float64
	}{
		{
			name:             "Cooler tenant gets Reefer mode and 100000 cargo value",
			tenant:           TenantCooler,
			mode:             models.RefrigeratedMode,
			expectedModeName: "Reefer",
			expectedCargoVal: 100000,
		},
		{
			name:             "Cooler tenant with uppercase gets correct values",
			tenant:           "COOLERLOGISTICSLLC",
			mode:             models.TLMode,
			expectedModeName: "Truckload",
			expectedCargoVal: 100000,
		},
		{
			name:             "Prosponsive tenant gets TL-Refrigerated mode and 0 cargo value",
			tenant:           TenantProsponsive,
			mode:             models.RefrigeratedMode,
			expectedModeName: "TL-Refrigerated",
			expectedCargoVal: 0,
		},
		{
			name:             "Unknown tenant gets default mode and 0 cargo value",
			tenant:           "unknowntenant",
			mode:             models.TLMode,
			expectedModeName: "Truckload",
			expectedCargoVal: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := Revenova{
				tms: models.Integration{
					Tenant: tt.tenant,
				},
			}

			load := models.Load{
				LoadCoreInfo: models.LoadCoreInfo{
					Mode: tt.mode,
					Pickup: models.Pickup{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Denver",
							State:   "CO",
							Zipcode: "80202",
						},
					},
					Consignee: models.Consignee{
						CompanyCoreInfo: models.CompanyCoreInfo{
							City:    "Phoenix",
							State:   "AZ",
							Zipcode: "85001",
						},
					},
				},
			}

			result := r.DrumkitLoadToRevenovaLoad(load)

			assert.Equal(t, tt.expectedModeName, result.Wsl.ModeName, "ModeName should match expected value")
			assert.Equal(t, tt.expectedCargoVal, result.Wsl.CargoValue, "CargoValue should match expected value")
		})
	}
}

func TestGetRevenovaModeName(t *testing.T) {
	tests := []struct {
		name         string
		tenant       string
		mode         models.LoadMode
		expectedMode string
	}{
		{
			name:         "Cooler - TL mode returns Truckload",
			tenant:       TenantCooler,
			mode:         models.TLMode,
			expectedMode: "Truckload",
		},
		{
			name:         "Cooler - Refrigerated mode returns Reefer",
			tenant:       TenantCooler,
			mode:         models.RefrigeratedMode,
			expectedMode: "Reefer",
		},
		{
			name:         "Cooler - empty mode returns default Reefer",
			tenant:       TenantCooler,
			mode:         "",
			expectedMode: "Reefer",
		},
		{
			name:         "Prosponsive - TL mode returns TL-Dry Van",
			tenant:       TenantProsponsive,
			mode:         models.TLMode,
			expectedMode: "TL-Dry Van",
		},
		{
			name:         "Prosponsive - Refrigerated mode returns TL-Refrigerated",
			tenant:       TenantProsponsive,
			mode:         models.RefrigeratedMode,
			expectedMode: "TL-Refrigerated",
		},
		{
			name:         "Prosponsive - empty mode returns default TL-Dry Van",
			tenant:       TenantProsponsive,
			mode:         "",
			expectedMode: "TL-Dry Van",
		},
		{
			name:         "Unknown tenant - TL mode returns Truckload (default config)",
			tenant:       "unknowntenant",
			mode:         models.TLMode,
			expectedMode: "Truckload",
		},
		{
			name:         "Unknown tenant - empty mode returns LTL (default config default)",
			tenant:       "unknowntenant",
			mode:         "",
			expectedMode: "LTL",
		},
		{
			name:         "Tenant name with extra text (contains match) - Cooler",
			tenant:       "CoolerLogisticsLLC-Production",
			mode:         models.RefrigeratedMode,
			expectedMode: "Reefer",
		},
		{
			name:         "Tenant name with different case - Prosponsive",
			tenant:       "CLServicesInc",
			mode:         models.TLMode,
			expectedMode: "TL-Dry Van",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getRevenovaModeName(tt.tenant, tt.mode)
			assert.Equal(t, tt.expectedMode, result, "Mode name should match expected value")
		})
	}
}

func TestRevenova_buildCustomerFields(t *testing.T) {
	tests := []struct {
		name            string
		tenant          string
		wantContains    []string
		wantNotContains []string
	}{
		{
			name:   "Cooler Tenant gets Rich Fields",
			tenant: TenantCooler,
			wantContains: []string{
				"rtms__Customer__r.Name",
				"rtms__Customer__r.Customer_Support_Email__c",
				"rtms__Customer__r.BillingStateCode",
				"rtms__Customer__r.BillingCountryCode",
				"rtms__Customer__r.ShippingStateCode",
				"rtms__Customer__r.ShippingCountryCode",
			},
			wantNotContains: []string{},
		},
		{
			name:   "Prosponsive Tenant gets Safe Fields Only",
			tenant: TenantProsponsive,
			wantContains: []string{
				"rtms__Customer__r.Name",
			},
			wantNotContains: []string{
				"rtms__Customer__r.Customer_Support_Email__c",
				"rtms__Customer__r.BillingStateCode",
				"rtms__Customer__r.BillingCountryCode",
				"rtms__Customer__r.ShippingStateCode",
				"rtms__Customer__r.ShippingCountryCode",
			},
		},
		{
			name:   "Unknown Tenant defaults to Safe Fields",
			tenant: "unknowntenant",
			wantContains: []string{
				"rtms__Customer__r.Name",
			},
			wantNotContains: []string{
				"rtms__Customer__r.Customer_Support_Email__c",
				"rtms__Customer__r.BillingStateCode",
				"rtms__Customer__r.BillingCountryCode",
				"rtms__Customer__r.ShippingStateCode",
				"rtms__Customer__r.ShippingCountryCode",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Initialize the struct with the tenant name
			r := &Revenova{
				tms: models.Integration{
					Tenant: tt.tenant,
				},
			}

			got := r.buildCustomerFields()

			// 1. Verify Expected Fields are present
			for _, substr := range tt.wantContains {
				if !strings.Contains(got, substr) {
					t.Errorf("buildCustomerFields() missing expected field.\nWanted: %q\nGot: %q", substr, got)
				}
			}

			// 2. Verify Excluded Fields are absent
			for _, substr := range tt.wantNotContains {
				if strings.Contains(got, substr) {
					t.Errorf("buildCustomerFields() included a forbidden field.\nForbidden: %q\nGot: %q", substr, got)
				}
			}
		})
	}
}
