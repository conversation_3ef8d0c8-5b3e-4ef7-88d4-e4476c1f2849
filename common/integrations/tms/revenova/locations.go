package revenova

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/tmsrefresh"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	salesforceLoadQueryPath = "/services/data/v59.0/query"
	locationBatchSize       = 2000 // Salesforce max records per query
)

// GetLocations retrieves locations from Salesforce Account data
// It follows the same pattern as Turvo - uses database cache and only calls external API when needed
func (r *<PERSON><PERSON><PERSON>) GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	// Apply options
	options := &models.TMSOptions{}
	options.Apply(opts...)

	// Determine job type based on whether this is a poller job
	jobType := redis.LocationJob
	if options.IsPollerJob {
		jobType = redis.LocationRefreshJob
	}

	log.Info(ctx, "revenova.GetLocations - extracting locations from Salesforce Account data")

	var nextRecordsPath string

	// Priority for cursor (nextRecordsPath): explicit option > Redis state > default (empty)
	if options.Cursor != "" {
		nextRecordsPath = options.Cursor
		log.Info(ctx, "using explicit cursor for pagination", zap.String("path", nextRecordsPath))
	} else {
		// Check Redis for saved cursor from previous run
		_, savedCursor, err := redis.GetIntegrationState(ctx, r.tms.ID, jobType)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
		}
		if savedCursor != "" {
			nextRecordsPath = savedCursor
			log.Info(ctx, "using saved cursor from Redis for pagination", zap.String("path", nextRecordsPath))
		}
	}

	// Use normalized full address as key to avoid duplicates from capitalization differences
	locationsMap := make(map[string]models.TMSLocation)

	batchNumber := 0

	for {
		// Save the state in the options
		if options.OnProgress != nil {
			options.OnProgress(
				"",
				nextRecordsPath,
			)
		}

		batchNumber++
		log.Info(ctx, "fetching location batch from Salesforce Accounts",
			zap.Int("batchNumber", batchNumber),
			zap.Int("uniqueLocationsSoFar", len(locationsMap)))

		var accountResp SalesforceAccountLocationQueryResponse
		var err error

		if nextRecordsPath != "" {
			// Use the nextRecordsUrl from previous response
			err = r.get(ctx, nextRecordsPath, url.Values{}, &accountResp, s3backup.TypeLocations)
		} else {
			// First query - fetch all account locations
			accountResp, err = r.getLoadLocations(ctx)
		}

		if err != nil {
			// Convert map to slice once for return
			allLocations := convertMapToSlice(locationsMap)
			log.Error(ctx, "error fetching account location batch", zap.Error(err))
			// Save state for resuming
			tmsrefresh.SetIntegrationStateWithWarning(
				ctx,
				r.tms.ID,
				jobType,
				"",
				nextRecordsPath,
			)
			return allLocations, fmt.Errorf("failed to get account location batch: %w", err)
		}

		log.Info(ctx, "received account location batch",
			zap.Int("locationCount", len(accountResp.Records)),
			zap.Int("totalSize", accountResp.TotalSize),
			zap.Bool("done", accountResp.Done))

		// Track only NEW locations found in this batch
		newLocationsInBatch := make([]models.TMSLocation, 0)

		// Convert account records to TMSLocation
		for _, account := range accountResp.Records {
			if location := convertAccountToTMSLocation(ctx, r.tms.ID, account); location.FullAddress != "" {
				// Normalize address for deduplication (lowercase, trimmed)
				normalizedKey := normalizeAddress(location.FullAddress)
				// Only add if not already in the map (this is a NEW location)
				if _, exists := locationsMap[normalizedKey]; !exists {
					locationsMap[normalizedKey] = location
					newLocationsInBatch = append(newLocationsInBatch, location)
				}
			}
		}

		log.Info(ctx, "extracted unique locations from batch",
			zap.Int("newLocationsInBatch", len(newLocationsInBatch)),
			zap.Int("totalUniqueLocations", len(locationsMap)))

		// Upsert ONLY the NEW locations found in this batch to database
		if len(newLocationsInBatch) > 0 {
			log.Info(ctx, "upserting new locations to database", zap.Int("count", len(newLocationsInBatch)))

			if err = tmsLocationDB.RefreshTMSLocations(ctx, &newLocationsInBatch); err != nil {
				// Convert map to slice once for return
				allLocations := convertMapToSlice(locationsMap)

				log.Error(ctx, "failed to upsert locations to database", zap.Error(err))
				// Save state for resuming
				tmsrefresh.SetIntegrationStateWithWarning(
					ctx,
					r.tms.ID,
					jobType,
					"",
					nextRecordsPath,
				)
				return allLocations, fmt.Errorf("failed to upsert locations: %w", err)
			}
		} else {
			log.Info(ctx, "no new unique locations found in this batch, skipping database upsert")
		}

		// Check if we're done with pagination
		if accountResp.Done {
			log.Info(ctx, "completed fetching all account locations - pagination complete!",
				zap.Int("totalUniqueLocations", len(locationsMap)))
			break
		}

		// Update cursor for next iteration
		if accountResp.NextRecordsURL != "" {
			nextRecordsPath = accountResp.NextRecordsURL
		} else {
			log.Warn(ctx, "no nextRecordsUrl but done=false, breaking loop")
			break
		}
	}

	// Clear redis state after successful completion
	if err := redis.ClearIntegrationState(ctx, r.tms.ID, jobType); err != nil {
		log.Error(ctx, "failed to clear redis state after successful completion", zap.Error(err))
	}

	// Convert map to slice once at the end
	allLocations := convertMapToSlice(locationsMap)
	log.Info(ctx, "successfully retrieved all unique locations", zap.Int("count", len(allLocations)))
	return allLocations, nil
}

func (r *Revenova) CreateLocation(
	context.Context,
	models.CreateLocationRequest,
	*models.TMSUser,
) (models.TMSLocation, error) {
	return models.TMSLocation{}, helpers.NotImplemented(models.Revenova, "CreateLocation")
}

func (r *Revenova) GetCommodities(context.Context, ...models.TMSOption) ([]models.TMSCommodity, error) {
	return nil, helpers.NotImplemented(models.Revenova, "GetCommodities")
}

func (r *Revenova) GetCommodityDetails(context.Context, string, *uint) (models.TMSCommodity, error) {
	return models.TMSCommodity{}, helpers.NotImplemented(models.Revenova, "GetCommodityDetails")
}

func (r *Revenova) CreateCommodity(
	context.Context,
	models.CreateCommodityRequest,
	*models.TMSUser,
) (models.TMSCommodity, error) {
	return models.TMSCommodity{}, helpers.NotImplemented(models.Revenova, "CreateCommodity")
}

// getLoadLocations fetches account locations from Salesforce
func (r *Revenova) getLoadLocations(ctx context.Context) (SalesforceAccountLocationQueryResponse, error) {
	queryParams := make(url.Values)

	// SOQL query to get account locations directly
	soqlQuery := `SELECT Id, Name, ShippingStreet, ShippingCity, ShippingState, 
		ShippingPostalCode, ShippingCountry, Phone 
		FROM Account 
		WHERE Id IN 
		    (SELECT rtms__Location2__c 
		     FROM rtms__Stop__c) 
		  AND ShippingCountry IN ('United States', 'Canada') 
		ORDER BY CreatedDate DESC`

	queryParams.Set("q", soqlQuery)

	log.Info(ctx, "executing Salesforce SOQL query for account locations",
		zap.String("query", soqlQuery))

	var resp SalesforceAccountLocationQueryResponse
	if err := r.get(ctx, salesforceLoadQueryPath, queryParams, &resp, s3backup.TypeLocations); err != nil {
		log.Error(ctx, "Salesforce account location query failed", zap.Error(err))
		return resp, fmt.Errorf("failed to query account locations: %w", err)
	}

	log.Info(ctx, "Salesforce account location query response",
		zap.Int("totalSize", resp.TotalSize),
		zap.Int("recordCount", len(resp.Records)),
		zap.Bool("done", resp.Done))

	return resp, nil
}

// convertAccountToTMSLocation creates a TMSLocation from Account fields
func convertAccountToTMSLocation(
	ctx context.Context,
	tmsID uint,
	account SalesforceAccountLocation,
) models.TMSLocation {
	street := strings.TrimSpace(account.ShippingStreet)
	city := strings.TrimSpace(account.ShippingCity)
	state := strings.TrimSpace(account.ShippingState)
	zipcode := strings.TrimSpace(account.ShippingPostalCode)
	country := strings.TrimSpace(account.ShippingCountry)

	// Skip if essential fields are missing
	if city == "" || state == "" {
		return models.TMSLocation{}
	}

	stateCode := state
	// Convert state to two-letter code if it's not already a two-letter code
	if len(state) > 2 {
		stateCode = helpers.GetStateAbbreviation(ctx, state)
	}

	// Determine country code for consistency
	countryCode := "US" // Default to US
	if country == "Canada" {
		countryCode = "CA"
	}

	fullAddress := models.ConcatAddress(models.CompanyCoreInfo{
		AddressLine1: street,
		City:         city,
		State:        stateCode,
		Zipcode:      zipcode,
		Country:      countryCode,
	})

	// Use the Account ID as the external ID
	externalID := account.ID

	// Use the Account Name as the location name
	name := strings.TrimSpace(account.Name)
	if name == "" {
		name = city + ", " + stateCode
	}

	return models.TMSLocation{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: externalID,
			Name:          name,
			AddressLine1:  street,
			City:          city,
			State:         stateCode,
			Zipcode:       zipcode,
			Country:       countryCode,
			Phone:         strings.TrimSpace(account.Phone),
		},
		FullAddress: fullAddress,
		NameAddress: models.ConcatNameAddress(name, fullAddress),
		// Account locations can be both shipper and consignee
		IsShipper:   true,
		IsConsignee: true,
	}
}

// normalizeAddress normalizes an address for consistent comparison
// Converts to lowercase and trims whitespace
func normalizeAddress(address string) string {
	return strings.ToLower(strings.TrimSpace(address))
}

// convertMapToSlice converts the locations map to a slice
func convertMapToSlice(locationsMap map[string]models.TMSLocation) []models.TMSLocation {
	slice := make([]models.TMSLocation, 0, len(locationsMap))
	for _, loc := range locationsMap {
		slice = append(slice, loc)
	}
	return slice
}
