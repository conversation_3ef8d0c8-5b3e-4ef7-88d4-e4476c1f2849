package revenova

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	equipmentTypeCacheKey = "revenova:equipment_types:%d"
	equipmentTypeCacheTTL = 24 * time.Hour
)

// GetEquipmentTypes fetches available equipment types from Salesforce, using Redis caching.
func (r *<PERSON>enova) GetEquipmentTypes(ctx context.Context) ([]models.TMSEquipmentType, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetEquipmentTypesRevenova", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	// 1. Check Redis Cache
	cacheKey := fmt.Sprintf(equipmentTypeCacheKey, r.tms.ServiceID)
	cachedTypes, found, err := redis.GetKey[[]models.TMSEquipmentType](ctx, cacheKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get equipment types from redis", zap.Error(err))
	} else if found {
		return cachedTypes, nil
	}

	// 2. Fetch from Salesforce API
	// Query all equipment types (usually < 100 records, so one page is fine)
	soqlQuery := "SELECT Id, Name FROM rtms__EquipmentType__c ORDER BY Name ASC"
	queryParams := url.Values{}
	queryParams.Set("q", soqlQuery)

	var response SalesforceEquipmentTypeResponse
	err = r.get(ctx, salesforceQueryPath, queryParams, &response, s3backup.TypeLoads)
	if err != nil {
		return nil, fmt.Errorf("failed to query equipment types from Salesforce: %w", err)
	}

	equipmentTypes := make([]models.TMSEquipmentType, len(response.Records))
	for i, rec := range response.Records {
		equipmentTypes[i] = models.TMSEquipmentType{
			ID:   rec.ID,
			Name: rec.Name,
		}
	}

	// 3. Store in Redis Cache
	if len(equipmentTypes) > 0 {
		if err := redis.SetKey(ctx, cacheKey, equipmentTypes, equipmentTypeCacheTTL); err != nil {
			log.Warn(ctx, "failed to cache equipment types in redis", zap.Error(err))
		}
	}

	log.Info(ctx, "fetched equipment types from Salesforce", zap.Int("count", len(equipmentTypes)))
	return equipmentTypes, nil
}

// ResolveEquipmentTypeID attempts to find the best matching Salesforce Equipment Type ID
// for a given transport type string (e.g. "VAN", "REEFER") or a Salesforce ID (e.g. "a0nHs...").
// If the input is already a valid Salesforce ID, it returns it directly.
func (r *Revenova) ResolveEquipmentTypeID(ctx context.Context, transportType string) (string, error) {
	if transportType == "" {
		return "", nil
	}

	types, err := r.GetEquipmentTypes(ctx)
	if err != nil {
		return "", err
	}

	// Check if transportType is already a Salesforce ID (15 or 18 alphanumeric characters)
	// If so, validate it exists and return it
	if len(transportType) == 15 || len(transportType) == 18 {
		// Check if it matches an existing equipment type ID
		for _, et := range types {
			if et.ID == transportType {
				return transportType, nil
			}
		}
	}

	transportType = strings.ToUpper(strings.TrimSpace(transportType))

	// Define matching rules (Priority order)
	// 1. Exact Name Match (Case-insensitive handled by normalization)
	// 2. Specific Defaults for common types
	// 3. Substring Match

	var defaultMatch string

	for _, et := range types {
		normalizedName := strings.ToUpper(et.Name)

		// Direct match
		if normalizedName == transportType {
			return et.ID, nil
		}

		// Logic for defaults
		switch transportType {
		case "VAN", "DRY VAN", "DRYVAN", "TL":
			if normalizedName == "DRY VAN 53'" {
				return et.ID, nil
			}
			if normalizedName == "VAN TRAILER" && defaultMatch == "" {
				defaultMatch = et.ID
			}
		case "REEFER", "REFRIGERATED":
			if normalizedName == "REEFER 53'" {
				return et.ID, nil
			}
			if normalizedName == "REEFER TRAILER" && defaultMatch == "" {
				defaultMatch = et.ID
			}
		case "FLATBED", "FLAT BED":
			if normalizedName == "FLATBED TRAILER" {
				return et.ID, nil
			}
			if normalizedName == "FLATBED 3 AXLE 53'" && defaultMatch == "" {
				defaultMatch = et.ID
			}
		case "CONTAINER":
			if normalizedName == "CONTAINER 40'" {
				return et.ID, nil
			}
		}
	}

	// If we found a secondary default match, return it
	if defaultMatch != "" {
		return defaultMatch, nil
	}

	// Fallback: Try to find *any* match containing the transport type
	// e.g. "Box Truck" input matches "Straight Box Truck"
	for _, et := range types {
		if strings.Contains(strings.ToUpper(et.Name), transportType) {
			return et.ID, nil
		}
	}

	return "", nil
}
