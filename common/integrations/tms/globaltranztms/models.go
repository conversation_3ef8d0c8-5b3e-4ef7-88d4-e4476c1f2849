package globaltranztms

type (
	// Customer Models
	GetCustomerBoardRequest struct {
		FromDate                  string   `json:"fromDate"`
		IncludeInactive           bool     `json:"includeInactive"`
		IncludeMasterAccountsOnly bool     `json:"includeMasterAccountsOnly"`
		IsTimeframeAll            bool     `json:"isTimeframeAll"`
		PageNumber                int      `json:"pageNumber"`
		PageSize                  int      `json:"pageSize"`
		SearchFilter              []string `json:"searchFilter"`
		SearchText                string   `json:"searchText"`
		SortModel                 []string `json:"sortModel"`
		ToDate                    string   `json:"toDate"`
	}

	GetCustomerBoardResponse struct {
		Message       any                 `json:"message"`
		DidError      bool                `json:"didError"`
		ErrorMessages any                 `json:"errorMessages"`
		Model         []CustomerBoardItem `json:"model"`
		PageSize      int                 `json:"pageSize"`
		PageNumber    int                 `json:"pageNumber"`
		ItemsCount    int                 `json:"itemsCount"`
		PageCount     int                 `json:"pageCount"`
	}

	CustomerBoardItem struct {
		CustomerBk   int    `json:"customerBk"`
		CustomerName string `json:"customerName"`
		SalesRepName string `json:"salesRepName"`
		PhoneNumber  string `json:"phoneNumber"`
		EmailAddress string `json:"emailAddress"`
	}

	// Customer Account Settings and Info Models
	GetCustomerAccountSettingsAndInfoResponse struct {
		Message          any                            `json:"message"`
		DidError         bool                           `json:"didError"`
		ErrorMessages    any                            `json:"errorMessages"`
		ObjectWasCreated bool                           `json:"objectWasCreated"`
		Model            CustomerAccountSettingsAndInfo `json:"model"`
	}

	CustomerAccountSettingsAndInfo struct {
		AccountInformation AccountInformation `json:"accountInformation"`
		AccountSettings    AccountSettings    `json:"accountSettings"`
	}

	AccountInformation struct {
		ContactName          string          `json:"contactName"`
		CompanyID            int             `json:"companyId"`
		CompanyName          string          `json:"companyName"`
		CompanyLegalName     string          `json:"companyLegalName"`
		ContactEmail         string          `json:"contactEmail"`
		Phone                string          `json:"phone"`
		LegacyGlobalOsUserID int             `json:"legacyGlobalOsUserId"`
		BillingAddress       CustomerAddress `json:"billingAddress"`
		CustomerBk           int             `json:"customerBk"`
	}

	AccountSettings struct {
		PhysicalAddress          CustomerAddress `json:"physicalAddress"`
		AccountRep               string          `json:"accountRep"`
		AccountRepEmail          string          `json:"accountRepEmail"`
		AccountRepBk             int             `json:"accountRepBk"`
		AccountType              int             `json:"accountType"`
		CustomerSpecificArUser   string          `json:"customerSpecificArUser"`
		CustomerSpecificArUserID int             `json:"customerSpecificArUserId"`
		AccountDesignation       int             `json:"accountDesignation"`
		GtzGroupEmail            *string         `json:"gtzGroupEmail"`
		BOLSettings              int             `json:"bolSettings"`
		DistanceMileageProfile   *string         `json:"distanceMileageProfile"`
		ContactName              string          `json:"contactName"`
		ContactEmail             string          `json:"contactEmail"`
		Phone                    string          `json:"phone"`
		IsSameAsBilling          bool            `json:"isSameAsBilling"`
		IsCsp                    bool            `json:"isCsp"`
		SeasonalShipper          int             `json:"seasonalShipper"`
		IsSeasonal               bool            `json:"isSeasonal"`
		AgencyName               string          `json:"agencyName"`
		LoginName                string          `json:"loginName"`
		LoginPassword            string          `json:"loginPassword"`
		EmailPassword            bool            `json:"emailPassword"`
		GtzShipContactEmail      *string         `json:"gtzShipContactEmail"`
		AccountManagerID         int             `json:"accountManagerId"`
		AccountManagerName       string          `json:"accountManagerName"`
		AccountManagerEmailID    string          `json:"accountManagerEmailId"`
	}

	CustomerAddress struct {
		ID                int     `json:"id"`
		Street1           string  `json:"street1"`
		Street2           string  `json:"street2"`
		City              string  `json:"city"`
		State             string  `json:"state"`
		Zip               string  `json:"zip"`
		Country           int     `json:"country"`
		AddressType       int     `json:"addressType"`
		CustomerAddressBk int     `json:"customerAddressBk"`
		CustomerBk        int     `json:"customerBk"`
		Phone             *string `json:"phone"`
		Contact           *string `json:"contact"`
	}
)
