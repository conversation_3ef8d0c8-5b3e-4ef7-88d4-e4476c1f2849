package globaltranztms

import (
	"os"
	"strconv"
)

var (
	MaxOrderDetailsRequestsPerMinute = 30
	MaxOrderBoardRequestsPerMinute   = 10
)

func init() {
	MaxOrderDetailsRequestsPerMinute = getEnvInt("GTZ_ORDER_DETAILS_RATE_LIMIT", 30)
	MaxOrderBoardRequestsPerMinute = getEnvInt("GTZ_ORDER_BOARD_RATE_LIMIT", 10)
}

func getEnvInt(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return intValue
}
