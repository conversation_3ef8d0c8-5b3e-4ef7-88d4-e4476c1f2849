package globaltranztms

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

const (
	GetEquipmentTypePath    = "api/tms-lookup/Lookup/EquipmentType"
	GetEquipmentLengthPath  = "api/tms-lookup/Lookup/EquipmentLength"
	GetEquipmentMappingPath = "api/tms-lookup/Lookup/EquipmentMapping"
	GetAllAccessorialsPath  = "api/tms-lookup/Lookup/AllAccessorials"
	GetDisplayFieldsPath    = "api/tms-lookup/Lookup/DisplayFields"
)

// LookupResponse represents the standard GlobalTranz lookup API response structure
type LookupResponse struct {
	Message       *string      `json:"message"`
	DidError      bool         `json:"didError"`
	ErrorMessages []string     `json:"errorMessages"`
	Model         []LookupItem `json:"model"`
}

// LookupItem represents a single item in a lookup response
type LookupItem struct {
	ID          int    `json:"id"`
	DisplayText string `json:"displayText"`
}

// AccessorialItem represents an accessorial item from AllAccessorials API
type AccessorialItem struct {
	AccessorialID     int    `json:"accessorialId"`
	DisplayText       string `json:"displayText"`
	AccessorialTypeID int    `json:"accessorialTypeId"`
}

// AllAccessorialsResponse represents the response from AllAccessorials API
type AllAccessorialsResponse struct {
	Message       *string           `json:"message"`
	DidError      bool              `json:"didError"`
	ErrorMessages []string          `json:"errorMessages"`
	Model         []AccessorialItem `json:"model"`
}

// EquipmentMappingResponse represents the response from EquipmentMapping API
type EquipmentMappingResponse struct {
	Message       *string                `json:"message"`
	DidError      bool                   `json:"didError"`
	ErrorMessages []string               `json:"errorMessages"`
	Model         []EquipmentMappingItem `json:"model"`
}

// EquipmentMappingItem represents a single equipment type mapping
type EquipmentMappingItem struct {
	ID            int                     `json:"id"`
	DisplayFields []EquipmentDisplayField `json:"displayFields"`
}

// EquipmentDisplayField represents a display field in the equipment mapping
type EquipmentDisplayField struct {
	ID     int                     `json:"id"`
	Values []EquipmentMappingValue `json:"values"`
}

// EquipmentMappingValue represents a value in the equipment mapping
type EquipmentMappingValue struct {
	ID            int                     `json:"id"`
	DisplayFields []EquipmentDisplayField `json:"displayFields"`
}

// GetEquipmentTypes fetches the list of equipment types from GlobalTranz
func (gt GlobalTranz) GetEquipmentTypes(ctx context.Context) ([]LookupItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetEquipmentTypesGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching equipment types from GlobalTranz")

	var response LookupResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetEquipmentTypePath,
		nil,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch equipment types",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to fetch equipment types: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GlobalTranz equipment types API returned error",
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	log.Info(
		ctx,
		"successfully fetched equipment types",
		zap.Int("count", len(response.Model)),
	)

	return response.Model, nil
}

// GetEquipmentLengths fetches the list of equipment lengths from GlobalTranz
func (gt GlobalTranz) GetEquipmentLengths(ctx context.Context) ([]LookupItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetEquipmentLengthsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching equipment lengths from GlobalTranz")

	var response LookupResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetEquipmentLengthPath,
		nil,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch equipment lengths",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to fetch equipment lengths: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GlobalTranz equipment lengths API returned error",
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	log.Info(
		ctx,
		"successfully fetched equipment lengths",
		zap.Int("count", len(response.Model)),
	)

	return response.Model, nil
}

// GetEquipmentMapping fetches the equipment mapping from GlobalTranz
// This mapping defines which equipment lengths are available for each equipment type
func (gt GlobalTranz) GetEquipmentMapping(ctx context.Context) ([]EquipmentMappingItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetEquipmentMappingGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching equipment mapping from GlobalTranz")

	var response EquipmentMappingResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetEquipmentMappingPath,
		nil,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch equipment mapping",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to fetch equipment mapping: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GlobalTranz equipment mapping API returned error",
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	log.Info(
		ctx,
		"successfully fetched equipment mapping",
		zap.Int("count", len(response.Model)),
	)

	return response.Model, nil
}

// GetAvailableLengthsForEquipment returns the available equipment lengths for a given equipment type ID
// based on the equipment mapping
func (gt GlobalTranz) GetAvailableLengthsForEquipment(
	ctx context.Context,
	equipmentTypeID int,
) ([]int, error) {
	mapping, err := gt.GetEquipmentMapping(ctx)
	if err != nil {
		return nil, err
	}

	// Find the equipment type in the mapping
	for _, item := range mapping {
		if item.ID == equipmentTypeID {
			// Look for displayField with id 0 (equipment length field)
			for _, displayField := range item.DisplayFields {
				if displayField.ID == 0 {
					// Extract length IDs from values
					var lengthIDs []int
					for _, value := range displayField.Values {
						lengthIDs = append(lengthIDs, value.ID)
					}
					return lengthIDs, nil
				}
			}
			// If no length field found, return empty (e.g., Power Only)
			return []int{}, nil
		}
	}

	return nil, errtypes.NewUserFacingError(
		fmt.Errorf("equipment type ID %d not found in mapping", equipmentTypeID),
	)
}

// GetAllAccessorials fetches the list of all accessorials from GlobalTranz
func (gt GlobalTranz) GetAllAccessorials(ctx context.Context, quoteModeTypeEnum int) ([]AccessorialItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetAllAccessorialsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching all accessorials from GlobalTranz", zap.Int("quoteModeTypeEnum", quoteModeTypeEnum))

	// Build query parameters
	queryParams := url.Values{}
	queryParams.Set("quoteModeTypeEnum", fmt.Sprintf("%d", quoteModeTypeEnum))

	var response AllAccessorialsResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetAllAccessorialsPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch all accessorials",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to fetch all accessorials: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GlobalTranz all accessorials API returned error",
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	log.Info(
		ctx,
		"successfully fetched all accessorials",
		zap.Int("count", len(response.Model)),
	)

	return response.Model, nil
}

// GetDisplayFields fetches the list of display fields from GlobalTranz
func (gt GlobalTranz) GetDisplayFields(ctx context.Context) ([]LookupItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetDisplayFieldsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching display fields from GlobalTranz")

	var response LookupResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetDisplayFieldsPath,
		nil,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch display fields",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to fetch display fields: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GlobalTranz display fields API returned error",
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	log.Info(
		ctx,
		"successfully fetched display fields",
		zap.Int("count", len(response.Model)),
	)

	return response.Model, nil
}
