package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

const (
	GetAddressBookPath        = "api/tms-addressbook/AddressBook/GetAddressesByCustomerBK"
	SaveAddressPath           = "api/tms-addressbook/AddressBook/SaveAddressForCustomer"
	SearchLocationPath        = "api/tms-addressbook/AddressBook/SearchLocation"
	SearchAddressesPath       = "api/tms-addressbook/AddressBook/SearchAddresses"
	GetAddressDetailsByBKPath = "api/tms-addressbook/AddressBook/GetAddressDetailsByAddressBK"
)

type AddressBookResponse struct {
	Message          *string           `json:"message"`
	DidError         bool              `json:"didError"`
	ErrorMessages    []string          `json:"errorMessages"`
	ObjectWasCreated bool              `json:"objectWasCreated"`
	Model            []AddressBookItem `json:"model"`
}

type AddressBookItem struct {
	AddressBK      int64              `json:"addressBK"`
	CompanyName    string             `json:"companyName"`
	Contact        AddressBookContact `json:"contact"`
	Address        AddressBookAddress `json:"address"`
	DockHours      *string            `json:"dockHours"`
	LocationNotes  *string            `json:"locationNotes"`
	DefaultGroupID int                `json:"defaultGroupId"`
	Remarks        *string            `json:"remarks"`
}

type AddressBookContact struct {
	Name  string  `json:"name"`
	Phone string  `json:"phone"`
	Email *string `json:"email"`
	Fax   *string `json:"fax"`
}

type AddressBookAddress struct {
	Street1   string  `json:"street1"`
	Street2   string  `json:"street2"`
	City      string  `json:"city"`
	State     string  `json:"state"`
	Zip       string  `json:"zip"`
	Country   int     `json:"country"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// LocationSearchItem represents a location search result from GlobalTranz
type LocationSearchItem struct {
	Street             string  `json:"street"`
	City               string  `json:"city"`
	StateCode          string  `json:"stateCode"`
	State              string  `json:"state"`
	Zip                string  `json:"zip"`
	CountryCode        int     `json:"countryCode"`
	Country            string  `json:"country"`
	Zone               string  `json:"zone"`
	CountyAbbreviation *string `json:"countyAbbreviation"`
	Latitude           float64 `json:"latitude"`
	Longitude          float64 `json:"longitude"`
	IsCanada           bool    `json:"isCanada"`
	IsMexico           bool    `json:"isMexico"`
	IsHawaii           bool    `json:"isHawaii"`
	IsAlaska           bool    `json:"isAlaska"`
}

// LocationSearchResponse represents the response from SearchLocation API
type LocationSearchResponse struct {
	Message       *string              `json:"message"`
	DidError      bool                 `json:"didError"`
	ErrorMessages []string             `json:"errorMessages"`
	Model         []LocationSearchItem `json:"model"`
}

// cleanLocationErrorMessage extracts a user-facing error string from GlobalTranz location error responses.
// - If body is HTML (e.g., Azure Gateway 400 Bad Request), returns a user-friendly message.
// - If body is JSON with errorMessages, extracts and returns those messages.
// - Otherwise returns a generic user-friendly message.
func cleanLocationErrorMessage(origErr error) string {
	var httpErr errtypes.HTTPResponseError
	if !errors.As(origErr, &httpErr) {
		return ""
	}

	body := strings.TrimSpace(string(httpErr.ResponseBody))
	if body == "" {
		return "Please check all required fields are filled correctly"
	}

	// Check if response is HTML (e.g., Azure Gateway error page)
	if strings.HasPrefix(body, "<html") || strings.HasPrefix(body, "<!DOCTYPE") {
		// For HTML responses (like Azure Gateway 400 Bad Request), provide a user-friendly message
		if httpErr.StatusCode == http.StatusBadRequest {
			return "Please check all required fields are filled correctly"
		}
		return "An error occurred while creating the location. Please try again."
	}

	// Try to parse as JSON to extract error messages
	var jsonErr struct {
		Message       *string  `json:"message"`
		DidError      bool     `json:"didError"`
		ErrorMessages []string `json:"errorMessages"`
	}
	if err := json.Unmarshal(httpErr.ResponseBody, &jsonErr); err == nil {
		// If we have error messages, return them
		if len(jsonErr.ErrorMessages) > 0 {
			return strings.Join(jsonErr.ErrorMessages, "; ")
		}
		// If we have a message field, return it
		if jsonErr.Message != nil && strings.TrimSpace(*jsonErr.Message) != "" {
			return *jsonErr.Message
		}
	}

	// For 400 Bad Request, provide a user-friendly message
	if httpErr.StatusCode == http.StatusBadRequest {
		return "Please check all required fields are filled correctly"
	}

	// Fallback: return a generic message
	return "An error occurred while creating the location. Please try again."
}

func (gt GlobalTranz) GetLocations(ctx context.Context, _ ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	customers, err := tmsCustomerDB.GetTMSCustomersByTMSID(ctx, rds.GenericGetQuery{
		TMSID: gt.tms.ID,
	})
	if err != nil {
		log.Error(
			ctx,
			"failed to get TMS customers",
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get TMS customers: %w", err)
	}

	log.Info(
		ctx,
		"fetching locations for GlobalTranz customers",
		zap.Int("customerCount", len(customers)),
	)

	var allLocations []models.TMSLocation
	successfulBatches := 0
	failedBatches := 0

	for _, customer := range customers {
		if customer.ExternalTMSID == "" {
			log.WarnNoSentry(
				ctx,
				"customer has no external TMS ID, skipping",
				zap.Uint("customerId", customer.ID),
				zap.String("customerName", customer.Name),
			)
			continue
		}

		locations, fetchErr := gt.getLocationsByCustomer(ctx, customer.ExternalTMSID)
		if fetchErr != nil {
			log.Error(
				ctx,
				"failed to fetch locations for customer",
				zap.String("customerBK", customer.ExternalTMSID),
				zap.String("customerName", customer.Name),
				zap.Error(fetchErr),
			)
			failedBatches++
			continue
		}

		log.Info(
			ctx,
			"fetched locations for customer",
			zap.String("customerName", customer.Name),
			zap.Int("locationCount", len(locations)),
		)

		if len(locations) > 0 {
			if saveErr := tmsLocationDB.RefreshTMSLocations(ctx, &locations); saveErr != nil {
				log.Error(
					ctx,
					"failed to save locations for customer",
					zap.String("customerBK", customer.ExternalTMSID),
					zap.String("customerName", customer.Name),
					zap.Error(saveErr),
				)
				failedBatches++
				continue
			}

			log.Info(
				ctx,
				"saved locations for customer",
				zap.String("customerName", customer.Name),
				zap.Int("locationCount", len(locations)),
			)
		}

		allLocations = append(allLocations, locations...)
		successfulBatches++
	}

	// If 0 batches succeeded, mark the span as failed
	if successfulBatches == 0 && failedBatches > 0 {
		err = fmt.Errorf("all %d customer batches failed to fetch or save locations", failedBatches)
		log.Error(
			ctx,
			"all customer batches failed",
			zap.Int("failedBatches", failedBatches),
			zap.Error(err),
		)
	}

	return allLocations, nil
}

func (gt *GlobalTranz) getLocationsByCustomer(ctx context.Context, customerBK string) ([]models.TMSLocation, error) {
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)

	var response AddressBookResponse
	err := gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetAddressBookPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	locations := make([]models.TMSLocation, 0, len(response.Model))
	for _, item := range response.Model {
		location := toTMSLocation(gt.tms.ID, item)
		locations = append(locations, location)
	}

	return locations, nil
}

// searchLocationsByCustomer searches for locations by customerBK and company name using SearchAddresses API
// This is more efficient than GetAddressesByCustomerBK when looking for a specific location
// and avoids limit issues that can prevent finding newly created locations
func (gt *GlobalTranz) searchLocationsByCustomer(
	ctx context.Context,
	customerBK string,
	companyName string,
) ([]models.TMSLocation, error) {
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)
	queryParams.Set("name", companyName)

	var response AddressBookResponse
	err := gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		SearchAddressesPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"SearchAddresses API request failed",
			zap.String("customerBK", customerBK),
			zap.String("companyName", companyName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("SearchAddresses API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"SearchAddresses API returned error",
			zap.String("customerBK", customerBK),
			zap.String("companyName", companyName),
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("SearchAddresses API returned error: %s", errorMsg)
	}

	locations := make([]models.TMSLocation, 0, len(response.Model))
	for _, item := range response.Model {
		location := toTMSLocation(gt.tms.ID, item)
		locations = append(locations, location)
	}

	return locations, nil
}

// getAddressDetailsByBK fetches full location details including dock hours using GetAddressDetailsByAddressBK API
func (gt *GlobalTranz) getAddressDetailsByBK(
	ctx context.Context,
	customerBK string,
	addressBK string,
) (models.TMSLocation, error) {
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)
	queryParams.Set("addressBK", addressBK)

	var response GetAddressDetailsResponse
	err := gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetAddressDetailsByBKPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"GetAddressDetailsByAddressBK API request failed",
			zap.String("customerBK", customerBK),
			zap.String("addressBK", addressBK),
			zap.Error(err),
		)
		return models.TMSLocation{}, fmt.Errorf("GetAddressDetailsByAddressBK API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"GetAddressDetailsByAddressBK API returned error",
			zap.String("customerBK", customerBK),
			zap.String("addressBK", addressBK),
			zap.String("errorMessage", errorMsg),
		)
		return models.TMSLocation{}, fmt.Errorf("GetAddressDetailsByAddressBK API returned error: %s", errorMsg)
	}

	return addressDetailsToTMSLocation(gt.tms.ID, response.Model), nil
}

// countryCodeToString converts GlobalTranz country code to ISO 2-letter country code
func countryCodeToString(countryCode int) string {
	switch countryCode {
	case 1:
		return "US" // USA
	case 2:
		return "CA" // Canada
	case 3:
		return "CN" // China
	case 4:
		return "MX" // Mexico
	default:
		return "" // Unknown country code
	}
}

// addressDetailsToTMSLocation converts AddressDetailsItem to models.TMSLocation
func addressDetailsToTMSLocation(tmsIntegrationID uint, item AddressDetailsItem) models.TMSLocation {
	externalTMSID := strconv.FormatInt(item.AddressBK, 10)

	// Convert country code to ISO 2-letter country code
	country := countryCodeToString(item.Address.Country)

	email := ""
	if item.Contact.Email != nil && *item.Contact.Email != "" {
		email = *item.Contact.Email
	}

	notes := ""
	if item.LocationNotes != nil && *item.LocationNotes != "" {
		notes = *item.LocationNotes
	}
	if item.PickUpRemarks != nil && *item.PickUpRemarks != "" {
		if notes != "" {
			notes += " | "
		}
		notes += *item.PickUpRemarks
	}
	if item.DeliveryRemarks != nil && *item.DeliveryRemarks != "" {
		if notes != "" {
			notes += " | "
		}
		notes += *item.DeliveryRemarks
	}

	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: externalTMSID,
		Name:          item.CompanyName,
		AddressLine1:  item.Address.Street1,
		AddressLine2:  item.Address.Street2,
		City:          item.Address.City,
		State:         item.Address.State,
		Zipcode:       item.Address.Zip,
		Country:       country,
		Contact:       item.Contact.Name,
		Phone:         item.Contact.Phone,
		Email:         email,
	}

	// Parse dock hours in priority order:
	// 1. DockHours (general, used for both pickup and consignee)
	// 2. If empty, check PickUpDockHours (for pickup locations)
	// 3. If empty, check DeliveryDockHours (for consignee locations)
	openTime := ""
	closeTime := ""

	parseTime := func(timeStr string) string {
		timeStr = strings.TrimSpace(timeStr)
		if timeStr == "" {
			return ""
		}
		if strings.Count(timeStr, ":") == 2 {
			parts := strings.Split(timeStr, ":")
			if len(parts) >= 2 {
				return parts[0] + ":" + parts[1]
			}
		}
		return timeStr
	}

	hasValidDockHours := func(dockHours AddressDockHours) bool {
		return dockHours.From != "" && dockHours.To != ""
	}

	extractTimes := func(dockHours AddressDockHours) (string, string) {
		if !hasValidDockHours(dockHours) {
			return "", ""
		}
		return parseTime(dockHours.From), parseTime(dockHours.To)
	}

	var selectedDockHours *AddressDockHours
	switch {
	case hasValidDockHours(item.DockHours):
		selectedDockHours = &item.DockHours
	case hasValidDockHours(item.PickUpDockHours):
		selectedDockHours = &item.PickUpDockHours
	case item.DeliveryDockHours != nil && hasValidDockHours(*item.DeliveryDockHours):
		selectedDockHours = item.DeliveryDockHours
	}

	if selectedDockHours != nil {
		openTime, closeTime = extractTimes(*selectedDockHours)
	}

	location := models.TMSLocation{
		TMSIntegrationID: tmsIntegrationID,
		CompanyCoreInfo:  companyCoreInfo,
		Latitude:         item.Address.Latitude,
		Longitude:        item.Address.Longitude,
		Point: models.Point{
			Latitude:  float32(item.Address.Latitude),
			Longitude: float32(item.Address.Longitude),
		},
		Notes:          notes,
		OpenTime:       openTime,
		CloseTime:      closeTime,
		DefaultGroupID: item.DefaultGroupID, // Store defaultGroupId from location details
		// Timezone will be preserved from original request if set
	}

	location.FullAddress = models.ConcatAddress(companyCoreInfo)
	location.NameAddress = location.Name + ", " + location.FullAddress

	return location
}

func toTMSLocation(tmsIntegrationID uint, item AddressBookItem) models.TMSLocation {
	externalTMSID := strconv.FormatInt(item.AddressBK, 10)

	// Convert country code to ISO 2-letter country code
	country := countryCodeToString(item.Address.Country)

	email := ""
	if item.Contact.Email != nil && *item.Contact.Email != "" {
		email = *item.Contact.Email
	}

	notes := ""
	if item.LocationNotes != nil && *item.LocationNotes != "" {
		notes = *item.LocationNotes
	}
	if item.Remarks != nil && *item.Remarks != "" {
		if notes != "" {
			notes += " | "
		}
		notes += *item.Remarks
	}

	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: externalTMSID,
		Name:          item.CompanyName,
		AddressLine1:  item.Address.Street1,
		AddressLine2:  item.Address.Street2,
		City:          item.Address.City,
		State:         item.Address.State,
		Zipcode:       item.Address.Zip,
		Country:       country,
		Contact:       item.Contact.Name,
		Phone:         item.Contact.Phone,
		Email:         email,
	}

	// Parse dock hours from DockHours string (format: "HH:MM-HH:MM" like "06:00-21:00")
	openTime := ""
	closeTime := ""
	if item.DockHours != nil && *item.DockHours != "" {
		dockHoursStr := strings.TrimSpace(*item.DockHours)
		// Parse format like "06:00-21:00" or "06:00 - 21:00"
		parts := strings.Split(dockHoursStr, "-")
		if len(parts) == 2 {
			openTime = strings.TrimSpace(parts[0])
			closeTime = strings.TrimSpace(parts[1])
		}
	}

	fullAddress := models.ConcatAddress(companyCoreInfo)
	location := models.TMSLocation{
		TMSIntegrationID: tmsIntegrationID,
		CompanyCoreInfo:  companyCoreInfo,
		NameAddress:      models.ConcatNameAddress(companyCoreInfo.Name, fullAddress),
		FullAddress:      fullAddress,
		Latitude:         item.Address.Latitude,
		Longitude:        item.Address.Longitude,
		Point: models.Point{
			Latitude:  float32(item.Address.Latitude),
			Longitude: float32(item.Address.Longitude),
		},
		Notes:     notes,
		OpenTime:  openTime,
		CloseTime: closeTime,
	}

	return location
}

// GlobalTranzLocationCreateOptions contains TMS-specific options for creating a location
type GlobalTranzLocationCreateOptions struct {
	CustomerBK  string  `json:"customerBK,omitempty"`  // Required: specific customer to use
	AddressType string  `json:"addressType,omitempty"` // "origin" or "destination"
	Latitude    float64 `json:"latitude,omitempty"`    // Latitude from location search
	Longitude   float64 `json:"longitude,omitempty"`   // Longitude from location search
}

// SaveAddressRequest represents the request payload for SaveAddressForCustomer API
type SaveAddressRequest struct {
	AddressBK       int64              `json:"addressBK"` // 0 for new address
	CompanyName     string             `json:"companyName"`
	Contact         AddressBookContact `json:"contact"`
	Address         AddressBookAddress `json:"address"`
	DockHours       *AddressDockHours  `json:"dockHours,omitempty"`
	PickUpRemarks   *string            `json:"pickUpRemarks"`
	DeliveryRemarks *string            `json:"deliveryRemarks"`
	LocationNotes   *string            `json:"locationNotes"`
	Accessorials    []any              `json:"accessorials"`
	Remarks         []any              `json:"remarks"`
	DefaultGroupID  int                `json:"defaultGroupId"`
}

// AddressDockHours represents dock hours for address creation
type AddressDockHours struct {
	From string `json:"from"` // Format: "HH:MM" or "HH:MM:SS"
	To   string `json:"to"`   // Format: "HH:MM" or "HH:MM:SS"
}

// GetAddressDetailsResponse represents the response from GetAddressDetailsByAddressBK API
type GetAddressDetailsResponse struct {
	Message          *string            `json:"message"`
	DidError         bool               `json:"didError"`
	ErrorMessages    []string           `json:"errorMessages"`
	ObjectWasCreated bool               `json:"objectWasCreated"`
	Model            AddressDetailsItem `json:"model"`
}

// AddressDetailsItem represents a single address with full details
type AddressDetailsItem struct {
	AddressBK         int64              `json:"addressBK"`
	CompanyName       string             `json:"companyName"`
	Contact           AddressBookContact `json:"contact"`
	Address           AddressBookAddress `json:"address"`
	DockHours         AddressDockHours   `json:"dockHours"`
	PickUpDockHours   AddressDockHours   `json:"pickUpDockHours"`
	DeliveryDockHours *AddressDockHours  `json:"deliveryDockHours"`
	PickUpRemarks     *string            `json:"pickUpRemarks"`
	DeliveryRemarks   *string            `json:"deliveryRemarks"`
	LocationNotes     *string            `json:"locationNotes"`
	Accessorials      []any              `json:"accessorials"`
	DefaultGroupID    int                `json:"defaultGroupId"`
	Remarks           []any              `json:"remarks"`
}

// SaveAddressResponse represents the response from SaveAddressForCustomer API
type SaveAddressResponse struct {
	Message          *string  `json:"message"`
	DidError         bool     `json:"didError"`
	ErrorMessages    []string `json:"errorMessages"`
	ObjectWasCreated bool     `json:"objectWasCreated"`
	Model            bool     `json:"model"`
}

func (gt GlobalTranz) CreateLocation(
	ctx context.Context,
	req models.CreateLocationRequest,
	_ *models.TMSUser,
) (models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLocationGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	loc := req.Location

	// Validate required fields according to GlobalTranz requirements:
	// Required: Contact Name, Contact Phone, Address Line 1, City/State/Zip
	// Not Required: Company Name, Address Line 2, Contact Email, Country, Open/Close Time
	if strings.TrimSpace(loc.Contact) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("contact name is required"))
	}

	if strings.TrimSpace(loc.Phone) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("contact phone is required"))
	}

	if strings.TrimSpace(loc.AddressLine1) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("address line 1 is required"))
	}

	if strings.TrimSpace(loc.City) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("city is required"))
	}

	if strings.TrimSpace(loc.State) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("state is required"))
	}

	if strings.TrimSpace(loc.Zipcode) == "" {
		return models.TMSLocation{}, errtypes.NewUserFacingError(errors.New("zipcode is required"))
	}

	// Parse options
	var opts *GlobalTranzLocationCreateOptions
	if len(req.Options) > 0 {
		var decoded GlobalTranzLocationCreateOptions
		if err := json.Unmarshal(req.Options, &decoded); err != nil {
			return models.TMSLocation{}, errtypes.WrapNewUserFacingError(
				"invalid GlobalTranz options payload",
				err,
				func(err error) string { return err.Error() },
			)
		}
		opts = &decoded
	}

	// Get customerBK from options - required
	var customerBK string
	if opts != nil && opts.CustomerBK != "" {
		customerBK = opts.CustomerBK
	} else {
		return models.TMSLocation{}, errtypes.NewUserFacingError(
			errors.New("customer is required to create a location. Please select a customer"),
		)
	}

	// Determine addressType based on IsShipper/IsConsignee or from options
	// Note: opts is guaranteed to be non-nil here due to customerBK validation above
	addressType := "origin"
	if opts.AddressType != "" {
		addressType = opts.AddressType
	} else if loc.IsConsignee && !loc.IsShipper {
		addressType = "destination"
	}

	// Get latitude/longitude from options (from location search) or use defaults
	latitude := 0.0
	longitude := 0.0
	hasCoordinates := opts.Latitude != 0 || opts.Longitude != 0
	if hasCoordinates {
		latitude = opts.Latitude
		longitude = opts.Longitude
	}

	// Convert country string to country code
	var countryCode int
	countryUpper := strings.ToUpper(loc.Country)
	switch countryUpper {
	case "US", "USA", "UNITED STATES":
		countryCode = 1
	case "CA", "CAN", "CANADA":
		countryCode = 2
	case "CN", "CHN", "CHINA":
		countryCode = 3
	case "MX", "MEX", "MEXICO":
		countryCode = 4
	default:
		log.Warn(
			ctx,
			"unknown country code, defaulting to USA",
			zap.String("country", loc.Country),
		)
		countryCode = 1 // Default to USA
	}

	// Build dock hours from OpenTime/CloseTime if available
	var dockHours *AddressDockHours
	if loc.OpenTime != "" && loc.CloseTime != "" {
		dockHours = &AddressDockHours{
			From: loc.OpenTime,
			To:   loc.CloseTime,
		}
	} else {
		// Default dock hours
		dockHours = &AddressDockHours{
			From: "06:00",
			To:   "21:00",
		}
	}

	// Build contact info
	contactName := loc.Contact
	if contactName == "" {
		contactName = loc.Name // Fallback to company name
	}

	// Format phone number to match GlobalTranz API format: (XXX)XXX-XXXX (no space after area code)
	contactPhone := loc.Phone
	// Remove all non-digits, then format as (XXX)XXX-XXXX
	phoneDigits := strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(contactPhone, "(", ""), ")", ""), "-", "")
	phoneDigits = strings.ReplaceAll(phoneDigits, " ", "")
	phoneDigits = strings.TrimSpace(phoneDigits)

	// Handle phone numbers with US/Canada country code
	if len(phoneDigits) == 11 && phoneDigits[0] == '1' {
		phoneDigits = phoneDigits[1:]
	}

	// Validate phone number has exactly 10 digits (after country code removal)
	if len(phoneDigits) != 10 {
		return models.TMSLocation{}, errtypes.NewUserFacingError(
			errors.New("phone number must be exactly 10 digits"),
		)
	}

	// Format as (XXX)XXX-XXXX
	contactPhone = fmt.Sprintf("(%s)%s-%s", phoneDigits[:3], phoneDigits[3:6], phoneDigits[6:10])

	// Email should be empty string when not provided, not null (matches GlobalTranz API expectation)
	contactEmail := loc.Email
	contactEmailPtr := &contactEmail // Always send email (empty string if not provided)

	// Build save address request
	saveReq := SaveAddressRequest{
		AddressBK:   0, // 0 for new address
		CompanyName: loc.Name,
		Contact: AddressBookContact{
			Name:  contactName,
			Phone: contactPhone,    // Formatted as (XXX)XXX-XXXX (no space after area code)
			Email: contactEmailPtr, // Always send email (empty string if not provided)
			Fax:   nil,             // Fax is null when not provided
		},
		Address: AddressBookAddress{
			Street1:   loc.AddressLine1,
			Street2:   loc.AddressLine2, // Empty string when not provided
			City:      loc.City,
			State:     loc.State,
			Zip:       loc.Zipcode,
			Country:   countryCode,
			Latitude:  latitude,
			Longitude: longitude,
		},
		DockHours:       dockHours,
		PickUpRemarks:   nil,
		DeliveryRemarks: nil,
		LocationNotes:   nil,
		Accessorials:    []any{},
		Remarks:         []any{},
		DefaultGroupID:  0,
	}

	// Add location notes if available
	if loc.Notes != "" {
		saveReq.LocationNotes = &loc.Notes
	}

	// Build request body
	reqBody, err := json.Marshal(saveReq)
	if err != nil {
		log.Error(
			ctx,
			"failed to marshal save address request",
			zap.Error(err),
		)
		return models.TMSLocation{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Build query parameters
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)
	queryParams.Set("addressType", addressType)

	// Call SaveAddressForCustomer API
	var response SaveAddressResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodPost,
		gt.tmsHost,
		SaveAddressPath,
		queryParams,
		bytes.NewBuffer(reqBody),
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("customerBK", customerBK),
			zap.String("addressType", addressType),
			zap.Error(err),
		)
		return models.TMSLocation{}, errtypes.WrapNewUserFacingError(
			"error creating location in GlobalTranz",
			err,
			cleanLocationErrorMessage,
		)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = strings.Join(response.ErrorMessages, "; ")
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return models.TMSLocation{}, errtypes.NewUserFacingError(
			fmt.Errorf("failed to create location: %s", errorMsg),
		)
	}

	if !response.ObjectWasCreated {
		log.Error(
			ctx,
			"location was not created",
			zap.String("customerBK", customerBK),
		)
		return models.TMSLocation{}, errtypes.NewUserFacingError(
			errors.New("location was not created in GlobalTranz"),
		)
	}

	// Fetch the newly created location to get the AddressBK
	// Use SearchAddresses API instead of GetAddressesByCustomerBK to avoid limit issues
	// Retry a few times as GlobalTranz might need a moment to index the new location
	var fetchedLoc *models.TMSLocation
	maxRetries := 3
	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// Wait a bit before retrying (exponential backoff: 500ms, 1s, 2s)
			time.Sleep(time.Duration(attempt) * 500 * time.Millisecond)
		}

		// Use SearchAddresses API with company name to find the newly created location
		// This avoids limit issues with GetAddressesByCustomerBK
		locations, err := gt.searchLocationsByCustomer(ctx, customerBK, loc.Name)
		if err != nil {
			log.Warn(
				ctx,
				"failed to search for newly created location",
				zap.Int("attempt", attempt+1),
				zap.Int("maxRetries", maxRetries),
				zap.String("companyName", loc.Name),
				zap.Error(err),
			)
			if attempt == maxRetries-1 {
				// Last attempt failed, will return error
				break
			}
			continue
		}

		// Find the location we just created by matching address details
		// Match on: AddressLine1, City, State, Zipcode (required)
		// Optionally match on Name for better accuracy
		for i := range locations {
			locCandidate := &locations[i]

			// Required matches: AddressLine1, City, State, Zipcode
			if !strings.EqualFold(strings.TrimSpace(locCandidate.AddressLine1), strings.TrimSpace(loc.AddressLine1)) ||
				!strings.EqualFold(strings.TrimSpace(locCandidate.City), strings.TrimSpace(loc.City)) ||
				!strings.EqualFold(strings.TrimSpace(locCandidate.State), strings.TrimSpace(loc.State)) ||
				!strings.EqualFold(strings.TrimSpace(locCandidate.Zipcode), strings.TrimSpace(loc.Zipcode)) {
				continue
			}

			// Optional match on name if provided (but don't require it)
			if loc.Name != "" && locCandidate.Name != "" {
				if !strings.EqualFold(strings.TrimSpace(locCandidate.Name), strings.TrimSpace(loc.Name)) {
					// Name doesn't match, but continue checking other locations
					// as the name might have been modified by GlobalTranz
					continue
				}
			}

			// Found a matching location (by address) - now get full details including dock hours
			addressBK := locCandidate.ExternalTMSID
			if addressBK != "" {
				// Fetch full location details using GetAddressDetailsByAddressBK
				detailsLoc, err := gt.getAddressDetailsByBK(ctx, customerBK, addressBK)
				if err != nil {
					log.Warn(
						ctx,
						"failed to fetch full location details, using search result",
						zap.String("addressBK", addressBK),
						zap.Error(err),
					)
					// Fallback to search result if details fetch fails
					// Preserve latitude/longitude from original request if search result doesn't have them
					locCandidate.TMSIntegrationID = gt.tms.ID
					if (locCandidate.Latitude == 0 && locCandidate.Longitude == 0) && hasCoordinates {
						locCandidate.Latitude = latitude
						locCandidate.Longitude = longitude
						locCandidate.Point = models.Point{
							Latitude:  float32(latitude),
							Longitude: float32(longitude),
						}
					}
					// Preserve open/close time from original request if available
					if loc.OpenTime != "" && loc.CloseTime != "" {
						locCandidate.OpenTime = loc.OpenTime
						locCandidate.CloseTime = loc.CloseTime
					}
					fetchedLoc = locCandidate
				} else {
					// Use full details from GetAddressDetailsByAddressBK
					// Preserve timezone from original request if available
					if loc.Timezone != "" {
						detailsLoc.Timezone = loc.Timezone
					}
					// Preserve latitude/longitude from original request if details don't have them
					// Note: GlobalTranz may return 0,0 for coordinates even though we sent them correctly.
					// This appears to be a GlobalTranz limitation where coordinates aren't immediately
					// persisted or returned after creation. We preserve the original coordinates in our DB.
					if (detailsLoc.Latitude == 0 && detailsLoc.Longitude == 0) && hasCoordinates {
						detailsLoc.Latitude = latitude
						detailsLoc.Longitude = longitude
						detailsLoc.Point = models.Point{
							Latitude:  float32(latitude),
							Longitude: float32(longitude),
						}
					}
					// Preserve open/close time from original request if available
					if loc.OpenTime != "" && loc.CloseTime != "" {
						detailsLoc.OpenTime = loc.OpenTime
						detailsLoc.CloseTime = loc.CloseTime
					}
					fetchedLoc = &detailsLoc
				}
			} else {
				// No addressBK found, use search result
				// Preserve latitude/longitude from original request if search result doesn't have them
				locCandidate.TMSIntegrationID = gt.tms.ID
				if (locCandidate.Latitude == 0 && locCandidate.Longitude == 0) && hasCoordinates {
					locCandidate.Latitude = latitude
					locCandidate.Longitude = longitude
					locCandidate.Point = models.Point{
						Latitude:  float32(latitude),
						Longitude: float32(longitude),
					}
				}
				// Preserve open/close time from original request if available
				if loc.OpenTime != "" && loc.CloseTime != "" {
					locCandidate.OpenTime = loc.OpenTime
					locCandidate.CloseTime = loc.CloseTime
				}
				fetchedLoc = locCandidate
			}
			break
		}

		if fetchedLoc != nil {
			break
		}

		log.Debug(
			ctx,
			"newly created location not found in search results, retrying",
			zap.Int("attempt", attempt+1),
			zap.Int("maxRetries", maxRetries),
			zap.Int("locationsFound", len(locations)),
			zap.String("companyName", loc.Name),
		)
	}

	if fetchedLoc != nil {
		return *fetchedLoc, nil
	}

	// If we couldn't find the location after all retries, this is an error
	// We should not return a location without externalTMSID as it won't work properly
	log.Error(
		ctx,
		"failed to fetch newly created location after all retries",
		zap.String("customerBK", customerBK),
		zap.String("addressType", addressType),
		zap.String("locationName", loc.Name),
		zap.String("addressLine1", loc.AddressLine1),
		zap.String("city", loc.City),
		zap.String("state", loc.State),
		zap.String("zipcode", loc.Zipcode),
		zap.Int("maxRetries", maxRetries),
	)
	return models.TMSLocation{}, errtypes.NewUserFacingError(
		errors.New(
			"location was created in GlobalTranz but could not be retrieved. " +
				"Please refresh locations to see it in the dropdown",
		),
	)
}

// SearchLocation searches for locations using GlobalTranz SearchLocation API
func (gt GlobalTranz) SearchLocation(
	ctx context.Context,
	keyword string,
) ([]LocationSearchItem, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "SearchLocationGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	if strings.TrimSpace(keyword) == "" {
		return nil, errtypes.NewUserFacingError(errors.New("search keyword is required"))
	}

	queryParams := url.Values{}
	queryParams.Set("keyWord", keyword)
	queryParams.Set("zipCodes", "true")
	queryParams.Set("countryCode", "1") // USA

	var response LocationSearchResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		SearchLocationPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("keyword", keyword),
			zap.Error(err),
		)
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if len(response.ErrorMessages) > 0 {
			errorMsg = response.ErrorMessages[0]
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("keyword", keyword),
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	return response.Model, nil
}
