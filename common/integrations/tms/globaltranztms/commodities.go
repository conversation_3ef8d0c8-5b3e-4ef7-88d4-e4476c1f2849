package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmsCommodityDB "github.com/drumkitai/drumkit/common/rds/tms_commodity"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

const (
	ListProductPath    = "api/tms-product/Customer/%s/Product/List"
	ProductDetailsPath = "api/tms-product/Customer/%s/Product/%s"
	AddProductPath     = "api/tms-product/Customer/%s/Product"
)

// extractErrorMessages extracts error messages from the ErrorMessages field
// which can be []string, []any, or nil when JSON unmarshaled
func extractErrorMessages(errorMessages any) []string {
	if errorMessages == nil {
		return nil
	}

	// Try []string first (direct case)
	if strSlice, ok := errorMessages.([]string); ok {
		return strSlice
	}

	if ifaceSlice, ok := errorMessages.([]any); ok {
		messages := make([]string, 0, len(ifaceSlice))
		for _, item := range ifaceSlice {
			if str, ok := item.(string); ok {
				messages = append(messages, str)
			}
		}
		if len(messages) > 0 {
			return messages
		}
	}

	return nil
}

// ProductListResponse represents the response from List Product API
type ProductListResponse struct {
	Message       any           `json:"message"`
	DidError      bool          `json:"didError"`
	ErrorMessages any           `json:"errorMessages"`
	Model         []ProductItem `json:"model"`
}

// ProductItem represents a product from List Product API (basic details)
type ProductItem struct {
	ID             int        `json:"id"`
	Commodity      string     `json:"commodity"`
	FreightClassID int        `json:"freightClassId"`
	NMFC           string     `json:"nmfc"`
	Dim            ProductDim `json:"dim"`
}

// ProductDim represents dimensions from API
type ProductDim struct {
	Length *float64 `json:"length"`
	Width  *float64 `json:"width"`
	Height *float64 `json:"height"`
	Unit   *string  `json:"unit"`
	UnitID *int     `json:"unitId"`
}

// ProductWeight represents weight from API
type ProductWeight struct {
	Amount *float64 `json:"amount"` // API sends as number
	Unit   *string  `json:"unit"`
	UnitID *int     `json:"unitId"`
}

// ProductHazmat represents hazmat information from API
type ProductHazmat struct {
	Flag                   bool    `json:"flag"`
	Class                  *string `json:"class"`
	ClassID                int     `json:"classId"`
	Group                  *string `json:"group"`
	GroupID                *int    `json:"groupId"`
	Code                   *string `json:"code"`
	HazmatPrefixID         int     `json:"hazmatPrefixId"`
	ChemicalName           *string `json:"chemicalName"`
	EmergencyContactNumber *string `json:"emergencyContactNumber"`
}

// ProductDetailsResponse represents the response from Product Details API
type ProductDetailsResponse struct {
	Message          any            `json:"message"`
	DidError         bool           `json:"didError"`
	ErrorMessages    any            `json:"errorMessages"`
	ObjectWasCreated bool           `json:"objectWasCreated"`
	Model            ProductDetails `json:"model"`
}

// ProductDetails represents full product details from Product Details API
type ProductDetails struct {
	ID                      int           `json:"id"`
	ProductDefaultDetailsID int           `json:"productDefaultDetailsId"`
	CommodityDescription    string        `json:"commodityDescription"`
	Commodity               string        `json:"commodity"`
	Weight                  ProductWeight `json:"weight"`
	HandlingUnitTypeID      *int          `json:"handlingUnitTypeId"`
	HandlingUnitCount       *int          `json:"handlingUnitCount"`
	NMFCNumber              string        `json:"nmfcNumber"`
	Hazmat                  ProductHazmat `json:"hazmat"`
	Stackable               bool          `json:"stackable"`
	Dim                     ProductDim    `json:"dim"`
	FreightClassID          int           `json:"freightClassId"`
	PieceCount              int           `json:"pieceCount"`
	CarrierType             int           `json:"carrierType"`
}

// AddProductRequest represents the request payload for Add Product API
type AddProductRequest struct {
	ID                      int            `json:"id"` // 0 for new product
	CarrierType             int            `json:"carrierType"`
	CommodityDescription    string         `json:"commodityDescription"`
	Commodity               *string        `json:"commodity"`
	Weight                  ProductWeight  `json:"weight"`
	PieceCount              string         `json:"pieceCount"` // API expects string
	HandlingUnitTypeID      int            `json:"handlingUnitTypeId"`
	HandlingUnitCount       string         `json:"handlingUnitCount"` // API expects string
	NMFCNumber              string         `json:"nmfcNumber"`
	FreightClassID          *int           `json:"freightClassId"`
	Hazmat                  *ProductHazmat `json:"hazmat,omitempty"` // Optional hazmat information
	Stackable               bool           `json:"stackable"`
	Dim                     ProductDim     `json:"dim"`
	ProductDefaultDetailsID int            `json:"productDefaultDetailsId"`
	Density                 float64        `json:"density"`
}

// AddProductResponse represents the response from Add Product API
type AddProductResponse struct {
	Message          any            `json:"message"`
	DidError         bool           `json:"didError"`
	ErrorMessages    any            `json:"errorMessages"`
	ObjectWasCreated bool           `json:"objectWasCreated"`
	Model            ProductDetails `json:"model"`
}

// GetCommodities fetches all commodities from GlobalTranz TMS and saves them to the database
func (gt GlobalTranz) GetCommodities(ctx context.Context, opts ...models.TMSOption) ([]models.TMSCommodity, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCommoditiesGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	// Parse options
	options := &models.TMSOptions{}
	options.Apply(opts...)

	log.Info(ctx, "fetching commodities from GlobalTranz TMS", zap.Any("customerID", options.CustomerID))

	var customers []models.TMSCustomer

	// If customerID is provided, only fetch for that specific customer
	if options.CustomerID != nil {
		var customer models.TMSCustomer
		customer, err = tmsCustomerDB.GetCustomerByID(ctx, *options.CustomerID)
		if err != nil {
			log.Error(
				ctx,
				"failed to get TMS customer by ID",
				zap.Uint("customerID", *options.CustomerID),
				zap.Error(err),
			)
			return nil, fmt.Errorf("failed to get TMS customer by ID: %w", err)
		}
		customers = []models.TMSCustomer{customer}
		log.Info(
			ctx,
			"fetching commodities for specific customer",
			zap.String("customerName", customer.Name),
			zap.Uint("customerID", customer.ID),
		)
	} else {
		// Fetch for all customers (only when customerID is not provided)
		customers, err = tmsCustomerDB.GetTMSCustomersByTMSID(ctx, rds.GenericGetQuery{
			TMSID: gt.tms.ID,
		})
		if err != nil {
			log.Error(
				ctx,
				"failed to get TMS customers",
				zap.Error(err),
			)
			return nil, fmt.Errorf("failed to get TMS customers: %w", err)
		}
		log.Info(
			ctx,
			"fetching commodities for all GlobalTranz customers",
			zap.Int("customerCount", len(customers)),
		)
	}

	var allCommodities []models.TMSCommodity
	successfulBatches := 0
	failedBatches := 0

	for _, customer := range customers {
		if customer.ExternalTMSID == "" {
			log.WarnNoSentry(
				ctx,
				"customer has no external TMS ID, skipping",
				zap.Uint("customerId", customer.ID),
				zap.String("customerName", customer.Name),
			)
			continue
		}

		commodities, fetchErr := gt.getCommoditiesByCustomer(ctx, customer.ExternalTMSID, customer.ID)
		if fetchErr != nil {
			log.Error(
				ctx,
				"failed to fetch commodities for customer",
				zap.String("customerBK", customer.ExternalTMSID),
				zap.String("customerName", customer.Name),
				zap.Error(fetchErr),
			)
			failedBatches++
			continue
		}

		log.Info(
			ctx,
			"fetched commodities for customer",
			zap.String("customerName", customer.Name),
			zap.Int("commodityCount", len(commodities)),
		)

		if len(commodities) > 0 {
			if saveErr := tmsCommodityDB.RefreshTMSCommodities(ctx, &commodities); saveErr != nil {
				log.Error(
					ctx,
					"failed to save commodities for customer",
					zap.String("customerBK", customer.ExternalTMSID),
					zap.String("customerName", customer.Name),
					zap.Error(saveErr),
				)
				failedBatches++
				continue
			}

			log.Info(
				ctx,
				"saved commodities for customer",
				zap.String("customerName", customer.Name),
				zap.Int("commodityCount", len(commodities)),
			)
		}

		allCommodities = append(allCommodities, commodities...)
		successfulBatches++
	}

	// If 0 batches succeeded, mark the span as failed
	if successfulBatches == 0 && failedBatches > 0 {
		err = fmt.Errorf("all %d customer batches failed to fetch or save commodities", failedBatches)
		log.Error(
			ctx,
			"all customer batches failed",
			zap.Int("failedBatches", failedBatches),
			zap.Error(err),
		)
		return allCommodities, err
	}

	return allCommodities, nil
}

func (gt *GlobalTranz) getCommoditiesByCustomer(
	ctx context.Context,
	customerBK string,
	customerID uint,
) ([]models.TMSCommodity, error) {
	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)
	queryParams.Set("pageSize", "300") // Use pageSize from API example

	path := fmt.Sprintf(ListProductPath, customerBK)

	var response ProductListResponse
	err := gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		path,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		return nil, fmt.Errorf("API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if errorMessages := extractErrorMessages(response.ErrorMessages); len(errorMessages) > 0 {
			errorMsg = errorMessages[0]
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return nil, fmt.Errorf("API returned error: %s", errorMsg)
	}

	// Handle 204 No Content (empty list) - response body will be empty, so response.Model will be nil
	// This is a valid success case meaning the customer has no commodities
	if response.Model == nil {
		log.Info(
			ctx,
			"customer has no commodities (204 No Content)",
			zap.String("customerBK", customerBK),
		)
		return []models.TMSCommodity{}, nil
	}

	commodities := make([]models.TMSCommodity, 0, len(response.Model))
	for _, item := range response.Model {
		commodity := gt.mapProductItemToTMSCommodity(item, customerID)
		commodities = append(commodities, commodity)
	}

	return commodities, nil
}

func (gt *GlobalTranz) mapProductItemToTMSCommodity(item ProductItem, customerID uint) models.TMSCommodity {
	externalTMSID := strconv.Itoa(item.ID)

	commodity := models.TMSCommodity{
		TMSIntegrationID:  gt.tms.ID,
		TMSCustomerID:     &customerID, // Store customer relationship
		ExternalTMSID:     externalTMSID,
		Commodity:         item.Commodity,
		FreightClassID:    &item.FreightClassID,
		NMFC:              item.NMFC,
		HasDetailsFetched: false, // Basic list doesn't have full details
	}

	// Map dimensions
	if item.Dim.Length != nil {
		commodity.Length = item.Dim.Length
	}
	if item.Dim.Width != nil {
		commodity.Width = item.Dim.Width
	}
	if item.Dim.Height != nil {
		commodity.Height = item.Dim.Height
	}
	if item.Dim.Unit != nil {
		commodity.DimUnit = item.Dim.Unit
	}
	if item.Dim.UnitID != nil {
		commodity.DimUnitID = item.Dim.UnitID
	}

	return commodity
}

// GetCommodityDetails fetches full product details for a specific commodity
func (gt GlobalTranz) GetCommodityDetails(
	ctx context.Context,
	commodityID string,
	customerID *uint,
) (models.TMSCommodity, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCommodityDetailsGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	// Get customer to retrieve customerBK (externalTMSID) for the API call
	var customer models.TMSCustomer
	var customerIDToUse *uint

	// Determine customerID to use for lookup
	if customerID != nil {
		customerIDToUse = customerID
	}

	// First, try to get from DB if details are already fetched
	// Pass customerID to ensure we get the correct customer-scoped commodity
	commodity, dbErr := tmsCommodityDB.GetTMSCommodityByExternalID(ctx, gt.tms.ID, commodityID, customerIDToUse)
	if dbErr == nil && commodity.HasDetailsFetched {
		log.Info(
			ctx,
			"returning commodity details from DB",
			zap.String("commodityID", commodityID),
			zap.Any("customerID", customerIDToUse),
		)
		return commodity, nil
	}

	// If customerID wasn't provided but we got a commodity from DB, use its customerID
	if customerIDToUse == nil && commodity.TMSCustomerID != nil {
		customerIDToUse = commodity.TMSCustomerID
	}

	switch {
	case customerIDToUse != nil:
		// Use determined customerID (from parameter or from DB commodity)
		// Continue below to fetch customer and make API call
	default:
		// Last resort: try to find customer by searching all customers
		// This is inefficient but necessary if customerID is not provided
		var customers []models.TMSCustomer
		customers, err = tmsCustomerDB.GetTMSCustomersByTMSID(ctx, rds.GenericGetQuery{
			TMSID: gt.tms.ID,
		})
		if err != nil {
			return models.TMSCommodity{}, fmt.Errorf("failed to get TMS customers: %w", err)
		}

		// Try each customer until we find the commodity
		for _, cust := range customers {
			if cust.ExternalTMSID == "" {
				continue
			}

			path := fmt.Sprintf(ProductDetailsPath, cust.ExternalTMSID, commodityID)
			var response ProductDetailsResponse
			apiErr := gt.doWithRetry(
				ctx,
				http.MethodGet,
				gt.tmsHost,
				path,
				nil,
				nil,
				&response,
			)

			if apiErr == nil && !response.DidError {
				// Validate product ID to prevent saving corrupted data from 204 No Content responses
				if response.Model.ID == 0 {
					continue // Skip this customer and try next
				}
				commodity := gt.mapProductDetailsToTMSCommodity(response.Model, cust.ID)
				commodity.HasDetailsFetched = true
				commodities := []models.TMSCommodity{commodity}
				if saveErr := tmsCommodityDB.RefreshTMSCommodities(ctx, &commodities); saveErr != nil {
					log.Error(ctx, "failed to save commodity details to DB", zap.Error(saveErr))
					err = fmt.Errorf("failed to save commodity to database: %w", saveErr)
					return models.TMSCommodity{}, err
				}
				return commodity, nil
			}
		}

		err = fmt.Errorf("commodity not found and no customerID provided: %s", commodityID)
		return models.TMSCommodity{}, err
	}

	// Fetch customer using the determined customerID
	// customerIDToUse is guaranteed to be non-nil here (we're in the case customerIDToUse != nil branch)
	customer, err = tmsCustomerDB.GetCustomerByID(ctx, *customerIDToUse)
	if err != nil {
		return models.TMSCommodity{}, fmt.Errorf("failed to get customer by ID: %w", err)
	}

	if customer.ExternalTMSID == "" {
		err = fmt.Errorf("customer has no externalTMSID: %d", customer.ID)
		return models.TMSCommodity{}, err
	}

	// Call API with customerBK and productID
	path := fmt.Sprintf(ProductDetailsPath, customer.ExternalTMSID, commodityID)
	var response ProductDetailsResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		path,
		nil,
		nil,
		&response,
	)

	if err != nil {
		return models.TMSCommodity{}, fmt.Errorf("API request failed: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if errorMessages := extractErrorMessages(response.ErrorMessages); len(errorMessages) > 0 {
			errorMsg = errorMessages[0]
		}
		err = fmt.Errorf("API returned error: %s", errorMsg)
		return models.TMSCommodity{}, err
	}

	// Validate product ID to prevent saving corrupted data from 204 No Content responses
	if response.Model.ID == 0 {
		err = errors.New(
			"commodity not found: invalid product ID (received 204 No Content or empty response)",
		)
		return models.TMSCommodity{}, err
	}

	// Map to TMSCommodity
	commodity = gt.mapProductDetailsToTMSCommodity(response.Model, customer.ID)
	commodity.HasDetailsFetched = true

	// Save to DB - return error if save fails to prevent data inconsistency
	commodities := []models.TMSCommodity{commodity}
	if saveErr := tmsCommodityDB.RefreshTMSCommodities(ctx, &commodities); saveErr != nil {
		log.Error(ctx, "failed to save commodity details to DB", zap.Error(saveErr))
		err = fmt.Errorf("failed to save commodity to database: %w", saveErr)
		return models.TMSCommodity{}, err
	}

	return commodity, nil
}

func (gt *GlobalTranz) mapProductDetailsToTMSCommodity(details ProductDetails, customerID uint) models.TMSCommodity {
	externalTMSID := strconv.Itoa(details.ID)

	commodity := models.TMSCommodity{
		TMSIntegrationID:             gt.tms.ID,
		TMSCustomerID:                &customerID, // Store customer relationship
		ExternalTMSID:                externalTMSID,
		Commodity:                    details.Commodity,
		CommodityDescription:         details.CommodityDescription,
		ProductDefaultDetailsID:      details.ProductDefaultDetailsID,
		FreightClassID:               &details.FreightClassID,
		NMFCNumber:                   details.NMFCNumber,
		HandlingUnitTypeID:           details.HandlingUnitTypeID,
		HandlingUnitCount:            details.HandlingUnitCount,
		PieceCount:                   details.PieceCount,
		Stackable:                    details.Stackable,
		CarrierType:                  details.CarrierType,
		HazardousMaterial:            details.Hazmat.Flag,
		HazmatClassID:                details.Hazmat.ClassID,
		HazmatPrefixID:               details.Hazmat.HazmatPrefixID,
		HazmatEmergencyContactNumber: details.Hazmat.EmergencyContactNumber,
		HasDetailsFetched:            true,
	}

	// Map weight
	if details.Weight.Amount != nil {
		commodity.WeightAmount = details.Weight.Amount
	}
	if details.Weight.Unit != nil {
		commodity.WeightUnit = details.Weight.Unit
	}
	if details.Weight.UnitID != nil {
		commodity.WeightUnitID = details.Weight.UnitID
	}

	// Map dimensions
	if details.Dim.Length != nil {
		commodity.Length = details.Dim.Length
	}
	if details.Dim.Width != nil {
		commodity.Width = details.Dim.Width
	}
	if details.Dim.Height != nil {
		commodity.Height = details.Dim.Height
	}
	if details.Dim.Unit != nil {
		commodity.DimUnit = details.Dim.Unit
	}
	if details.Dim.UnitID != nil {
		commodity.DimUnitID = details.Dim.UnitID
	}

	// Map hazmat fields
	if details.Hazmat.Class != nil {
		commodity.HazmatClass = details.Hazmat.Class
	}
	if details.Hazmat.Group != nil {
		commodity.HazmatGroup = details.Hazmat.Group
	}
	if details.Hazmat.GroupID != nil {
		commodity.HazmatGroupID = details.Hazmat.GroupID
	}
	if details.Hazmat.Code != nil {
		commodity.HazmatCode = details.Hazmat.Code
	}
	if details.Hazmat.ChemicalName != nil {
		commodity.HazmatChemicalName = details.Hazmat.ChemicalName
	}
	if details.Hazmat.EmergencyContactNumber != nil {
		commodity.HazmatEmergencyContactNumber = details.Hazmat.EmergencyContactNumber
	}

	return commodity
}

// GlobalTranzCommodityCreateOptions contains TMS-specific options for creating a commodity
type GlobalTranzCommodityCreateOptions struct {
	CustomerBK string `json:"customerBK,omitempty"` // Required: specific customer to use
}

// CreateCommodity creates a new commodity in GlobalTranz TMS
func (gt GlobalTranz) CreateCommodity(
	ctx context.Context,
	req models.CreateCommodityRequest,
	_ *models.TMSUser,
) (models.TMSCommodity, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateCommodityGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	commodity := req.Commodity

	// Validate required fields according to GlobalTranz requirements:
	// Required: CommodityDescription, WeightAmount, HandlingUnitCount
	if strings.TrimSpace(commodity.CommodityDescription) == "" {
		return models.TMSCommodity{}, errtypes.NewUserFacingError(errors.New("commodity description is required"))
	}

	if commodity.WeightAmount == nil || *commodity.WeightAmount <= 0 {
		return models.TMSCommodity{}, errtypes.NewUserFacingError(errors.New("weight per piece is required"))
	}

	if commodity.HandlingUnitCount == nil || *commodity.HandlingUnitCount <= 0 {
		return models.TMSCommodity{}, errtypes.NewUserFacingError(errors.New("pallet count is required"))
	}

	// Parse options
	var opts *GlobalTranzCommodityCreateOptions
	if len(req.Options) > 0 {
		var decoded GlobalTranzCommodityCreateOptions
		if unmarshalErr := json.Unmarshal(req.Options, &decoded); unmarshalErr != nil {
			err = errtypes.WrapNewUserFacingError(
				"invalid GlobalTranz options payload",
				unmarshalErr,
				func(err error) string { return err.Error() },
			)
			return models.TMSCommodity{}, err
		}
		opts = &decoded
	}

	// Get customerBK from options - required
	var customerBK string
	var customerID uint
	if opts != nil && opts.CustomerBK != "" {
		customerBK = opts.CustomerBK
		// Find the customer ID from the customerBK
		customers, custErr := tmsCustomerDB.GetTMSCustomersByTMSID(ctx, rds.GenericGetQuery{
			TMSID: gt.tms.ID,
		})
		if custErr != nil {
			return models.TMSCommodity{}, errtypes.NewUserFacingError(
				fmt.Errorf("failed to lookup customer: %w", custErr),
			)
		}
		for _, cust := range customers {
			if cust.ExternalTMSID == customerBK {
				customerID = cust.ID
				break
			}
		}
		if customerID == 0 {
			return models.TMSCommodity{}, errtypes.NewUserFacingError(
				fmt.Errorf("customer not found with ID: %s", customerBK),
			)
		}
	} else {
		return models.TMSCommodity{}, errtypes.NewUserFacingError(
			errors.New("customer is required to create a commodity. Please select a customer"),
		)
	}

	// Build weight object
	weightAmount := *commodity.WeightAmount
	weightUnitID := 1 // Default to lbs
	if commodity.WeightUnitID != nil {
		weightUnitID = *commodity.WeightUnitID
	}

	// Build dimensions
	dimLength := 0.0
	dimWidth := 0.0
	dimHeight := 0.0
	dimUnitID := 0 // Default to Inches
	if commodity.Length != nil {
		dimLength = *commodity.Length
	}
	if commodity.Width != nil {
		dimWidth = *commodity.Width
	}
	if commodity.Height != nil {
		dimHeight = *commodity.Height
	}
	if commodity.DimUnitID != nil {
		dimUnitID = *commodity.DimUnitID
	}

	// Use provided density if available, otherwise calculate it
	// Formula: density = weight_per_pallet_lb / volume_per_pallet_cuft
	// where weight_per_pallet_lb = wt_per_unit × 2.20462262 (if kg) or wt_per_unit (if lb)
	// and volume_per_pallet = (length/12) × (width/12) × (height/12) if inches, or L × W × H if feet
	density := 0.0
	if commodity.Density != nil && *commodity.Density > 0 {
		density = *commodity.Density
	} else if dimLength > 0 && dimWidth > 0 && dimHeight > 0 && commodity.WeightAmount != nil &&
		commodity.HandlingUnitCount != nil && *commodity.HandlingUnitCount > 0 {
		// Convert per-piece weight to total pallet weight in pounds (lb)
		// weight_per_pallet_lb = (wt_per_piece × handling_unit_count) × 2.20462262 (if kg) or
		// (wt_per_piece × handling_unit_count) (if lb)
		weightPerPieceLb := weightAmount
		if weightUnitID == 0 { // Kg to lbs: wt_per_piece × 2.20462262
			weightPerPieceLb = weightAmount * 2.20462262
		}
		// Multiply by handling unit count to get total pallet weight
		weightLb := weightPerPieceLb * float64(*commodity.HandlingUnitCount)

		// Convert dimensions to feet (ft)
		lengthFt := dimLength
		widthFt := dimWidth
		heightFt := dimHeight
		if dimUnitID == 0 { // Inches to feet: divide by 12
			lengthFt = dimLength / 12.0
			widthFt = dimWidth / 12.0
			heightFt = dimHeight / 12.0
		}
		// If unit is feet, dimensions are already in feet

		// Volume per pallet (cubic feet)
		volumePerPalletCuFt := lengthFt * widthFt * heightFt
		if volumePerPalletCuFt > 0 {
			// Density (lb / cu ft) = weight_per_pallet_lb / volume_per_pallet_cuft
			density = weightLb / volumePerPalletCuFt
		}
	}

	// Build hazmat object if hazmat fields are present
	// Note: HazmatClassID and HazmatPrefixID can be 0 (valid values like "Class not specified" and "UN")
	// We check for presence of pointer/string fields to determine if hazmat data exists
	var hazmat *ProductHazmat
	hasHazmatData := commodity.HazmatGroupID != nil ||
		commodity.HazmatCode != nil ||
		commodity.HazmatChemicalName != nil ||
		commodity.HazmatEmergencyContactNumber != nil

	if hasHazmatData {
		// Include all hazmat fields, even if ClassID or PrefixID are 0 (valid values)
		hazmat = &ProductHazmat{
			Flag:                   true,
			Class:                  commodity.HazmatClass,
			ClassID:                commodity.HazmatClassID, // Can be 0 (Class not specified)
			Group:                  commodity.HazmatGroup,
			GroupID:                commodity.HazmatGroupID,
			Code:                   commodity.HazmatCode,
			HazmatPrefixID:         commodity.HazmatPrefixID, // Can be 0 (UN)
			ChemicalName:           commodity.HazmatChemicalName,
			EmergencyContactNumber: commodity.HazmatEmergencyContactNumber,
		}
	}

	// Build add product request
	pieceCountStr := fmt.Sprintf("%d", commodity.PieceCount)
	handlingUnitCountStr := fmt.Sprintf("%d", *commodity.HandlingUnitCount)

	addReq := AddProductRequest{
		ID:                   0, // 0 for new product
		CarrierType:          commodity.CarrierType,
		CommodityDescription: commodity.CommodityDescription,
		Commodity:            nil,
		Weight: ProductWeight{
			Amount: &weightAmount,
			Unit:   commodity.WeightUnit,
			UnitID: &weightUnitID,
		},
		PieceCount:         pieceCountStr,
		HandlingUnitTypeID: 0, // Default
		HandlingUnitCount:  handlingUnitCountStr,
		NMFCNumber:         commodity.NMFCNumber,
		FreightClassID:     commodity.FreightClassID,
		Hazmat:             hazmat,
		Stackable:          commodity.Stackable,
		Dim: ProductDim{
			Length: &dimLength,
			Width:  &dimWidth,
			Height: &dimHeight,
			UnitID: &dimUnitID,
		},
		ProductDefaultDetailsID: 0,
		Density:                 density,
	}

	// Build request body
	reqBody, err := json.Marshal(addReq)
	if err != nil {
		log.Error(
			ctx,
			"failed to marshal add product request",
			zap.Error(err),
		)
		return models.TMSCommodity{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	log.Debug(
		ctx,
		"creating commodity in GlobalTranz",
		zap.String("customerBK", customerBK),
		zap.String("requestBody", string(reqBody)),
	)

	path := fmt.Sprintf(AddProductPath, customerBK)

	// Call Add Product API
	var response AddProductResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodPost,
		gt.tmsHost,
		path,
		nil,
		bytes.NewBuffer(reqBody),
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"API request failed",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		// Return user-friendly error message instead of technical details
		return models.TMSCommodity{}, errtypes.NewUserFacingError(
			errors.New("failed to create commodity: please check your inputs and try again"),
		)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if errorMessages := extractErrorMessages(response.ErrorMessages); len(errorMessages) > 0 {
			errorMsg = strings.Join(errorMessages, "; ")
		}
		log.Error(
			ctx,
			"API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return models.TMSCommodity{}, errtypes.NewUserFacingError(
			fmt.Errorf("failed to create commodity: %s", errorMsg),
		)
	}

	// Get the product ID from the response
	productID := strconv.Itoa(response.Model.ID)
	if productID == "0" || productID == "" {
		return models.TMSCommodity{}, errtypes.NewUserFacingError(
			errors.New("failed to create commodity: product ID not returned"),
		)
	}

	// Fetch full product details using GetCommodityDetails API
	// This ensures we have complete, accurate data and avoids duplicates from force refresh
	// NOTE: GetCommodityDetails already saves to DB, so we don't need to save again here
	detailedCommodity, fetchErr := gt.GetCommodityDetails(ctx, productID, &customerID)
	if fetchErr != nil {
		log.Warn(
			ctx,
			"failed to fetch commodity details after creation, using response data",
			zap.String("productID", productID),
			zap.Error(fetchErr),
		)
		// Fallback: use the response data if details fetch fails
		// This should rarely happen, but if it does, we still have the basic data from Add Product response
		err = nil
		createdCommodity := gt.mapProductDetailsToTMSCommodity(response.Model, customerID)
		createdCommodity.TMSIntegrationID = gt.tms.ID
		createdCommodity.HasDetailsFetched = true

		// Save to DB using upsert to avoid duplicates (only in fallback case)
		commodities := []models.TMSCommodity{createdCommodity}
		if saveErr := tmsCommodityDB.RefreshTMSCommodities(ctx, &commodities); saveErr != nil {
			log.Warn(
				ctx,
				"failed to save created commodity to DB",
				zap.Error(saveErr),
			)
		}

		return createdCommodity, nil
	}

	// GetCommodityDetails already saved to DB, so we just return it
	// No need to save again - that would create duplicates!
	return detailedCommodity, nil
}
