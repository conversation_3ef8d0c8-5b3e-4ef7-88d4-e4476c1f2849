package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

const (
	GetCustomerBoardPath                  = "api/tms-customer/Customer/Board"
	GetCustomerAccountSettingsAndInfoPath = "api/tms-customer/Customer/GetCustomerAccountSettingsAndInfo"
)

// GetCustomers fetches all customers from GlobalTranz TMS and saves them to the database
func (gt GlobalTranz) GetCustomers(ctx context.Context, _ ...models.TMSOption) ([]models.TMSCustomer, error) {
	var uniqueCustomers []models.TMSCustomer
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersGlobalTranzTMS", otel.IntegrationAttrs(gt.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "fetching customers from GlobalTranz TMS")

	allCustomers, err := gt.getCustomers(ctx)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch customer pages",
			zap.Error(err),
		)
		return nil, err
	}

	// Deduplicate customers by ExternalTMSID (CustomerBK in TMS)
	seenExternalCustomerIDs := make(map[int]bool)
	for _, item := range allCustomers {
		if _, exists := seenExternalCustomerIDs[item.CustomerBk]; !exists {
			seenExternalCustomerIDs[item.CustomerBk] = true

			customer := gt.mapCustomerBoardItemToTMSCustomer(item)
			uniqueCustomers = append(uniqueCustomers, customer)
		}
	}

	log.Info(
		ctx,
		"mapped customers to TMSCustomer model",
		zap.Int("totalFetched", len(allCustomers)),
		zap.Int("uniqueCustomers", len(uniqueCustomers)),
	)

	if len(uniqueCustomers) > 0 {
		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &uniqueCustomers); err != nil {
			log.Error(
				ctx,
				"failed to save customers to database",
				zap.Error(err),
			)
			return nil, err
		}

		log.Info(
			ctx,
			"successfully saved customers to database",
			zap.Int("count", len(uniqueCustomers)),
		)
	}

	return uniqueCustomers, nil
}

func (gt GlobalTranz) getCustomers(ctx context.Context) ([]CustomerBoardItem, error) {
	var allCustomers []CustomerBoardItem
	pageSize := 1000
	pageNumber := 1

	for {
		response, err := gt.getCustomerBoardPage(ctx, pageNumber, pageSize)
		if err != nil {
			log.Error(
				ctx,
				"failed to fetch customer board page",
				zap.Int("pageNumber", pageNumber),
				zap.Error(err),
			)
			return nil, err
		}

		allCustomers = append(allCustomers, response.Model...)

		log.Debug(
			ctx,
			"fetched customer board page",
			zap.Int("pageNumber", pageNumber),
			zap.Int("pageSize", pageSize),
			zap.Int("itemsInPage", len(response.Model)),
			zap.Int("totalItems", response.ItemsCount),
			zap.Int("totalPages", response.PageCount),
		)

		if pageNumber >= response.PageCount || len(response.Model) == 0 {
			break
		}

		pageNumber++
	}

	log.Info(
		ctx,
		"fetched all customer board pages",
		zap.Int("totalCustomers", len(allCustomers)),
		zap.Int("totalPages", pageNumber),
	)

	return allCustomers, nil
}

func (gt GlobalTranz) getCustomerBoardPage(
	ctx context.Context,
	pageNumber, pageSize int,
) (GetCustomerBoardResponse, error) {
	var result GetCustomerBoardResponse

	fromDate := time.Date(1970, 1, 1, 3, 0, 0, 0, time.UTC)
	toDate := time.Now()

	payload := GetCustomerBoardRequest{
		FromDate:                  fromDate.Format(time.RFC3339),
		IncludeInactive:           false,
		IncludeMasterAccountsOnly: true,
		IsTimeframeAll:            true,
		PageNumber:                pageNumber,
		PageSize:                  pageSize,
		SearchFilter:              []string{},
		SearchText:                "",
		SortModel:                 []string{},
		ToDate:                    toDate.Format(time.RFC3339),
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(
			ctx,
			"failed to marshal request body",
			zap.Error(err),
		)
		return GetCustomerBoardResponse{}, err
	}

	err = gt.doWithRetry(
		ctx,
		http.MethodPost,
		gt.tmsHost,
		GetCustomerBoardPath,
		nil,
		bytes.NewBuffer(reqBody),
		&result,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch customer board",
			zap.Error(err),
		)
		return GetCustomerBoardResponse{}, err
	}

	if result.DidError {
		log.Error(
			ctx,
			"globaltranztms customer board returned errors",
			zap.Any("errorMessages", result.ErrorMessages),
		)
		return GetCustomerBoardResponse{}, errors.New("globaltranztms customer board returned errors")
	}

	return result, nil
}

func (gt GlobalTranz) mapCustomerBoardItemToTMSCustomer(item CustomerBoardItem) models.TMSCustomer {
	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: strconv.Itoa(item.CustomerBk),
		Name:          item.CustomerName,
		Phone:         item.PhoneNumber,
		Email:         item.EmailAddress,
	}

	fullAddress := models.ConcatAddress(companyCoreInfo)

	return models.TMSCustomer{
		CompanyCoreInfo:  companyCoreInfo,
		TMSIntegrationID: gt.tms.ID,
		TMSIntegration:   gt.tms,
		OwnerName:        item.SalesRepName,
		NameAddress:      models.ConcatNameAddress(item.CustomerName, fullAddress),
	}
}

// GetCustomerAccountSettingsAndInfo fetches detailed customer information including contact name and address
func (gt GlobalTranz) GetCustomerAccountSettingsAndInfo(
	ctx context.Context,
	customerBK string,
) (CustomerAccountSettingsAndInfo, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomerAccountSettingsAndInfoGlobalTranz", otel.IntegrationAttrs(gt.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	queryParams.Set("customerBK", customerBK)

	var response GetCustomerAccountSettingsAndInfoResponse
	err = gt.doWithRetry(
		ctx,
		http.MethodGet,
		gt.tmsHost,
		GetCustomerAccountSettingsAndInfoPath,
		queryParams,
		nil,
		&response,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch customer account settings and info",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		return CustomerAccountSettingsAndInfo{},
			fmt.Errorf("failed to fetch customer account settings and info: %w", err)
	}

	if response.DidError {
		errorMsg := "unknown error"
		if response.ErrorMessages != nil {
			if messages := extractErrorMessages(response.ErrorMessages); len(messages) > 0 {
				errorMsg = messages[0]
			} else if msgStr, ok := response.ErrorMessages.(string); ok {
				errorMsg = msgStr
			}
		}
		log.Error(
			ctx,
			"GlobalTranz customer account settings API returned error",
			zap.String("customerBK", customerBK),
			zap.String("errorMessage", errorMsg),
		)
		return CustomerAccountSettingsAndInfo{}, fmt.Errorf("API returned error: %s", errorMsg)
	}

	return response.Model, nil
}

// UpdateCustomerWithDetails fetches customer details and updates the customer in the database
func (gt GlobalTranz) UpdateCustomerWithDetails(ctx context.Context, customerBK string) (models.TMSCustomer, error) {
	// Fetch customer details from API
	customerDetails, err := gt.GetCustomerAccountSettingsAndInfo(ctx, customerBK)
	if err != nil {
		return models.TMSCustomer{}, err
	}

	// Map to TMSCustomer
	customer := gt.mapCustomerDetailsToTMSCustomer(customerDetails)

	// Update customer in database
	customers := []models.TMSCustomer{customer}
	if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &customers); err != nil {
		log.Error(
			ctx,
			"failed to update customer in database",
			zap.String("customerBK", customerBK),
			zap.Error(err),
		)
		return models.TMSCustomer{}, fmt.Errorf("failed to update customer in database: %w", err)
	}

	log.Info(
		ctx,
		"successfully updated customer with details",
		zap.String("customerBK", customerBK),
		zap.String("contactName", customer.Contact),
	)

	return customer, nil
}

// mapCustomerDetailsToTMSCustomer maps CustomerAccountSettingsAndInfo to TMSCustomer
func (gt GlobalTranz) mapCustomerDetailsToTMSCustomer(details CustomerAccountSettingsAndInfo) models.TMSCustomer {
	// Use accountSettings for contact info (it has contactName, contactEmail, phone)
	// Use accountInformation.billingAddress for address
	accountInfo := details.AccountInformation
	accountSettings := details.AccountSettings
	billingAddress := accountInfo.BillingAddress

	// Convert country code to ISO 2-letter country code
	var country string
	switch billingAddress.Country {
	case 1:
		country = "US"
	case 2:
		country = "CA"
	case 3:
		country = "CN"
	case 4:
		country = "MX"
	default:
		country = ""
	}

	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: strconv.Itoa(accountInfo.CustomerBk),
		Name:          accountInfo.CompanyName,
		AddressLine1:  billingAddress.Street1,
		AddressLine2:  billingAddress.Street2,
		City:          billingAddress.City,
		State:         billingAddress.State,
		Zipcode:       billingAddress.Zip,
		Country:       country,
		Contact:       accountSettings.ContactName,  // Use contactName from accountSettings
		Phone:         accountSettings.Phone,        // Use phone from accountSettings
		Email:         accountSettings.ContactEmail, // Use contactEmail from accountSettings
	}

	fullAddress := models.ConcatAddress(companyCoreInfo)

	return models.TMSCustomer{
		CompanyCoreInfo:  companyCoreInfo,
		TMSIntegrationID: gt.tms.ID,
		TMSIntegration:   gt.tms,
		OwnerName:        accountSettings.AccountRep,
		NameAddress:      models.ConcatNameAddress(accountInfo.CompanyName, fullAddress),
	}
}
