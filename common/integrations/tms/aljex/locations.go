package aljex

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

var alphaNumericCharacters = []string{
	"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
	"N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
	"0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
}

// GetLocations fetches Aljex locations by submitting the same HTML form used in the UI,
// parsing the response, and converting rows to Drumkit TMS locations.
func (a *Aljex) GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsAljex", otel.IntegrationAttrs(a.tms))
	var spanErr error
	defer func() { metaSpan.End(spanErr) }()

	// Apply options
	options := &models.TMSOptions{}
	options.Apply(opts...)

	log.Info(
		ctx,
		"aljex.GetLocations",
		zap.Bool("isPollerJob", options.IsPollerJob),
		zap.String("cursor", options.Cursor),
	)

	// Resolve the queue of prefixes to process
	var queue []string
	if options.Cursor != "" {
		queue = strings.Split(options.Cursor, ",")
	} else {
		queue = make([]string, len(alphaNumericCharacters))
		copy(queue, alphaNumericCharacters)
	}

	// If we're starting with a saved cursor, report it immediately to the poller
	if options.Cursor != "" && options.OnProgress != nil {
		options.OnProgress("", options.Cursor)
	}

	locationsMap := make(map[string]models.TMSLocation)
	var processedLocations []models.TMSLocation

	// Process prefixes. We use a sequential loop for robust progress tracking and sub-sharding.
	for len(queue) > 0 {
		char := queue[0]

		// Save the current state in the options before the API call
		// This ensures that if we hit a timeout, the poller knows exactly which queue we were on.
		if options.OnProgress != nil {
			options.OnProgress("", strings.Join(queue, ","))
		}

		log.Info(
			ctx,
			"Fetching Aljex locations",
			zap.String("customerFilter", char),
			zap.Int("remainingInQueue", len(queue)),
		)

		locationRows, searchErr := a.fetchLocationRows(ctx, char)
		if searchErr != nil {
			if isSubShardError(searchErr) && len(char) < 2 {
				log.Warn(
					ctx,
					"high volume or timeout detected for prefix, sub-sharding",
					zap.String("prefix", char),
					zap.Error(searchErr),
				)
				subShards := generateSubShards(char)
				// Replace the current prefix with its sub-shards at the front of the queue
				queue = append(subShards, queue[1:]...)

				// Save immediate progress so poller knows we expanded the search
				if options.OnProgress != nil {
					options.OnProgress("", strings.Join(queue, ","))
				}
				continue
			}

			log.Error(
				ctx,
				"failed to fetch Aljex locations",
				zap.Error(searchErr),
				zap.String("customerFilter", char),
			)
			spanErr = searchErr
			return processedLocations, searchErr
		}

		if len(locationRows) > 0 {
			locations := locationRowsToTMSLocations(locationRows, a.tms.ID)

			// Deduplicate and collect
			allLocations := make([]models.TMSLocation, 0, len(locations))
			for _, location := range locations {
				key := a.locationMapKey(location)
				if _, exists := locationsMap[key]; !exists {
					locationsMap[key] = location
					allLocations = append(allLocations, location)
					processedLocations = append(processedLocations, location)
				}
			}

			// Batch upsert to DB in chunks of 500
			const batchSize = 500
			totalBatches := (len(allLocations) + batchSize - 1) / batchSize

			for i := 0; i < len(allLocations); i += batchSize {
				end := i + batchSize
				if end > len(allLocations) {
					end = len(allLocations)
				}

				batch := allLocations[i:end]
				batchNum := (i / batchSize) + 1

				log.Info(
					ctx,
					"upserting locations batch",
					zap.String("prefix", char),
					zap.Int("batchNumber", batchNum),
					zap.Int("totalBatches", totalBatches),
					zap.Int("batchSize", len(batch)),
				)

				if err := tmsLocationDB.RefreshTMSLocations(ctx, &batch); err != nil {
					log.Error(
						ctx,
						"failed to upsert location batch into database",
						zap.Error(err),
						zap.String("prefix", char),
						zap.Int("batchNumber", batchNum),
						zap.Int("batchSize", len(batch)),
						zap.Int("totalBatches", totalBatches),
					)
					spanErr = err
					return processedLocations, err
				}
			}

			log.Info(
				ctx,
				"successfully upserted all locations to database",
				zap.String("prefix", char),
				zap.Int("total_count", len(allLocations)),
			)
		}

		// Move to next prefix in queue
		queue = queue[1:]
		if options.OnProgress != nil {
			options.OnProgress("", strings.Join(queue, ","))
		}
	}

	log.Info(
		ctx,
		"aljex.GetLocations completed",
		zap.Int("totalLocations", len(processedLocations)),
	)

	return processedLocations, nil
}

func (a *Aljex) buildLocationFormData(location models.TMSLocation, opts *LocationCreateOptions) url.Values {
	formData := url.Values{}

	// Required authentication and routing fields
	formData.Set("qual", a.creds.Qual)
	formData.Set("prcnam", "addcust")
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)
	formData.Set("backcnt", "2")
	formData.Set("sys", "3a")
	formData.Set("type", "addcust more")
	formData.Set("addcar", "yes")
	formData.Set("relog", " ")
	formData.Set("goto", "")
	formData.Set("repno", "   ")
	formData.Set("custid", "")
	formData.Set("ctlrec", "")
	formData.Set("ctlval", "")

	// Map TMSLocation fields to Aljex form fields
	if location.Name != "" {
		formData.Set("fld2", location.Name)
	}

	if location.AddressLine1 != "" {
		formData.Set("fld3", location.AddressLine1)
	}
	if location.AddressLine2 != "" {
		formData.Set("fld4", location.AddressLine2)
	}

	if location.City != "" {
		formData.Set("fld7", location.City)
	}

	if location.State != "" {
		formData.Set("fld5", location.State)
	}

	if location.Zipcode != "" {
		// Aljex expects 5-digit zipcode in fld6 and extension in fld114
		zipcode := strings.TrimSpace(location.Zipcode)
		switch {
		case strings.Contains(zipcode, "-"):
			parts := strings.Split(zipcode, "-")
			switch {
			case len(parts) == 2:
				formData.Set("fld6", parts[0])
				formData.Set("fld114", parts[1])
			case len(parts[0]) >= 5:
				// Handle malformed hyphenated zipcodes by using first part
				formData.Set("fld6", parts[0][:5])
				if len(parts[0]) > 5 {
					formData.Set("fld114", parts[0][5:])
				}
			default:
				// Handle short malformed zipcodes
				formData.Set("fld6", zipcode)
			}
		case len(zipcode) > 5:
			// Handle non-hyphenated zipcodes longer than 5 digits
			formData.Set("fld6", zipcode[:5])
			formData.Set("fld114", zipcode[5:])
		default:
			// Handle standard 5-digit zipcodes
			formData.Set("fld6", zipcode)
		}
	}

	if location.Phone != "" {
		formData.Set("fld8", location.Phone)
	}

	if location.Email != "" {
		formData.Set("fld23", location.Email)
	}

	if location.Contact != "" {
		formData.Set("fld9", location.Contact)
	}

	customerType := location.LocationType
	if customerType == "" {
		customerType = "PU/CON"
	}
	formData.Set("fld113", customerType)

	status := "N"

	formData.Set("fld133", status)

	if location.Country != "" {
		formData.Set("fld20", location.Country)
	}

	formData.Set("fld112", "")

	if opts != nil {
		if opts.PrimaryContactName != "" && location.Contact == "" {
			formData.Set("fld9", opts.PrimaryContactName)
		}
		if opts.PrimaryContactTitle != "" {
			formData.Set("fld150", opts.PrimaryContactTitle)
		}
		if opts.PrimaryContactPhone != "" {
			formData.Set("fld180", opts.PrimaryContactPhone)
		}
		if opts.PrimaryContactExt != "" {
			formData.Set("fld183", opts.PrimaryContactExt)
		}
		if opts.PrimaryContactEmail != "" && location.Email == "" {
			formData.Set("fld23", opts.PrimaryContactEmail)
		}
		if opts.PrimaryContactFax != "" {
			formData.Set("fld174", opts.PrimaryContactFax)
		}
		if opts.PrimaryWorkHours != "" {
			formData.Set("fld168", opts.PrimaryWorkHours)
		}

		if opts.SecondaryContactName != "" {
			formData.Set("fld41", opts.SecondaryContactName)
		}
		if opts.SecondaryContactTitle != "" {
			formData.Set("fld151", opts.SecondaryContactTitle)
		}
		if opts.SecondaryContactPhone != "" {
			formData.Set("fld181", opts.SecondaryContactPhone)
		}
		if opts.SecondaryContactExt != "" {
			formData.Set("fld184", opts.SecondaryContactExt)
		}
		if opts.SecondaryContactEmail != "" {
			formData.Set("fld24", opts.SecondaryContactEmail)
		}
		if opts.SecondaryContactFax != "" {
			formData.Set("fld175", opts.SecondaryContactFax)
		}
		if opts.SecondaryWorkHours != "" {
			formData.Set("fld169", opts.SecondaryWorkHours)
		}

		if opts.Office != "" {
			formData.Set("fld104", opts.Office)
		}
		if opts.StoreNumber != "" {
			formData.Set("fld617", opts.StoreNumber)
		}
		if opts.LocationNumber != "" {
			formData.Set("fld118", opts.LocationNumber)
		}
		if opts.FreightDescription != "" {
			formData.Set("fld10", opts.FreightDescription)
		}
		if opts.Hours != "" {
			formData.Set("fld130", opts.Hours)
		}

		if len(opts.Comments) > 0 {
			for i, comment := range opts.Comments {
				if i >= 4 {
					break
				}
				fieldNames := []string{"fld12", "fld13", "fld14", "fld15"}
				formData.Set(fieldNames[i], comment)
			}
		}
		if opts.SalesRepName != "" {
			formData.Set("fld366", opts.SalesRepName)
		}
		if opts.SalesRepID != "" {
			formData.Set("fld111", opts.SalesRepID)
		}
		if opts.ServiceRep != "" {
			formData.Set("fld335", opts.ServiceRep)
		}
		if opts.AssignedDispatcher != "" {
			formData.Set("fld619", opts.AssignedDispatcher)
		}

		if opts.PortalLogin != "" {
			formData.Set("fld284", opts.PortalLogin)
		}
		if opts.PortalPassword != "" {
			formData.Set("fld282", opts.PortalPassword)
		}
		if opts.TrackingService != "" {
			formData.Set("fld560", opts.TrackingService)
		}
		if opts.TrackingAccount != "" {
			formData.Set("fld497", opts.TrackingAccount)
		}

		if opts.SpecialInstructions != "" {
			formData.Set("fld16", opts.SpecialInstructions)
		}

		if len(opts.AdditionalEmails) > 0 {
			for i, email := range opts.AdditionalEmails {
				if i >= 2 {
					break
				}
				fieldNames := []string{"fld536", "fld537"}
				formData.Set(fieldNames[i], email)
			}
		}
	}

	return formData
}

func (a *Aljex) responseToTMSLocation(location models.TMSLocation, accountNumber string) models.TMSLocation {
	createdLocation := models.TMSLocation{
		TMSIntegrationID: a.tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: accountNumber,
			Name:          location.Name,
			AddressLine1:  location.AddressLine1,
			AddressLine2:  location.AddressLine2,
			City:          location.City,
			State:         location.State,
			Zipcode:       location.Zipcode,
			Phone:         location.Phone,
			Email:         location.Email,
			Contact:       location.Contact,
			Country:       location.Country,
		},
		LocationType: location.LocationType,
		Notes:        location.Notes,
		Instructions: location.Instructions,
	}

	// Set FullAddress for the created location
	createdLocation.FullAddress = models.ConcatAddress(createdLocation.CompanyCoreInfo)

	return createdLocation
}

func (a *Aljex) CreateLocation(
	ctx context.Context,
	req models.CreateLocationRequest,
	_ *models.TMSUser,
) (models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLocationAljex", otel.IntegrationAttrs(a.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(
		ctx,
		"aljex.CreateLocation",
		zap.String("name", req.Location.Name),
	)

	var opts *LocationCreateOptions
	if len(req.Options) > 0 {
		var decoded LocationCreateOptions
		err = json.Unmarshal(req.Options, &decoded)
		if err != nil {
			log.Error(
				ctx,
				"invalid Aljex options payload",
				zap.Error(err),
			)
			return models.TMSLocation{}, err
		}
		opts = &decoded
	}

	formData := a.buildLocationFormData(req.Location, opts)

	// Submit the form
	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeCustomers)
	if err != nil {
		log.Error(
			ctx,
			"failed to create Aljex location",
			zap.Error(err),
		)
		return models.TMSLocation{}, err
	}

	// Parse the response to extract the created location ID
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		log.Error(
			ctx,
			"error parsing response HTML",
			zap.Error(err),
		)
		return models.TMSLocation{}, err
	}

	accountNumber := ""
	doc.Find("input[name='fld1']").Each(func(_ int, s *goquery.Selection) {
		if val, exists := s.Attr("value"); exists {
			accountNumber = strings.TrimSpace(val)
		}
	})

	// If not found in input, try to find it in the form or page text
	if accountNumber == "" {
		doc.Find("td").Each(func(_ int, s *goquery.Selection) {
			text := strings.TrimSpace(s.Text())
			if strings.Contains(text, "Account #") || strings.Contains(text, "Account:") {
				// Try to extract number from adjacent cells or text
				nextText := strings.TrimSpace(s.Next().Text())
				if nextText != "" && len(nextText) <= 10 {
					accountNumber = nextText
				}
			}
		})
	}

	createdLocation := a.responseToTMSLocation(req.Location, accountNumber)

	if accountNumber == "" {
		log.Warn(
			ctx,
			"Could not extract account number from Aljex response, location may have been created but ID unknown",
		)
	}

	log.Info(
		ctx,
		"Successfully created Aljex location",
		zap.String("accountNumber", accountNumber),
	)

	return createdLocation, nil
}

func (a *Aljex) fetchLocationRows(ctx context.Context, customerFilter string) ([]LocationRow, error) {
	body, err := a.postLocationSearch(ctx, customerFilter)
	if err != nil {
		return nil, err
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		log.Error(
			ctx,
			"error parsing Aljex locations HTML",
			zap.Error(err),
		)
		return nil, err
	}

	return parseAljexLocationRows(doc), nil
}

func (a *Aljex) postLocationSearch(ctx context.Context, customerFilter string) ([]byte, error) {
	formData := url.Values{}
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)
	formData.Set("qual", a.creds.Qual)
	formData.Set("sys", "3a")
	formData.Set("type", "lookup")
	formData.Set("prcnam", "vcust")
	formData.Set("status", "N")
	formData.Set("drillto", "P")
	formData.Set("customer", customerFilter)
	formData.Set("drill", "P")

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeLocations)
	if err != nil {
		log.Error(
			ctx,
			"failed to fetch Aljex locations for filter",
			zap.String("customerFilter", customerFilter),
			zap.Error(err),
		)
		return nil, err
	}

	return resp, nil
}

func parseAljexLocationRows(doc *goquery.Document) []LocationRow {
	var rows []LocationRow

	table := doc.Find("table.sortable")
	if table.Length() == 0 {
		return rows
	}

	table.Find("tbody tr.lo").Each(func(_ int, row *goquery.Selection) {
		cells := row.Find("td")
		if cells.Length() < 10 {
			return
		}

		locationRow := LocationRow{
			Account:  StringValue{Value: strings.TrimSpace(cells.Eq(0).Text())},
			Customer: StringValue{Value: strings.TrimSpace(cells.Eq(1).Text())},
			Status:   StringValue{Value: strings.TrimSpace(cells.Eq(2).Text())},
			Type:     StringValue{Value: strings.TrimSpace(cells.Eq(3).Text())},
			Address:  StringValue{Value: strings.TrimSpace(cells.Eq(4).Text())},
			City:     StringValue{Value: strings.TrimSpace(cells.Eq(5).Text())},
			State:    StringValue{Value: strings.TrimSpace(cells.Eq(6).Text())},
			Zip:      StringValue{Value: strings.TrimSpace(cells.Eq(7).Text())},
			Phone:    StringValue{Value: strings.TrimSpace(cells.Eq(8).Text())},
			Contact:  StringValue{Value: strings.TrimSpace(cells.Eq(9).Text())},
		}

		rows = append(rows, locationRow)
	})

	return rows
}

func locationRowsToTMSLocations(rows []LocationRow, tmsID uint) []models.TMSLocation {
	locations := make([]models.TMSLocation, 0, len(rows))

	for _, row := range rows {
		if location, ok := row.ToTMSLocation(tmsID); ok {
			locations = append(locations, location)
		}
	}

	return locations
}

func isSubShardError(err error) bool {
	if err == nil {
		return false
	}
	// Check for standard HTTP timeout/5xx status codes
	var httpErr *errtypes.HTTPResponseError
	if errors.As(err, &httpErr) {
		status := httpErr.StatusCode
		if status == 408 || status == 500 || status == 502 || status == 503 || status == 504 {
			return true
		}
	}
	// Check for context deadlines or connection timeouts
	errMsg := strings.ToLower(err.Error())
	if errors.Is(err, context.DeadlineExceeded) ||
		strings.Contains(errMsg, "timeout") ||
		strings.Contains(errMsg, "deadline exceeded") {
		return true
	}
	return false
}

func generateSubShards(prefix string) []string {
	shards := make([]string, 0, len(alphaNumericCharacters))
	for _, char := range alphaNumericCharacters {
		shards = append(shards, prefix+char)
	}
	return shards
}

func (r LocationRow) ToTMSLocation(tmsID uint) (models.TMSLocation, bool) {
	externalID := strings.TrimSpace(r.Account.Value)
	name := strings.TrimSpace(r.Customer.Value)

	if externalID == "" || name == "" {
		return models.TMSLocation{}, false
	}

	coreInfo := models.CompanyCoreInfo{
		ExternalTMSID: externalID,
		Name:          name,
		AddressLine1:  strings.TrimSpace(r.Address.Value),
		City:          strings.TrimSpace(r.City.Value),
		State:         strings.TrimSpace(r.State.Value),
		Zipcode:       strings.TrimSpace(r.Zip.Value),
		Contact:       strings.TrimSpace(r.Contact.Value),
		Phone:         strings.TrimSpace(r.Phone.Value),
	}

	location := models.TMSLocation{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo:  coreInfo,
		IsShipper:        true,
		IsConsignee:      true,
	}

	location.FullAddress = models.ConcatAddress(coreInfo)
	switch {
	case location.Name != "" && location.FullAddress != "":
		location.NameAddress = location.Name + ", " + location.FullAddress
	case location.Name != "":
		location.NameAddress = location.Name
	default:
		location.NameAddress = location.FullAddress
	}

	return location, true
}

// locationMapKey generates a unique key for deduplication using tms_integration_id:external_tms_id
// This matches the database unique constraint on (tms_integration_id, external_tms_id)
func (a *Aljex) locationMapKey(location models.TMSLocation) string {
	return strconv.FormatUint(uint64(location.TMSIntegrationID), 10) + ":" + location.ExternalTMSID
}
