<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Picks and Stops</title>

<script type="text/javascript" src="/common/import.min.js"></script>
<script type="text/javascript" src="/common/v2_loader.js"></script>

<!--
***********************************************
PAGE-SPECIFIC CSS GOES HERE
***********************************************
-->

<style type=text/css>
.title {  font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 14px; font-weight: bold; color: #333399}

.protected {background-color: #e8e8e8}

.insbutt {
	font-weight:900;
	font-size:11px;
	color: white;
	background-color: green;
}

.delbutt {
	font-weight:900;
	font-size:11px;
	color: white;
	background-color: #cb2828;
}
input[readonly] {
	background-color: #e0e0e0;
	}
</style>

<!--
***********************************************
PAGE-SPECIFIC JAVASCRIPT FUNCTIONS GO HERE
***********************************************
-->
<!--
DDD - added dup59_pickstops() due to job 20865
(See further comments below)
-->
<script language="Javascript" type="text/javascript">

var hesdeadjim =false;

function page_init()
{
//bmw 062016 #23833
if (window.parent.$('#hesdeadjim').length)
{
	// if (window.parent.$('#hesdeadjim').html() != '')
	var jimspop = window.parent.$('#hesdeadjim').text();
	if (jimspop.indexOf("VOID") >= 0 || jimspop.indexOf("RELEASED") >= 0)
	{
		hesdeadjim = true;
	}
}

// NRS #30022 ********
if(document.Web.must_protect.value == 'Y')
{
var psnumb = 1;
while(document.getElementById('fld38-' + psnumb))
{
protect(document.getElementById('fld38-' + psnumb));
protect(document.getElementById('fld39-' + psnumb));
psnumb++;
}
}

	if (hesdeadjim)
	{
		$(':input').each(function (){ protect(this) });
	    $('.datepick, .time, .butt').each(function (){ unprotect(this) });
		$('.insdel').html('');
	}

if(false) setlock();

if(window.parent.main){

	if(window.parent.main.enforce_seq)
	$('#enforce_seq').val(window.parent.main.enforce_seq.value);
	if(window.parent.main.enforce_seq_by_cust)
		$('#enforce_seq_by_cust').val(window.parent.main.enforce_seq_by_cust.value);
$('#military').val(window.parent.main.military.value);
}
	// do_masks();
	job15621();
	$(document).tooltip();
	$('.zip').focusout( function() {
		ChkDigit(this.id,this.value)
		$('#'+this.id).focus();
	});
	if ($('#new').val() > 3)
	{
		window.location.hash = 'row' + $('#new').val();
	}

	// bmw 041918 #31615
	$("#2nd input[class*='late_']")
	.on('blur',function() {
var stop = $(this).attr('stop');
		var odate = $('#fld38-' + stop).val();
		var otime = $('#fld39-' + stop).val();
		var adate = $('#fld10-' + stop).val();
		var atime = $('#fld11-' + stop).val();
		var ddate = $('#late_pu_' + stop + '_od').val();
		var dtime = $('#late_pu_' + stop + '_ot').val();

		$('#late_appt').val('');
		if (odate == '' || otime == '') return;
		if (adate == '' || atime == '') return;
		if (adate == ddate && atime == dtime) return;
		if (otime == 'FCFS' || otime == 'APPT') return;
		if (isbefore(odate,otime,adate,atime))
			$('#late_appt').val(stop);
	});
get_required_fields('edistops');

autosuggests();

}

function ifmust()
{
var errmsg = '',errmsg_text='';

$('input,select').filter('.musthave').each(function ()
{

if (this.type == 'hidden') return;
if (this.value.trim() != '') return;
if ($(this).hasClass('twin') && $(this).css('display') == 'none') return;
var stop_num = this.name.split('-')[1];
var _errmsg = $('label[for="' + this.id + '"]').text().trim() || $(this).data('label');
if(typeof _errmsg == 'undefined') return;
if(typeof stop_num !== 'undefined'){
_errmsg = 'Stop# '+ stop_num + ': ' + _errmsg;
}
errmsg += '\n' + _errmsg;
});
if(errmsg == "\n") errmsg = '';
return errmsg;
}
//safia job 23997 departed date is readonly
//until both arrived date and time are filled in
function enforceArrivedBeforeDeparted(){

// bmw 092616 pepsi only for now
if ('gj56nb24' != 'einc8hs3')
return;

//if arrived date blank protect departed date
$("[name^='fld10-']").each(function(){
if( ! $(this).val()  ){
var n = $(this).attr("stop");
//only if not already protected for some other reason
if( !$("#fld15-" + n).hasClass("protected") ){
//protect departed
protect($("#fld15-" + n)[0]);
protect($("#fld16-" + n)[0]);
//on arrived date blur ... if arrived date not empty
$(this).blur(function(){
if( $(this).val() ){
//unprotect departed
unprotect($("#fld15-" + n)[0]);
unprotect($("#fld16-" + n)[0]);
}
});
}
}
});
$("[name^='fld15-'], [name^='fld16-']").blur(function(){
var n = $(this).attr('stop');
if($("#fld15-"+stop).val()){
var d1=$("#fld15-"+stop).val();
var t1=$("#fld16-"+stop).val();
var d2=$("#fld10-"+stop).val();
var t2=$("#fld11-"+stop).val();
if(isbefore(d1, t1, d2, t2)){
alert("The departed date/time must be be after the arrived date/time.");
}
}
});
}
function protect(fld)
{
		$('#'+fld.id).addClass("protected").on("click.nonono", function() {this.blur();})
		.on("mousedown.nonono", function() {this.blur();})
		.on("keypress.nonono", function() {this.blur();});
		fld.style.backgroundColor=grayedOut;
	if ($('#'+fld.id).hasClass('datepick')) $('#'+fld.id).datepicker("disable");
}
function unprotect(fld)
{
		$('#'+fld.id).removeClass("protected").off("click.nonono")
		.off("mousedown.nonono")
		.off("keypress.nonono");
		fld.style.backgroundColor='white';
	if ($('#'+fld.id).hasClass('datepick')) $('#'+fld.id).datepicker("enable");
}

// **************
// AJAX Functions
// **************

function applylocationdata(num, datastr) {
	var d = datastr.split("|");
	var aa=1+num;
	//id
		document.getElementById("fld19-" + aa ).value = d[6];
	//name
		document.getElementById("fld4-" + aa ).value = d[0];
	//address
		document.getElementById("fld21-" + aa ).value= d[1];
	//address 2
		document.getElementById("fld22-" + aa ).value= d[7];
	//city
		document.getElementById("fld5-" + aa ).value = d[3];
	//state
		document.getElementById("fld6-" + aa ).value = d[4];
	//zip
		document.getElementById("fld23-" + aa ).value= d[5];
	//phone
		document.getElementById("fld24-" + aa ).value= d[2];
	//Hours
		document.getElementById("fld61-" + aa ).value= d[19];
	//account# added 20872 hm ********
	//	document.getElementById("fld19-" + aa ).value= d[12];
}


function applycity(num, datastr) {
	var d = datastr.split("|");
	var aa=1+num;
//city
	document.getElementById("fld5-" + aa ).value = d[0];
}


function applycs(num, datastr) {
var d = datastr.split("|");
var aa=1+num;
//city
document.getElementById("fld6-"+aa).value = d[1];
//zip
document.getElementById("fld23-"+aa).value = d[2];
}

	var grayedOut="#e8e8e8";

function do_mask(d_fld,t_fld)
{
$.mask.definitions['h']='[ ,0-2]';
$.mask.definitions['m']='[0-5]';
$.mask.definitions['M']='[/ 01]';
$.mask.definitions['D']='[ ,0-3]';

if($('#military').val() == 'Y')
{
t = $(t_fld).val().split(":");
if (t[0].length == 1) $(t_fld).val(" " + $(t_fld).val());

$(t_fld).mask('h9:m9',{completed:function(){
var t = this.val().split(":");
t[0] = t[0].replace(' ','0');
this.val(t[0] + ':' + t[1]);
}});
}

$(d_fld).mask('M9/D9/99',{
completed:function(){
var t = this.val().replace(/ /g,'0');
this.val(t);
}
});
}

function do_seqs()
{
if ( $('#enforce_seq_by_cust').val() == 'Y'
|| ($('#enforce_seq').val() == 'Y' && $('#enforce_seq_by_cust').val() != 'N') )
{
var stopno = 1;
while (document.getElementById("fld4-" + stopno))
{
do_seq(stopno);
stopno++;
}
//safia job 23997 departed date is readonly
//until both arrived date and time are filled in
enforceArrivedBeforeDeparted();
}
}

function do_seq(stopno)
{
	if (hesdeadjim) return;

	var arr_d = document.getElementById("fld10-" + stopno);
	var arr_t = document.getElementById("fld11-" + stopno);

	var dep_d = document.getElementById("fld15-" + stopno);
	var dep_t = document.getElementById("fld16-" + stopno);

	if (stopno > 1)
	{
		var prevstop = stopno - 1;
		var prv_d = document.getElementById("fld15-" + prevstop);
		var prv_t = document.getElementById("fld16-" + prevstop);
	}
	else
	{
		// var prv_d = window.parent.main.di_ldate;
		// var prv_t = window.parent.main.di_ltime;
		var prv_d = ' ';
		var prv_t = ' ';
	}

	protect(arr_d); protect(arr_t);

	if (prv_d.value != '' && prv_t.value != '')
	{
		unprotect(arr_d); unprotect(arr_t);
		// do_mask(arr_d, arr_t);
	}

	if (arr_d.value.trim() != '')
	{
		if(isbefore(arr_d.value,arr_t.value,prv_d.value,prv_t.value))
			{
				alert('Stop Number ' + stopno
					+ ': Arrival Date/Time Must Be After Previous Stop Departure');
				if (arr_t.value) arr_t.value = '';
				else arr_d.value = '';
			}
	}

	protect(dep_d); protect(dep_t);

	if (arr_d.value.trim() != '' && arr_t.value.trim() != '')
	{
		unprotect(dep_d); unprotect(dep_t);
		// do_mask(dep_d, dep_t);
	}
	if (dep_d.value.trim() != '')
	{
		if(isbefore(dep_d.value,dep_t.value,arr_d.value,arr_t.value))
			{
				alert('Stop Number ' + stopno + ': Departed Date/Time Must be After Arrived');
				if (dep_t.value) dep_t.value = '';
				else dep_d.value = '';
			}
	}
}

function isbefore(d1,t1,d2,t2)
{
/* return true if date1 < date2 */
	var date1 = d1;
	var date2 = d2;
	if (t1.trim())
	{
		date1 += " " + t1;
		date2 += " " + t2;
	}
var now = new Date(date1);
var then = new Date(date2);

if (now < then)
return true;
return false;
}

function iflate(code)
{
	if ( $('#enforce_seq_by_cust').val() == 'Y'
|| ($('#enforce_seq').val() == 'Y' && $('#enforce_seq_by_cust').val() != 'N') )
	{
	switch (code) {

	case "late_pu":
				var d1 = document.main.fld185.value + ' ' + document.main.fld186.value;
				var d2 = document.main.di_avdate.value + ' ' + document.main.di_avtime.value;
				break;
	case "late_deliv":
				var d1 = document.main.fld150.value + ' ' + document.main.fld151.value;
				var d2 = document.main.di_addate.value + ' ' + document.main.di_addtime.value;
				break;
		}
		if (isbefore(d1,d2))
		{
			mkevent('X',code)
		}
	}
}

function mkevent(val,code)
{
	if ( ( $('#enforce_seq_by_cust').val() == 'Y'
|| ($('#enforce_seq').val() == 'Y' && $('#enforce_seq_by_cust').val() != 'N') )
&& val.trim() != '')
	{
		alert('call Add Event page here with code: ' + code);
	}
}



function ChkDigit(x,y) {
	var len = y.length;
	if(len == 0){return false;}
	if(len < 5 || len > 6) {
		alert("Please Enter Valid Zip or Postal Code " );
	   x.value="";
	   x.focus();
	return false;
	}
	var c = y.charAt(1)
	if(isNaN(c)) { alert("Please Enter Valid Zip or Postal Code 2-" + c); x.value=""; return false; }
	var c = y.charAt(3)
	if(isNaN(c)) { alert("Please Enter Valid Zip or Postal Code 4-" + c); x.value=""; x.value="" ; return false; }
	var c = y.charAt(5)
	if(isNaN(c) && c != " ") { alert("Please Enter Valid Zip or Postal Code 6-" + c); return false; }
	return true;
}



function checkForm(){

	if (TimerOn) return false;

	var er = 0
	var msg = "";
	var max = document.getElementById("max").value;

	for(i=1; i<=max; i++)
	{
	var stuff0
	var stuff1
	var stuff2
	var stuff3

	stuff0="fld3-" + i;
	var num=document.getElementById(stuff0).value;


	stuff1="fld2-" + i;
	var tst=document.getElementById(stuff1).value;

	stuff2="fld4-" + i;
	var name=document.getElementById(stuff2).value;

	stuff3="fld5-" + i;
	var city=document.getElementById(stuff3).value;

		if (num == 1 && tst != "P")
		{
			msg = msg + "Line: " + i + " Must be a Pick\n";
			document.getElementById(stuff0).style.backgroundColor="#FFd0d0";
			er++;
		}

		if (num == max && tst != "S")
		{
			msg = msg + "Line: " + max + " Must be a Stop\n";
			document.getElementById(stuff0).style.backgroundColor="#FFd0d0";
			er++;
		}


		if (tst != "p" && tst != "P"  && tst != "s" && tst != "S" && tst != "" && tst != " ")
			{
			y = i;
			msg = msg + "Line: " + y + " Not a Pick, Stop or blank\n";
			document.getElementById(stuff1).style.backgroundColor="#FFd0d0";

			er = er + 1;
			}
		if((name == "" || name == " ")&&(city == "" || city == " "))
		{
		}
		else
		{
		if (tst != "p" && tst != "P"  && tst != "s" && tst != "S")
			{
			y = i;
			msg = msg +  "Line: " + y + " Missing Pick or Stop\n";
			er = er + 1;
			document.getElementById(stuff1).style.backgroundColor="#FFd0d0";
			}
		}
	}
	if(er == 0)
	{
var missing_must_have = ifmust();
if(missing_must_have){
alert("Cannot save, missing the following required fields:" + missing_must_have);
return false;
}else{
var out = '<font size=+1 color=red>Saving...</font>';
document.getElementById("buttonrow").innerHTML = out;
document.Web.submit();
return true;
}
	}
	else
	{
	alert(msg);
	return false;
	}
}

function ChkPS(ps,me) {
	var pick = ps.value;
	pick = pick.toUpperCase();
	ps.value = pick;
	var me2 = "fld2-" + me
	if (pick != 'P' && pick != 'S') {
		alert('\nPlease Enter P for Pick or S for Stop.\n');
		eval(setTimeout('document.getElementById("' + me2 + '").focus()',100));
		eval(setTimeout('document.getElementById("' + me2 + '").select()',120));
		ps.value = "";
		document.getElementById(me2).style.backgroundColor="#FFd0d0";
		return
	}
		document.getElementById(me2).style.backgroundColor="#FFFFFF";
	return;
}

function ChkNAN(num,me) {
	if(isNaN(num.value)) {
		alert('\nPlease Enter Numeric Value.\n');
		num.value = "";
		eval(setTimeout('document.getElementById("' + me + '").focus()',100));
		eval(setTimeout('document.getElementById("' + me + '").select()',120));
	}
return;
}

function ChkYN(YN,me) {
	var yesno = YN.value;
	yesno = yesno.toUpperCase();
	YN.value = yesno;
	if (yesno != 'Y' && yesno != 'N') {
		alert('Please Enter Either Y or N.');
		YN.value = "";
		eval(setTimeout('document.getElementById("' + me + '").focus()',100));
		eval(setTimeout('document.getElementById("' + me + '").select()',120));
	}
	return
}

function get_highest()
{
	var highest = '';
	for (i=98; highest == ''; i--)
	{
		isit = document.getElementById("fld2-" + i);
		if (isit)
		{
			if (isit.value != '')
				{ highest = i; }
		}
	}
	return (highest-0);
}

function chkNum(num,row)
{
	//correct to numering in payable(3)
	var val = (num.value-0);    // bmw 042616 #23108   + 1;
	var me  = (row-0);          // bmw 042616 #23108   + 1;
	var last = get_highest();   // bmw 050216 #23108   + 1;
	var dest = last + 1;

	var msg = "";

	// if (val < 2) val = 2;
	if (val >= last) val = last;

	if (val == me) return;

	var stuff0="fld3-" + row;
	var num=document.getElementById(stuff0).value;

	stuff1="fld2-" + row;
	var tst=document.getElementById(stuff1).value;

		if (val == 1 && tst != "P")
		{
			msg = msg + "Line: " + val + " Must be a Pick\n";
			document.getElementById(stuff1).style.backgroundColor="#FFd0d0";
		}

		if (num == last && tst != "S")
		{
			msg = msg + "Line: " + last + " Must be a Stop\n";
			document.getElementById(stuff1).style.backgroundColor="#FFd0d0";
		}

		if (msg)
		{
			alert(msg);
			return false;
		}


	chgstop(me,0);
	chgstop(dest,99);

	if (val < me)
	{
		for (i=me; i > val; i--)
		{ chgstop(i-1, i); }
	}
	if (val > me)
	{
		for (i=me; i < val; i++)
		{ chgstop(i+1, i); }
	}
	chgstop(0, val);
	chgstop(99, get_highest() + 2);
	document.web.prcnam.value = 'edistops';
	document.web.type.value = 'pick&stop';
	document.web.pro.value = '86873';
	document.web.submit();
}

function strPad(i,l,s)
{
	var o = i.toString();
	if (!s) { s = '0'; }
	while (o.length < l) {
		o = s + o;
	}
	return o;
}

function chgstop(oldstop,newstop)
{
	var key = strPad(document.Web.pro.value,7,' ') + strPad(oldstop,2,' ');
	var request = new XMLHttpRequest();
	var url = "/route.php?prcnam=fpweb&qual=gj56nb24&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&sys=3a"
		+ "&fpweb_pg=_&type=save&fpweb_fn=edistops&fpweb_ky=" + key + "&fpweb_lp=9"
		+ "&fpweb_ix=A&fld3=" + newstop;
	//alert(url);
	request.open("GET",url,false);
	// request.setRequestHeader("User-Agent",navigator.userAgent);
	request.send(null);

}

var TimerId = [];
var TimerOn = false;

var adate;
var atime;
var atarget;
var codetype;

function inchist(code,stop,txt)
{
if (txt.trim() != '')
{
var link = '&nbsp;<img src="/images/vision/info.png" class=always>&nbsp;';
var target = $('#' + code + '_' + stop + '_ico');
target.html(link).attr('title', txt);
}
}
function job15621()
{
	if( ( $('#enforce_seq_by_cust').val() == 'Y'
|| ($('#enforce_seq').val() == 'Y' && $('#enforce_seq_by_cust').val() != 'N'))
&& '86873' != '')
{
		do_seqs();

$("#2nd input[class*='appt_']")
		.each(function() {
			var txt = $(this).attr('evtext');
			if (txt)
			{
				var stop = $(this).attr('stop');
				inchist('appt_pickup',stop,txt);
			}
		})
.on('focus',function() {
var stop = $(this).attr('stop');
//safia  job 24784 -- make sure appropriate incident code is used
var type = $("#fld2-"+stop).val();
var code = (type == 'P') ? 'appt_pickup': 'appt_deliv';
var timerid = code + stop;
if (TimerId[timerid])
			{
				clearTimeout(TimerId[timerid]);
				TimerOn = false;
			}
})
.on('blur',function() {
// if (!$.fancybox.isOpen)
			if (!$('#dialog_container'))
{
var stop = $(this).attr('stop');
//safia  job 24784 -- make sure appropriate incident code is used
var type = $("#fld2-"+stop).val();
var code = (type == 'P') ? 'appt_pickup': 'appt_deliv';

//these are hard coded to appt_pickup but its okay since the stopnum
//uniquly IDs
var odate = $('#appt_pickup_' + stop + '_od').val();
var otime = $('#appt_pickup_' + stop + '_ot').val();

if ($(this).hasClass('date'))
{
var adate = $(this).val();
var atime = $('#fld39-' + stop).val();
}
if ($(this).hasClass('time'))
{
atime = $(this).val();
adate = $('#fld38-' + stop).val();
}

// bmw 011217 #26438 setevent if different, not later
				//if (isbefore(odate,otime,adate,atime))
				if (atime != '' && odate + otime != '' && adate + atime != odate + otime)
{
setevent(code,stop,odate,otime,adate,atime);
}
}
});

$("#2nd input[class*='late_']")
.each(function() {
var txt = $(this).attr('evtext');
if (txt)
{
var stop = $(this).attr('stop');
inchist('late_pu', stop,txt);
}
})
.on('focus',function() {
var stop = $(this).attr('stop');
//safia  job 24784 -- make sure appropriate incident code is used
var type = $("#fld2-"+stop).val();
var code = (type == 'P') ? 'late_pu': 'late_deliv';
var timerid = code + stop;
if (TimerId[timerid])
{
clearTimeout(TimerId[timerid]);
TimerOn = false;
}
})
.on('blur',function() {

// if (!$.fancybox.isOpen)
			if (!$('#dialog_container'))
{
var stop = $(this).attr('stop');
var adate = $('#fld10-' + stop).val();
var atime = $('#fld11-' + stop).val();

//safia job 25158 .... for arrival date ... only popup on change
var orig = $("#late_pu_" +stop +"_od").val() + $("#late_pu_" +stop +"_ot").val();
var curr = adate + atime;

if( orig == curr ) return;

//safia  job 24784 -- make sure appropriate incident code is used
var type = $("#fld2-"+stop).val();
var code = (type == 'P') ? 'late_pu': 'late_deliv';

var odate = $('#fld38-' + stop).val();
var otime = $('#fld39-' + stop).val();


if (isbefore(odate,otime,adate,atime)){
setevent(code,stop,odate,otime,adate,atime);
}
}
});
}
}

function setevent(code,stop,odate,otime,adate,atime)
{
$('#event_fbox').val('fail');

atarget = '/route.php?type=add&qual=gj56nb24&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&sys=3a&pro=86873'
+ '&prcnam=incident&event=' + code + '&pkst=' + stop
		+ '&odate=' + odate + '&otime=' + otime
+ '&adate=' + adate + '&atime=' + atime + '&isfbox=0';
codetype = code;
timer = setTimeout("fbox(atarget,'large'," + stop + ");",1000);
	timerid = code + stop;
TimerId[timerid] = timer;
	TimerOn = true;
// alert("TimerId[" + code + "] = " + TimerId[code]);
}

var curr_stop;

function fbox(url,size,stop)
{
var bwidth = 780;
var bheight = 520;
if (size == 'med')
{
bwidth = 960;
bheight = 600;
}
if (size == 'large')
{
bwidth = 1200;
bheight = 720;
}
if (size == 'larger')
{
bwidth = 1100;
bheight = 820;
}
if (size == 'huge')
{
bwidth = 1300;
bheight = 800;
}

	curr_stop = stop;

if ($('#dialog_container')) $('#dialog_container').remove();
$('<div/>', {'class':'myDlgClass', 'id':'dialog_container'})
.html($('<iframe/>', {
'src' : url,
'style' :'width:100%; height:100%;border:none;'
})).appendTo('body')
.dialog({
//'title' : $(this).text(),
width : bwidth,
height :bheight,
position: { my: "center top", at: "center top+20", of: window },
closeText: ""
});

}

function postfld(args)
{
var url = "/route.php?prcnam=fpweb&qual=gj56nb24&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&sys=3a"
+ "&pro=86873&fpweb_pg=_&type=save&fpweb_ix=A&fpweb_fn=edistops"
+ args;
var request = new XMLHttpRequest();
request.open("GET",url,false);
// request.setRequestHeader("User-Agent",navigator.userAgent);
request.send(null)
}

function xitfbox()
{
// bmw 073014 job 15621 stuff
	TimerOn = false;
	var stop = curr_stop - '0';

// safia job 25106 : now destinguishing between pickup and deliv so this
// had to be changed
var code_prefix = codetype.split("_")[0];
codetype = (code_prefix == 'appt') ? 'appt_pickup' : 'late_pu'
	var old_pd = $('#'+codetype+'_'+stop+'_od');
	var old_pt = $('#'+codetype+'_'+stop+'_ot');
	var old_ad = $('#'+codetype+'_'+stop+'_od');
	var old_at = $('#'+codetype+'_'+stop+'_ot');

var event = $('#event_fbox');
if (event.val() == 'pass')
{
var args = '';
switch(codetype)
{
case "appt_pickup":
args = '&fld38=' + $('#fld38-'+stop).val() + '&fld39=' + $('#fld39-'+stop).val();
$(old_pd).val($('#fld38-'+stop).val());
$(old_pt).val($('#fld39-'+stop).val());
break;
case "late_pu":
args = '&fld10=' + $('#fld10-'+stop).val() + '&fld11=' + $('#fld11-'+stop).val();
old_ad.val($('#fld10-'+stop).val());
old_at.val($('#fld11-'+stop).val());
break;
}
		stop = stop + 1;
		var key = strPad('86873',7,' ') + strPad(stop,2,' ');
		args += '&fpweb_ky=' + key;
		args += '&fpweb_lp=9';
		// bmw 072621 found OOR on plcy
// postfld(args);
		document.Web.submit();
		// end OOR
codetype = '';
(event.val(''));
}
else if (event.val() == 'fail')
{
		if (codetype == 'appt_pickup')
		{
			$('#fld38-'+stop).val(old_pd.val());
			$('#fld39-'+stop).val(old_pt.val());
		}
		if (codetype == 'late_pu')
		{
			$('#fld10-'+stop).val(old_ad.val());
			$('#fld11-'+stop).val(old_at.val());
		}
}
// bmw 073014 end job 15621 stuff
}

// DDD--- This function added as part of job 20865, and it's used as the onchange handler for
// fields in which reference numbers are entered. These fields are labelled fld([2][679|[3][034])-([1-9]|[10])
// This function is functionally identical to the code in base.js's function dup59. If any substantive
// changes are made to one, they need to be made to both.
function dup59_pickstops(fld)
{
	var ref = fld.value;
	var pro = $("#pro").val();
var ps_num = fld.name.split("-")[1];
var qual = $("#qual").val();
	var cust_name = $("#fld4-" + ps_num).val();
	var cust_num = $("#fld19-" + ps_num).val();
	if (ref != '')
	{
		var request = new XMLHttpRequest();
		var url = "/route.php?prcnam=ckref&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual="+qual+"&pro=86873"
			+ "&acct=" + cust_num + "&ref=" + ref + "&fld=" + fld.name
			+ "&cuname=" + encodeURIComponent(cust_name);

		request.open("GET",url,false);
		//request.setRequestHeader("User-Agent",navigator.userAgent);
		request.send(null)
		var response = request.responseText;

		if (response != '')
		{
			splitlist = response.split("|");
			alert('Customer Reference # ' + ref
				+ '\n\nAlready Appears In Pro # ' + splitlist[0]
				+ '\n\nShip Date ' + splitlist[1]);
			if (splitlist[2] == 'stop') fld.value = '';
		}
	}
}

function rusure(row)
{
//	if (confirm('Delete Line ' + row + '?'))
		checkForm();
//	else
//		return false;
}


// #23169 081116 NHS
function getCustomerID( custname ) {
var url= '/route.php?prcnam=fpweb&type=ajax2&fpweb_fn=gocust&fld1=true&fpweb_xt=Y&fpweb_ix=B&qual=gj56nb24&sys=3a&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&fpweb_ky=' + custname;
var num = $.parseJSON(getajax(url));
return ( num == null ) ? null : num['fld1'];
}
function upCust(custid,custname) {
if( custid == "" ) {
alert("No Account #\nCan't lookup.");
return;
}
//var num = getCustomerID( custid );
//if( num == null ) {
//  alert("Company '" + custname + "' was not found.");
//  return;
// }
	document.upcust.custid.value=custid;
	document.upcust.relog.value="no";
	document.upcust.submit();
}
function getajax(url) {
var request = new XMLHttpRequest();
request.open("GET",url,false);
request.send(null)
return request.responseText;
}


var lock_retries = (0-0);

function setlock()
{
var myrandom = Math.floor(Math.random() * 32767) + 1;
//alert(myrandom);
myrandom = "4n3mqi6"
var url = "/route.php?prcnam=setlock&name=mmhd50&file=3disp&qual=gj56nb24"
+ "&record=2293"
	  + "&pronum=86873"
//      + "&sessid=32000"
+ "&sessid=" + myrandom
+ "&usenew=Y";

	//var request = new XMLHttpRequest();
	//request.open("GET",url,false);
	//request.send(null)
$.ajax(url)
.done(function(response){
	lock_retries = 0;
	got_locks(response);
})
.error(function(){
	lock_retries += 1;
	if (lock_retries > 2)
	{
		alert ("Cannot Check Lock Status of This Pro;\nPlease Try Again Later");
islocked = true;
txt =
disablePage('Cannot Check Lock Status');
	}
	else
	{
		setTimeout('setlock()', 500);   // retry in 1/2 second
	}
});
}
var islocked

function got_locks(response)
{
//var response = request.responseText;
var d = response.split("|");

//Craig 122315 job 16846
var lockLimit = $('#max_locks').val();

if(lockLimit > 0){
//alert('lock limit = ' + lockLimit + ' for qual ' + mp_qu);
if(areMoreLocksAllowed(lockLimit, d) === false) return;
}

var type = d[0];
var left = d[1];
var hms = left.split(":");
var min = parseInt(hms[1],10);
var sec = parseInt(hms[2],10);
var whom = d[2];
	var blinkit=false;
	var until;

$('.blink').css('display','inline');
$('.blink').removeClass('blink');
	if (min > 1)
		until = ' for ' + min + ' Minutes';
	if (min == 1)
		until = ' for ' + min + ' Minute';
if (min == 0)
	{
		until = ' for ' + sec + ' Seconds';
		blinkit=true;
	}

var val = parseInt(type,10);
switch (val)
{
case 0:
// not locked
if (islocked) say(mp_pn);
break;
case 1:
// locked
islocked = true;
txt =
disablePage('Locked By ' + whom + until);
break;
case 2:
// supervisor cancel or time ran out
disablePage('Your Lock Has Expired');
/*
if (changed)
{
document.main.type.value='saveback';
dosubmit();
}
else
*/
dohome();
break;
case 3:
// show time left
if (islocked) { say(mp_pn); break; }
var payload = '<font size=+1 style="color:#ff0000">'
+ 'You Have this Locked' + until + '</font>';
if (blinkit) $('[id^=locktop]').addClass('blink');

//Craig 060915 Job16499: added the next line to focus the browswer tab and warn users when lock has less than one minute left
if (min == 0 && sec < 59 && sec >50)
{
if (goctrl[641] == 'Y')  //bmw 010416 only if control is on
alert('Your lock will expire in ' + sec + ' seconds!');
}

//      document.getElementById("locktop1").innerHTML=payload;
//      document.getElementById("locktop2").innerHTML=payload;
}
setTimeout('setlock()', 5000);

locking_fault = false;
}

//Craig 122315 job 16846 Lock Limit
function areMoreLocksAllowed(lockLimit, d){
lockedLoads = getLockedLoads(d)
if(lockedLoads.search(mp_pn) == -1){
lockedLoadsCount = lockedLoads.split("|").length;
if(lockedLoadsCount >= lockLimit){
var message = 'Max Locked Shipments('+lockLimit +') ' + lockedLoads;
disablePage(message);
return false;
}
}
return true;
}

var mp_pn = "86873";

function disablePage(message)
{
$("input").prop('disabled', true);
document.getElementById("lmess").innerHTML="Locked";
}


</script>

<!--
***********************************************
END OF PAGE-SPECIFIC JAVASCRIPT FUNCTIONS
***********************************************
-->

</head>

<!--- html template: pickstop_edi.htm --->
<body >
<div align="center">

<span id="page_header"></span>
<input type=hidden id=user_perms value="YYYYYNYYYNYYNNNYYYYYYYYNYYYYNYYYYNYYYYYYYYN|mmhd|MLM SUPPLY CHAIN LLC|mmhd50|prod-50.aljex.com|<EMAIL>|KIT|DRUM

">
<form method="post" name="web" action="/route.php">
		<input name="prcnam" value="forms" type="hidden">
		<input name="qual" id="qual" value="gj56nb24" type="hidden">
		<input name="name" id="name" value="mmhd50" type="hidden">
		<input name="c_tok" id="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW" type="hidden">
		<input name="type" value="continue" type="hidden">
		<input name="sys" id="sys" value="3a" type="hidden">
		<input name="ctlrec" value="" type="hidden">
		<input name="ctlval" value="" type="hidden">
<input type="hidden" name="webprint" value="">
<input type="hidden" name=pro value="86873">
<input type="hidden" name="repno" value="">
<input type="hidden" name="thirdparty" value="">
<input type="hidden" name=origin>
<input type="hidden" name=destination>
<input type="hidden" name=locator>

<input type="hidden" name=isfbox id=isfbox value="1">
</form>

<form name="upcust" target="_blank" method"post" action="/route.php">
<input type="hidden" name="prcnam" value="addcust">
<input type="hidden" name="qual" value="gj56nb24">
<input type="hidden" name="sys" value="3a">
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name="compan" value="mmhd50">
<input type="hidden" name="c_tok2" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name="type" value="update">
<input type="hidden" name="dtyp">
<input type="hidden" name="relog">
<input type="hidden" name="custid">

</form>

<form method="post" name="Web" action="/route.php" autocomplete="off">
<input type="hidden" name=type value=finished>
<input type="hidden" name=repid value="">
<input type="hidden" name=qual value="gj56nb24">
<input type="hidden" name=name value="mmhd50">
<input type="hidden" name=prcnam value="edistops">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name=ctlrec value="">
<input type="hidden" name=ctlval value="">
<input type="hidden" name=account value="">
<input type="hidden" name=pro value="86873">
<input type="hidden" name="sys" value=3a>
<input type="hidden" name="more">
<input type="hidden" name="sub" value="Update Picks / Stops">

<input type="hidden" name=isfbox id=isfbox value="2">
<!--NRS #30022 ********-->
<input type="hidden" name=must_protect id=must_protect value="">
<!--/NRS-->
<input type="hidden" id=military>
<input type="hidden" id=enforce_seq>
<!--DDD 022516 #22556: selectively turn off incident management in the case that it should be turned off
for some customers -->
<input type="hidden" id=enforce_seq_by_cust name=enforce_seq_by_cust>
<!--DDD 022516: #22556 -->
<input type="hidden" id=event_fbox>

<!--bmw 041918 #31615 -->
<input type=hidden id=late_appt name=late_appt>

<!-- START OPENING bkb styling #############################################-->
<table>
<tr>
<td>
<!-- END OPENING bkb styling #############################################-->

<!-- BUTTONS -->

<table border=0 align=center><tr>

<td align="center"><span id=buttonrow>
<input type="button" value="Save" onclick="checkForm();">
</span></td>
<td align="center">
<span id=fboxmenu2>
<input type="button" value="Back" onclick="history.go(-1);">
<input type="button" value="Home" onclick="dohome();">
</span></td>
</span>
</tr></table>
<p>

<!-- END BUTTONS -->

<table border="0" cellpadding="0" cellspacing="0">
<tbody><tr><td>
<table class="tab" bordercolorlight="white" bordercolordark="white" bgcolor="#f0f0f0" border="2" bordercolor="#000000" cellpadding="2" cellspacing="1" width="570">
<tbody>

<tr><!-- %%%%%%%%%%%% -->
<td class="headrow" colspan=4>
<table width="100%" border="0">
<tr>
<td width=33%><font color="#000000">Pro #&nbsp;</font>
<span class="headidno">&nbsp;86873&nbsp;</span></td>
<td width=33% align=center><font size=+2 color=red> &nbsp;&nbsp;&nbsp;&nbsp; <span name=lmess id=lmess></span></font> </td>
<td width=33% align=right valign=middle class="headcolor stitle">Extra Picks and Stops &nbsp;</td>
</tr>
</table>
</td>
</tr><!-- %%%%%%%%%%%% -->

<tr><td><table border=0 align=center cellpadding=3 cellspacing=0>
<tr><td><table>
<tr>
<td align=right>Type</td>
<td><b>DRAY&nbsp;&nbsp;&nbsp;</td>
<td align=right>Status</td>
<td><b>COVERED&nbsp;&nbsp;&nbsp;</td>
<td align=right nowrap>Ready Date</td>
<td><b>12/03/25&nbsp;&nbsp;&nbsp;</td>
</tr>
<tr>
<td align=right>Customer</td>
<td colspan=5><b>ACME LOGISTICS, LLC - ATL&nbsp;&nbsp;&nbsp;</td>
</tr>
<tr>
<td align=right>Carrier</td>
<td colspan=5><b>EMCA LOGISTICS LLC&nbsp;&nbsp;&nbsp;</td>
</tr>
</table></td>
<td><table>
<tr>
<!-- jh 112822 job 40989
<td align=right>Origin</td>
<td colspan=3><b>MOUNT PLEASANT SC&nbsp;&nbsp;&nbsp;</td>
<td align=right>Loaded Date</td>
<td><b>&nbsp;&nbsp;&nbsp;</td>
-->
</tr>
<tr>
<!-- jh 112822 job 40989
<td align=right>Destination</td>
<td colspan=3><b>MOUNT PLEASANT SC&nbsp;&nbsp;&nbsp;</td>
<td align=right>Delivered Date</td>
<td ><b>&nbsp;&nbsp;&nbsp;</td>
-->
</tr>
</table></td></tr>
</table>
</td>
</tr>

<tr>
<td colspan="2">
<table id="2nd" border="0" align="center" cellpadding="2" cellspacing="0" bgcolor=#f0f0f0>


<input type="hidden" id=appt_pickup_1_od value="12/03/25">
<input type="hidden" id=appt_pickup_1_ot value="">
<input type="hidden" id=late_pu_1_od value="">
<input type="hidden" id=late_pu_1_ot value="">

<!--
<input type="hidden" name="fld19-1" id="fld19-1" value=101636>
-->

<tr id=row1 BGCOLOR=#e0e0e0>
<td nowrap>
<span class="style1">&nbsp;Number&nbsp;</span>
</td>
	<td nowrap><input type=text name=fld3-1 id=fld3-1 size=2 maxlength=2 value=1
		onchange="chkNum(this, 1);">
&nbsp;
</td>
<td align=right><b>P</b>ick/<b>S</b>top</td>
<td colspan="3"><nobr>
<input name="fld2-1" id="fld2-1" type="text" size="1" maxlength="1" value="P" onChange="ChkPS(this,1);">
<nobr>&nbsp;&nbsp;&nbsp; Appointment &nbsp;
<input name="fld37-1" type="text" id="fld37-1" value="" size="1" maxlength="1" onChange="ChkYN(this,'fld37-1');">
&nbsp;<font size="-1">(Y/N)</font></nobr></td>
<td align=right nowrap>
Appt Note</td>
<td colspan=5 nowrap>
<input name="fld40-1" id="fld40-1" type="text" size="15" maxlength="20" value="EVER EXCEL" />
&nbsp;&nbsp;&nbsp;
Seal &nbsp;
<input name="fld28-1" id="fld28-1" type="text" size="20" maxlength="20" value="" />
</td>

<td align="right">Reference &nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld26-1" id="fld26-1" type="text" size="20" maxlength="20" value="CMAU2146283" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td>&nbsp;&nbsp;&nbsp;&nbsp;</a></td>
<td></td>
<td align=right><a href="#" onclick="upCust('101636','WANDO TERMINAL')">Name</a> &nbsp;</td>
<td colspan="3" nowrap>
<input name="fld4-1" id="fld4-1" type="text" size="30" maxlength="30" value="WANDO TERMINAL" />


</td>
<td align=right nowrap>Appt Date</td>
<td nowrap>
<input name="fld38-1" class="datepick appt_pickup" stop="1" evtext="" id="fld38-1" type="text" size="6" maxlength="8" value="12/03/25" onchange="domdy(this);">
</td>
<td align=right nowrap>Appt Time</td>
<td nowrap>
<input name="fld39-1" id="fld39-1" class="time appt_pickup" stop="1" type="text" size="5" maxlength="5" value="" onchange="dohms(this);"> <!--onKeyDown="javascript:return dFilter (event.keyCode, this, '##:##');" /> -->
&nbsp;<span id="appt_pickup_1_ico"></span>
</td>
<td align="right">
&nbsp;
Hours </td><td nowrap> <input name="fld61-1" id="fld61-1" type=text size="8" value="">
&nbsp;&nbsp;&nbsp;&nbsp;
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld27-1" id="fld27-1" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld21-1" id="fld21-1" type="text" size="30" maxlength="30" value="400 LONGPOINT RD" />
</td>
<td align=right nowrap>Arrival Date </td>
<td nowrap>
<input name="fld10-1" type="text" id="fld10-1" class="datepick late_pu" evtext="" stop="1" value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>Arrival Time</td>
<td nowrap>
<input name="fld11-1" type="text" id="fld11-1" class="time late_pu" stop="1"  value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
&nbsp;<span id="late_pu_1_ico"></span>
<td align="right">&nbsp;&nbsp;Weight</td>
<td>
<input name="fld47-1" id="fld47-1" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld47-1');" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld29-1" id="fld29-1" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld22-1" id="fld22-1" type="text" size="30" maxlength="30" value="" />
</td>
<td align=right nowrap>Departed Date</td>
<td nowrap>
<input name="fld15-1" type="text" id="fld15-1" class=datepick value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>&nbsp;&nbsp;Departed Time </td>
<td nowrap>
<input name="fld16-1" type="text" id="fld16-1" class=time value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
</td>
<td align=right>Pieces</td>
<td>
<input name="fld45-1" id="fld45-1" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld45-1',1);"/>
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld30-1" id="fld30-1" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>City &nbsp;</td>
<td colspan="7" nowrap>
<input name="fld5-1" id="fld5-1" type="text" size="15" maxlength="20" value="MOUNT PLEASANT" />

&nbsp;&nbsp;State &nbsp;
<input type=text name="fld6-1" id="fld6-1" size=2 maxlength=2 value="SC">
&nbsp;&nbsp;Zip &nbsp;
<input name="fld23-1" id="fld23-1" type="text" size="6" maxlength="6" value="29464" onChange="ChkDigit(this,this.value);" >
<!--onKeyDown="javascript:return dFilter (event.keyCode, this, '######);" />-->

&nbsp;&nbsp;&nbsp;Received by
<input name="fld17-1" id="fld17-1" type="text" size="20" maxlength="30" value="" />
</td>
<td align=right>Pallets</td>
<td>
<input name="fld46-1" id="fld46-1" type="text" size="2" maxlength="5" value="" onChange="NAN(this, fld46-1);" />
</td>

<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld33-1" id="fld33-1" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld24-1" id="fld24-1" type="text" size="14" maxlength="14" value="(*************"> </td>
<td>&nbsp;</td>
<td>Comments </td>
<td colspan="6">
<input name="fld67-1" id="fld67-1" type="text" size="60" maxlength="60" value="" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld34-1" id="fld34-1" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld65-1" id="fld65-1" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld68-1" id="fld68-1" type="text" size="60" maxlength="60" value="" />
</td>
<!-- td colspan="2">&nbsp;</td -->
<!-- Account# added 20872 hm ******** -->
<td>Account #&nbsp;</td>
<td>
<input name="fld19-1" id="fld19-1" type="text" size="20" maxlength="6" readonly value="101636" />
</td>
</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld66-1" id="fld66-1" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld69-1" id="fld69-1" type="text" size="60" maxlength="60" value="" />
</td>
<td colspan="2" align=center><nobr>
<span class=insdel>
<input type="button" value="Insert Above" class=insbutt onclick="$('#copy1').val('187167');checkForm();">
<input type="button" value="Delete Location" class=delbutt onclick="$('#delete1').val('1');rusure(1);">

<input type=hidden id=copy1 name=copy1>
<input type=hidden id=delete1 name=delete1>
</span>

</td>
</tr>

<tr>
<td colspan="14"><hr width="90%" size="2" color="#999999" align="center"></td>
</tr>


<input type="hidden" id=appt_pickup_2_od value="">
<input type="hidden" id=appt_pickup_2_ot value="">
<input type="hidden" id=late_pu_2_od value="">
<input type="hidden" id=late_pu_2_ot value="">

<!--
<input type="hidden" name="fld19-2" id="fld19-2" value=100006>
-->

<tr id=row2 BGCOLOR=#e0e0e0>
<td nowrap>
<span class="style1">&nbsp;Number&nbsp;</span>
</td>
	<td nowrap><input type=text name=fld3-2 id=fld3-2 size=2 maxlength=2 value=2
		onchange="chkNum(this, 2);">
&nbsp;
</td>
<td align=right><b>P</b>ick/<b>S</b>top</td>
<td colspan="3"><nobr>
<input name="fld2-2" id="fld2-2" type="text" size="1" maxlength="1" value="P" onChange="ChkPS(this,2);">
<nobr>&nbsp;&nbsp;&nbsp; Appointment &nbsp;
<input name="fld37-2" type="text" id="fld37-2" value="" size="1" maxlength="1" onChange="ChkYN(this,'fld37-2');">
&nbsp;<font size="-1">(Y/N)</font></nobr></td>
<td align=right nowrap>
Appt Note</td>
<td colspan=5 nowrap>
<input name="fld40-2" id="fld40-2" type="text" size="15" maxlength="20" value="APPT" />
&nbsp;&nbsp;&nbsp;
Seal &nbsp;
<input name="fld28-2" id="fld28-2" type="text" size="20" maxlength="20" value="" />
</td>

<td align="right">Reference &nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld26-2" id="fld26-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td>&nbsp;&nbsp;&nbsp;&nbsp;</a></td>
<td></td>
<td align=right><a href="#" onclick="upCust('100006','BAUSCHLINNEMANN NORTH AMERICA')">Name</a> &nbsp;</td>
<td colspan="3" nowrap>
<input name="fld4-2" id="fld4-2" type="text" size="30" maxlength="30" value="BAUSCHLINNEMANN NORTH AMERICA" />


</td>
<td align=right nowrap>Appt Date</td>
<td nowrap>
<input name="fld38-2" class="datepick appt_pickup" stop="2" evtext="" id="fld38-2" type="text" size="6" maxlength="8" value="" onchange="domdy(this);">
</td>
<td align=right nowrap>Appt Time</td>
<td nowrap>
<input name="fld39-2" id="fld39-2" class="time appt_pickup" stop="2" type="text" size="5" maxlength="5" value="" onchange="dohms(this);"> <!--onKeyDown="javascript:return dFilter (event.keyCode, this, '##:##');" /> -->
&nbsp;<span id="appt_pickup_2_ico"></span>
</td>
<td align="right">
&nbsp;
Hours </td><td nowrap> <input name="fld61-2" id="fld61-2" type=text size="8" value="">
&nbsp;&nbsp;&nbsp;&nbsp;
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld27-2" id="fld27-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld21-2" id="fld21-2" type="text" size="30" maxlength="30" value="1175 HARRELSON BLVD" />
</td>
<td align=right nowrap>Arrival Date </td>
<td nowrap>
<input name="fld10-2" type="text" id="fld10-2" class="datepick late_pu" evtext="" stop="2" value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>Arrival Time</td>
<td nowrap>
<input name="fld11-2" type="text" id="fld11-2" class="time late_pu" stop="2"  value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
&nbsp;<span id="late_pu_2_ico"></span>
<td align="right">&nbsp;&nbsp;Weight</td>
<td>
<input name="fld47-2" id="fld47-2" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld47-2');" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld29-2" id="fld29-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld22-2" id="fld22-2" type="text" size="30" maxlength="30" value="" />
</td>
<td align=right nowrap>Departed Date</td>
<td nowrap>
<input name="fld15-2" type="text" id="fld15-2" class=datepick value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>&nbsp;&nbsp;Departed Time </td>
<td nowrap>
<input name="fld16-2" type="text" id="fld16-2" class=time value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
</td>
<td align=right>Pieces</td>
<td>
<input name="fld45-2" id="fld45-2" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld45-2',2);"/>
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld30-2" id="fld30-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>City &nbsp;</td>
<td colspan="7" nowrap>
<input name="fld5-2" id="fld5-2" type="text" size="15" maxlength="20" value="MYRTLE BEACH" />

&nbsp;&nbsp;State &nbsp;
<input type=text name="fld6-2" id="fld6-2" size=2 maxlength=2 value="SC">
&nbsp;&nbsp;Zip &nbsp;
<input name="fld23-2" id="fld23-2" type="text" size="6" maxlength="6" value="29577" onChange="ChkDigit(this,this.value);" >
<!--onKeyDown="javascript:return dFilter (event.keyCode, this, '######);" />-->

&nbsp;&nbsp;&nbsp;Received by
<input name="fld17-2" id="fld17-2" type="text" size="20" maxlength="30" value="" />
</td>
<td align=right>Pallets</td>
<td>
<input name="fld46-2" id="fld46-2" type="text" size="2" maxlength="5" value="" onChange="NAN(this, fld46-2);" />
</td>

<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld33-2" id="fld33-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld24-2" id="fld24-2" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments </td>
<td colspan="6">
<input name="fld67-2" id="fld67-2" type="text" size="60" maxlength="60" value="" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld34-2" id="fld34-2" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld65-2" id="fld65-2" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld68-2" id="fld68-2" type="text" size="60" maxlength="60" value="" />
</td>
<!-- td colspan="2">&nbsp;</td -->
<!-- Account# added 20872 hm ******** -->
<td>Account #&nbsp;</td>
<td>
<input name="fld19-2" id="fld19-2" type="text" size="20" maxlength="6" readonly value="100006" />
</td>
</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld66-2" id="fld66-2" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld69-2" id="fld69-2" type="text" size="60" maxlength="60" value="" />
</td>
<td colspan="2" align=center><nobr>
<span class=insdel>
<input type="button" value="Insert Above" class=insbutt onclick="$('#copy2').val('187168');checkForm();">
<input type="button" value="Delete Location" class=delbutt onclick="$('#delete2').val('2');rusure(2);">

<input type=hidden id=copy2 name=copy2>
<input type=hidden id=delete2 name=delete2>
</span>

</td>
</tr>

<tr>
<td colspan="14"><hr width="90%" size="2" color="#999999" align="center"></td>
</tr>


<input type="hidden" id=appt_pickup_3_od value="">
<input type="hidden" id=appt_pickup_3_ot value="">
<input type="hidden" id=late_pu_3_od value="">
<input type="hidden" id=late_pu_3_ot value="">

<!--
<input type="hidden" name="fld19-3" id="fld19-3" value=101636>
-->

<tr id=row3 BGCOLOR=#e0e0e0>
<td nowrap>
<span class="style1">&nbsp;Number&nbsp;</span>
</td>
	<td nowrap><input type=text name=fld3-3 id=fld3-3 size=2 maxlength=2 value=3
		onchange="chkNum(this, 3);">
&nbsp;
</td>
<td align=right><b>P</b>ick/<b>S</b>top</td>
<td colspan="3"><nobr>
<input name="fld2-3" id="fld2-3" type="text" size="1" maxlength="1" value="S" onChange="ChkPS(this,3);">
<nobr>&nbsp;&nbsp;&nbsp; Appointment &nbsp;
<input name="fld37-3" type="text" id="fld37-3" value="" size="1" maxlength="1" onChange="ChkYN(this,'fld37-3');">
&nbsp;<font size="-1">(Y/N)</font></nobr></td>
<td align=right nowrap>
Appt Note</td>
<td colspan=5 nowrap>
<input name="fld40-3" id="fld40-3" type="text" size="15" maxlength="20" value="" />
&nbsp;&nbsp;&nbsp;
Seal &nbsp;
<input name="fld28-3" id="fld28-3" type="text" size="20" maxlength="20" value="" />
</td>

<td align="right">Reference &nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld26-3" id="fld26-3" type="text" size="20" maxlength="20" value="*********/********" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td>&nbsp;&nbsp;&nbsp;&nbsp;</a></td>
<td></td>
<td align=right><a href="#" onclick="upCust('101636','WANDO TERMINAL')">Name</a> &nbsp;</td>
<td colspan="3" nowrap>
<input name="fld4-3" id="fld4-3" type="text" size="30" maxlength="30" value="WANDO TERMINAL" />


</td>
<td align=right nowrap>Appt Date</td>
<td nowrap>
<input name="fld38-3" class="datepick appt_pickup" stop="3" evtext="" id="fld38-3" type="text" size="6" maxlength="8" value="" onchange="domdy(this);">
</td>
<td align=right nowrap>Appt Time</td>
<td nowrap>
<input name="fld39-3" id="fld39-3" class="time appt_pickup" stop="3" type="text" size="5" maxlength="5" value="" onchange="dohms(this);"> <!--onKeyDown="javascript:return dFilter (event.keyCode, this, '##:##');" /> -->
&nbsp;<span id="appt_pickup_3_ico"></span>
</td>
<td align="right">
&nbsp;
Hours </td><td nowrap> <input name="fld61-3" id="fld61-3" type=text size="8" value="">
&nbsp;&nbsp;&nbsp;&nbsp;
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld27-3" id="fld27-3" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld21-3" id="fld21-3" type="text" size="30" maxlength="30" value="400 LONGPOINT RD" />
</td>
<td align=right nowrap>Arrival Date </td>
<td nowrap>
<input name="fld10-3" type="text" id="fld10-3" class="datepick late_pu" evtext="" stop="3" value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>Arrival Time</td>
<td nowrap>
<input name="fld11-3" type="text" id="fld11-3" class="time late_pu" stop="3"  value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
&nbsp;<span id="late_pu_3_ico"></span>
<td align="right">&nbsp;&nbsp;Weight</td>
<td>
<input name="fld47-3" id="fld47-3" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld47-3');" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld29-3" id="fld29-3" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align=right>Address &nbsp;</td>
<td colspan="3">
<input name="fld22-3" id="fld22-3" type="text" size="30" maxlength="30" value="" />
</td>
<td align=right nowrap>Departed Date</td>
<td nowrap>
<input name="fld15-3" type="text" id="fld15-3" class=datepick value="" size="6" maxlength="8" onchange="domdy(this);do_seqs();">
</td>
<td align=right nowrap>&nbsp;&nbsp;Departed Time </td>
<td nowrap>
<input name="fld16-3" type="text" id="fld16-3" class=time value="" size="5" maxlength="5" onchange="dohms(this);do_seqs();">
</td>
<td align=right>Pieces</td>
<td>
<input name="fld45-3" id="fld45-3" type="text" size="5" maxlength="5" value="" onChange="ChkNAN(this,'fld45-3',3);"/>
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld30-3" id="fld30-3" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>

</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align=right>City &nbsp;</td>
<td colspan="7" nowrap>
<input name="fld5-3" id="fld5-3" type="text" size="15" maxlength="20" value="MOUNT PLEASANT" />

&nbsp;&nbsp;State &nbsp;
<input type=text name="fld6-3" id="fld6-3" size=2 maxlength=2 value="SC">
&nbsp;&nbsp;Zip &nbsp;
<input name="fld23-3" id="fld23-3" type="text" size="6" maxlength="6" value="29464" onChange="ChkDigit(this,this.value);" >
<!--onKeyDown="javascript:return dFilter (event.keyCode, this, '######);" />-->

&nbsp;&nbsp;&nbsp;Received by
<input name="fld17-3" id="fld17-3" type="text" size="20" maxlength="30" value="" />
</td>
<td align=right>Pallets</td>
<td>
<input name="fld46-3" id="fld46-3" type="text" size="2" maxlength="5" value="" onChange="NAN(this, fld46-3);" />
</td>

<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld33-3" id="fld33-3" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld24-3" id="fld24-3" type="text" size="14" maxlength="14" value="(*************"> </td>
<td>&nbsp;</td>
<td>Comments </td>
<td colspan="6">
<input name="fld67-3" id="fld67-3" type="text" size="60" maxlength="60" value="" />
</td>
<td>Reference&nbsp;</td>
<td>
<!--
DDD - added onchange handler using dup59_pickstops() due to job 20865
-->
<input name="fld34-3" id="fld34-3" type="text" size="20" maxlength="20" value="" onchange="dup59_pickstops(this);" />
</td>
</tr>
<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld65-3" id="fld65-3" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld68-3" id="fld68-3" type="text" size="60" maxlength="60" value="" />
</td>
<!-- td colspan="2">&nbsp;</td -->
<!-- Account# added 20872 hm ******** -->
<td>Account #&nbsp;</td>
<td>
<input name="fld19-3" id="fld19-3" type="text" size="20" maxlength="6" readonly value="101636" />
</td>
</tr>

<tr>
<td colspan=2>&nbsp;</td>
<td align="right">Phone &nbsp;</td><td>
<input name="fld66-3" id="fld66-3" type="text" size="14" maxlength="14" value=""> </td>
<td>&nbsp;</td>
<td>Comments</td>
<td colspan="6">
<input name="fld69-3" id="fld69-3" type="text" size="60" maxlength="60" value="" />
</td>
<td colspan="2" align=center><nobr>
<span class=insdel>
<input type="button" value="Insert Above" class=insbutt onclick="$('#copy3').val('187169');checkForm();">
<input type="button" value="Delete Location" class=delbutt onclick="$('#delete3').val('3');rusure(3);">

<input type=hidden id=copy3 name=copy3>
<input type=hidden id=delete3 name=delete3>
</span>

</td>
</tr>

<tr>
<td colspan="14"><hr width="90%" size="2" color="#999999" align="center"></td>
</tr>

	</table></td></tr>

<input type="hidden" id="max" name=max value="3">
<input type="hidden" id="new" name=new value="">

<tr>
<td colspan="4" align="middle" width="100%">

<table class="tab" bordercolorlight="white" bordercolordark="white" bgcolor="#ffffff" border="0" bordercolor="#000000" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td colspan="5" align="middle" class="headrow">&nbsp;</td>
</tr>
<tr>
<td align="center">
<table><tr>
<td align="center"><span id=buttonrow2>
<input type="button" value="Save" onclick="checkForm();">
</span></td>
<td align="center">
<span id=fboxmenu3>
<input type="button" value="Back" onclick="history.go(-1);">
<input type="button" value="Home" onclick="dohome();">
</span></td>
</tr></table>
</td></tr></table>
</td>
</tr>


</tbody>
</table>
</td>
</tr>
</tbody>
</table>


<!-- START CLOSING bkb styling +++++++++++++++++++++++++++++++++++++++++++ -->
</td>
</tr>
</table>
<!-- END CLOSING bkb styling +++++++++++++++++++++++++++++++++++++++++++ -->

</form>

<script language="Javascript" type="text/javascript">

function autosuggests()
{

var options200 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 2;
		applylocationdata(0, obj.id);
//		eval("document.Web.fld4-1.focus();");
//		eval("document.Web.fld4-1.select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-1', options200);


var options201 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 4-2;
		applylocationdata(1, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-2', options201);


var options202 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 202;
		applylocationdata(2, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-3', options202);


var options203 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 203;
		applylocationdata(3, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-4', options203);


var options204 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 204;
		applylocationdata(4, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-5', options204);


var options205 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 205;
		applylocationdata(5, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-6', options205);


var options206 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 206;
		applylocationdata(6, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-7', options206);


var options207 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 207;
		applylocationdata(7, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-8', options207);


var options208 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 208;
		applylocationdata(8, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-9', options208);


var options209 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 209;
		applylocationdata(9, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-10', options209);


var options210 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 210;
		applylocationdata(10, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-11', options210);


var options211 = {
	script: function(str) {
//		releaselock(0);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 211;
		applylocationdata(11, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-12', options211);

var options211a = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(12, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-13', options211a);

var options211b = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(13, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-14', options211b);

var options211c = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(14, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-15', options211c);

var options211d = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(15, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-16', options211d);

var options211d = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(16, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-17', options211d);

var options211e = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(17, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-18', options211e);

var options211f = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(18, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-19', options211f);

var options211g = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(19, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-20', options211g);

var options211h = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(20, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-21', options211h);

var options211i = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(21, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-22', options211i);

var options211j = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(22, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-23', options211j);

var options211k = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(23, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-24', options211k);

var options211l = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(24, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-25', options211l);

var options211m = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-26', options211m);

var options211n = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(26, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-27', options211n);

var options211o = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(27, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-28', options211o);

var options211p = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(28, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-29', options211p);

var options211q = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(29, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-30', options211q);

var options211r = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(30, obj.id);
		return true;
	},
	requireselect: false,
	json: false
}
var as = new bsn.AutoSuggest('fld4-31', options211r);

////
////
////


// from city
var options340 = {
	script: function(str) {
		fldid = $(document.activeElement).attr("id");
		return "/route.php?name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&prcnam=brwcitzip&repid=mmhd50&brwname=brwczip&fnname=zipcodes&index=E&type=CS&ufld=1&isnum=N&id=" + fldid;

	},
	varname: "name",
	delay: 10,
	timeout: 50000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var ix = fldid.split("-" );
		var i = ix[1];
		var d = obj.id.split("|");
		$('#fld5-'+i).val( d[0] );
		$('#fld6-'+i).val( d[1] );
		$('#fld23-'+i).val( d[2] );
		return true;
	},
	json: false
};

var options341 = options340;
var options342 = options340;
var options343 = options340;
var options344 = options340;
var options345 = options340;
var options346 = options340;
var options347 = options340;
var options348 = options340;
var options349 = options340;
var options350 = options340;
var options351 = options340;
var options340a = options340;
var options341a = options340;
var options342a = options340;
var options343a = options340;
var options344a = options340;
var options345a = options340;
var options346a = options340;
var options347a = options340;
var options348a = options340;
var options349a = options340;
var options350a = options340;
var options351a = options340;
var options340c = options340;
var options341c = options340;
var options342c = options340;
var options343c = options340;
var options344c = options340;
var options345c = options340;
var options346c = options340;

var as = new bsn.AutoSuggest('fld5-1', options340);  // city
var as = new bsn.AutoSuggest('fld5-2', options341);  // city
var as = new bsn.AutoSuggest('fld5-3', options342);  // city
var as = new bsn.AutoSuggest('fld5-4', options343);  // city
var as = new bsn.AutoSuggest('fld5-5', options344);  // city
var as = new bsn.AutoSuggest('fld5-6', options345);  // city
var as = new bsn.AutoSuggest('fld5-7', options346);  // city
var as = new bsn.AutoSuggest('fld5-8', options347);  // city
var as = new bsn.AutoSuggest('fld5-9', options348);  // city
var as = new bsn.AutoSuggest('fld5-10', options349);  // city
var as = new bsn.AutoSuggest('fld5-11', options350);  // city
var as = new bsn.AutoSuggest('fld5-12', options351);  // city
var as = new bsn.AutoSuggest('fld5-13', options340a);  // city
var as = new bsn.AutoSuggest('fld5-14', options341a);  // city
var as = new bsn.AutoSuggest('fld5-15', options342a);  // city
var as = new bsn.AutoSuggest('fld5-16', options343a);  // city
var as = new bsn.AutoSuggest('fld5-17', options344a);  // city
var as = new bsn.AutoSuggest('fld5-18', options345a);  // city
var as = new bsn.AutoSuggest('fld5-19', options346a);  // city
var as = new bsn.AutoSuggest('fld5-20', options347a);  // city
var as = new bsn.AutoSuggest('fld5-21', options348a);  // city
var as = new bsn.AutoSuggest('fld5-22', options349a);  // city
var as = new bsn.AutoSuggest('fld5-23', options350a);  // city
var as = new bsn.AutoSuggest('fld5-24', options351a);  // city
var as = new bsn.AutoSuggest('fld5-25', options340c);  // city
var as = new bsn.AutoSuggest('fld5-26', options341c);  // city
var as = new bsn.AutoSuggest('fld5-27', options342c);  // city
var as = new bsn.AutoSuggest('fld5-28', options343c);  // city
var as = new bsn.AutoSuggest('fld5-29', options344c);  // city
var as = new bsn.AutoSuggest('fld5-30', options345c);  // city
var as = new bsn.AutoSuggest('fld5-31', options346c);  // city

// from zip
var options440 = {
	script: function(str) {
		fldid = $(document.activeElement).attr("id");
		return "/route.php?name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&prcnam=brwcitzip&repid=mmhd50&brwname=brwczip&fnname=zipcodes&index=B&type=ZIP&ufld=1&isnum=N&id=" + fldid;

	},
	varname: "name",
	delay: 10,
	minchars: 1,
	timeout: 50000,
	cache: false,
	shownoresults: false,
	divwidth: 3,
	callback: function(obj) {
		var ix = fldid.split("-" );
		var i = ix[1];
		var d = obj.id.split("|");
		$('#fld5-'+i).val( d[0] );
		$('#fld6-'+i).val( d[1] );
		$('#fld23-'+i).val( d[2] );
		fldid = "";
		return true;
	},
	requireselect: true,
	json: false
};

var options441 = options440;
var options442 = options440;
var options443 = options440;
var options444 = options440;
var options445 = options440;
var options446 = options440;
var options447 = options440;
var options448 = options440;
var options449 = options440;
var options450 = options440;
var options451 = options440;
var options440a = options440;
var options441a = options440;
var options442a = options440;
var options443a = options440;
var options444a = options440;
var options445a = options440;
var options446a = options440;
var options447a = options440;
var options448a = options440;
var options449a = options440;
var options450a = options440;
var options451a = options440;
var options440c = options440;
var options441c = options440;
var options442c = options440;
var options443c = options440;
var options444c = options440;
var options445c = options440;
var options446c = options440;

var as = new bsn.AutoSuggest('fld23-1', options440);  // zip
var as = new bsn.AutoSuggest('fld23-2', options441);  // zip
var as = new bsn.AutoSuggest('fld23-3', options442);  // zip
var as = new bsn.AutoSuggest('fld23-4', options443);  // zip
var as = new bsn.AutoSuggest('fld23-5', options444);  // zip
var as = new bsn.AutoSuggest('fld23-6', options445);  // zip
var as = new bsn.AutoSuggest('fld23-7', options446);  // zip
var as = new bsn.AutoSuggest('fld23-8', options447);  // zip
var as = new bsn.AutoSuggest('fld23-9', options448);  // zip
var as = new bsn.AutoSuggest('fld23-10', options449);  // zip
var as = new bsn.AutoSuggest('fld23-11', options450);  // zip
var as = new bsn.AutoSuggest('fld23-12', options451);  // zip
var as = new bsn.AutoSuggest('fld23-13', options440a);  // zip
var as = new bsn.AutoSuggest('fld23-14', options441a);  // zip
var as = new bsn.AutoSuggest('fld23-15', options442a);  // zip
var as = new bsn.AutoSuggest('fld23-16', options443a);  // zip
var as = new bsn.AutoSuggest('fld23-17', options444a);  // zip
var as = new bsn.AutoSuggest('fld23-18', options445a);  // zip
var as = new bsn.AutoSuggest('fld23-19', options446a);  // zip
var as = new bsn.AutoSuggest('fld23-20', options447a);  // zip
var as = new bsn.AutoSuggest('fld23-21', options448a);  // zip
var as = new bsn.AutoSuggest('fld23-22', options449a);  // zip
var as = new bsn.AutoSuggest('fld23-23', options450a);  // zip
var as = new bsn.AutoSuggest('fld23-24', options451a);  // zip
var as = new bsn.AutoSuggest('fld23-25', options440c);  // zip
var as = new bsn.AutoSuggest('fld23-26', options441c);  // zip
var as = new bsn.AutoSuggest('fld23-27', options442c);  // zip
var as = new bsn.AutoSuggest('fld23-28', options443c);  // zip
var as = new bsn.AutoSuggest('fld23-29', options444c);  // zip
var as = new bsn.AutoSuggest('fld23-30', options445c);  // zip
var as = new bsn.AutoSuggest('fld23-31', options446c);  // zip


///////////
///////////

//state 1
var options220 = {
	script: function(str) {
	var key=document.getElementById("fld6-1").value + "|" + document.getElementById("fld5-1").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 220;
		applycs(0, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-1', options220);//state

//state
var options221 = {
	script: function(str) {
	var key=document.getElementById("fld6-2").value + "|" + document.getElementById("fld5-2").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 221;
		applycs(1, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-2', options221);//state


//state
var options222 = {
	script: function(str) {
	var key=document.getElementById("fld6-3").value + "|" + document.getElementById("fld5-3").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 222;
		applycs(2, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-3', options222);//state


//state
var options223 = {
	script: function(str) {
	var key=document.getElementById("fld6-4").value + "|" + document.getElementById("fld5-4").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 223;
		applycs(3, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-4', options223);//state


//state
var options224 = {
	script: function(str) {
	var key=document.getElementById("fld6-5").value + "|" + document.getElementById("fld5-5").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 224;
		applycs(4, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-5', options224);//state


//state
var options225 = {
	script: function(str) {
	var key=document.getElementById("fld6-6").value + "|" + document.getElementById("fld5-6").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 225;
		applycs(5, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-6', options225);//state


//state
var options226 = {
	script: function(str) {
	var key=document.getElementById("fld6-7").value + "|" + document.getElementById("fld5-7").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 226;
		applycs(6, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-7', options226);//state


//state
var options227 = {
	script: function(str) {
	var key=document.getElementById("fld6-8").value + "|" + document.getElementById("fld5-8").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 227;
		applycs(7, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-8', options227);//state


//state
var options228 = {
	script: function(str) {
	var key=document.getElementById("fld6-9").value + "|" + document.getElementById("fld5-9").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 228;
		applycs(8, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-9', options228);//state


//state
var options229 = {
	script: function(str) {
	var key=document.getElementById("fld6-10").value + "|" + document.getElementById("fld5-10").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 229;
		applycs(9, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-10', options229);//state


//state
var options230 = {
	script: function(str) {
	var key=document.getElementById("fld6-11").value + "|" + document.getElementById("fld5-11").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 230;
		applycs(10, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-11', options230);//state


//state
var options231 = {
	script: function(str) {
	var key=document.getElementById("fld6-12").value + "|" + document.getElementById("fld5-12").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		var fldnum = 231;
		applycs(11, obj.id);
//		eval("document.Web.fld" + fldnum + ".focus();");
//		eval("document.Web.fld" + fldnum + ".select();");
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-12', options231);//state

//state
var options222a = {
	script: function(str) {
	var key=document.getElementById("fld6-13").value + "|" + document.getElementById("fld5-13").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(12, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-13', options222a);//state

//state
var options223a = {
	script: function(str) {
	var key=document.getElementById("fld6-14").value + "|" + document.getElementById("fld5-14").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(13, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-14', options223a);//state

//state
var options224a = {
	script: function(str) {
	var key=document.getElementById("fld6-15").value + "|" + document.getElementById("fld5-15").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(14, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-15', options224a);//state

//state
var options225a = {
	script: function(str) {
	var key=document.getElementById("fld6-16").value + "|" + document.getElementById("fld5-16").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(15, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-16', options225a);//state


//state
var options226a = {
	script: function(str) {
	var key=document.getElementById("fld6-17").value + "|" + document.getElementById("fld5-17").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(16, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-17', options226a);//state

//state
var options227a = {
	script: function(str) {
	var key=document.getElementById("fld6-18").value + "|" + document.getElementById("fld5-18").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(17, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-18', options227a);//state

//state
var options228a = {
	script: function(str) {
	var key=document.getElementById("fld6-19").value + "|" + document.getElementById("fld5-19").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(18, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-19', options228a);//state

//state
var options229a = {
	script: function(str) {
	var key=document.getElementById("fld6-20").value + "|" + document.getElementById("fld5-20").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(19, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-20', options229a);//state


//state 1
var options220b = {
	script: function(str) {
	var key=document.getElementById("fld6-21").value + "|" + document.getElementById("fld5-21").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(20, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-21', options220b);//state



var options221b = {
	script: function(str) {
	var key=document.getElementById("fld6-22").value + "|" + document.getElementById("fld5-22").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(21, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-22', options221b);//state

//state
var options222b = {
	script: function(str) {
	var key=document.getElementById("fld6-23").value + "|" + document.getElementById("fld5-23").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(22, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-23', options222b);//state

//state
var options223b = {
	script: function(str) {
	var key=document.getElementById("fld6-24").value + "|" + document.getElementById("fld5-24").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(23, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-24', options223b);//state

//state
var options224b = {
	script: function(str) {
	var key=document.getElementById("fld6-25").value + "|" + document.getElementById("fld5-25").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(24, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-25', options224b);//state

//state
var options225b = {
	script: function(str) {
	var key=document.getElementById("fld6-26").value + "|" + document.getElementById("fld5-26").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-26', options225b);//state


//state
var options226b = {
	script: function(str) {
	var key=document.getElementById("fld6-27").value + "|" + document.getElementById("fld5-27").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(26, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-27', options226b);//state

//state
var options227b = {
	script: function(str) {
	var key=document.getElementById("fld6-28").value + "|" + document.getElementById("fld5-28").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(27, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-28', options227b);//state

//state
var options228b = {
	script: function(str) {
	var key=document.getElementById("fld6-29").value + "|" + document.getElementById("fld5-29").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(28, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-29', options228b);//state

//state
var options229b = {
	script: function(str) {
	var key=document.getElementById("fld6-30").value + "|" + document.getElementById("fld5-30").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(29, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-30', options229b);//state


//state
var options230b = {
	script: function(str) {
	var key=document.getElementById("fld6-31").value + "|" + document.getElementById("fld5-31").value;
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + key;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applycs(30, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld6-31', options230b);//state

}
</script>

</div>
</body>
</html>
