<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<title>Load Page - Aljex Vision</title>

<meta NAME="v2page" CONTENT="load">
<meta NAME="v2head" CONTENT="new">

<meta NAME="saferwatch" CONTENT="load">
<meta NAME="laneprice" CONTENT="load">

<script type="text/javascript" src="/common/import.min.js"></script>
<script type="text/javascript" src="/common/v2_loader.js"></script>

<link rel="stylesheet" href="/fpmerge/load/load.css">
<link rel="stylesheet" href="/common/tabs3.css">

<script type="text/javascript" src="/fpmerge/load/init_2.2.js?v=2.2.2504.000"></script>
<script type="text/javascript" src="/fpmerge/load/base_2.2.js?v=2.2.2504.000"></script>
<script type="text/javascript" src="/fpmerge/load/foot_2.2.js?v=2.2.2504.000"></script>
<script type="text/javascript" src="/fpmerge/load/jobs_2.2.js?v=2.2.2504.000"></script>

<script async type="text/javascript" src="//maps.googleapis.com/maps/api/js?client=gme-thedescartessystems&loading=async&callback=Function.prototype"></script>

<!-- mbl #16679 08252015 -->
<script language="javascript" src="/common/vision_wd.js"></script>

<script>

// bmw 021017 default error handler
// bmw 070721 allow js errors to occur if anro
if ("gj56nb24" !== "bj262j10")
{
	window.onerror = function(msg, url, line, col, error) {
		return true;
	};
}

/* bmw 102418 stop throwing all these ajax calls
var target = "/route.php?prcnam=erremail" +
"&msg="  + msg + "&url=" + url + "&line=" + line +
"&col=" + col + "&error=" + error +
"&qual=gj56nb24&name=mmhd50&pro=86873" +
"&validate=N";
$.ajax(target);
*/

</script>

</head>

<!--- html template: load/load_2.2.htm --->
<body style="background:#FFFFFF;">

<input type=hidden id=do_saferwatch>

<div align="center">

<table cellpadding=0 cellspacing=0 border="0" width=100% bgcolor="#ffffff" align=center>
<tr>
<td>
<!-- END OPENING bkb styling #############################################-->

<div id="page_header" style="height:47px"></div>
<input type=hidden id=user_perms value="YYYYYNYYYNYYNNNYYYYYYYYNYYYYNYYYYNYYYYYYYYN|mmhd|MLM SUPPLY CHAIN LLC|mmhd50|prod-50.aljex.com|<EMAIL>|KIT|DRUM

">
<input type=hidden id=newheader value=true>

<input type=hidden id=page_version value="2.2">

<input type=hidden name=oldvalue id=oldvalue value="700.00">

<input type=hidden name=reldate id=relby value="">

<input type=hidden id=mask_drivers_cell value=" " /> <!-- DDD 050516 job 23022 -->
<input type=hidden id=4kites_tracking value="N" /> <!-- DDD 050516 job 23021 -->

<!-- Craig 030117 job 27167 SaferWatch -->
<input type=hidden name=includingScript id=includingScript value="load">
<input type="hidden" id="saferwatch_enabled" name="saferwatch_enabled" value="">

<input type="hidden" id="type" value=" ">

<input type=hidden name=advmsg id=advmsg value="" />       <!-- hjw 082618 #32179   -->

<form name=hell2 id=hell2 method=post action=/route.php>
<input type=hidden name=prcnam value="fmfunc">
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=item value="">
<input type=hidden name=itemnew value="">
<input type=hidden name=pro value="86873">
<input type=hidden name=sys value="3a">
<input type=hidden name=type>
</form>

<form name="web" method="post" action="/route.php">
		<input name="prcnam" value="forms" type="hidden">
		<input name="qual" id="qual" value="gj56nb24" type="hidden">
		<input name="name" id="name" value="mmhd50" type="hidden">
		<input name="c_tok" id="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW" type="hidden">
		<input name="type" type="hidden">
		<input name="sys" id="sys" value="3a" type="hidden">
		<input name="ctlrec" value="  131119" type="hidden">
		<input name="ctlval" value="" type="hidden">
<input type=hidden name="webprint" value="">
<input type=hidden name=pro value="86873">
<input type=hidden name="repno" value="">
<input type=hidden name="thirdparty" value="">
<input type=hidden name=origin>
<input type=hidden name=destination>
<input type=hidden name=carrid>
<input type=hidden name=ten4url id=ten4url>
</form>

<input type=hidden id=shortcut_help>

<input type=hidden name="cover" id="cover" value="">

<form name=formgo action=/route.php method=post target="_blank">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=sys value="3a">
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name=pro>
<input type=hidden name=prcnam value="t3aretin">
<INPUT TYPE="hidden" NAME="ctlval" VALUE="">
<INPUT TYPE="hidden" NAME="ctlrec" VALUE="  131119">
</form>

<form name=doform method=post action=/route.php>
<input type=hidden name=ctlrec value="  131119">
<input type=hidden name=ctlval value="">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=pro value="86873">
<input type=hidden name=prorecno value=2293>
<input type=hidden name=prcnam value=forms>
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="sys" value=3a>
<input type=hidden name=dtyp>
<input type=hidden name=webprint>
<input type=hidden name=type>
</form>

<form name=lcrate method=post action=/route.php>
<input type=hidden name="qual" value=gj56nb24>
<input type=hidden name="type" value=leastcost>
<input type=hidden name="prcnam" value=leastcost>
<input type=hidden name="pro" value="86873">
<input type=hidden name=prorecno value=2293>
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
	<input type=hidden name="ctlrec" value="  131119">
	<input type=hidden name="ctlval" value="">
<input type="hidden" name="sys" value=3a>
</form>

<form name=intlrate method=post action=/route.php>
<input type=hidden name="qual" value=gj56nb24>
<input type=hidden name="type" value=intlrate>
<input type=hidden name="prcnam" value=intlrate>
<input type=hidden name="pro" value="86873">
<input type=hidden name=prorecno value=2293>
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
	<input type=hidden name="ctlrec" value="  131119">
	<input type=hidden name="ctlval" value="">
<input type="hidden" name="sys" value=3a>
</form>

<form name=dostuff method=post action=/route.php>
<input type=hidden name=ctlrec value="  131119">
<input type=hidden name=ctlval value="">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=pro value="86873">
<input type=hidden name=prorecno value=2293>
<input type=hidden name=prcnam>
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name="sys" value="3a">
<input type=hidden name=webprint>
<input type=hidden name=type>
<input type=hidden name=refresh>
</form>

<form name=tagroute action=/route.php method=post target="_top">
<input type=hidden name=qual value="gj56nb24">
<input type="hidden" name="sys" value="3a">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=pro value="86873">
<input type=hidden name=fld26 value="86873">
<input type=hidden name=prcnam>
<input type=hidden name=type>
<input type=hidden name=webprint>
<input type=hidden name=custid>
<input type=hidden name=carid>
<input type=hidden name=goback>
<input type=hidden name=docdir>
<input type=hidden name=notenum>
<input type=hidden name=prorecno value=2293>
<input type=hidden name=docdir>
<input type=hidden name=tcorgzip>
<input type=hidden name=tcdstzip>
<input type=hidden name=tcequipt>
<!-- Craig 082015 job 16728 Lane Pricing, added next four lines again...sigh -->
<input type=hidden name=originCity>
<input type=hidden name=originState>
<input type=hidden name=destinationCity>
<input type=hidden name=destinationState>

<input type=hidden name=nrpt>

</form>


<form name=coverdrv action=/route.php method=post target="_top">
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name=sys value="3a">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=pro value="86873">
<input type=hidden name=prcnam value="triplegs">
<input type=hidden name=type value="save">
<input type=hidden name=no_triplegs value='N'>
<input type=hidden name=no_tripleg_browse value='Y'>
<input type=hidden name=fld11>
<input type=hidden name=fld112>
<input type=hidden name=fld122>
<input type=hidden name=fld126>
<input type=hidden name=fld127>
<input type=hidden name=fld128>
<input type=hidden name=fld13>
<input type=hidden name=fld130>
<input type=hidden name=fld131>
<input type=hidden name=fld140>
<input type=hidden name=fld141>
<input type=hidden name=fld142>
<input type=hidden name=fld2>
<input type=hidden name=fld23>
<input type=hidden name=fld231>
<input type=hidden name=fld232>
<input type=hidden name=fld233>
<input type=hidden name=fld234>
<input type=hidden name=fld235>
<input type=hidden name=fld24>
<input type=hidden name=fld25>
<input type=hidden name=fld63>
<input type=hidden name=fld64>
<input type=hidden name=fld65>
<input type=hidden name=fld97>

<input type="hidden" name="drvnamex">
<input type="hidden" name="fld191x">
<input type="hidden" name="drv231x">
<input type="hidden" name="fld89x">
<input type="hidden" name="drv232x">
<input type="hidden" name="drv233x">
<input type="hidden" name="drv234x">
<input type="hidden" name="drv235x">
<input type="hidden" name="drv130x">
<input type="hidden" name="drv131x">
<input type="hidden" name="drv126x">
<input type="hidden" name="drv127x">
<input type="hidden" name="drv128x">

</form>

<form name=doinv method=post action=/route.php target="_blank">
<input type=hidden name=prcnam value="genpod">
<input type=hidden name=ctlrec value="  131119">
<input type=hidden name=ctlval value="">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=pro value="86873">
<input type=hidden name=gonote value="86873">
<input type=hidden name=prorecno value=2293>
<input type=hidden name=preprint value="yes">
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name="sys" value="3a">
<input type=hidden name=invoice value=invoice>
<input type=hidden name=type value=invoice>
</form>

<form name=upinv method=post action=/route.php target="_blank">
<input type=hidden name=ctlrec value="  131119">
<input type=hidden name=ctlval value="">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name="sys" value="3a">
<input type=hidden name=pro value="86873">
<input type=hidden name=prorecno value=2293>
<input type=hidden name=prcnam value="upinvoice">
<input type=hidden name=inv1 value=invoice>
</form>

<form name=upcust method=post action=/route.php target="_blank">
<input type=hidden name=prcnam value="addcust">
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="sys" value="3a">
<input type=hidden name=name value=mmhd50>
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=company value="mmhd50">
<input type=hidden name="c_tok2" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=type value="update">
<input type=hidden name=dtyp>
<input type=hidden name=relog>
<input type=hidden name=custid>

</form>

<form name=upcar method=post action=/route.php target="_blank">
<input type=hidden name=prcnam value=addcar>
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="sys" value="3a">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=relog>
<input type=hidden name=type value="update">
<input type=hidden name=carid>
</form>


<!-- <form name=mapshpment method="post" action="/cgi-bin/mapshipment" target="_blank"> -->
<form name=mapshpment method="post" action="/mapshipment.php" target="_blank">
<input type="hidden" name="pro" value=  86873 />
<input type="hidden" name="qual" value=gj56nb24 />
</form>

<form method=post name=form9a action=/route.php>
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="sys" value="3a">
<input type=hidden name=name value="mmhd50">
<input type=hidden name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=company value=mmhd50>
<input type=hidden name="c_tok2" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=prcnam value="manifest">
<input type=hidden name=type value="listone">
<input type=hidden name=mfno value="      ">
</form>

<form name=main id=main method=post action=/route.php>
<input type=hidden name="qual" value="gj56nb24">
<input type=hidden name="type" value="save">
<input type=hidden name="prcnam" value="t3atagexa">
<input type=hidden name="pro" id="pro" value="86873">
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
	<input type=hidden name="ctlrec" value="  131119">
	<input type=hidden name="ctlval" value="">
<input type="hidden" name="sys" value="3a">
<input type=hidden name=fld170 id=pacct value="101636">
<input type=hidden name=fld578 id=fld578 value="">
<input type=hidden name=fld17 id=fld17 value="N">
<input type=hidden name=fld171 id=fld171 value="100002">
<input type=hidden name=fld55 id=fld55 value="">
<input type=hidden name=fld528 id=fld528 value="USD">
<!-- <input type=hidden name=fld43 id=fld43 value="WANDO TERMINAL"> -->
<input type=hidden name=fld72 id=cacct value="101636">
<input type=hidden name=fld1 id=cuacct value="100002">
<input type=hidden name=fld4 id=cuaddr2 value="">
<input type=hidden name=reprn id=reprn>
<input type="hidden" name="fld861x">
<input type="hidden" name="fld586x">
<input type="hidden" name="fld2x">
<input type="hidden" name="fld169x">
<input type="hidden" name="fld30x">
<input type="hidden" name="fld29x">
<input type="hidden" name="fld578x">
<input type="hidden" name="fld62x">
<input type="hidden" name="fld529x">
<input type="hidden" name="fld65x">
<input type="hidden" name="fld104x">
<input type="hidden" name="fld866x">
<input type="hidden" name="fld906x">
<input type="hidden" name="fld32x">
<input type="hidden" name="fld10x">
<input type="hidden" name="fld33x">
<input type="hidden" name="fld41x">
<input type="hidden" name="fld34x">
<input type="hidden" name="fld70x">
<input type="hidden" name="flda1x">
<input type="hidden" name="flda2x">
<input type="hidden" name="flda5x">
<input type="hidden" name="fldb1x">
<input type="hidden" name="fldb2x">
<input type="hidden" name="fldb5x">
<input type="hidden" name="fldc1x">
<input type="hidden" name="fldc2x">
<input type="hidden" name="fldc5x">
<input type="hidden" name="fldd1x">
<input type="hidden" name="fldd2x">
<input type="hidden" name="fldd5x">
<input type="hidden" name="flde1x">
<input type="hidden" name="flde2x">
<input type="hidden" name="flde5x">
<input type="hidden" name="fldf1x">
<input type="hidden" name="fldf2x">
<input type="hidden" name="fldf5x">
<input type="hidden" name="fldg1x">
<input type="hidden" name="fldg2x">
<input type="hidden" name="fldg5x">
<input type="hidden" name="fldh1x">
<input type="hidden" name="fldh2x">
<input type="hidden" name="fldh5x">
<input type="hidden" name="fldi1x">
<input type="hidden" name="fldi2x">
<input type="hidden" name="fldi5x">
<input type="hidden" name="fldj1x">
<input type="hidden" name="fldj2x">
<input type="hidden" name="fldj5x">
<input type="hidden" name="fldcarrx">
<input type="hidden" name="drvnamex">
<input type="hidden" name="fld191x">
<input type="hidden" name="drv231x">
<input type="hidden" name="fld89x">
<input type="hidden" name="drv232x">
<input type="hidden" name="drv233x">
<input type="hidden" name="drv234x">
<input type="hidden" name="drv235x">
<input type="hidden" name="drv130x">
<input type="hidden" name="drv131x">
<input type="hidden" name="drv126x">
<input type="hidden" name="drv127x">
<input type="hidden" name="drv128x">

<input type="hidden" name="fld73" id="fld73" value="CLEVES LOGISTICS LLC">
<input type="hidden" name="drv2" id="drv2" value="">

<input type="hidden" name="wdate">
<input type="hidden" name="fld54">
<input type="hidden" name="fld145" id="fld145" value="">
<input type="hidden" name="dtime">
<input type="hidden" name="hdate">
<input type="hidden" name="fld53">
<input type="hidden" name="addate">
<input type="hidden" name="addtime">
<input type="hidden" name="avdate">
<input type="hidden" name="avtime">
<input type="hidden" name="ldate">
<input type="hidden" name="ltime">
<input type="hidden" name="fld148" id="fld148">
<input type="hidden" name="edate">
<input type="hidden" name="etime">
<input type="hidden" name="fld147">
<input type="hidden" name="fld191">
<input type="hidden" name="fld89">
<input type="hidden" name="fld177">
<input type="hidden" name="fld188">
<input type="hidden" name="fld189">
<input type=hidden name=fld158>

<input type=hidden name=docover>
<input type=hidden name=carsafe>
<input type=hidden name=lcr_cusrate>
<input type=hidden name=cvr_fld1>
<input type=hidden name=cvr_fld29>

<input type=hidden name=no_triplegs value='N'>
<input type=hidden name=no_tripleg_browse value='Y'>
<input type=hidden name=cvr_fld11>
<input type=hidden name=cvr_fld112>
<input type=hidden name=cvr_fld122>
<input type=hidden name=cvr_fld126>
<input type=hidden name=cvr_fld127>
<input type=hidden name=cvr_fld128>
<input type=hidden name=cvr_fld13>
<input type=hidden name=cvr_fld130>
<input type=hidden name=cvr_fld131>
<input type=hidden name=cvr_fld140>
<input type=hidden name=cvr_fld141>
<input type=hidden name=cvr_fld142>
<input type=hidden name=cvr_fld2>
<input type=hidden name=cvr_fld23>
<input type=hidden name=cvr_fld231>
<input type=hidden name=cvr_fld232>
<input type=hidden name=cvr_fld233>
<input type=hidden name=cvr_fld234>
<input type=hidden name=cvr_fld235>
<input type=hidden name=cvr_fld24>
<input type=hidden name=cvr_fld25>
<input type=hidden name=cvr_fld63>
<input type=hidden name=cvr_fld64>
<input type=hidden name=cvr_fld65>
<input type=hidden name=cvr_fld97>
<input type=hidden name=cvr_fld90>

<input type=hidden name=isfbox id=isfbox>
<input type=hidden name=refresh id=refresh value="">
<input type=hidden id=fbox2pro name=fbox2pro value="">

<input type=hidden id=fld881 name=fld881 value="N">

<input type=hidden name=macropoint id=macropoint value="Y">
<input type=hidden id=locaid value="N">
<input type=hidden id=fld883 name=fld883 value="">

<input type=hidden name=mp_ostart value="202512030000ET">
<input type=hidden name=mp_oend   value="202512032359ET">
<input type=hidden name=mp_dstart value="">
<input type=hidden name=mp_dend   value="">

<input type=hidden name=fld79 id=fld79 value="Y">
<input type=hidden name=fld80 id=fld80 value="">
<input type=hidden name=fld668 id=fld668 value="">
<input type=hidden name=fld116 id=fld116 value="">
<input type=hidden name=fld174 id=fld174 value="">
<input type=hidden name=fld175 id=fld175 value="">

<input type=hidden name=fld144 value="79686">

<input type=hidden name=relreqconf value="N">
<input type=hidden name=relreqcustconf value="N">

<input type=hidden name=rsvexpire value="10">
<input type=hidden id=showtab value="7">
<input type=hidden id=showproj name=showproj value=" ">
<input type=hidden name=hideinv value=" ">

<input type=hidden id=agent_sec name=agent_sec value="">
<input type=hidden id=event_mgmt name=event_mgmt value="Y">
<input type=hidden id=no_billto name=no_billto value=" ">
<input type=hidden id=inv_date name=inv_date value="        ">
<input type=hidden id=allow_range name=allow_range value=" ">
<input type=hidden id=enforce_seq name=enforce_seq value="N">
<input type=hidden id=fsctype name=fsctype value="">

<input type=hidden id=repsup value="Y">
<input type=hidden id=must_time name=must_time value=" ">
<input type=hidden id=tryon_features name=tryon_features value=" ">
<input type=hidden id=musthave_path name=musthave_path value=" ">
<input type=hidden id=epay_flags name=epay_flags value="NYY">
<input type=hidden id=ins_exp name=ins_exp value="NNNNNNNNNNNNNN">
<input type=hidden id=car_adv name=car_adv value="">
<input type=hidden id=uncov_allowed value="Y">
<input type=hidden id=must_prot name=must_prot value="">
<input type=hidden id=rel_nocustrate value="Y">
<input type=hidden id=rel_nocarrrate value="Y">

<input type=hidden id=military name=military>
<input type=hidden id=exp_dates name=exp_dates value="12/03/25||||||||||">

<input type=hidden id=appt_ready_od value=12/03/25>
<input type=hidden id=appt_ready_ot value=>
<input type=hidden id=appt_pickup_od value=12/03/25>
<input type=hidden id=appt_pickup_ot value=>
<input type=hidden id=appt_must_od value=>
<input type=hidden id=appt_deliv_od value=>
<input type=hidden id=appt_deliv_ot value=>

<input type=hidden id=disp_req value=" ">
<input type=hidden id=drvr_req value=" ">
<input type=hidden id=event_fbox>

<input type=hidden id=defaultloadtype value="BROKERAGE ">
<input type=hidden id=defaultweight value="">
<input type=hidden id=ckallcredit value="Y">
<input type=hidden id=fpwsys value="dx3aj6i">
<input type=hidden id=loadboards value="">
<input type=hidden id=ifdisabled value="">
<input type=hidden id=rateconfifopen value="Y">
<input type=hidden id=loadstatus name=loadstatus value="COVERED">
<input type=hidden id=fi value="3disp">
<input type=hidden id=payw1 value="1374985">
<input type=hidden id=payw4 value="">
<input type=hidden id=payw5 value="">
<input type=hidden id=payw6 value="">
<input type=hidden id=payw7 value="N">
<input type=hidden id=qx value="mmhd">
<input type=hidden id=mpoff value="">
<input type=hidden id=dispsec value="N">
<input type=hidden id=useroff value="  ">
<input type=hidden id=bypass value="Y">
<input type=hidden id=usertype value ="SUPER">
<input type=hidden id=oldzipp value="29464">
<input type=hidden id=oldzipc value="29464">
<input type=hidden id=unlim value="">
<input type=hidden id=today value="12/01/25">
<input type=hidden id=custommiler value=" ">
<input type=hidden id=cpactive value="N">
<input type=hidden id=cpuserls value="Y">
<input type=hidden id=carrrls value="N">

<input type=hidden id=seqgp value="">
<input type=hidden id=seqgq value="">
<input type=hidden id=seqgr value="">
<input type=hidden id=seqgs value="">
<input type=hidden id=seqgt value="">
<input type=hidden id=seqgu value="">

<input type=hidden id=podrefs value=" ">
<input type=hidden id=descfc value='<input type=text name=pld360 id=pld360 size=18 value="">'>
<input type=hidden id=descfd value='<input type=text name=pld361 id=pld361 size=18 value="">'>
<input type=hidden id=descfe value='<input type=text name=pld362 id=pld362 size=18 value="">'>
<input type=hidden id=descff value='<input type=text name=pld363 id=pld363 size=18 value="">'>
<input type=hidden id=descfg value='<input type=text name=pld364 id=pld364 size=18 value="">'>
<input type=hidden id=descfh value='<input type=text name=pld365 id=pld365 size=18 value="">'>

<input type=hidden id=fld110 value="">
<input type=hidden id=fld111 value="">
<input type=hidden id=fld114 value="">

<input type=hidden id=prorecno value="2293">
<input type=hidden id=rec_locking value="Y">
<input type=hidden id=commented value="<!--">
<input type=hidden id=advance_label value="Advance Payments">
<input type=hidden id=noosd value="">
<input type=hidden id=ifincmgmt value="style="display:none;"">
<input type=hidden id=using_portal value="">

<input type=hidden id=expand_pref name=expand_pref value="Y">

<input type=hidden id=multi_curr value=" ">
<input type=hidden id=cus_curr value="USD">
<input type=hidden id=car_curr value="">
<input type=hidden id=rep_curr value="USD">

<input type=hidden id=reserved_loads value=" ">

<input type=hidden id=controls value="|MLM SUPPLY CHAIN LLC|260 SEVEN FARMS DR|SUITE B|DANIEL ISLAND|SC|29492||(843) 825-7582||(843) 825-7582|(843) 825-7582|||10000.00||||.00|N|300000001||1443514|3910766|x||Y|2000|2000|1200|||||2100||60||VSI||6120|6150|4250||2005|4400||3800||4150|1230-00|5020-00|||6681|1000|4100|3000|3000|4125|7030|4445|4125||||100000|12/31/65|********|35169||||||||N|||||||<EMAIL>,<EMAIL>|||Y|Y|Y|||||1000.00|mmhd||||SHIP||||||||||N|N|N|Y|N|Y|P|Y||N|N|Y|N|Y|10||30|30|1||||||||365||N|N|Y|Y|Y|N|||11|12|||||Y|2|7||||N|N||DEMO|PASSWORD|DEMO|BROKERAGE|PASSWORD||||||||20|||||||||542049.002.00004|17$know$FRIENDS|/pix/mmhd/edi/dat/mmhddat.txt||||||||12/01/25|12/01/25|08/14/08 demo||||09/19/08 15:54:38|08/13/06 23:01:18|09/24/05 09:31:38|Y|||||||||||accountname|password||Y|||Y|Y|Y|Y|Y|N|Y|N|N||N||N|Y|Y|Y|Y|N|N|N|Y|Y|N|N|Y|N|N|90|Y|password|N|N|Y|Y|Y|Y|Y|Y|Y|N||Y|N|N|N|N|N|N|N|N||N|N|N|N|||N|N|Y|N|N|Y|Y|Y|Y|Y|N|N|Y|Y|N|Y|N|N||N|Y|Y|N|N|Y|Y||Y|260 SEVEN FARMS DR|SUITE B|DANIEL ISLAND|SC|29492||||||P.O. BOX 668||PURVIS|MS|39475|Y|Y|N|Y|Y|Y|Y||N|Y|||Y|Y|N|N|Y|N|Y|N|Y||Y|N|N|2|Y|N||N|N|Y|N|N|Y|Y|N|N|Y|N|N|N|||||N|N||Y||N|Y|Y|Y|N|Y|N|N|Y|||Y||Y||Y|N|N|Y|Y|N|N|N|P|N|N|N||A|N|N|N|Y|||Y|Y|N||N|Y||Y|N||N|N|||MMHD|NJ088732||Accounts Payable Aljex|Freight/Operating|Freight Expense|||Accounts Receivable Aljex|Freight/Operating|Services|||DISCOUNT|TRANSPORTATION||||Net 30||||||www.google.com||||Consultant|||Corporate|||||||||Y||Y|Y||N|||Y||||N|||||||||N||N|||||N||Y||||||||2|Y||Y|N||Y|||||||||N|||Y|Y|||||||||||||N||||||||||||||||Y||Y|||||Y||||||Y|N|Y|N||Y||N|Y||||Y|N|Y|N||||||Y||||||||||Y|||||BROKERAGE|||||||N|Y|Y|Y|Y|N|N|N|Y|Y|N|Y|Y|Y|N|N|N||||||||||||||||||||||||||||||||N|N|Y||||||N||N||N|||||||N||||||||Y||Y|N||||||||||||||||||||||||||||Y|||||Y|Y|Y|Y|Y|Y|||||||||||||2|2||Y|||Y|||Y|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||2.2|2.2||||||5|||||||||||||||||||||||||||||||||||||">

<input type=hidden id=imrlsd name=imrlsd value="">
<input type=hidden id=imrlsrecs name=imrlsrecs value="">

<input type=hidden id=metric_display value=" ">
<input type=hidden id=metric_perm value=" ">

<input type=hidden id=pickcnt value="2">
<input type=hidden id=stopcnt value="1">

<input type=hidden id=carr_haz_auth value=" ">
<input type=hidden id=carr_haz_expired value="">

<input type=hidden id=bill_hold value="Y">
<input type=hidden id=accting value="N">
<input type=hidden id=view_scans value="Y">
<input type=hidden id=update_declared_val value="Y">
<input type=hidden id=max_locks value="3">

<input type=hidden id=cust_revtype value=" ">

<input type=hidden id=carrier_host value="">
<input type=hidden id=carrier_start value="">

<input type=hidden id=upd_equip value="Y">
<input type=hidden id=upd_svcrep value="Y">
<input type=hidden id=empty_miles value=" ">

<input type=hidden id=fld193 name=fld193 value="">

<!-- bmw 042418 #31615 -->
<input type=hidden id=late_appt name=late_appt>
<input type=hidden id=arr_pu_date value="">
<input type=hidden id=arr_pu_time value="">
<input type=hidden id=arr_dst_date value="">
<input type=hidden id=arr_dst_time value="">

<input type=hidden id=last_upd value="251201020216">
<input type=hidden id=billable_wgt value=" ">			<!-- control 139 -->
<input type=hidden id=update_rates value=" "> 			<!-- control 238 -->

<input type=hidden id=perm_perm value="y">				<!-- permploy 139 -->

<input type=hidden id=is_watchdog value="">
<input type=hidden id=is_saferwatch value="">

<input type=hidden id=posttonu name=posttonu value="">
<input type=hidden id="satisonly" value="N">

<input type=hidden id=fld858 name=fld858>
<input type=hidden id=fld100 name=fld100>
<input type=hidden id=fld859 name=fld859>
<input type=hidden id=fld153 name=fld153>
<input type=hidden id=fld154 name=fld154>
<input type=hidden id=fld863 name=fld863>

<!-- <script>do_header();</script>-->	<!-- so page will populate top-down instead of jumping -->

<!-- END OPENING bkb styling #############################################-->

<!-- BUTTONS -->

<table border=0 align=center><tr>
<td align=center nowrap style="background:#FFFFFF;">

<span id=locktop1></span>

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

<font size=+1><span id=locktext1 style="color: #333399" cellpadding="0" cellspacing="0">
</span>
</font>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

<span id=buttonrow>
<input type="button" id="savebutt" name="savebutt" onClick="dosubmit('save');" value="Save" />
<input type=button value="Save + Home" onclick="dosubmit('saveback');"/>
<div id="lcr" style="display:none">
<input type=button class=butt name="rateit" id="rateit" value="Least Cost Routing" onclick="lccheck();">
</div>
<div id="intl" style="display:none">
<input type=button class=butt name="irateit" id="irateit" value="International Pricing" onclick="intlcheck();">
</div>
<div id="ss" style="display:none">
<input type=button class="butt ifsaved" name="ssearch" id="ssearch" value="Smart Search" onclick="doss('  86873','2293');">
</div>
<input type=button class="always ifsaved" id=psbutt onclick="extraps();" value="Picks 2 / Stops 1 ">
<div id="covuncov" style="display:inline">
<input type=button class="ifadv butt" name="doc" id="doc" value="Un-Cover" onclick="uncover(  86873,2293,'COVERED');">
</div>
<div id="release" style="display:none">
<input type=button class="butt ifsaved" name="rel" id="rel" value="Release" onclick="relcheck();">
</div>
<input type=button class="ifadv butt ifsaved" name="void" id="void1" value="Void" onclick="dovd(  86873,2293);">
<input type="button" class="always" value="Back" onclick="history.go(-1);">
<input type="button" class="always" value="Home" onclick="dohome();">
<div id="relhome" style="display:none">
<input type=button value="Release + Home" onclick="document.main.type.value='relhome'; relcheck();"/>
</div>
</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<font size=+1>
<span id=locktext style="color:#333399" cellpadding="0" cellspacing="0">
</span>

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span id=locktop2></span>

</font>

</td>

</tr></table>

<!-- END BUTTONS -->

<table class="tab" bordercolorlight="white" bordercolordark="white" bgcolor="#f0f0f0" border="2" bordercolor="#000000" cellpadding="0" cellspacing="0"> <!-- width="570"> -->
<tbody>

<tr><!-- %%%%%%%%%%%% -->
<td class="headrow">
<table width="100%" border="0" cellpadding=2 cellspacing=2>
<tr>
<td align=left> &nbsp; <font color="#000000">Pro #&nbsp;</font>
<span id="headpro" class="headidno">&nbsp;86873&nbsp;</span>
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
<font color='#000000'> </span></font></td>
<td align=center class="headcolor stitle"><span id=hesdeadjim></span></td>

<!-- jfh addition job # 11259 "added button"-->

<td align=right>
	<!-- removed 121914 per jh
<button type="button" onclick = holdShipment();dosubmit();>Hold Shipment!</button>
	-->
&nbsp; <font color="#000000"<b>Status &nbsp;</b></font>
<!--<span id="headlbl" class="headidno"><b>COVERED</b></span> &nbsp;-->
<span id="headlbl" class="headidno">&nbsp;<b>COVERED</b>&nbsp;</span> &nbsp; </td>
</td>

<!-- end jfh addition job # 11259 -->

</tr>
</table>
</td>
</tr><!-- %%%%%%%%%%%% -->

<tr>
<td align=center id="repeat_row" width=100%>
<div id="repeat_load" style="display:none">
<table  border=0 cellpadding=2 cellspacing=2>
<tr>
<td class=title> &nbsp;Enter Customer and States to Copy a Shipment &nbsp; &nbsp;</td>
<td>&nbsp; Customer</td>
<td><input  onfocus="hide();" onchange="this.style.backgroundColor='#FFFFFF';" type=text size=25 maxlength=20 name="rptcus" id="rptcus" tabindex=99></td>
<td align=right> &nbsp; Origin State</td>
<td><input  type=text size=2 maxlength=2 name="rptfst" id="rptfst" tabindex=99 onchange="this.style.backgroundColor='#FFFFFF';"></td>
<td align=right> &nbsp; Destination State</td>
<td><input  type=text size=2 maxlength=2 name="rpttst" id="rpttst" tabindex=99 onchange="this.style.backgroundColor='#FFFFFF';"></td>
<td> &nbsp; <input  type=button value="Search Loads" onclick="search();">&nbsp;</td>
</tr>
</table>

<iframe name=previous id=previous height=0 width=0 style="display:none">
<!--- html template: load/load_2.2.htm --->
		<html><head></head><body></body></html>
</iframe>

</div>  <!-- repeat_load -->
</td>
</tr>

<div style="display:none;" id="wrtchangedtxt" title="Unsaved Changes">
<p>There are unsaved changes on this page.</p>
			<p>You can save your changes now,<br>
			or Continue without saving.</p>
</div>

<!-- <div id=flash> -->

			<span style="display:none" id=customsdivfrom>
&nbsp;&nbsp;<a href="javascript:void(0)" onclick="docb();">Customs Broker</a>
			</span>

<tr><td>

<!-- ##################### start 1st data pane ############### -->

<table class=panel1><tr><td>
<table class=panel2>
<tr>
<td align=right><label for=fld104>Office </label><input type=text size=3 maxlength=2 data-musthave=true
		name="fld104" id="fld104" value="HA"></td>
	      <td align=right><label for=fld115>Mode</label></td>
<td align=left nowrap> <select  class=musthave name=fld115 id=fld115
		onchange="changeMode2(this.value);"> <option value=BROKERAGE>BROKERAGE</option><option value='Brokerage'>Brokerage</option> </select> </td>
	  <td align=right><label for=fld18>Equipment</label></td>
	  <td align=left><select class=musthave name=fld18 id=fld18
	  		onblur="ifreefer(this.value);"> <option value=DRAY >DRAYAGE</option><option value="AIR">AIR FREIGHT</option><option value="CNG">CONESTOGA</option><option value="DD">DOUBLE DROP</option><option value="DF">DROP FRAME</option><option value="DRAY">DRAYAGE</option><option value="F">FLATBED</option><option value="FA">AIR-RIDE FLATBED</option><option value="FM">FLATBED TEAM</option><option value="FS">FLATBED W/ SIDES</option><option value="FT">FLATBED W/ TARPS</option><option value="HAND">HANDLING</option><option value="HC">HIGH CUBE</option><option value="LB">LOWBOY</option><option value="LTL">LESS THAN TL</option><option value="OCN">OCEAN</option><option value="PO">POWER ONLY</option><option value="QUOT">**TURN A QUOTE TO A TAG**</option><option value="R">REEFER</option><option value="RM">REEFER TEAM</option><option value="RZ">REEFER HAZMAT</option><option value="SBT">STRAIGHT BOX TRUCK</option><option value="SD">STEP OR DROP DECK</option><option value="ST">STANDARD</option><option value="STOR">STORAGE</option><option value="SV">SPRINTER VAN</option><option value="TL">TRUCKLOAD</option><option value="TORD">TRUCK ORDERED NOT USED</option><option value="V">VAN</option><option value="VA">VAN AIR-RIDE</option><option value="VC">CURTAIN VAN</option><option value="VM">VAN TEAM</option><option value="VM">VAN/TEAM</option><option value="VR">VAN OR REEFER</option><option value="VZ">VAN HAZMAT</option> </select></td>
	  <td align=right>Footage</td>
	  <td align=left><input type=text class=numeric name=fld90 id=fld90 size=2 maxlength=2
			value="40"> </td>
	  <td align=right>Pieces</td>
	  <td align=left nowrap><input class=numeric type=text
				size=5 maxlength=5 id=fld11 name=fld11 id=fld11 value=""
				onfocus="getgrid(this);" onblur="dogrid(this);">
		&nbsp;&nbsp;
		<label for=fld13>Weight</label>
			<input class="musthave numeric" type=text  id=fld13
			name=fld13 id=fld13 size=4 maxlength=6 value="38000" onfocus="getgrid(this);"
			onblur="dogrid(this);dochg();dopay();">
				<span id=wht class="metric" style="display:none">
					&nbsp;&nbsp;Kg <input class="protected" readonly="readonly"
					style="text-align: right" type=text id=cuswat size=3 maxlength=3
					value="17237">
				</span>
		</td>
	  <td align=right nowrap>Billable Weight</td>
	  <td align=left nowrap><input class=numeric type=text id=fld911
			name=fld911 size=4 maxlength=6 value="38000" onblur="dochg();dopay();">
				<span id=bwht class="metric" style="display:none">
					&nbsp;&nbsp;Kg <input class="protected" readonly
					style="text-align: right" type=text id=cusbwat size=3 maxlength=3
					value="17237">
				</span>&nbsp;&nbsp;
		  </td>
<td align=right nowrap>
		    Liftgate
		        <input type=checkbox name=box619 id=box619 class=link2yn>
		        <input type=hidden name=fld619 id=fld619 value="N">
		        <label for=box619> Pick </label>
		        <input type=checkbox name=box683 id=box683 class=link2yn>
		        <label for=box683> Del </label>
		        <input type=hidden name=fld683 id=fld683 value="N">
</td>
	  <td align=left nowrap>
	    &nbsp;&nbsp;
			<input type=checkbox name=box614 id=box614 class=link2yn>
				<label for=box614> Tarps </label>
				<input type=hidden name=fld614 id=fld614 value="">
				&nbsp;&nbsp; Tarp Size &nbsp;
				<input type=text size=4 maxlength=5 name=fld520 id=fld520 value="">
				&nbsp;&nbsp;
			<input type=checkbox name=box613 id=box613 class=link2yn onclick="mkacc(this);">
				<label for=box613> Permits
				<input type=hidden name=fld613 id=fld613 value="">
	  </td>
		  <td align=left nowrap>
<!-- Craig 110816 job 20224 added id for loadbd -->
			<input type=checkbox name=loadbd id="loadbd" onclick="doloadbd(this);">
				<label for=loadbd>All Load Boards &nbsp;&nbsp;</label>
<!--<input name="fld45" maxlength="1" size="1" type="text" value="N" >
-->
				<input type=hidden name=fld45 id=fld45 value="N">
	      </td>
	    </tr>

		<tr>
<td align=right nowrap>Cust Miles <input class=numeric type=text BROKERAGE
		name=fld88 id=fld88 size=3 maxlength=6 value="186" onblur="dochg();dopay();"></td>
		  <td nowrap align=right><span id=km1 class="metric" style="display:none">Km
				<input class="protected numeric" readonly type=text id=custkm size=3 maxlength=6 value="299"></span>

<!--<img id="custmiles" class="rfrshmiles" src="/images/vision/btn_refresh.png" />
<div style="display:none;" id="rfshmilestxt" title="Recalculate Mileage?">
<p>Do you want to recalculate the mileage?</p>
</div>-->
</td>
	  <td align=left nowrap>
	<input type=checkbox name=boxextra1 id=boxextra1 class=link2yn disabled>
		<label for=boxextra1 id=boxlabelextra1>FSMA</label>
		<input type=hidden name=fldextra1 id=fldextra1 value=" ">
				</td>
				<input type=hidden name=fld661 id=fld661 value="">
	  <td align=right nowrap><label for=revtyp>Revenue Type</label></td>
	  <td align=left><select    name=revtyp id=revtyp
	  		onchange="putval('fld661',this.value);"> <option></option><option value="R">REVENUE</option> <option></option> </select></td>
<td align=right nowrap>Freight Class</td>
<td align=left>
<select  name=fld581 id=fld581>
<option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
	  <td align=right nowrap>&nbsp; <label for=fld12>Description</label></td>
	  <td align=left><input type=text  data-musthave=true id=fld12
			name=fld12 value="EDGE BAND" size=22 maxlength=18></td>
		  <td align=right nowrap>Declared Value</td>
<td align=left><input data-label="Declared Value" type=text class=numeric   name=fld586 value="" size=8 maxlength=9 id=fld586></td>
		  <td align=right nowrap>
			Inside
				<input type=checkbox name=box618 id=box618 class=link2yn>
				<label for=box618> Pick </label>
				<input type=hidden name=fld618 id=fld618 value="N">
				<input type=checkbox name=box684 id=box684 class=link2yn>
				<label for=box684> Del </label>
				<input type=hidden name=fld684 id=fld684 value="N">
	  </td>
	  <td align=left nowrap>
	    &nbsp;&nbsp;
			<input type=checkbox name=box612 id=box612 class=link2yn>
				<label for=box612> Oversized </label>
				<input type=hidden name=fld612 id=fld612 value="">
			&nbsp;
			<input type=checkbox name=box572 id=box572 class=link2yn onclick="mkacc(this);">
				<label for=box572> Escorts </label>
				<input type=hidden name=fld572 id=fld572 value="">
			&nbsp;
	    	<input type=checkbox name=box611 id=box611 class=link2yn>
				<label for=box611> Straps/Chains </label>
				<input type=hidden name=fld611 id=fld611 value="">
	&nbsp;
	  </td>
		  <td align=left>
			<input type=checkbox name=internal onclick="dointernal(this);">
				<!-- <label for=internal>Our Board Only </label> -->
				<a class=lo href="/mmhd/avail.htm" target="_blank">Our Board Only </a>
	      </td>

				<input type=hidden name=fld599 id=fld599 value="">
		</tr>

		<tr>
<td align=right nowrap>Truck Miles <input class=numeric type=text
		name=fld663 id=fld663 size=3 maxlength=6 value="185"></td>
		  <td nowrap align=right><span id=km2 style="display:none">Km
				<input class=numeric type=text id=trukkm size=3 maxlength=6 value="298"></span>
		  </td>
		  <td align=left>
				<input type=checkbox name=box69 id=box69 class=link2yn disabled>
	    <label for=box69> Temp Control </label>
	    <input type=hidden name=fld69 id=fld69 value="N">
</td>
<!--
	Temp &deg;F <input class=numeric type=text name=fld95 id=fld95 size=2 maxlength=3 value="">
<span id=fcels class="metric" style="display:none">
				&nbsp;&nbsp;&deg;C <input class="protected" readonly style="text-align: right" type=text id=celsius size=2 maxlength=3 value=""></span>
-->
		  <td align=left valign=top colspan=4 nowrap>
			<table>
			  <tr>
				<td align=right>
					Min Temp &deg;F <input class=numeric type=text name=fld95 id=fld95 size=2
						maxlength=3 value=" ">
				</td>
				<td align=right>
					Max Temp <input class=numeric type=text name=fldextra2 id=fldextra2 size=2
						maxlength=3 value="   ">
				</td>
				<td align=right>
					PreCool Temp <input class=numeric type=text name=fldextra3 id=fldextra3 size=2
						maxlength=3 value="   ">
				</td>
			  </tr>
			  <tr>
				<td align=right>
					<span id=fcels class="metric" style="display:none">
					&nbsp;&nbsp;&deg;C
					<input class="protected" readonly style="text-align: right" type=text
						id=celsius size=2 maxlength=4 value="" onfocus="blur();">
					</span>
				</td>
				<td align=right>
					<span id=fcels2 class="metric" style="display:none">
					&nbsp;&nbsp;&deg;C
					<input class="protected" readonly style="text-align: right" type=text
						id=celsius2 size=2 maxlength=4 value="" onfocus="blur();">
					</span>
				</td>
				<td align=right>
					<span id=fcels3 class="metric" style="display:none">
					&nbsp;&nbsp;&deg;C
					<input class="protected" readonly style="text-align: right" type=text
						id=celsius3 size=2 maxlength=4 value="" onfocus="blur();">
					</span>
				</td>
			  </tr>
			</table>
		  </td>
		  <td></td>
		  </td>
		  <td colspan=4 align=right>
&nbsp;&nbsp;&nbsp;&nbsp;
			<input type=checkbox name=box67 id=box67 class=link2yn>
			    <label for=box67> Pallet Exchange </label>
			    <input type=hidden name=fld67 id=fld67 value="N">
&nbsp;&nbsp;&nbsp;&nbsp;
		  Pallets In <input type=text class=numeric name=fld96 id=fld96
				size=2 maxlength=2 value="">
		    &nbsp;
		  Out <input type=text name=fld97 class=numeric id=fld97 size=2 maxlength=2 value="" onfocus="getgrid(this);">
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
			<span id=slsteam style="display:none">  <!-- only for brock -->
&nbsp;&nbsp;&nbsp;&nbsp;
<label for=fld654>Sales Team</label>
			 <select  name=fld654 id=fld654 />  </select>
			</span>
&nbsp;&nbsp;&nbsp;&nbsp;

		    <input type=checkbox name=box662 id=box662 class=link2yn>
		        <label for=box662> Team </label>
		        <input type=hidden name=fld662 id=fld662 value="">
&nbsp;&nbsp;&nbsp;&nbsp;
		 </td>
		 <td align=left>
&nbsp;&nbsp;
			<input type=checkbox name=box569 id=box569 class=link2yn>
				<label for=box569> Labor </label>
				<input type=hidden name=fld569 id=fld569 value="">
	&nbsp;&nbsp;&nbsp;&nbsp;
	  &nbsp;&nbsp;
	<input type=checkbox name=box595 id=box595 class=link2yn>
		<label for=box595> Seal </label>
		<input type=hidden name=fld595 id=fld595 value="">
	&nbsp;&nbsp;&nbsp;&nbsp;
	  &nbsp;&nbsp;
				<input type=checkbox name=box568 id=box568
	  		disabled onclick="tronly(this);">
				<label id=rtcy568 for=box568>Do Not Broker </label>
				<input type=hidden name=fld568 id=fld568 value="">
	  </td>
<td align=left>
			<input type=checkbox name=box672 id=box672 class=link2yn>
				<label for=box672> Hot Load </label>
				<input type=hidden name=fld672 id=fld672 value="N">
</td>
		</tr>
		<tr>
<td align=right>
			<input type=checkbox name=box615 id=box615 class=link2yn>
				<label for=box615> Disable Mileage </label>
				<input type=hidden name=fld615 id=fld615 value="">
</td>

		  <td></td>
		  <td align=left>
		    <input type=checkbox name=box970 id=box970 class=link2yn >
			    <label for=box970> Disable Highlight </label>
			    <input type=hidden name=fld970 id=fld970 value="">

		  </td>

		  <td colspan=4 align=left>
			<span class="v3only" style="display:none">
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
		    <input type=checkbox name=box972 id=box972 class=link2yn
			  onclick="dotonu()";>
			    <label for=box972> TONU </label>
			    <input type=hidden name=fld972 id=fld972 value="">
			</span>
		  </td>

		  <td align=left>
			<span class="rtcy" style="display:none"
					onclick="dois(document.getElementById('hell').value);
					hellf(item, document.getElementById('hell').value);" title="Click to Modify, Delete, Add Freight Mix Types" >
				<a href="#">Freight Mix</a>
			</span>
		  </td>
	  <td align=left colspan=2>
			<span class="rtcy" style="display:none">
			<select id=hell name=fld332 onfocus="doi(this.value);"
				onchange="doi(this.value);">
				<option value="" selected></option>

			</select>

&nbsp;&nbsp;&nbsp;&nbsp;

		    <input type=checkbox name=box971 id=box971 class=link2yn >
			    <label for=box971> Open Board </label>
			    <input type=hidden name=fld971 id=fld971 value="">
			</span>

		  </td>

		  <td></td>
		  <td colspan=2 align=left>
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
		    <input type=checkbox name=box571 id=box571 class=link2yn>
			    <label for=box571> Blind </label>
			    <input type=hidden name=fld571 id=fld571 value="">
&nbsp;&nbsp;
		    <input type=checkbox name=box68 id=box68 class=link2yn>
			    <label for=box68> Hazmat </label>
			    <input type=hidden name=fld68 id=fld68 value="N">

&nbsp;&nbsp;
		    <input type=checkbox name=box885 id=box885 class=link2yn>
			    <label for=box885> Customs Bonded </label>
			    <input type=hidden name=fld885 id=fld885 value="">
&nbsp;&nbsp;
		    <input type=checkbox name=box596 id=box596 class=link2yn>
			    <label for=box596> Mass Release </label>
			    <input type=hidden name=fld596 id=fld596 value="Y">
		  </td>
		  <td align=left>
<input type=checkbox name=box76 id=box76 class=link2yn>
<label for=box76 id="boxlabel76"> High Risk </label>
<input type=hidden name=fld76 id=fld76 value="N">
		  </td>
		</tr>
	  </table>
</td></tr></table>

<!-- ##################### end 1st data pane ############### -->

</td>
</tr>
<tr>
<td>

<!-- ##################### start 2nd data pane ############### -->

<div id="hidden-labels-div" style="display:none";>
<label for="fld11">Pieces</label>
<label for="fld15">Pickup Hrs</label>
<label for="cuaddr">Customer Address</label>
<label for="cucity">Customer City</label>
<label for="custate">Customer State</label>
<label for="fld8">Customer Phone</label>
<label for="fld155">Pickup Ref #</label>
<label for="paddr">Pickup Address</label>
<label for="pzip">Pickup Zip</label>
<label for="pphone">Pickup Phone</label>
<label for="fld42">Consignee Hrs</label>
<label for="fld185">Pickup Appt Date</label>
<label for="fld29">Consignee Name</label>
<label for="caddr">Consignee Address</label>
<label for="czip">Consignee Zip</label>
<label for="fld43">Consignee Contact</label>
<label for="cphone">Consignee Phone</label>
<label for="fld156">Consignee Ref#</label>
<label for="fld150">Delivery Appt Date</label>
<label for="fld602">Must Deliver Date</label>
	  	<label for=s_fld2>Customer Name</label>
	  	<label for=fld2>Customer Name</label>
	  	<label for=fld59>Customer Ref #</label>
	  	<label for=s_fld59>Customer Ref #</label>
	  	<label for=s_fld169>Bill To Name</label>
	  	<label for=fld169>Bill To Name</label>
	  	<label for=s_fld30>Pick Up Name</label>
	  	<label for=s_pcity>Pickup City</label>
	  	<label for=s_pstate>Pickup State</label>
	  	<label for=s_fld39>Ready Date</label>
	  	<label for=fld30>Pick Up Name</label>
	  	<label for=pcity>Pickup City</label>
	  	<label for=pstate>Pickup State</label>
	  	<label for=fld39>Ready Date</label>
	  	<label for=s_ccity>Consignee City</label>
	  	<label for=s_cstate>Consignee State</label>
	  	<label for=ccity>Consignee City</label>
	  	<label for=cstate>Consignee State</label>
	  	<label for=di_edate>ETA Date</label>

	  	<label for=fld186>Pickup Appt Time</label>
	  	<label for=fld559>Conf Sent Time</label>
		<label for=fld562>Conf Rec'd Time</label>
		<label for=di_fld54>Will PU Time</label>
		<label for=di_fld53>Dispatched Time</label>
		<label for=di_avtime>Arrived PU Time</label>
		<label for=di_ltime>Loaded Time</label>
		<label for=di_etime>ETA Time</label>
		<label for=di_addtime>Arrived Cons Time</label>
		<label for=di_dtime>Delivered Time</label>
		<label for=fld14>Ready Time</label>
		<label for=fld151>Delivery Appt Time</label>
		<label for=tr_wdate>Will Pickup Date</label>

		<label for=fld176>Dispatcher Name</label>
		<label for=fld172>Dispatcher Phone</label>
		<label for=dname>Driver Name</label>
		<label for=di_fld177>Driver Cell Phone</label>
		<label for=fld95>Min Temp</label>
		<label for=fldextra2>Max Temp</label>
		<label for=fldextra3>PreCool Temp</label>
<!-- Craig 032816 job 20768 -->
<label for=truckno>Truck #</label>
<label for=drv130>Projected Available date for truck</label>
<label for=drv126>Available City for truck</label>
<label for=drv127>Available State for truck</label>

</div>

<table id=2nd border=1 width=100%>
<tr><!-- %%%%%%%%%%%% -->
<td class="headrow" align=left>
	<span id=moreless2></span>
	&nbsp;&nbsp;&nbsp;&nbsp;
	&nbsp;&nbsp;&nbsp;&nbsp;
	&nbsp;&nbsp;&nbsp;&nbsp;
	&nbsp;&nbsp;&nbsp;&nbsp;
	&nbsp;&nbsp;&nbsp;&nbsp;
	<a class=always href="javascript:upCust('100002');" style="line-height:14pt;font-weight:600;">Customer</a>
</td>
<td class="headrow" align=center><a id=billto class=always href="javascript:upCust('100002');" style="line-height:14pt;font-weight:600;">Bill To</a></td>
<td class="headrow" align=center><a class=always href="javascript:upCust('101636');" style="line-height:14pt;font-weight:600;">Pick Up</a></td>
<td class="headrow" align=center><a class=always href="javascript:upCust('101636');" style="line-height:14pt;font-weight:600;">Consignee</a></td>
<td class="headrow" align=right><span id=moreless></span>&nbsp;</td>
</tr><!-- %%%%%%%%%%%% -->

<tr>

<!-- ********** CUSTOMER ************** -->

<td valign="top" align=center width=20%>

	<span class=short>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp; </td>
		<td align=left><input class="twin musthave" type=text   size=25 maxlength=30
				name="s_fld2" id="s_fld2" value="ACME LOGISTICS, LLC - ATL" class=twin></td>
	  </tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td nowrap><input type=text  name=s_fld7 id=s_cucity maxlength=20 size=15 value="ANYTOWN"
				readonly>
		&nbsp; St
		<input type=text  name=s_fld5 id=s_custate maxlength=2 size=2 value="IL" readonly></td>
<tr>
<td nowrap align=right>Ref #</td>
<td align=left><input type=text class="musthave twin" name=s_fld59 id=s_fld59 value="S00000000/S0000000"
		size=18 maxlength=18 onchange="dup59(this);"></td>
</tr>
	</table>
	</span>	<!-- short -->

	<span class=long>
<table>
	  <tr>
		<td align=right>Name&nbsp;</td>
		<td align=left><input class="musthave twin" type=text   size=27 maxlength=30
				name="fld2" id="fld2" value="ACME LOGISTICS, LLC - ATL" ></td>
	  </tr>
<tr>
<td align=right>Address</td>
<td align=left nowrap><input type=text  size=27 maxlength=30 name=fld3 id=cuaddr
		value="123 MAIN ST" readonly>
</tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td align=left nowrap><input type=text  name=fld7 id=cucity maxlength=20 size=17
				value="ANYTOWN" readonly>
		&nbsp; St
		<input type=text  name=fld5 id=custate maxlength=2 size=2 value="IL" readonly></td>
	  </tr>
	  <tr>
	    <td align=right>Zip</td>
<td align=left nowrap><input type=text  name=fld6 id=cuzip maxlength=6 size=4
		value="12345" readonly>
&nbsp;&nbsp; Country
<input type=text  name=fld333 id=fld333 maxlength=20 size=10 value="" readonly></td>
</tr>
<tr>
<td align=right>Contact</td>
<td align=left><input type=text  name=fld9 id=fld9 maxlength=18 size=27 value="JOHN DOE"></td>
	  </tr>
	  <tr>
		<td align=right>Phone</td>
		<td align=left><input type=text  name=fld8 id=fld8 class=phone class=phone maxlength=14 size=14 value="(*************"></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp4.value);">Email</a></td>
		<td align=left><input type=text  name=fldp4 id=fldp4 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp5.value);">Email</a></td>
		<td align=left><input type=text  name=fldp5 id=fldp5 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp6.value);">Email</a></td>
		<td align=left><input type=text  name=fldp6 id=fldp6 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp7.value);">Email</a></td>
		<td align=left><input type=text  name=fldp7 id=fldp7 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp8.value);">Email</a></td>
		<td align=left><input type=text  name=fldp8 id=fldp8 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fldp9.value);">Email</a></td>
		<td align=left><input type=text  name=fldp9 id=fldp9 maxlength=60 size=27
				value="<EMAIL>" readonly></td>
	  </tr>
<tr>
<td nowrap align=right>Ref #</td>
<td align=left><input type=text class="musthave twin" name=fld59 id="fld59" value="S00000000/S0000000"
		size=18 maxlength=18 onchange="dup59(this);"></td>
</tr>

	</table>
	</span> <!-- long -->


</td>


<!-- ********** BILL TO ************** -->

<td align=center valign="top" width=20%>
	<span class=short>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input type=text class="musthave twin"   size=25 maxlength=30
				name="s_fld169" id="s_fld169" value="ACME LOGISTICS, LLC - ATL" ></td>
	  </tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td align=left nowrap><input type=text  id=s_btcity maxlength=20 size=15 value="ANYTOWN" readonly>
		&nbsp; St
		<input type=text  id=s_btstate maxlength=2 size=2 value="IL" readonly></td>
	  </tr>
<tr>
		<td align=right>Phone</td>
		<td align=left><input type=text  id=s_btphone maxlength=14 size=14 value="(*************" class="phone twin"></td>
	  </tr>
</table>
</span> <!-- short -->

	<span class=long>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input type=text class="musthave twin"   size=27 maxlength=30
				name="fld169" id="fld169" value="ACME LOGISTICS, LLC - ATL" ></td>
	  </tr>
	  <tr>
	  	<td align=right>Address</td>
	  	<td align=left><input type=text  maxlength=30 size=27 name=btaddr id=btaddr value="123 MAIN ST" readonly></td>
	  </tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td align=left nowrap><input type=text  id=btcity maxlength=20 size=17 value="ANYTOWN" readonly>
		&nbsp; St
		<input type=text  id=btstate maxlength=2 size=2 value="IL" readonly></td>
	  </tr>
<tr>
		<td align=right>Zip</td>
		<td align=left nowrap><input type=text  name=btzip id=btzip maxlength=6 size=4 value="12345 " readonly> &nbsp;
		&nbsp; Country
		<input type=text  name=btcountry id=btcountry maxlength=20 size=10 value="" readonly></td>
	  </tr>
<tr>
<td align=right>Contact</td>
		<td align=left><input type=text  name=btcontact id=btcontact maxlength=18 size=27 value="JOHN DOE"></td>
	  </tr>
<tr>
		<td align=right>Phone</td>
		<td align=left><input type=text  id=btphone class=phone maxlength=14 size=14 value="(*************"></td>
	  </tr>
</table>
</span> <!-- long -->
</td>


<!-- ********** PICKUP ************** -->

<td align="center" valign="top" width=20%>
<span class=short>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input class="musthave twin" type=text  size=25 maxlength=30
					name="s_fld30" id="s_fld30" value="WANDO TERMINAL" ></td>
	  </tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td align=left nowrap><input class="musthave twin" type=text  name=s_fld32 id=s_pcity
				maxlength=15 size=15 value="MOUNT PLEASANT" >
		&nbsp; St
		<input class="musthave twin" type=text  name=s_fld33 id=s_pstate
				maxlength=2 size=2 value="SC" ></td>
		<td align=center valign=bottom nowrap>Ready
		&nbsp;<span id="appt_ready_ico"></span>
		</td>
	  </tr>
	  <tr>
		<td align=right>Ref #</td>
		<td align=left><input type=text name=s_fld155 id=s_fld155 value="XXXX0000000" size=25 maxlength=18
				class=twin></td>
	<td nowrap><input type=text class="musthave twin event datepick appt_ready" appt="ready" size=6 maxlength=8 name=s_fld39 id=s_fld39
			value="12/03/25" onchange="domdy(this);">
	</td>

	  </tr>
</table>
</span> <!-- short -->

<span class=long>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input class="musthave twin" type=text  size=27 maxlength=30
					name="fld30" id="fld30" value="WANDO TERMINAL" ></td>
	  </tr>
<tr>
		<td align=right>Address</td>
		<td align=left><input type=text data-label="Pickup Address"  size=27 maxlength=30 name=fld31 id=paddr value="400 LONGPOINT RD"></td>
<tr>
<tr>
		<td align=right>Address</td>
		<td align=left><input type=text  size=27 maxlength=30 name=fld181 id=paddr2 value=""></td>
<tr>
	  <tr>
		<td align=right nowrap>City</td>
		<td align=left nowrap><input class="musthave twin" type=text  name=fld32 id=pcity
				maxlength=15 size=17 value="MOUNT PLEASANT" >
		&nbsp;&nbsp; St
		<input class="musthave twin" type=text  name=fld33 id=pstate
				maxlength=2 size=2 value="SC" ></td>
	  </tr>
<tr>
		<td align=right>Zip</td>
		<td align=left>
			<div id=pzipdiv style="display: inline">
			<input type=text  name=fld34 id=pzip maxlength=6 size=4 value="29464"></div>
		&nbsp; Country
		<input type=text  name=fld334 id=fld334 maxlength=20 size=10 value=""></td>
	  </tr>
<tr>
		<td align=right>Contact</td>
		<td align=left><input type=text data-label="Pickup Contact"  name=fld35 id=fld35 maxlength=18 size=27 value="WANDO TERMINAL"></td>
	  </tr>
	  <tr>
		<td align=right>Phone</td>
		<td align=left nowrap><input type=text  name=fld36 id=pphone class=phone maxlength=14 size=14
				value="(*************">
		&nbsp;&nbsp; Hrs <input type=text  size=8 maxlength=9 name=fld15 id=fld15 value=""></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fld197.value);">Email</a></td>
		<td align=left><input type=text  name=fld197 id=fld197 maxlength=60 size=27 value=""></td>
	  </tr>
	  <tr>
		<td align=right>Ref #</td>
		<td align=left><input type=text name=fld155 id="fld155" value="XXXX0000000" size=27 maxlength=18
				class=twin></td>
</tr>
<!--
<tr>
		<td align=right>Must Ship</td>
		<td align=left nowrap><input type=text size=6 maxlength=8 name=fld601 id=fld601 value=""
				onchange="domdy(this);">
		<script language="javascript">
		new tcal ({'formname':'main','controlname':'fld601'});
		</script>
		</td>
	  </tr>
	  -->
<tr>
		<td align=right>Ready</td>
		<td align=left nowrap><input class="musthave event datepick twin appt_ready" appt="ready" type=text size=6 maxlength=8 name=fld39 id=fld39 value="12/03/25" onchange="domdy(this);">
				<!-- withtime=fld14 -->

		&nbsp;&nbsp;&nbsp;Time <input type=text  size=5 maxlength=8 name=fld14 id=fld14
				value="" class="time appt_ready" appt="ready" onchange="tryhms(this);">
		&nbsp;<span id="appt_ready2_ico"></span>
		</td>
	  </tr>
	  <tr>
		<td align=right><nobr>Appt</nobr></td>
		<td align=left nowrap><input type=text  size=6 maxlength=8
				withtime=fld186
				name=fld185 id=fld185 value="12/03/25" class="event datepick appt_pickup" appt="pickup"
				onchange="domdy(this);">
		&nbsp;&nbsp;&nbsp;Time <input type=text  size=5 maxlength=8 id=fld186 name=fld186 value="" class="time appt_pickup" appt="pickup" onchange="tryhms(this);">
		&nbsp;<span id="appt_pickup_ico"></span>
		</td>
	  </tr>
	  <tr>
	    <td align=right nowrap>Appt Note</td>
	    <td align=left><input type=text  size=27 maxlength=19 name=fld187 id=fld187
				value="EVER EXCEL"></td>
	  </tr>

	  <tr>
		<td colspan=2><table width=100%>
		<tr>
		  <td><a href="javascript:puweather();" class="lo" >Pick Up Weather</a></td>
		  <td><a href="javascript:doradsch('orig');" class="lo" >Radius Search</a></td>
	    </tr>

	    <tr>
		  <td><a href="javascript:putraff();" class="lo" >Pick Up Traffic</a></td>
		  <td><a href="javascript:dotagmap('orig')" class="lo" >Map Location</a>
			<div style="display:none;" id="tagmap">
			</div>
		  </td>
		</tr></table>
		</td>
	  </tr>

	  </tr>

</table>
</span> <!-- long -->
</td>

<!-- ********** CONSIGNEE ************** -->

<td align="center" valign="top" width=20%>
	<span class=short>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input type=text  size=25 maxlength=30 name="s_fld29" id="s_fld29" value="WANDO TERMINAL"
				class=twin>
			</td>
	  </tr>
	  <tr>
		<td align=right>City</td>
		<td align=left nowrap><input class="musthave twin" type=text  name=s_fld10 id=s_ccity
				maxlength=20 size=15 value="MOUNT PLEASANT" >
		&nbsp; St
		<input class="musthave twin" type=text  name=s_fld41 id=s_cstate maxlength=2 size=2 value="SC" ></td>
		<td align=center valign=bottom>Appt
		&nbsp;<span id="appt_deliv2_ico"></span>
		</td>
	  </tr>
	  <tr>
		<td align=right>Ref #</td>
		<td align=left><input type=text name=s_fld156 id=s_fld156 value="S00000000/S0000000" size=25 maxlength=18
				class=twin></td>
		<td nowrap><input type=text class="twin event datepick appt_deliv" appt="deliv" withtime=fld151 size=6 maxlength=8 name=s_fld150 id=s_fld150 value="" onchange="domdy(this);">
	  </tr>
</table>
</span> <!-- short -->

	<span class=long>
<table>
	  <tr>
		<td align=right nowrap>Name&nbsp;</a></td>
		<td align=left><input type=text  size=27 maxlength=30 name=fld29 id=fld29 value="WANDO TERMINAL" class=twin></td>
	  </tr>
<tr>
		<td align=right>Address</td>
		<td align=left><input type=text  size=27 maxlength=30 name=fld60 id=caddr value="400 LONGPOINT RD"></td>
<tr>
<tr>
		<td align=right>Address</td>
		<td align=left><input type=text  size=27 maxlength=30 name=fld182 id=caddr2 value=""></td>
<tr>
	  <tr>
		<td align=right>City</td>
		<td align=left nowrap><input class="musthave twin" type=text  name=fld10 id=ccity
				maxlength=20 size=17 value="MOUNT PLEASANT" >
		&nbsp;&nbsp; St
		<input class="musthave twin" type=text  name=fld41 id=cstate maxlength=2 size=2 value="SC" ></td>
	  </tr>
<tr>
		<td align=right>Zip</td>
		<td align=left>
			<div id=czipdiv style="display: inline">
			<input type=text  name=fld70 id=czip maxlength=6 size=4 value="29464">
			</div>
		&nbsp; Country
		<input type=text  name=fld335 id=fld335 maxlength=20 size=10 value=""></td>
	  </tr>
<tr>
		<td align=right>Contact</td>
		<td align=left><input type=text  name=fld43 id=fld43 maxlength=18 size=27 value="WANDO TERMINAL"></td>
	  </tr>
	  <tr>
		<td align=right>Phone</td>
		<td align=left nowrap><input type=text  name=fld58 id=cphone class=phone maxlength=14 size=14 value="(*************">
		&nbsp;&nbsp; Hrs <input type=text  size=8 maxlength=9 name=fld42 id=fld42 value=""></td>
	  </tr>
	  <tr>
		<td align=right><a class=lo href="javascript:domail(document.main.fld198.value);">Email</a></td>
		<td align=left><input type=text  name=fld198 id=fld198 maxlength=60 size=27 value=""></td>
	  </tr>
	  <tr>
		<td align=right>Ref #</td>
		<td align=left><input type=text name=fld156 id="fld156" value="S00000000/S0000000" size=27 maxlength=18
				class=twin></td>
</tr>
<tr>
		<td align=right>Must Deliver</td>
		<td align=left nowrap><input type=text size=6 maxlength=8 name=fld602 id=fld602 value=""
				class="event datepick appt_must" appt="must" onchange="domdy(this);">
		&nbsp;<span id="appt_must_ico"></span>
		</td>
	  </tr>
	  <tr>
		<td align=right><nobr>Appt </nobr></td>
		<td align=left nowrap><input type=text  class="twin event datepick appt_deliv" appt="deliv" withtime=fld151 size=6 maxlength=8 name=fld150 id=fld150 value=""
				onchange="domdy(this);">
&nbsp;&nbsp;&nbsp;Time <input type=text  size=5 maxlength=8 name=fld151 id=fld151 value="" class="time appt_deliv" appt="deliv"
onchange="tryhms(this);">
		&nbsp;<span id="appt_deliv_ico"></span>
		</td>

	  </tr>
	  <tr>
	    <td align=right nowrap>Appt Note</td>
	    <td align=left><input type=text  size=27 maxlength=19 name=fld152 id=fld152
				value=""></td>
	  </tr>

	  <tr>
	  	<td colspan=2>
	  	<table width=100%>
	  	<tr>
		  <td align=left><a href="javascript:coweather();" class="lo" >Consignee Weather</a></td>
		  <td align=left><a href="javascript:doradsch('dest');" class="lo" >Radius Search</a></td>
	      </tr>

	  	  <tr>
		  <td align=left><a href="javascript:cotraff();" class="lo" >Consignee Traffic</a></td>
		  <td align=left><a href="javascript:dotagmap('dest')" class="lo" >Map Location</a></td>
		</tr></table>
		</td>

	  </tr>

</tr>

</table>
</span> <!-- long -->
</td>


<!-- ********** ADDITIONAL ************** -->

<td align="center" valign="top" width=20%>
	<span class=short>
<table>
	  <tr>
		<td align=right nowrap><label for=s_fld884>Sales Rep</label></td>
		<td align=left colspan=3 nowrap><input type=text data-musthave=true  size=25 maxlength=25
			name="s_fld884" id="s_fld884" value="HOUSE ACCOUNT" class=twin>
			&nbsp; Rep ID
		   <input type=text readonly style="background-color:#f0f0f0;" onclick="blur();"
		   		id=s_fld65 name=s_fld65 size=3 value="HA">
		</td>
	  </tr>
	  <tr>
	<td align=right nowrap>Service Rep </td>
	<td align=left><input type=text id=s_fld529 name=s_fld529 value="" size=8 maxlength=8
				class=twin></td>
	<td align=right>Quote #</td>
	<td align=left><input type=text id=fld585 name=fld585 value="" size=8 maxlength=7></td>
	</tr>
<tr>
<td nowrap align=right>Disp Assigned</td>
		<td align=left nowrap><input type=text  name=s_fld62 id=s_fld62 value="" size=8 maxlength=8
					class=twin>&nbsp;
<img id="rmvrep" class="unassrep" src="/images/vision/btn_minus.png" style="display:none;"/>
<div style="display:none;" id="unassreptxt" title="Unassign Disp ?">
<p>Remove the Assigned Dispatcher  ?</p>
</div>
</td>
		<td align=right>&nbsp; Disp Actual </td>
</td>
<td>
<input type=text id=s_fld63 name=s_fld63 value="JDOE" size=8 maxlength=8 class=twin>
</td>

</tr>
</table>
</span> <!-- short -->

	<span class=long>
<table>
	  <tr>
		<td align=right nowrap><label for=fld884>Sales Rep</label></td>
		<td align=left colspan=3 nowrap><input type=text data-musthave=true  size=25 maxlength=25
			name="fld884" id="fld884" value="HOUSE ACCOUNT" class=twin>
			&nbsp; ID
		   <input type=text readonly style="background-color:#f0f0f0;" onclick="blur();"
		   		id=fld65 name=fld65 size=3 value="HA">
		</td>
	  </tr>
	  <tr>
	<td align=right nowrap>Service Rep </td>
	<td align=left><input type=text id=fld529 name=fld529 value="" size=8 maxlength=8
				class=twin></td>
<!--
	<td align=right>Quote #</td>
		<td align=left><input type=text disabled style="background-color:#e8e8e8;" value="" size=8 maxlength=7></td>
-->
	  </tr>
<tr>
<td nowrap align=right>Disp Assigned</td>
		<td align=left nowrap><input type=text  name=fld62 id=fld62 value="" size=8 maxlength=8
					class=twin>
<img id="rmvrep" class="unassrep" src="/images/vision/btn_minus.png" style="display:none;"/>
		</td>

		<td align=right>&nbsp; Disp Actual </td>
<td>
<input type=text id=fld63 name=fld63 value="JDOE" size=8 maxlength=8 class=twin>
</td>
</tr>
	  <tr>
		<!-- job 25588 jh 102416 -->
		<td align=right>Tariff #</td>
		<td align=left><input type=text readonly name=fld101 id=fld101
				style="background-color:#e8e8e8;" value="" size=6 maxlength=6></td>
		<td align=right>Quote #</td>
		<td align=left><input type=text  name=fld585 id=fld585 value="" size=8 maxlength=7></td>
	</tr>
		<!--
		<td align=right>Tariff</td>
		<td>
		<select name=fld111 id=fld111 >
		-->

	<tr>
		<!-- job 42294 jh 092523 -->
		<td align=right>Repeated Pro</td>
		<td align=left><input type=text readonly name=fld144 id=fld144
			style="background-color:#e8e8e8;" value="79686" size=8 maxlength=8></td>
	</tr>

<tr><td></td></tr>
<tr>
<td colspan=4 align=center>
<a href="javascript:commstatus();" class="lo" >Commissions</a>
</td>
</tr>
</table>
</span> <!-- long -->
</td>
</tr>

</table>
</td></tr>

<!-- ##################### end 2nd data pane ############### -->


<!-- ##################### start 3rd data pane ############### -->

<input type=hidden id=fld102 name=fld102>
<input type=hidden id=fld103 name=fld103>
<input type=hidden id=percent name=fld582>

<input type=hidden name=fld105 value="">

<tr><td><table class=panel1> <!-- ### overall 3rd panel table *** -->

<tr>
<td>
	<!-- lefthand half -->
	<table class=panel2>
	<tr>
	  <td colspan=4>
		<!-- customer/carrier stuff -->
<table>
<tr  >
<td align=right><a class=lo href="javascript:upCust('100002');">Customer</a></td>
<td><select   name=fld23 id=fld23 onblur="dochg();">
<option value=F>Flat Rate</option>
<option value=F>Flat Rate</option>
<option value=I>All In</option>
<option value=A>Auto Rate</option>
<option value=C>CWT</option>
<option value=T>Ton</option>
<option value=P>Pieces</option>
<option value=M>Mileage</option>
<option value=G>Gainshare</option>
<option value=H>Hourly</option>
</select>
</td>
<td align=right nowrap><span id=hrly1># of Hours&nbsp;</span></td>
<td><input type=text class=numeric name=fld526 id=hrly2 size=5
maxlength=7 value="" onchange="dochg();"></td>
<td align=right class=cus_curr nowrap>LH Rate </td>
<td><input type=text class="numeric curr cus"    name=fld24 id=fld24 size=8
maxlength=8 value="900.00" onchange="dot2(this);dochg();"></td>
<td align=right nowrap>&nbsp;&nbsp;FSC % </td>
<td><input type=text class=numeric size=2 maxlength=4 id=fld664 name=fld664
value="" onchange="do_fsc(this);"></td>
<td align=right nowrap>FSC/Mile &nbsp;</td>
<td><input type=text class=numeric size=2 maxlength=4 id=fld665 name=fld665
value="" onchange="do_fsc(this);"></td>
</tr>

<tr>
<td align=right><a class=lo id="whopay1" href="javascript:upCar('300000026');">Carrier</a></td>
<td><span class=driver_rtyp><select       id=paytype name=paytype
			onchange="ifdriver_pct();dopay();">
<option value=F>Flat Rate</option><option value=F>Flat Rate&nbsp;</option><option value=I>All In&nbsp;</option><option value=A>Auto Rate</option><option value=C>CWT</option><option value=H>Hourly</option><option value=M>Mileage</option><option value=%>Percent</option><option value=P>Pieces</option><option value=T>Ton</option>
</select>
</span></td>

<td align=right nowrap><span id=hrly1># of Hours&nbsp;</span></td>
<td><input type=text class="numeric "   name=fld527 id=hrly2 size=5 maxlength=7 value="" onchange="dopay();"></td>
<td align=right class=car_curr >LH Rate </td>
<td align=left><input type=text class="numeric curr car"      name=payrate id=payrate
	size=8 maxlength=8 value="700.00" onchange="dot2(this);dopay();">
	<!--  jh 051618 job 32657 max rate field -->
<td align=right><font color=red>Max Rate</font></td>
<td colspan=1><input type=text class="numeric " name=fld604 size=8 id=fld604   value="" /></td>
<font color=red style="background_color:#ffff00;">
<span id="new" style="background-color:#FFFF00; font-weight:700;color: #ff0000"></span>
</font>

</td>

<td align=right colspan=2 nowrap>
			<span class=driver_pct>
				Pay % &nbsp;
		<input name=paypct id=paypct class="numeric"   type=text size=2
					maxlength=3 value="    " onchange="ifdriver_pct(); dopay();">
				&nbsp;
			</span>
			<a class="info" target="_blank" href="https://www.macropoint.com/capacity-matching-for-lsps/">
			<font color=red>&nbsp; SMR  &nbsp;</font>
			<span style="width:20em;">
				SMR = Suggested Market Rate <br><br>
				This data is fed to Macropoint for customers <br>
				who subscribe to MacroPoint Capacity. <br>
				Click to learn more. <br><br>
				The field is for reference, there is no <br>
				functionality or reporting currently built <br>
				around this data.
			</span></a>
			&nbsp;
<input type=text class="numeric "  name=fld914 id=fld914
maxlength=8 size=8 value="">

		</td>
</tr>
</table>
	  </td>
	</tr>
	<tr>
	  <td>
	    <table>
		<!-- accessorial stuff -->
<!-- Accessorial rows -->

<tr style="display:none;"     2293>
<td colspan="5" style="color:red" align="center"><b>Copy Accessorial Charges?</b>&nbsp;
<br>
<span id=ifcpitems>
<b>Copy Items Detail?</b>&nbsp;
<select id=cpitems name=cpitems>
<option value="Y" selected>Copy Items</option>
<option value="N">Do Not Copy Items</option>
</select>
</span>
</td>

</tr>

<tr>
<td align=center valign=bottom><b><span id="whopay2">Carrier </span></b></td>
<td align=center valign=bottom><b>Accessorial</b></td>
<td align=right colspan=3 nowrap><b><span id="whopay3">Carrier</span>
/ Release</b>&nbsp;&nbsp;</td>
<td align=center valign=bottom><b><span class=cus_curr>Customer </span></b></td>
</tr>

<tr>
<td align=left > CLEVES LOGISTICS LLC &nbsp;</td>
<td align=left > &nbsp;Line Haul </td>
<td align=right><input style="background-color:#e8e8e8;" readonly class=numeric type=text size=8 maxlength=8 value="700.00" onfocus="blur();" id=paynet name=paynet>&nbsp;</td>
<td align=center valign=bottom></td>
<td align=center>
	  <input type=checkbox name=boxlh id=boxlh onclick="lhrelbox(this);">
	  <input type=hidden name=fldlh id=fldlh value="">
</td>
<td align=right><input style="background-color:#e8e8e8;" readonly class=numeric type=text size=8 maxlength=8 value="900.00" onfocus="blur();" name=flx44 id="flx44">&nbsp;</td>
</tr>


<!-- tpay 1-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="CLEVES LOGISTICS LLC" name='a1' id='a1'> </td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="CHAS" name='a2' id='a2'> </td>
<td align=right><nobr><input class="numeric access" type=text   size=8 maxlength=8 value="40.00" name=a3 id='a3' onchange="dot2(this);">&nbsp;</nobr></td>
<td align=left id=curr1></td>
<td align=center>
<input type=hidden name=xr1 id=xr1 value="">
<input type=hidden name=sva1 id=sva1 value="CLEVES LOGISTICS LLC">
<input type=hidden name=sva2 id=sva2 value="CHAS">
<input type=hidden name=sva3 id=sva3 value="40.00">
<input type=hidden name=sva4 id=sva4 value="40.00">
	  <input type=checkbox name=boxa7 id=boxa7 onclick="relbox(this);">
	  <input type=hidden name=flda7 id=flda7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="40.00" name=a4 id='a4' onchange="dot2(this);">
<input type=hidden name=a5 id=a5 value="300000026">
<input type=hidden name=a6 id=a6 value="134737">
</td>
</tr>

<!-- tpay 2-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='b1' id='b1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='b2' id='b2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=b3 id='b3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr2></td>
<td align=center>
<input type=hidden name=xr2 id=xr2 value="">
<input type=hidden name=svb1 id=svb1 value="">
<input type=hidden name=svb2 id=svb2 value="">
<input type=hidden name=svb3 id=svb3 value="">
<input type=hidden name=svb4 id=svb4 value="">
	  <input type=checkbox name=boxb7 id=boxb7 onclick="relbox(this);">
	  <input type=hidden name=fldb7 id=fldb7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=b4 id='b4' onchange="dot2(this);">
<input type=hidden name=b5 id=b5 value="">
<input type=hidden name=b6 id=b6 value="">
</td>
</tr>

<!-- tpay 3-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='c1' id='c1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='c2' id='c2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=c3 id='c3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr3></td>
<td align=center>
<input type=hidden name=xr3 id=xr3 value="">
<input type=hidden name=svc1 id=svc1 value="">
<input type=hidden name=svc2 id=svc2 value="">
<input type=hidden name=svc3 id=svc3 value="">
<input type=hidden name=svc4 id=svc4 value="">
	  <input type=checkbox name=boxc7 id=boxc7 onclick="relbox(this);">
	  <input type=hidden name=fldc7 id=fldc7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=c4 id='c4' onchange="dot2(this);">
<input type=hidden name=c5 id=c5 value="">
<input type=hidden name=c6 id=c6 value="">
</td>
</tr>

<!-- tpay 4-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='d1' id='d1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='d2' id='d2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=d3 id='d3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr4></td>
<td align=center>
<input type=hidden name=xr4 id=xr4 value="">
<input type=hidden name=svd1 id=svd1 value="">
<input type=hidden name=svd2 id=svd2 value="">
<input type=hidden name=svd3 id=svd3 value="">
<input type=hidden name=svd4 id=svd4 value="">
	  <input type=checkbox name=boxd7 id=boxd7 onclick="relbox(this);">
	  <input type=hidden name=fldd7 id=fldd7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=d4 id='d4' onchange="dot2(this);">
<input type=hidden name=d5 id=d5 value="">
<input type=hidden name=d6 id=d6 value="">
</td>
</tr>

<!-- tpay 5-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='e1' id='e1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='e2' id='e2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=e3 id='e3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr5></td>
<td align=center>
<input type=hidden name=xr5 id=xr5 value="">
<input type=hidden name=sve1 id=sve1 value="">
<input type=hidden name=sve2 id=sve2 value="">
<input type=hidden name=sve3 id=sve3 value="">
<input type=hidden name=sve4 id=sve4 value="">
	  <input type=checkbox name=boxe7 id=boxe7 onclick="relbox(this);">
	  <input type=hidden name=flde7 id=flde7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=e4 id='e4' onchange="dot2(this);">
<input type=hidden name=e5 id=e5 value="">
<input type=hidden name=e6 id=e6 value="">
</td>
</tr>

<!-- tpay 6-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='f1' id='f1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='f2' id='f2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=f3 id='f3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr6></td>
<td align=center>
<input type=hidden name=xr6 id=xr6 value="">
<input type=hidden name=svf1 id=svf1 value="">
<input type=hidden name=svf2 id=svf2 value="">
<input type=hidden name=svf3 id=svf3 value="">
<input type=hidden name=svf4 id=svf4 value="">
	  <input type=checkbox name=boxf7 id=boxf7 onclick="relbox(this);">
	  <input type=hidden name=fldf7 id=fldf7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=f4 id='f4' onchange="dot2(this);">
<input type=hidden name=f5 id=f5 value="">
<input type=hidden name=f6 id=f6 value="">
</td>
</tr>

<!-- tpay 7-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='g1' id='g1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='g2' id='g2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=g3 id='g3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr7></td>
<td align=center>
<input type=hidden name=xr7 id=xr7 value="">
<input type=hidden name=svg1 id=svg1 value="">
<input type=hidden name=svg2 id=svg2 value="<!--">
<input type=hidden name=svg3 id=svg3 value="--->">
<input type=hidden name=svg4 id=svg4 value="">
	  <input type=checkbox name=boxg7 id=boxg7 onclick="relbox(this);">
	  <input type=hidden name=fldg7 id=fldg7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=g4 id='g4' onchange="dot2(this);">
<input type=hidden name=g5 id=g5 value="">
<input type=hidden name=g6 id=g6 value="">
</td>
</tr>

<!-- tpay 8-->
<tr>
<td align=left><input type=text class=access  size=35 maxlength=30 value="" name='h1' id='h1'></td>
<td align=left><input type=text class=access  size=8 maxlength=8 value="" name='h2' id='h2'></td>
<td align=right><input class="numeric access" type=text   size=8 maxlength=8 value="" name=h3 id='h3' onchange="dot2(this);">&nbsp;</td>
<td align=left id=curr8></td>
<td align=center>
<input type=hidden name=xr8 id=xr8 value="">
<input type=hidden name=svh1 id=svh1 value="">
<input type=hidden name=svh2 id=svh2 value="">
<input type=hidden name=svh3 id=svh3 value="">
<input type=hidden name=svh4 id=svh4 value="">
	  <input type=checkbox name=boxh7 id=boxh7 onclick="relbox(this);">
	  <input type=hidden name=fldh7 id=fldh7 value="">
</td>
<td align=right><input class="numeric access" type=text size=8 maxlength=8  value="" name=h4 id='h4' onchange="dot2(this);">
<input type=hidden name=h5 id=h5 value="">
<input type=hidden name=h6 id=h6 value="">
</td>
</tr>

<tr>
<td align=center nowrap>

<!-- pink/blue stuff -->
<table border=1 cellpadding=0 cellspacing=0><tr><td>
<table border=0 cellspacing=2 cellpadding=3 align=right id=proftab >
<tr>
<td>&nbsp;&nbsp; Net USD   200.00 &nbsp;&nbsp;
Profit  21.28 % &nbsp;&nbsp;</td>
<input type=hidden name=fldp0 id=fldp0 value='  200.00'>
<input type=hidden name=fldp1 id=fldp1 value=' 21.28'>
</tr>
</table>
</td></tr></table>
</td>
<td align=right>
	<b> Totals USD&nbsp;</b>
</td>

<td align=right><nobr><input disabled style="background-color:#e8e8e8;" class=numeric type=text size=8 maxlength=8 value="740.00" name='t3' id='t3'>&nbsp;</nobr></td>
<td></td>
<td></td>
<td align=right><nobr><input disabled style="background-color:#e8e8e8;" class=numeric type=text size=8 maxlength=8 value="  940.00" name='t4' id='t4'>&nbsp;</nobr></td>
</tr>
						<!-- Accessorial rows -->
</table>
</td>
</tr>
</table>

	<!-- end lefthand half -->
</td>
<td valign=top>
	<!-- righthand half -->
	  <!-- tab stuff -->
<!-- TABBED PANEL BEGINS HERE -->

<div id="container">
<ul class="menu">
<li id="tab1" class="active">Instructions</li>
<li id="tab2">Rate Conf Notes</li>  <!--safia yonker job 20501 09/22/2015 -->
<li id="tab3">Notes</li>
<li id="tab4">Ref #'s</li>
<li id="tab5">Items</li>
<li id="tab6">Updates</li>
<li id="tab7">Route</li>
<span id=dray name=dray>
<li id="tab8">Drayage</li>
</span>
</ul>

<span class="clear"></span>

<!-- INSTRUCTIONS BEGIN HERE -->
<div id=1 class="content tab1">

<span id="instructions">
<div class="scroll">

<table bgcolor="#f0f0f0" border="0" cellpadding="2" cellspacing="1"width=100% height=100%>
<tr><td>&nbsp;</td></tr>
<tr>
	<td align=center valign=top>
	  <table>
	    <tr>
	      <td align="left" nowrap>
	      <b> &nbsp;Special Instructions and Rate Confirmation Notes&nbsp;</b>
	      &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;

	      <span style="text-align: right;color: #0000dd; font-weight:600;" id=rem>(Remaining: 420)</span>
	      </td>
</tr>

<tr>
	      <td align=center>
	        <textarea wrap=none cols="50" rows="10" name="notes" id="notes"
onKeyUp="limitText(main.notes,'');">S00000000/S0000000</textarea>
</td>
</tr>

	    <tr>
	      <td height=10px align=left nowrap>
	        <img src=/images/red_rod_horizontal_200.gif id=bar height=100% style="width:0%;" name=bar>
	      <span id=mess style='text-align:center'></span>
	      </td>
	    </tr>

<!-- bmw 060916 #23655 -->
</table></td>
<td align=center valign=top><table>
<!-- bmw 060916 #23655 -->

	  <tr>
	  <td align="left">
	  <b> &nbsp;Bill of Lading Instructions and Notes&nbsp;</b>
	  &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
	  &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
	  <span style="text-align:right; color:#0000dd; font-weight:600;" id=rem2>(Remaining: 210)</span>
	  </td>
	</tr>

	<tr>
	  <td align=center>
	  <textarea wrap=none cols="50" rows="7" name="notes2" id="notes2"
	  onKeyUp="limitText(main.notes2,2);"></textarea>
	  </td>
	</tr>

	<tr>
	  <td height=10px align=left nowrap>
	  <img src=/images/red_rod_horizontal_200.gif id=bar2 height=100% style="width:0%;" name=bar2>
	  <span id=mess2 style='text-align:center'></span>
	  </td>
	</tr>
</table>
</td>
</tr>
</table>
</div>
</span>

</div>
<!-- INSTRUCTIONS END HERE -->

<!-- safia yonker job 20501 09/22/2015: add tab for gocust notes -->
<!-- fpweb/ajax calls will be used ot populate these -->
<!-- CONFIRMATION NOTES BEGIN HERE -->
<div id=2 class="content tab2">

<span>
<div class=scroll>
<table style="padding:1em" cellpadding=1 cellspacing=1 border=0 height=275px bgcolor=#f0f0f0 width=100%>
<tr><td>

<div id="customer_notes">
<h3>Customer Notes: ACME LOGISTICS, LLC - ATL</h3>
				<span> </span>
</div>

</td></tr>

<tr><td>

<div id="pickup_notes">
<h3>Pickup Notes: WANDO TERMINAL</h3>
				<span> </span>
</div>

</td></tr>
<tr><td>

<div id="consignee_notes">
<h3>Consignee Notes: WANDO TERMINAL</h3>
				<span> </span>
</div>

</td></tr>
</table>
</div>
</span>

</div>
<!-- CONFIRMATION NOTES END HERE -->

<!-- NOTES BEGIN HERE -->
<div id=3 class="content tab3">

<span>
<div class="scroll">

<table bgcolor="#ffffff" border="0" cellpadding="2" cellspacing="1" width=100% height=100%>
	<tr valign=top>
	  <td>
	  <table align=left id=notetab cellpadding=5 cellspacing=0 border="0" width="100%" bgcolor="#dfdfdf"  style="outline: #FF6600 1px; outline-style: outset; border-collapse: collapse" rules="rows">
	  <td align="center" nowrap><a href="Javascript:gonote('A')" class="bodyqa" style="color:blue;">Add Note</a></td><td align="center" nowrap>Updated By</td><td colspan=3 align="center">Note</td>

	  </table></td>
	</tr>
</table>
</div>
</span>

</div>
<!-- NOTES END HERE -->

<!-- REFERENCE NOS. START HERE -->
<div id=4 class="content tab4">
<div class=scroll>

<table bgcolor="#f0f0f0" border="0" cellpadding="2" cellspacing="1"width=100% height=100% border=1>
<tr>
	<td align=center>

<!-- ################################### -->

<table align=center border=0 cellpadding=2 cellspacing=4 height=100%>
	<tr>
	  <td valign=top>
	    <table>
	      <tr>
		    <td align=right>BL #</td>
		    <td><input type=text name=fld298 id=fld298 value="" size=18 maxlength=18></td>
		    <td align=right>Load #</td>
		    <td><input type=text name=fld299 id=fld299 value="" size=18 maxlength=18></td>
		  </tr>
		  <tr>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld292 id=fld292 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld293 id=fld293 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	      </tr>
	      <tr>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld294 id=fld294 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld295 id=fld295 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	  	  </tr>
	  	  <tr>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld296 id=fld296 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	  	    <td align=right>Ref #</td>
	  	    <td><input type=text name=fld297 id=fld297 value="" size=18 maxlength=18
	  	    		onchange="dup59(this);"></td>
	      </tr>

		  <tr class=job11678>
		    <td></td><td align=right><b>Required Ref #</b></td>
		    <td></td><td align=center><b>Value</b></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod360><label for=pod366><input type=text name=pld360 id=pld360 size=18 value=""></label></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod366 id=pod366 size=18 maxlength=18 value=""></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod361><label for=pod367><input type=text name=pld361 id=pld361 size=18 value=""></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod367 id=pod367 size=18 maxlength=18 value=""></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod362><label for=pod368><input type=text name=pld362 id=pld362 size=18 value=""></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod368 id=pod368 size=18 maxlength=18 value=""></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod363><label for=pod369><input type=text name=pld363 id=pld363 size=18 value=""></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod369 id=pod369 size=18 maxlength=18 value=""></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod364><label for=pod370><input type=text name=pld364 id=pld364 size=18 value=""></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod370 id=pod370 size=18 maxlength=18 value=""></td>
		  </tr>
		  <tr class=job11678>
		    <td></td><td align=right><span id=pod365><label for=pod371><input type=text name=pld365 id=pld365 size=18 value=""></span></td>
		    <td></td><td align=right>
		      <input type=text name=pod371 id=pod371 size=18 maxlength=18 value=""></td>
		  </tr>
	    </table>
	  </td>

	  <td>&nbsp;&nbsp;&nbsp;&nbsp;</td>

	  <td>
		<iframe id=addrefframe src='/route.php?prcnam=additionalrefs&qual=gj56nb24&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&sys=3a&pro=86873&fpweb_pg=addrefs&type=list&fpweb_ky=86873&fpweb_fn=prorefs&fpweb_ix=A' width=500px height=245px style="background-color:#ffffff"></iframe>
	  </td>
	</tr>
</table>

<!-- ################################### -->

</td></tr></table>

</div>
</div>
<!-- REFERENCE NOS. END HERE -->


<!--  "GRID" BEGINS HERE -->
<div id=5 class="content tab5">
<div class="scroll">

<table id="itemtbl" bgcolor="#f0f0f0" align=center cellpadding=2 cellspacing=1 width=100% height=100% border=0>
<tr><td align=center>
<table cellpadding=2 cellspacing=0 border=1 align="center">
<tr valign="center" style="line-height:1.5em;">
<td class=headrow align=center>Line</td>
<td class=headrow align=center>Pieces</td>
<td class=headrow align=center>Pallets</td>
<td class=headrow align=center>Type</td>
<td class=headrow align=center>Class</td>
<td class=headrow align=center>Weight (Lb)</td>
<td class="headrow metric" id="kgheader" style="display:none;" align=center>Weight (Kg)</td>
<td class=headrow align=center>Length</td>
<td class=headrow align=center>Width</td>
<td class=headrow align=center>Height</td>
<td class=headrow align=center>Product Code</td>
<!--<td class=headrow align=center><strong>Hazmat</strong></td>-->
<td class=headrow align=center>Description</td>
</tr>

<!-- -->

<!--getexa 1-->

<tr>
<td align=center>&nbsp;1</td>
<td align=center><input type=text  name=exa01 id=exa01 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa02 id=exa02 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc04 id=exc04><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa03 id=exa03><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa04 id=exa04 class=numeric size=5 maxlength=5 value="38000" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa04_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc01 id=exc01 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc02 id=exc02 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc03 id=exc03 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa05 id=exa05 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa07 id=exa07 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 2-->

<tr>
<td aling=center>&nbsp;2</td>
<td align=center><input type=text  name=exa11 id=exa11 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa12 id=exa12 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc14 id=exc14><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa13 id=exa13><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa14 id=exa14 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa14_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc11 id=exc11 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc12 id=exc12 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc13 id=exc13 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa15 id=exa15 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa17 id=exa17 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 3-->

<tr>
<td aling=center>&nbsp;3</td>
<td align=center><input type=text  name=exa21 id=exa21 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa22 id=exa22 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc24 id=exc24><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa23 id=exa23><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa24 id=exa24 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa24_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc21 id=exc21 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc22 id=exc22 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc23 id=exc23 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa25 id=exa25 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa27 id=exa27 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 4-->

<tr>
<td aling=center>&nbsp;4</td>
<td align=center><input type=text  name=exa31 id=exa31 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa32 id=exa32 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc34 id=exc34><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa33 id=exa33><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa34 id=exa34 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa34_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc31 id=exc31 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc32 id=exc32 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc33 id=exc33 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa35 id=exa35 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa37 id=exa37 size=40 maxlength=40 value=""></td>
</tr>


<!--getexa 5-->

<tr>
<td aling=center>&nbsp;5</td>
<td align=center><input type=text  name=exa41 id=exa41 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa42 id=exa42 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc44 id=exc44><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa43 id=exa43><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa44 id=exa44 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa44_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc41 id=exc41 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc42 id=exc42 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc43 id=exc43 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa45 id=exa45 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa47 id=exa47 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 6-->

<tr>
<td aling=center>&nbsp;6</td>
<td align=center><input type=text  name=exa51 id=exa51 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa52 id=exa52 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc54 id=exc54><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa53 id=exa53><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa54 id=exa54 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa54_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc51 id=exc51 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc52 id=exc52 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc53 id=exc53 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa55 id=exa55 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa57 id=exa57 size=40 maxlength=40 value=""></td>
</tr>


<!--getexa 7-->

<tr>
<td aling=center>&nbsp;7</td>
<td align=center><input type=text  name=exa61 id=exa61 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa62 id=exa62 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc64 id=exc64><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa63 id=exa63><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa64 id=exa64 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa64_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc61 id=exc61 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc62 id=exc62 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc63 id=exc63 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa65 id=exa65 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa67 id=exa67 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 8-->

<tr>
<td aling=center>&nbsp;8</td>
<td align=center><input type=text  name=exa71 id=exa71 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa72 id=exa72 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc74 id=exc74><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa73 id=exa73><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa74 id=exa74 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa74_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc71 id=exc71 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc72 id=exc72 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc73 id=exc73 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa75 id=exa75 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa77 id=exa77 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 9-->

<tr>
<td aling=center>&nbsp;9</td>
<td align=center><input type=text  name=exa81 id=exa81 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa82 id=exa82 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc84 id=exc84><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa83 id=exa83><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa84 id=exa84 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa84_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc81 id=exc81 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc82 id=exc82 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc83 id=exc83 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa85 id=exa85 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa87 id=exa87 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 10-->

<tr>
<td aling=center>&nbsp;10</td>
<td align=center><input type=text  name=exa91 id=exa91 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exa92 id=exa92 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exc94 id=exc94><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exa93 id=exa93><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exa94 id=exa94 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exa94_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exc91 id=exc91 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc92 id=exc92 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exc93 id=exc93 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exa95 id=exa95 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exa97 id=exa97 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 11-->

<tr>
<td aling=center>&nbsp;11</td>
<td align=center><input type=text  name=exb01 id=exb01 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb02 id=exb02 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd04 id=exd04><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb03 id=exb03><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb04 id=exb04 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb04_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd01 id=exd01 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd02 id=exd02 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd03 id=exd03 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb05 id=exb05 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb07 id=exb07 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 12-->

<tr>
<td aling=center>&nbsp;12</td>
<td align=center><input type=text  name=exb11 id=exb11 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb12 id=exb12 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd14 id=exd14><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb13 id=exb13><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb14 id=exb14 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb14_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd11 id=exd11 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd12 id=exd12 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd13 id=exd13 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb15 id=exb15 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb17 id=exb17 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 13-->

<tr>
<td aling=center>&nbsp;13</td>
<td align=center><input type=text  name=exb21 id=exb21 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb22 id=exb22 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd24 id=exd24><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb23 id=exb23><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb24 id=exb24 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb24_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd21 id=exd21 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd22 id=exd22 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd23 id=exd23 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb25 id=exb25 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb27 id=exb27 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 14-->

<tr>
<td aling=center>&nbsp;14</td>
<td align=center><input type=text  name=exb31 id=exb31 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb32 id=exb32 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd34 id=exd34><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb33 id=exb33><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb34 id=exb34 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb34_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd31 id=exd31 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd32 id=exd32 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd33 id=exd33 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb35 id=exb35 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb37 id=exb37 size=40 maxlength=40 value=""></td>
</tr>


<!--getexa 15-->

<tr>
<td aling=center>&nbsp;15</td>
<td align=center><input type=text  name=exb41 id=exb41 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb42 id=exb42 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd44 id=exd44><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb43 id=exb43><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb44 id=exb44 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb44_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd41 id=exd41 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd42 id=exd42 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd43 id=exd43 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb45 id=exb45 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb47 id=exb47 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 16-->

<tr>
<td aling=center>&nbsp;16</td>
<td align=center><input type=text  name=exb51 id=exb51 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb52 id=exb52 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd54 id=exd54><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb53 id=exb53><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb54 id=exb54 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb54_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd51 id=exd51 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd52 id=exd52 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd53 id=exd53 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb55 id=exb55 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb57 id=exb57 size=40 maxlength=40 value=""></td>
</tr>


<!--getexa 17-->

<tr>
<td aling=center>&nbsp;17</td>
<td align=center><input type=text  name=exb61 id=exb61 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb62 id=exb62 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd64 id=exd64><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb63 id=exb63><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb64 id=exb64 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb64_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd61 id=exd61 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd62 id=exd62 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd63 id=exd63 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb65 id=exb65 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb67 id=exb67 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 18-->

<tr>
<td aling=center>&nbsp;18</td>
<td align=center><input type=text  name=exb71 id=exb71 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb72 id=exb72 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd74 id=exd74><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb73 id=exb73><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb74 id=exb74 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb74_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd71 id=exd71 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd72 id=exd72 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd73 id=exd73 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb75 id=exb75 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb77 id=exb77 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 19-->

<tr>
<td aling=center>&nbsp;19</td>
<td align=center><input type=text  name=exb81 id=exb81 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb82 id=exb82 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd84 id=exd84><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb83 id=exb83><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb84 id=exb84 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb84_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd81 id=exd81 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd82 id=exd82 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd83 id=exd83 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb85 id=exb85 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb87 id=exb87 size=40 maxlength=40 value=""></td>
</tr>

<!--getexa 20-->

<tr>
<td aling=center>&nbsp;20</td>
<td align=center><input type=text  name=exb91 id=exb91 class=numeric size=5 maxlength=5 value="" onblur="addem();"></td>
<td align=center><input type=text  name=exb92 id=exb92 class=numeric size=2 maxlength=2 value="" onblur="addemplts();"></td>
<td align=center><select  name=exd94 id=exd94><option></option>
<option value=""></option>
</select></td>
<td align=center><select  name=exb93 id=exb93><option value=""></option>
<option>50</option><option>55</option><option>60</option><option>65</option><option>70</option><option>77.5</option><option>85</option><option>92.5</option><option>100</option><option>110</option><option>125</option><option>150</option><option>175</option><option>200</option><option>250</option><option>300</option><option>400</option><option>500</option>
</select>
</td>
<td class=lbweight align=center><input type=text  name=exb94 id=exb94 class=numeric size=5 maxlength=5 value="" onblur="addemw();"></td>
<td class="kgweight metric" align=center style="display:none"><input type=text id=exb94_m class=protected readonly size=5 maxlength=5 value=""></td>
<td align=center><input type=text  name=exd91 id=exd91 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd92 id=exd92 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exd93 id=exd93 size=4 maxlength=4 value=""></td>
<td align=center><input type=text  name=exb95 id=exb95 size=15 maxlength=15 value=""></td>
<td align=center><input type=text  name=exb97 id=exb97 size=40 maxlength=40 value=""></td>
</tr>
</table></td></tr>
</table>

</div>

</div>
<!--  "GRID" ENDS HERE -->


<!-- UPDATES BEGIN HERE -->
<div id=6 class="content tab6">

<span>
<div class=scroll>

<table cellpadding=1 cellspacing=1 border=0 height=275px bgcolor=#f0f0f0 width=100%>
<tr>
<td align=center>
<table cellspacing=3 cellpadding=3>

		<tr><td>&nbsp;&nbsp;&nbsp;&nbsp;</td>

		<td valign=top><table bgcolor="#dfdfdf" cellpadding=2 cellspacing=2>

	    <tr>
	      <td>&nbsp;</td>
	      <td align=center><b>&nbsp;&nbsp;&nbsp;&nbsp;User&nbsp;&nbsp;&nbsp;&nbsp;</td>
	      <td align=center>&nbsp;&nbsp;&nbsp;&nbsp; Date &nbsp;&nbsp;&nbsp; Time&nbsp;&nbsp;&nbsp;&nbsp;</b></td>
	    </tr>
	    <tr>
	      <td align=right><b>Created</b></td>
	      <td>JDOE</td>
	      <td>11/26/25 9:42 &nbsp;&nbsp;</td>
	    </tr>
	    <tr>
	      <td align=right><b>Reserved</b></td>
	      <td></td>
	      <td> &nbsp;&nbsp;</td>
	    </tr>
	    <tr>
	      <td align=right><b>Covered</b></td>
<td>JDOE</td>
	      <td>11/26/25 9:47 &nbsp;&nbsp;</td>
	    </tr>
	    <tr>
	      <td align=right><b>Pays</b></td>
<td>JDOE</td>
	      <td>11/26/25 9:47</td>
	    </tr>
	    <tr>
	      <td align=right><b>Conf Sent</b></td>
	      <td>JDOE</td>
	      <td>11/26/25 9:47</td>
	    </tr>
	    <tr>
	      <td align=right><b>Conf Rec'd</b></td>
	      <td></td>
	      <td> </td>
	    </tr>
<tr>
<td align=right><b>TONU</b></td>
<td>        </td>
<td>              </td>
</tr>

		</table></td>

		<td>&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;</td>

		<td valign=top><table bgcolor="#dfdfdf" cellpadding=2 cellspacing=2>

	    <tr>
	      <td>&nbsp;</td>
	      <td align=center><b>&nbsp;&nbsp;&nbsp;&nbsp;User&nbsp;&nbsp;&nbsp;&nbsp;</td>
	      <td align=center>&nbsp;&nbsp;&nbsp;&nbsp; Date &nbsp;&nbsp;&nbsp; Time&nbsp;&nbsp;&nbsp;&nbsp;</b></td>
	    </tr>

	    <tr>
	      <td align=right>&nbsp;&nbsp;<b>Dispatched</b></td>
<td></td>
	      <td> </td>
	    </tr>
	    <tr>
	      <td align=right><b>Will PU</b></td>
<td></td>
	      <td> </td>
	    </tr>
	    <tr>
	      <td align=right><b>Loaded</b></td>
<td></td>
	      <td> </td>
	    </tr>
	    <tr>
	      <td align=right><b>Delivered</b></td>
<td></td>
	      <td> </td>
	    </tr>
	    <tr>
	      <td align=right><b>Released</b></td>
<td></td>
	      <td> </td>
	    </tr>
	    <tr>
	      <td align=right><b>Invoiced</b></td>
	      <td></td>
	      <td nowrap>        </td>
	    </tr>
	    <tr>
	      <td align=right><b>Paid</b></td>
	      <td></td>
	      <td nowrap></td>
	    </tr>

	</table></td>

		<td>&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>
	  <tr><td>&nbsp;</td></tr>
<tr>
<td colspan=5 align=center>
	  <input type="button" class="ifsaved always" onClick="dologf();" value="View Log File" />
<!--
&nbsp;&nbsp;
<input type="button" class="ifsaved" onclick="doedilog();" value="View EDI Log File">
-->
</td></tr>
	    </table>
	    </td>
	  </tr>
</table>
</div>
</span>

</div>
<!-- UPDATES END HERE -->

<!-- SUGGESTED ROUTE BEGIN HERE -->
<div id=7 class="content tab7">

<span>
<div class=scroll>
<table cellpadding=1 cellspacing=1 border=0 height=275px bgcolor=#f0f0f0 width=100%>
<tr><td>

		<!-- <div id=suggroute></div> -->

<table width=945px><tr><td align=center>
<table class="view" width="100%" border="0" cellpadding="2" cellspacing="2">
<tr>
	<td align=right valign=top width=30%>
		<span style="line-height:16pt;font-size:11pt;font-weight:600;color:blue;">Pick Up Suggested Routing &nbsp;</span>
	</td>
	<td align=left bgcolor=#dfdfdf width=70%>
		                                                                      <br>
<br>
		                                                                      <br>
		                                                                      <br>
		                                                                      <br>
		                                                                      <br>
		                                                                      <br>
		<br>
		                                                                      <br>
<!--
		<input name=cu_fld30 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld31 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld32 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld33 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld34 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld35 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld36 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld37 disabled value="                                                                      " size=75 maxlength=70><br>
		<input name=cu_fld38 disabled value="                                                                      " size=75 maxlength=70>
-->
	</td>
</tr>
</table>
</td></tr></table>


	    </td></tr>

</table>
</div>
</span>

</div>
<!-- SUGGESTED ROUTE END HERE -->

<!-- DRAYAGE BEGIN HERE -->

<div id=8 class="content tab8">

<span>
<div class=scroll>
<table cellpadding=1 cellspacing=1 border=0 height=275px bgcolor=#f0f0f0 width=100%>
<tr><td>

<table width=945px><tr><td align=center>
<table class="view" width="100%" border="0" cellpadding="2" cellspacing="2">
<tr>
	<td align=right valign=top width=30%>
		<span style="line-height:16pt;font-size:11pt;font-weight:600;color:blue;">Drayage &nbsp;</span>
	</td>
	<td align=left bgcolor=#dfdfdf width=50%>
<!--        <b>-->
<table>

	    <tr><td>Booking # </td> <td><input type=text size=18 name="tb_fld858" id="tb_fld858" value=""></td></tr>
		<tr><td>Container # </td>
			<td><input type=text size=18 class="container" name="tb_fld100" id="tb_fld100"
				title="Required Format: AAAA - 123456[7]"
				value=""></td>
		</tr>
		<tr><td>Last Free Day </td> <td><input type=text class=datepick size=8 name="tb_fld859" id="tb_fld859" value="" onchange="domdy(this);"></td></tr>
		<tr><td>Master BL # </td> <td> <input type=text size=18 name="tb_fld153" id="tb_fld153" value=""></td></tr>
		<tr><td>STCC Code </td> <td> <input type=text size=8 name="tb_fld863" id="tb_fld863" value=""></td></tr>

</table>
<!--</b> -->
	</td>
</tr>
</table>
</td></tr></table>


	    </td></tr>

</table>
</div>
</span>
</div>
<!-- DRAYAGE END HERE -->

	<!-- end righthand half -->
</div>
</td>
<td>&nbsp;&nbsp;</td>
</tr>
<tr><td colspan=3>&nbsp;</td></tr>
</table>

<!-- end of 3rd panel -->

<input type=hidden name=paynet2 id=paynet2 value="700.00">


<!-- ##################### end 3rd data pane ############### -->


<tr><td align=left>


<!-- START BROKERAGE STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<div id="dispatch" class=panel3>

<table class=panel1>
<tr><td><table cellpadding=2 cellspacing=2 border=0 valign=top>
<tr>
<td align=right>
<a class=lo href="javascript:dosafer('300000026');">MC #</a>
</td>
<td align=left><input type=text class=mkcarr id=mcno name=mcno size=9 maxlength=9 value="1374985"></td>
<td align=right><a class="lo always" href="javascript:upCar('300000026');">Carrier</a></td>
	  <td align=left><input readonly type=text class=mkcarr id=fldcarr name=fldcarr value="CLEVES LOGISTICS LLC          " size=25 maxlength=30
		  onchange="reqeta(this);"></td>
	  <input type=hidden name=fld84 id=fldcarrid value="300000026"></td>
<td align=right>Phone</td>
<td align=left><input type=text id=fld172 name=fld172 value="(*************" size=18 class=phone maxlength=14></td>
</tr>

<tr>
<td align=right nowrap>DOT #</td>
<td align=left><input type=text class=mkcarr id=dotno name=dotno size=7 maxlength=9 value="3810489"></td>
	  <td align=right><nobr>&nbsp;&nbsp;&nbsp;Dispatcher</nobr></td>
	  <td align=left nowrap><input name="fld176" id=fld176 size="25" maxlength="20" value="DISPATCH" ></td>
		  <td align=right><label for=di_fld158>Seal #</label></td>
<td align=left><input type=text size=18 maxlength=18 name=di_fld158 id=di_fld158 value=""></td>
</tr>

	<tr>
<td align=right>SCAC</td>
<td align=left><input type=text class=mkcarr id=scac name=scac size=7 maxlength=4 value="CVPJ"></td>
<!--          <td align=right>Truck #</td>
<td align=left><input type=text id=di_fld191 name=di_fld191 value="" size=20 maxlength=18></td>
-->
<td align=right><a class=lo href="javascript:carriertracking();">Ref/Driver</a></td>
	  <td align=left><input type=text id=dname name=fld74 size=25 maxlength=18 value="">
<input type=hidden name=fld87 id=fld87 value="">              </td>
<td align=right>Cell</td>
<td align=left><input type=text id=di_fld177 name=di_fld177 value="" size=18 class=phone maxlength=14></td>
	</tr>

<tr>
<td align=right><nobr>Truck #</td>
<td align=left><input type=text id=di_fld191 name=di_fld191 value="" size=16 maxlength=18></td>
<!--          <td></td><td></td>-->
<td align=right><a class=lo href="javascript:carriertracking();">2nd Ref/Driver</a></td>
	  <td align=left><input type=text id=dname2 name=di_fld188 size=25 maxlength=18 value="">
</td>
<td align=right>Cell</td>
<td align=left><input type=text id=di_fld189 name=di_fld189 value="" size=18 class=phone maxlength=14></td>
	</tr>

	<tr>
<td align=right><nobr>Trailer #</td>
<td align=left><input type=text id=di_fld89 name=di_fld89 value="" size=16 maxlength=18></td>
<!--	  <td></td><td></td>-->
		<td align=right><a class=lo href="javascript:domail($('#fld196').val());">Email</a></td>
<td colspan=3 align=left>
	<div id=anyemail style="display:inline">
		<input type=text id=fld196 value="<EMAIL>" name=fld196 size=54 maxlength=60>
	</div>
	<div id=authemail style="display:none">
		<select name="fld196" id="fld196">  </select>
	</div>
</td>
</tr>

	<!-- jh 110216 job 25675-->
	<tr>
	  <td align=right nowrap><nobr>Disp City </td>
	  <td align=left colspan=3 nowrap>
		<input type=text size=15 maxlength=15 id=fld91 name=fld91 value="">
		&nbsp; St <input type=text size=2 maxlength=2 id=fld92 name=fld92 value="">
		<!-- bmw 012317 #26440 conditional protect of miles field -->
		&nbsp; Empty Miles <input type=text size=4 id=fld118 name=fld118 value=""</td>

	</tr>

</table>
</td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

	  <td>
		<table cellpadding=2 cellspacing=2 border=0 valign=top>
		<tr>
		  <td></td>
		  <td align=left colspan=2>&nbsp;&nbsp;&nbsp;&nbsp;<a class=lo href="javascript:prtform('ask_insreq');"> Expires</a></td>
		</tr>
		<tr>
		  <td align=right>Cargo</td>
		  <td align=left><input type=text disabled style="background-color:#e8e8e8;" id=car_cargo size=6></td>
		</tr>
		<tr>
		  <td align=right>Liability</td>
		  <td align=left><input type=text disabled style="background-color:#e8e8e8;" id=car_liab size=6></td>
		</tr>
		<tr>
		  <td align=right>Gen Liab</td>
		  <td align=left><input type=text disabled style="background-color:#e8e8e8;" id=car_genl size=6></td>
		</tr>
		<tr>
		  <td align=right nowrap>Safety Rating</td>
		  <td align=left><input type=text disabled style="background-color:#e8e8e8;" id=car_sfty size=15></td>
		</tr>
		</table>
	  </td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

	  <td>
		<table cellpadding=2 cellspacing=2 border=0 vaign=top>
		<tr>
		  <td align=center class=csa>CSA</td>
		  <td align=right class=csa>Driving</td>
		  <td align=left class=csa><input type=text disabled style="background-color:#e8e8e8;" id=car_safe size=6></td>
		  <td align=right class=csa>HOS </td>
		  <td align=left><input class=csa type=text disabled style="background-color:#e8e8e8;" id=car_acci size=6></td>
		  <td align=right class=csa>Fitness</td>
		  <td align=left><input class=csa type=text disabled style="background-color:#e8e8e8;" id=car_driv size=6></td>
		  <td align=right class=csa>Cont Sub</td>
		  <td align=left><input class=csa type=text disabled style="background-color:#e8e8e8;" id=car_vehi size=6></td>
		  <td align=right class=csa>Maint</td>
		  <td><input class=csa type=text disabled style="background-color:#e8e8e8;" id=car_mgmt size=6></td>
		</tr>
		</table>
	  </td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

<td>
<table cellpadding="2" cellspacing="2" border="0" valign=top>

<tr><td align=center colspan=2>
	  <div id="covuncov3" style="display:inline">
	  <input type=button class="ifadv butt" value="Un-Cover" onclick="uncover(  86873,2293,'COVERED');" style="color:#000000;font-weight:900">
	  </div>
<!-- </td></tr>

	<tr>
	  <td align=center colspan=2><br> -->
	  &nbsp;&nbsp;
	  <input type="button" onclick="dosubmit('save');" value="Save" style="color:#000000;font-weight:900"></td>
	</tr>

	<tr>
	  <td align=center colspan=2><br>
		<span id=dispbutt1 style="display:none">
		<input type="button" onclick="doss('  86873','2293');" class=ifsaved value="Smart Search" style="color:#000000;font-weight:900">
		</span>
		<span id=dispbutt2 style="display:none">
		<input type="button" onclick="prtform('c');" id=carconfx class=ifsaved value="Carrier Confirmation" style="color:#000000;font-weight:900">
		</span>
		<span id=dispbutt3 style="display:none">
		<input type="button" onclick="prtform('p');" class=ifsaved value="Ask for Pick Up/Delivery Info" style="color:#000000;font-weight:900">
		</span>
	  </td>
	</tr>

<tr style="display:none;">
	  <td align=center colspan=2><br><input type="button" name="bids" id=BidsButton class=ifsaved onclick="dobids('bids');" value="" style="color:#000000;font-weight:900"></td>
	</tr>

	<tr>
	  <td align=center colspan=2><br><input type="button" name="godo" id="godo" class="ifsaved always" onclick="docc();" value="Check Calls" style="color:#000000;font-weight:900"></td>
	</tr>

	<tr>
	  <td align=center colspan=2 rowspan=2 valign=top style="line-height:9pt;">

	  </td>
	</tr>
</table>
</td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

<td>
<table cellpadding="2" cellspacing="2" border="0" valign=top>

	<tr>
	  <td align=right>Conf Sent </td>
	  <td nowrap><input name="fld549" id="fld549" withtime=fld559 size=6 maxlength=8 value="11/26/25" class=datepick onchange="domdy(this);">
	  </td><td> Time &nbsp; <input name="fld559" id=fld559 size=3 maxlength=5 value="9:47" class=time onchange="tryhms(this);"></td>
	</tr>
	<tr>
	  <td align=right>Conf Rec'd </td>
	  <td nowrap><input name="fld561" id="fld561" withtime=fld562 size=6 maxlength=8 value="" class=datepick onchange="domdy(this);">
	  </td><td> Time &nbsp; <input name="fld562" id=fld562 size=3 maxlength=5 value="" class=time onchange="tryhms(this);"></td>
	</tr>
	<tr>
	  <td align=right nowrap>Dispatched </td>
<td nowrap align=left><input name="di_hdate" id=di_hdate onfocus="ckins(this);" withtime=di_fld53 size="6" maxlength="8" value="" class=datepick onchange="reqdri(this);ckins(this);domdy(this);">
</td><td> Time &nbsp; <input name="di_fld53" id=di_fld53 size="3" maxlength="5" value=""  class=time onchange="tryhms(this);"></td>
	</tr>
	<tr>
	  <td align=right>Will PU </td>
<td nowrap align=left><input name="di_wdate" id=di_wdate withtime=di_fld54 size="6" maxlength="8" value="" class=datepick onchange="domdy(this);">
</td><td> Time &nbsp; <input name="di_fld54" id=di_fld54 size="3" maxlength="5" value=""  class=time onchange="tryhms(this);"></td>
	</tr>
	<tr>
<td align=right nowrap>Arrived PU </td>
<td nowrap align=left><input name="di_avdate" id=di_avdate withtime=di_avtime size="6" maxlength="8"
		value="" class="event datepick late_pu" late="pu" onchange="domdy(this);do_seq();">
</td><td> Time &nbsp; <input name="di_avtime" id=di_avtime class="time late_pu" late="pu" size="3" maxlength="5"
		value="" onchange="tryhms(this);do_seq();">
		</td>
		<td nowrap><span id="late_pu_ico"></span>
	  </td>
	</tr>

</table>
</td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

	<td>
<table cellpadding="2" cellspacing="2" border="0" valign=top>
	<tr>
	  <td align=right>Loaded </td>
<td nowrap align=left><input name="di_ldate" id=di_ldate onfocus="ckins(this);" withtime=di_ltime size="6" maxlength="8" value="" class=datepick onchange="reqseal();reqdri(this);ckins(this);domdy(this);do_seq();">
</td><td align=left> Time &nbsp; <input name="di_ltime" id=di_ltime size="3" maxlength="5" value=""  class=time onchange="tryhms(this);do_seq();"></td>
	<td width=40px></td>
	</tr>
	<tr>
	  <td align=right>Del ETA </td>
<td nowrap align=left><input name="di_edate" id=di_edate withtime=di_etime size="6" maxlength="8" value="" class=datepick onchange="domdy(this);">
</td><td align=left> Time &nbsp; <input name="di_etime" id=di_etime size="3" maxlength="5" value=""  class=time onchange="tryhms(this);"></td>
	<td width=40px></td>
</tr>
	<tr>
	  <td align=right>Arrived Cons</td>
<td nowrap align=left><input name="di_addate" id=di_addate withtime=di_addtime size="6" maxlength="8"
				value=""  class="event datepick late_deliv" late="deliv" onchange="domdy(this);do_seq();">
</td><td align=left>Time &nbsp;  <input name="di_addtime" id=di_addtime size="3" maxlength="5"
				value=""   class="time late_deliv" late="deliv" onchange="tryhms(this);do_seq();ckdelvt();">
		</td>
		<td nowrap><span id="late_deliv_ico"></span>
	  </td>
	<td width=40px></td>
	</tr>
	<tr>
	  <td align=right nowrap>Delivered </td>
<td nowrap align=left><input name="di_fld145" id=di_fld145 onfocus="ckins(this);" size="6" withtime=di_dtime maxlength="8" value=""  class=datepick onchange="reqseal();ckins(this);domdy(this);do_seq();">
</td><td align=left>Time &nbsp; <input name="di_dtime" id=di_dtime size="3" maxlength="5" value=""   class=time onchange="tryhms(this);do_seq()"></td>
	<td width=40px></td>
</tr>
<tr>
	  <td align=right nowrap><b>Signed By</b></td>
	  <td colspan=3 nowrap><input type="text" value="" name="di_fld147" id="di_fld147"
			maxlength="25" size="20"  />
	  &nbsp;
	    <input type=checkbox name=di_box148 id=di_box148 class=link2yn onchange="ckosd(this);">
	    <label for=di_box148 id=boxlabel148> OSD </label>
	    <input type=hidden name=di_fld148 id=di_fld148 value="">

	  </td>
<!--
<td onclick="ckosd(this);" ><font size=+1>OSD</font></td>
-->
</tr>

<script language=javascript>

function ckosd(pointer){
var x=document.getElementById("di_box148").checked;
var target = "/route.php?" + fpw_stuff + "&prcnam=osd&type=show&pro=" + mp_pn + "&isfbox=0&is=fbox";
if (islocked) target += "&islocked=Y";
fbox(target,'large');
pointer.checked=true;

document.getElementById("fld148").value="Y";
document.getElementById("refresh").value = 'Y';
}

</script>


	</table>
	</td></tr>


<!--</table></td></tr>-->

</table>
</div>

<!-- END BROKERAGE STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<!-- START INTERMODAL STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<div id="intermodalh" class=panel3>

<input type=hidden name=fld44 value="900.00" id=fld44>
<input type=hidden name=fld77 value="700.00" id=fld77>

<input type=hidden name=im_fld145 id=im_fld145 value="">
<input type=hidden name=im_dtime id=im_dtime value="">

<table class=panel1>
<tr><td><!-- <table border=0 align=center>

<tr>

<td valign=top align=right> -->

	<table align=left cellpadding=2 cellspacing=0 border=0>
<tr><td>&nbsp;</td></tr>
<tr>
<td align=right valign=top nowrap>
		 <table>
		  <tr><td align=right>Container #</td>
		  <td><input type=text class="container" name=im_fld100 id=im_fld100
				title="Required Format: AAAA - 123456[7]"
				value="" size="16" maxlength="12" ></td>
		  </tr>
<tr><td align=right>Trailer #</td>
		  <td><input type=text name=im_fld89 id=im_fld89 value="" size="16" maxlength="18" ></td>
		  </tr>
<tr><td align=right>Routing #</td>
		  <td><input type=text name=fld861 id=fld861 value="" size="16" maxlength="32"></td>
		  </tr>
<tr><td align=right>Waybill # 1</td>
		  <td><input type=text name=fld855 id=fld855 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right>Waybill # 2</td>
		  <td><input type=text name=fld856 id=fld856 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right>Waybill # 3</td>
		  <td><input type=text name=fld857 id=fld857 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right>Booking #</td>
		  <td><input type=text name=im_fld858 id=im_fld858 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right><label for=im_fld158>Seal # </label></td>
		  <td><input type=text name=im_fld158 id=im_fld158 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right>STCC</td>
		  <td><input type=text name=im_fld863 id=im_fld863 value="" size="16" maxlength="8"></td>
		  </tr>
<tr><td align=right>HAWB</td>
		  <td><input type=text name=im_fld153 id=im_fld153 value="" size="16" maxlength="18"></td>
		  </tr>
<tr><td align=right>MAWB</td>
		  <td><input type=text name=im_fld154 id=im_fld154 value="" size="16" maxlength="18"></td>
		 </tr></table>

		</td>

	      <td>
			&nbsp;&nbsp;&nbsp;&nbsp;
		  </td>

		<td align=left valign=top><table>
		<tr>
		  <td align=left nowrap width=1210px>PU Ramp <input type=text name=fld864 id="fld864" value=""
			size="16" maxlength="20">&nbsp;
<input type=text name=fld865 value="" id="fld865" size="2" maxlength="2"> &nbsp;
Free Tm Ends
		  <input type=text name=im_fld859 id=_fld859 value="" size="6" maxlength="8"
			class=datepick onchange="domdy(this);">
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;
Dest Ramp <input type=text id=fld866 name=fld866 value=""
			size="16" maxlength="20">&nbsp;
<input type=text id=fld867 name=fld867 value="" size="2" maxlength="2"> &nbsp;
Free Tm Ends
		  <input type=text name=fld860 id=fld860 value="" size="6" maxlength="8"
			class=datepick onchange="domdy(this);">

		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;
		  &nbsp;&nbsp;&nbsp;&nbsp;

	      <input type="button" onclick="dosubmit('save');"
			value="Save" style="color:#000000;font-weight:900">
	      &nbsp;&nbsp;&nbsp;&nbsp;
	      <input  type=button style="color:#000000;font-weight:900" class=butt
			onclick="document.route2b.type.value='Add a Leg';imcheck('add a leg','');"
			name=zype value="Add A Leg">
	      &nbsp;&nbsp;&nbsp;&nbsp;
	      <input type=button style="color:#000000;font-weight:900" class=butt
			name="checkcall" id="checkcall" value="Check Calls" onclick="docc();">
		  </td>
			<!-- <tr style="display:none;">
			<td colspan=2 align=center valign="middle" bgcolor="#E8E8E8" style="width:150px;
				border:solid 2px #C0C0C0"> -->
			<!--  span style="display:none;"> style="padding:1px; width:100%; overflow:hidden;
				line-height:13px;">-->
		  <td nowrap><table valign=center style="display:none;">

			</td></tr></table></span>
	</td>
		<td>&nbsp;</td>
	</tr>

</form>

	<tr>
<td id=imdiv colspan=2>

<br>
<!-- Intermodal -->
<table align=center width=100% cellspacing=0 cellpadding=1 border=1>
<tr bgcolor="#ffffd0">
<td align=center><b>Action</b></td>
<td align=center><b>Carrier</b></td>
<td align=center><b><div id="imselecttyp">Type</div></b></td>
<td align=center><b><div id="imselectmsg">Status</div></b></td>
<td align=center><b>Reference</b></td>
<td align=center><b>Origin City</b></td>
<td align=center><b>State</b></td>
<td align=center><b>Zip</b></td>
<td align=center><b>Destination City</b></td>
<td align=center><b>State</b></td>
<td align=center><b>Zip</b></td>
<td align=center colspan=2><b>Will PU<br>Date / Time</b></td>
<td align=center colspan=2><b>Dispatched<br>Date / Time</b></td>
<td align=center colspan=2><b>Loaded<br>Date / Time</b></td>
<td align=center colspan=2><b>ETA<br>Date / Time</b></td>
<td align=center colspan=2><b>Complete<br>Date / Time</b></td>
<td align=center><b>Pays</b></td>
<td align=center><b>Release</b></td>
</tr>  <!-- fubar  86873 -->
<form method=post id=Webim name=Webim action=/route.php target="_top">
<input type=hidden name=pro value="100002">
<input type=hidden name=type value="update">
<input type=hidden id=impayrec name=payrec value=2293>
<input type="hidden" name="repno" value="">
<input type=hidden name=sys value="3a">
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="prcnam" value="imlegs">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name="row">
<input type="hidden" name="sub">
<input type="hidden" name="insert">
<input type="hidden" name="doins">
<input type=hidden name=ctlrec value="  131119">
<input type=hidden name=ctlval value="">

<input type=hidden id=im_fld5->

<tr  onmouseout="this.className='lo';" class="lo">


<td><select style="width: 100px" name="imacts" id="imacts" onclick="imactions(this.value,'','38000','','2293');"><option value="">Select Action</option><option value="updleg">Update This Leg</option><option value="delleg">Delete This Leg</option></select></td>
<td><input id=im_fld2-  size=16 maxlength=20 value="ACME LOGISTICS, LLC - ATL"</td>
<td><select id=im_typ-  class=imflds></select></td>
<td id="imcolor" nowrap style="background-color:;line-height:24px">&nbsp;<select style="width: 100px" class="imselect" name="imstat" id="imstat"></select>&nbsp;</td>
<td><input id=im_fld4- size=12 maxlength=12 value="" class=imflds></td>
<td><input id=im_fld23-  size=12 maxlength=20 value="F" class=imflds></td>
<td><input id=im_fld24-  size=2 maxlength=2 value="900.00" class=imflds></td>
<td><input id=im_fld25-  size=4 maxlength=6 value="4100" class=imflds></td>
<td><input id=im_fld63-  size=12 maxlength=20 value="JDOE" class=imflds></td>
<td><input id=im_fld64-  size=2 maxlength=2 value="40" class=imflds></td>
<td><input id=im_fld65-  size=4 maxlength=6 value="HA" class=imflds></td>
<td><input id=im_fld140- size=4 maxlength=8 value="" class=imflds
	onchange="domdy(this);"></td>
<td><input id=im_fld141- size=4 maxlength=8 value="" class=imflds></td>
<td><input id=im_fld223- size=4 maxlength=8 value="" class=imflds
	onchange="domdy(this);"></td>
<td><input id=im_fld224- size=4 maxlength=8 value="" class=imflds></td>
<td><input id=im_fld225- size=4 maxlength=8 value="" class=imflds
	onchange="domdy(this);"></td>
<td><input id=im_fld226- size=4 maxlength=8 value="" class=imflds></td>
<td><input id=im_fld142- size=4 maxlength=8 value="" class=imflds
	onchange="domdy(this);"></td>
<td><input id=im_fld143- size=4 maxlength=8 value="" class=imflds></td>
<td><input id=im_fld90- size=4 maxlength=8 value="40" class=imflds
	onchange="domdy(this);"></td>
<td><input id=im_fld91- size=4 maxlength=8 value="" class=imflds></td>
<td align=right><input id=im_fld233-  size=6 maxlength=8 value="" class=imflds></td>
<td align=center><input type=checkbox class="imrls" name="rls_2293" id="rls_2293" ></td>

</form>
</tr>  <!-- fubar  86873 -->
<tr>  <!-- fubar  86873 -->
<td colspan=18 align=center>
<form method=post name=route2b action=/route.php target="_top">
<input type=hidden name=pro value="  86873">
<input type=hidden name=type>
<input type=hidden name=payrec>
<input type=hidden name=qual value="gj56nb24">
<input type=hidden name=prcnam value=imlegs>
<input type=hidden name=sys value="3a">
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type=hidden name=no_triplegs value="N">
<input type=hidden name=no_tripleg_browse value='Y'>

	<input type=hidden name=ctlrec value="  131119">
	<input type=hidden name=ctlval value="">
&nbsp;<br>
</form>
</td>
</tr>
</table>


</td>
</tr>
</table>  <!-- panel1 -->
</td></tr></table>
</td></tr></table>
</div>


<!--- END INTERMODAL STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<!-- START EASY TRUCKING STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<div id="legs" class=panel3>

<table class=panel1>
<tr><td valign=top><table class=panel2>

<tr><td align=left colspan=4 valign=top>&nbsp;
<a href="javascript:setlegs('long');" class="lo" style="line-height:14pt;font-weight:600;">Triplegs View</a>
</td></tr>

<tr><td align=right valign=top><table>

<td align=right><tr>

<td align=right>Truck # &nbsp;
<input type="text" class=mkcarr name="tr_fld191"
		value="" size="6" maxlength="6" id="truckno"
		onchange="reqeta(this)";
		onfocus="HideDiv('autocomplete');"
		onkeyup="ShowTrucks(this);">
		&nbsp;&nbsp;
		Driver</td>
	  <td align=left>
<input type="text" class=mkcarr name="drvname" value="" size="20"
maxlength="20" id="drvname"
onchange="reqeta(this);"
onfocus="HideDiv('autocomplete');"
onkeyup="ShowDrivers(1, this);">
	</td>
	<td align=right>
		Cell</td>
	<td><input type=text class=phone name=tr_fld177 id=tr_fld177 size=14 class=phone maxlength=14 value="">
		&nbsp;&nbsp;&nbsp;&nbsp;
</td>

<input type=hidden id=drvid name=drvid>
<input type=hidden id=trkid name=trkid>

<td colspan=3 rowspan=4 valign=top align=center style="line-height:14pt;font-weight:600;">
<div id="covuncov4" style="display:inline">
<input type=button class="ifadv butt" value="Un-Cover" onclick="uncover(  86873,2293,'COVERED');">
</div>
<br>
<a class=lo href="javascript:ShowTrucks('F');">Show Free Trucks</a>
<br><nobr>
<a class=lo href="javascript:ShowTrucks('A');">Show Available Soon Trucks</a>
</nobr>
</td>
<td align=center valign=center>
<input type="button" name="godo" id="godo" onclick="docc();" class="always"
value="Add Check Call" style="color:#000000;font-weight:900">
</td>
</tr>

<tr>
<td align=right>
Trailer</td>
<td><input type="text" name="tr_fld89" value="" size="20" id="trailer" maxlength="12"
onfocus="HideDiv('autocomplete');ckt();"
onkeyup="ShowTra(this);">
</td>
<td align=right>Odometer</td>
<td><input type=text id="drv231" name=drv231 value="" size=8 maxlength=8
onfocus="HideDiv('autocomplete')";>
		&nbsp;&nbsp;&nbsp;&nbsp;
</td>
<!--  <td></td> -->
<td align=center rowspan=1 valign=top style="line-height:9pt;">
<br> <br>
</td>
</tr>

<input type=hidden id=drvtype name=drv232>
<input type=hidden id=rate name=drv233>
<input type=hidden id=per1 name=drv235 value="    ">
<input type=hidden id=net name=drv234>

<input type=hidden id=rate2 name=fldr2>
<input type=hidden id=per2 name=fldp2 >
<input type=hidden id=net2 name=fld230>

<tr>
<td align=right nowrap>Projected Available</td>
<!-- Craig 110216 job 20224 added class loadboard_required_field -->
<td colspan=4 align=left><input type="text" name="drv130" id="drv130"
		value="" size="8" maxlength="8"  class='datepick loadboard_req' onchange="domdy(this);">
- <input type="text" name="drv131" id="drv131" class=time value="" size="3" maxlength="5">
		&nbsp;&nbsp;
</td>
<td colspan=2 nowrap> &nbsp;&nbsp;
<input type=checkbox name=box573 id=box573 class=link2yn>
<label for=box573> Need Load At Destination </label>
<input type=hidden name=fld573 id=fld573 value="">
</td>
</tr>

<tr>
<td align="right">Available City</td>
<!-- Craig 110616 job 20224 added class loadboard_req -->
<td colspan=4 nowrap><input type="text" name="drv126" value="" size="20" maxlength="20" class=loadboard_req id=drv126 onfocus="HideDiv('autocomplete');">
&nbsp; State
<!-- Craig 110616 job 20224 added class loadboard_req -->
<input type="text" name="drv127" value="" size="2" maxlength="2" class=loadboard_req id=drv127>
&nbsp; Zip
&nbsp; <input type=text name=drv128 id=drv128 value="" size=5 maxlength=6>
</td>
<td colspan=2 nowrap> &nbsp;&nbsp;
<!--Craig 110216 job 20224 added onclick -->
<input type=checkbox name=boxplb id=boxplb class=link2yn onclick="checkRequiredFields();">
<label for=boxplb>Post Truck To Load Boards</label>
<input type=hidden name=fldplb id=fldplb value="">
</td>
</tr>

<tr>
<td align=right>Destination States</td>
<td colspan=4>
<input type=text size=2 maxlength=2 name=dstate1 id=dstate1 value="">
<input type=text size=2 maxlength=2 name=dstate2 id=dstate2 value="">
<input type=text size=2 maxlength=2 name=dstate3 id=dstate3 value="">
<input type=text size=2 maxlength=2 name=dstate4 id=dstate4 value="">
</td>
</tr>

</table>
</div>
</td>

<td> &nbsp;&nbsp;</td>

<td valign=top align=center>
<table cellpadding="2" cellspacing="2" border="0" valign=top>

	<tr>
	  <td align=right nowrap>Dispatched </td>
<td nowrap><input name="tr_hdate" id="tr_hdate" withtime=tr_fld53 size="6" maxlength="8" value="" class=datepick onchange="ckins(this);domdy(this);">
&nbsp; Time &nbsp; <input name="tr_fld53" id=tr_fld53 size="3" maxlength="5" class=time value=""  class=time onchange="tryhms(this);"></td>
	</tr>
	  <td align=right>Will PU</td>
<td nowrap><input name="tr_wdate" id="tr_wdate" size="6" maxlength="8" value="" class=datepick onchange="domdy(this);">
&nbsp; Time &nbsp; <input name="tr_fld54" id="tr_fld54" size="3" maxlength="5" class=time value="" ></td>
	</tr>
	<tr>
	  <td align=right>Del ETA</td>
<td nowrap><input name="tr_edate" id="tr_edate" size="6" maxlength="8" value="" class=datepick onchange="domdy(this);doproj(this.value);">
&nbsp; Time &nbsp; <input name="tr_etime" id="tr_etime" size="3" maxlength="5" class=time value="" ></td>
	</tr>
</table></td>

<td valign=top><table cellpadding=2 cellspacing=2 border=0 valign=top>
	<tr>
	  <td align=right nowrap>Arrived PU</td>
<td nowrap align=left><input name="tr_avdate" id="tr_avdate" size="6" maxlength="8" value="" class=datepick onchange="domdy(this);do_seq();">
</td><td align=left>Time &nbsp; <input name="tr_avtime" id="tr_avtime" size="3" maxlength="5" class=time value="" onchange="do_seq();"></td>
	</tr>
	<tr>
	  <td align=right>Loaded</td>
<td nowrap align=left><input name="tr_ldate" id="tr_ldate" size="6" maxlength="8" value="" class=datepick onchange="ckins(this);domdy(this);do_seq();">
</td><td align=left>Time &nbsp; <input name="tr_ltime" id="tr_ltime" size="3" maxlength="5" class=time value="" onchange="do_seq();"></td>
	</tr>
	<tr>
	  <td align=right>Arrived Cons</td>
<td nowrap align=left><input name="tr_addate" id="tr_addate" size="6" maxlength="8" value=""  class=datepick onchange="domdy(this);do_seq();">
</td><td align=left>Time &nbsp;  <input name="tr_addtime" id="tr_addtime" size="3" maxlength="5" class=time value=""  onchange="do_seq();"></td>
	</tr>
	<tr>
	  <td align=right nowrap>Delivered</td>
<td nowrap align=left><input name="tr_fld145" id="tr_fld145" size="6" maxlength="8" value=""  class=datepick onchange="ckins(this);domdy(this);do_seq();">
</td><td align=left>Time &nbsp; <input name="tr_dtime" id="tr_dtime" size="3" maxlength="5" class=time value=""  onchange="do_seq();"></td>
	</tr>
	<tr>
	  <td align=right><b>Signed By</b></td>
	  <td align=left colspan=3><input type="text" value="" name="tr_fld147" id="tr_fld147" maxlength="25" size="20"  />
	  &nbsp;&nbsp;
	    <input type=checkbox name=tr_box148 id=tr_box148 class=link2yn onchange="ckosd(this);">
	    <label for=tr_box148> OSD </label>
	    <input type=hidden name=tr_fld148 id=tr_fld148 value="">
	  </td>
	</tr>

</table>
</td>

</tr>
</table></td></tr>

<!--</table></td></tr>-->

</table>
</div>

<!-- END EASY TRUCKING STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<!-- START TRIPLEGS TRUCKING STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<div id="longlegs" class=panel3>

<table class=panel1>
<tr>
<td align=center valign=top>
<table class=panel2>
<tr>
<td colspan=2 align=left valign=top>&nbsp;

	  <a href="javascript:setlegs('short');" class="lo" style="line-height:14pt;font-weight:600;">Easy View</a>
	  </td>
	</tr>
	<tr><td>&nbsp;</td></tr>
	<tr><td>&nbsp;</td></tr>
	<tr>
	  <td></td>
<td align=center nowrap>
<div style="padding:1px; width:100%; overflow:hidden; line-height:13px;">
<input type=button style="cursor:pointer;color:#000000;font-weight:900" class=butt value="Add a Leg" onclick="doleg('triplegs');">
</div>
</td>
<input name=isfbox id=isfbox type=hidden value="">
</tr>
<tr>
<td></td>
<td align=center nowrap>
<div style="padding:1px; width:100%; overflow:hidden; line-height:13px;">
<input type=button style="cursor:pointer;color:#000000;font-weight:900" class=butt name="checkcall" id="checkcall" value="Check Calls" onclick="docc();">
</div>
</td>
</tr>

<tr style="display:none;">
<td>&nbsp;</td>
<td align=left rowspan=1 valign=top style="line-height:9pt;">

</td>
</tr>
</table>

</td>

<td> &nbsp;&nbsp;&nbsp;&nbsp;</td>

<td valign=top><br>

<table align=center width=100% cellspacing=0 cellpadding=1 border=1>
<tr bgcolor="#ffffd0">
<td align=center >Insert<br>Before</td>
<td align=center><b>&nbsp; Order &nbsp;</b></td>

<td align=center><b>&nbsp; Truck &nbsp;</b></td>
<td align=center><b>&nbsp; Driver &nbsp;</b></td>
<td align=center><b>&nbsp; Trailer &nbsp;</b></td>
<td align=center><b>&nbsp; Type &nbsp;</b></td>
<td align=center><b>&nbsp; Origin &nbsp;</b></td>
<td align=center><b>&nbsp; Destination &nbsp;</b></td>
<td align=center><b>&nbsp; Will PU &nbsp;</b></td>
<td align=center><b>&nbsp; Confirmation &nbsp;</b></td>
<td align=center><b>&nbsp; Dispatched &nbsp;</b></td>

<td align=center><b>&nbsp; Loaded &nbsp;</b></td>
<td align=center><b>&nbsp; ETA &nbsp;</b></td>
<td align=center><b>&nbsp; Complete &nbsp;</b></td>
<td align=center><b>&nbsp; Rate &nbsp;</b></td>
<td align=center><b>&nbsp; Pays &nbsp;</b></td>
<td align=center nowrap><b>&nbsp; Carrier &nbsp;<br>Rating</b></td>
<td BGCOLOR="gainsboro" align=center colspan=3 >- - - - - History - - - - -</td>
<td align=center><b>Delete<br>Line</b></td>
</tr>

<form method=post name=Webpt action=/route.php target="_top">
<input type=hidden name=prcnam value="triplegs">
<input type=hidden name=pro value="100002">
<input type=hidden name=type value="update">
<input type=hidden name=payrec value=2293>
<input type="hidden" name="repno" value="">
<input type=hidden name=sys value="3a">
<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="pxrcnam" value="globalpt">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name="row">
<input type="hidden" name="sub">
<input type="hidden" name="insert">
<input type="hidden" name="doins">
	<input type=hidden name=ctlrec value="  131119">

	<input type=hidden name=ctlval value="">
<input type=hidden name=no_triplegs value="N">
<input type=hidden name=no_tripleg_browse value='Y'>

<tr onmouseout="this.className='lo';" class="lo">

<td onclick="this.checked=false; doinstrip(Webpt,'100002','2293','38000','');" align=center><input name=insbox id=insbox type=checkbox></td>
<td align=center onclick="doleg2('triplegs','100002','2293');"> &nbsp;38000 &nbsp;</td>
<td onclick="doleg2('triplegs','100002','2293');">&nbsp;&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">ACME LOGISTICS, LLC - ATL&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp;&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');" align=center>&nbsp;<font color=blue></font>&nbsp;</td>

<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp;F 900.00 4100&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp;JDOE 40 HA&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<!--<td onclick="doleg2('triplegs','100002','@rn');">&nbsp; &nbsp;</td>-->
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp;40 &nbsp;</td>

<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');" align=right>&nbsp;&nbsp;</td>
<td nowrap onclick="doleg2('triplegs','100002','2293');">&nbsp; &nbsp;</td>
<td valign=middle><input class=dark  type=button name=typ1 id=typ1 value="Truck" onclick="dohist('triplegs','100002','2293','Truck');"> </td>
<td valign=middle><input class=dark  type=button name=typ2 id=typ2 value="Trailer" onclick="dohist('triplegs','100002','2293','Trailer');"> </td>
<td valign=middle><input class=dark  type=button name=typ3 id=typ3 value="Driver" onclick="dohist('triplegs','100002','2293','Driver');"> </td>
<td nowrap onclick="this.checked=false; ckdelete(Webpt);" align=center><input name=delbox id=delbox type=checkbox></td>
</form>
</tr>

</table>

</td>

<td> &nbsp;&nbsp;</td>

</tr>
<tr><td colspan=4>&nbsp;</td></tr>

</table>

</div>

<!-- END TRIPLEGS TRUCKING STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<!-- START AIRFREIGHT STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<!-- <div id="airfreight" class=panel3> -->
<div id="airfreight" style="padding:1px; width:20%; height:175px; overflow:auto; line-height:9px; display:none;" align="left">

<input type=hidden name=af_fld145 id=af_fld145 value="">
<input type=hidden name=af_dtime id=af_dtime value="">

<table class=panel1 style="height:100%;">
<tr><td align=center><table class=panel2>

<tr>
<td align=right> HAWB </td>
<td><input type=text size=8 maxlength=18 name="af_fld153" id="af_fld153" value=""/></td>
<td align=right>MAWB </td>
<td><input type=text size=8 maxlength=18 name="af_fld154" id="af_fld154" value="" /></td>
</tr>

<tr><td>&nbsp;</td></tr>

<tr>
<td colspan=4 align=center valign="middle" nowrap>

<div id="addleg" style="padding:1px; width:100%; overflow:hidden; line-height:13px;">
<input type=button class=butt style="cursor:pointer;color:#000000;font-weight:900" value="Add A Leg" onclick="doleg('airlegs')">
</div>
<br>
<div id="dwgt" style="padding:1px; width:100%; overflow:hidden; line-height:13px; display:inline;">
<input type=button class=butt style="cursor:pointer;color:#000000;font-weight:900" name="dimwt" id="dimwt" value="Dimensional Weight" onclick="dodimwt();">
<!-- <br>( 38000 )  -->
</div>
</td>
</tr>

</table>
</td>

<td>
&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;
</td>

<td>

<table align=center width=100% cellspacing=0 cellpadding=1 border=1>
<tr bgcolor="#ffffd0">
<td align=center >Insert<br>Before</td>
<td align=center><b>Order</b></td>

<td align=center><b>Carrier</b></td>
<td align=center><b>Type</b></td>
<td align=center><b>Reference</b></td>
<td align=center><b>Origin</b></td>
<td align=center><b>Destination</b></td>
<td align=center><b>Will PU</b></td>
<td align=center><b>Dispatched</b></td>
<td align=center><b>Loaded</b></td>

<td align=center><b>ETA</b></td>
<td align=center><b>Complete</b></td>
<td align=center><b>Pays</b></td>
<td align=center><b>Delete<br>Leg</b></td>
</tr>
<form method=post name=Webair0 action=/route.php target="_top">
<input type=hidden name=pro value="">
<input type=hidden name=type value="update">
<input type=hidden name=payrec value=>
<input type="hidden" name="repno" value="">
<input type=hidden name=sys value="3a">

<input type=hidden name=qual value=gj56nb24>
<input type="hidden" name="name" value="mmhd50">
<input type="hidden" name="prcnam" value="airlegs">
<input type="hidden" name="c_tok" value="UAKETBDTQWMIRAHDNNE7TLMH5FAEW">
<input type="hidden" name="row">
<input type="hidden" name="sub">
<input type="hidden" name="insert">
<input type="hidden" name="doins">
	<input type=hidden name=ctlrec value="  131119">
	<input type=hidden name=ctlval value="">

<tr onmouseout="this.className='lo';" class="lo">

<td onclick="this.checked=false; doinsert(Webair0,'','');" align=center><input name=insbox id=insbox type=checkbox></td>

<td onclick="Webair0.pro.value='';Webair0.submit();" align=center onclick=Webair0.pro.value='';Webair0.submit();>&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();" align=center>&nbsp;&nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp;  &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp;  &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>

<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();">&nbsp; &nbsp;</td>
<td onclick="Webair0.pro.value='';Webair0.submit();" align=right>&nbsp;&nbsp;</td>
<td onclick="this.checked=false; ckdelete(Webair0);" align=center><input name=delbox id=delbox type=checkbox></td>
</form>
</tr>
<tr>
<td colspan=18 align=center>
</td>
</tr>
</table>

</td>
</tr>
</table>

</div>

<!-- END AIRFREIGHT STUFF +++++++++++++++++++++++++++++++++++++++++++ -->

<div id="autocomplete" style="display:inline"></div>

</td></tr>

<!-- </div>  flash -->

<tr><td align=center width=100%>

<div id=docdivfrom style="display:none">
<select name=dtypc class="ifsaved always" id=dtypc style="background:#Ffe0e0" onchange="dodoc();">
<option value="">Please Choose (2)</option>
<option value=public style="background:#Ffe0e0">Public (Bills of Lading) (2)</option><option value=private style="background:#ffffff">Private (A Confirmation)</option><option value=X style="background:#ffffff">Exit</option>
</select>
</div>

<div id="botmenu"> <!-- style="width:97%; position:relative; bottom:1px; display:block; z-index:999; top:10px; left: 20px; right:20px"> --> <!-- onclick="this.style.display='none';">-->

</div> <!-- botmenu -->

</td></tr>

</table></td></tr>
</table></td></tr>

<tr>
<td align="center" colspan=5>
<table border=0 cellpadding=4 cellspacing=4>
<tr><td>
<span id=buttonrow2>
<form>
<input type="button" id="savebutt2" name="savebutt2" onClick="dosubmit('save');" value="Save" />
<input type=button value="Save + Home" onclick="dosubmit('saveback');"/>
<div id="lcr2" style="display:none">
<input type=button class=butt name="rateit2" id="rateit2" value="Least Cost Routing" onclick="lccheck();">
</div>
<div id="intl2" style="display:none">
<input type=button class=butt name="irateit2" id="irateit2" value="International Pricing" onclick="intlcheck();">
</div>
<div id="ss2" style="display:none">
<input type=button class="butt ifsaved" name="ssearch2" id="ssearch2" value="Smart Search" onclick="doss(  86873,2293);">
</div>
<input type=button class="always ifsaved" onclick="extraps()" value="Picks 2 / Stops 1 ">
<div id="covuncov2" style="display:inline">
<input type=button class="ifadv butt" name="doc2" id="doc2" value="Un-Cover" onclick="uncover(  86873,2293,'COVERED');">
</div>
<div id="release2" style="display:none">
<input type=button class="butt ifsaved" name="rel2" id="rel2" value="Release" onclick="relcheck();">
</div>
<input type=button class="ifadv butt ifsaved" name="void4" id="void4" value="Void" onclick="dovd(  86873,2293);">
<input type="button" value="Back" class="always" onclick="history.go(-1);">
<input type="button" value="Home" class="always" onclick="dohome();">
</form>
</span>
</td>
</tr>

</tbody>
</table>
</td>

</tr></table>

<!-- END CLOSING bkb styling +++++++++++++++++++++++++++++++++++++++++++ -->

<!--//-->

<script language="Javascript" type="text/javascript">

function autosuggests()
{

// --------------------
// autocomplete loaders

//item description
var optionsexa07 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a0";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa07', optionsexa07);

//item code
var optionsexa05 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a0";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa05', optionsexa05);

//item description
var optionsexa17 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a1";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa17', optionsexa17);

//item code
var optionsexa15 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a1";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa15', optionsexa15);

//item description
var optionsexa27 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a2";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa27', optionsexa27);

//item code
var optionsexa25 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a2";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa25', optionsexa25);

//item description
var optionsexa37 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a3";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa37', optionsexa37);

//item code
var optionsexa35 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a3";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa35', optionsexa35);

//item description
var optionsexa47 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a4";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa47', optionsexa47);

//item code
var optionsexa45 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a4";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa45', optionsexa45);

//item description
var optionsexa57 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a5";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa57', optionsexa57);

//item code
var optionsexa55 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a5";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa55', optionsexa55);

//item description
var optionsexa67 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a6";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa67', optionsexa67);

//item code
var optionsexa65 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a6";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa65', optionsexa65);

//item description
var optionsexa77 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a7";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa77', optionsexa77);

//item code
var optionsexa75 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a7";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa75', optionsexa75);

//item description
var optionsexa87 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a8";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa87', optionsexa87);

//item code
var optionsexa85 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a8";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa85', optionsexa85);

//item description
var optionsexa97 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=a9";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa97', optionsexa97);

//item code
var optionsexa95 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=a9";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exa95', optionsexa95);

//item description
var optionsexb07 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b0";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb07', optionsexb07);

//item code
var optionsexb05 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b0";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb05', optionsexb05);

//item description
var optionsexb17 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b1";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb17', optionsexb17);

//item code
var optionsexb15 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b1";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb15', optionsexb15);

//item description
var optionsexb27 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b2";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb27', optionsexb27);

//item code
var optionsexb25 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b2";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb25', optionsexb25);

//item description
var optionsexb37 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b3";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb37', optionsexb37);

//item code
var optionsexb35 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b3";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb35', optionsexb35);

//item description
var optionsexb47 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b4";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb47', optionsexb47);

//item code
var optionsexb45 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b4";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb45', optionsexb45);

//item description
var optionsexb57 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b5";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb57', optionsexb57);

//item code
var optionsexb55 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b5";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb55', optionsexb55);

//item description
var optionsexb67 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b6";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb67', optionsexb67);

//item code
var optionsexb65 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b6";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb65', optionsexb65);

//item description
var optionsexb77 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b7";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb77', optionsexb77);

//item code
var optionsexb75 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b7";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb75', optionsexb75);

//item description
var optionsexb87 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b8";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb87', optionsexb87);

//item code
var optionsexb85 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b8";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb85', optionsexb85);

//item description
var optionsexb97 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&rownam=b9";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb97', optionsexb97);

//item code
var optionsexb95 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlitem&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&code=" + str + "&rownam=b9";
	},
	varname: "name", delay: 10, timeout: 10000, cache: false, shownoresults: false,
	callback: function(obj) {
		applylocationdata(25, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('exb95', optionsexb95);

//office (goagent)
var options104 = {
	script: function(str) {
		return "/route.php?prcnam=brwoff&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&rep=mmhd50&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	divwidth:3,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld104', options104);

// short customer name
var optionss_2 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &cust=yes&qual=gj56nb24&key=" + str + "&repid=mmhd50" + "&srepid=" + $('#fld65').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(8, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld2', optionss_2);

// customer name
var options2 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &cust=yes&qual=gj56nb24&key=" + str + "&repid=mmhd50" + "&srepid=" + $('#fld65').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(8, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld2', options2);

// short bill to name
var optionss_169 = {
	script: function(str) {
var currency = document.getElementById("fld528").value;
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &cust=yes&billto=Y&currtype=" + currency + "&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
minchars: 1,
requireselect: true,
	callback: function(obj) {
		applylocationdata(9, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld169', optionss_169);

// bill to name
var options169 = {
	script: function(str) {
var currency = document.getElementById("fld528").value;
//        alert(currency);
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &cust=yes&billto=Y&currtype=" + currency + "&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(9, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld169', options169);

// short pickup name
var optionss_30 = {
	script: function(str) {
		document.getElementById("pacct").value="";
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(10, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld30', optionss_30);

// pickup name
var options30 = {
	script: function(str) {
		document.getElementById("pacct").value="";
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(10, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld30', options30);

// short consignee name
var optionss_29 = {
	script: function(str) {
		document.getElementById("cacct").value="";
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(11, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld29', optionss_29);

// consignee name
var options29 = {
	script: function(str) {
		document.getElementById("cacct").value="";
		return "/route.php?prcnam=browsexmlcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(11, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld29', options29);

//Service Rep (permploy)
// assigned dispatcher (permploy)
var options62 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	divwidth:2,
	twin:"s_fld62",
	callback: function(obj) {
		applylocationdata(28, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld62', options62);

//dispatcher (permploy)
var optionss_62 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	divwidth:2,
	twin:"fld62",
	callback: function(obj) {
		applylocationdata(28, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld62', optionss_62);

var options63 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	divwidth:2,
	twin:"s_fld63",
	callback: function(obj) {
		applylocationdata(128, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld63', options63);

//dispatcher (permploy)
var optionss_63 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	divwidth:2,
	twin:"fld63",
	callback: function(obj) {
		applylocationdata(128, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld63', optionss_63);

//Service Rep (permploy)
var options529 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(29, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld529', options529);

//Service Rep (permploy)
var optionss_529 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml4&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(29,obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld529', optionss_529);

//rep (gorep)
var options884 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml5&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			+ "&custid=" + $('#cuacct').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(26, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld884', options884);

//rep (gorep)
var optionss_884 = {
	script: function(str) {
		return "/route.php?prcnam=brwxml5&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			+ "&custid=" + $('#cuacct').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(26, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_fld884', optionss_884);

//rail ramps (ramps)
var options864 = {
	script: function(str) {
		return "/route.php?prcnam=brwramp&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(21, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld864', options864);

//rail ramps 2 (ramps)
var options866 = {
	script: function(str) {
		return "/route.php?prcnam=brwramp&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(22, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld866', options866);

//airport1
var options904 = {
	script: function(str) {
		return "/route.php?prcnam=brwair&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld904', options904);

//aitport2
var options906 = {
	script: function(str) {
		return "/route.php?prcnam=brwair&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld906', options906);

// PU city
var optionspu = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld32.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(14, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pcity', optionspu);

// DH city
var optionsdh = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld91.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata('dh', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld91', optionsdh);

var optionss_pu = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.s_fld32.value;
// + document.main.fld32.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(14, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_pcity', optionss_pu);

// cons city
var optionscons = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld10.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(15, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('ccity', optionscons);

var optionss_cons = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.s_fld10.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(15, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_ccity', optionss_cons);


// PU state
var options33 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld33.value + "|" + document.main.fld32.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(16, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pstate', options33);// PU state

// DH state
var optionsds = {
	script: function(str) {
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld92.value + "|" + document.main.fld91.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata("ds", obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld92', optionsds);// PU state

var optionss_33 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.s_fld33.value + "|" + document.main.s_fld32.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(16, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_pstate', optionss_33);// PU state

// Delv state
var options41 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.fld41.value + "|" + document.main.fld10.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(17, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('cstate', options41);// Delv state

var optionss_41 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmls&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.s_fld41.value + "|" + document.main.s_fld10.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(17, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('s_cstate', optionss_41);// Delv state

//Origin Zip
var options34 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlzip&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(18, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pzip', options34);

//Destination Zip
var options70 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlzip&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&type= &qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(19, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('czip', options70);

// customer name for repeat load
var optionsrptcus = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlrptcus&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	json: false
};
var as = new bsn.AutoSuggest('rptcus', optionsrptcus);

// from state for repeat load
var optionsrptfst = {
	script: function(str) {
		var fcus=escape(document.main.rptcus.value);
		return "/route.php?prcnam=brwxmlrptfst&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&fcust=" + fcus + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	json: false
};
var as = new bsn.AutoSuggest('rptfst', optionsrptfst);

// To state for repeat load
var optionsrpttst = {
	script: function(str) {
		var fcus=escape(document.main.rptcus.value);
		var fst=document.main.rptfst.value;
		return "/route.php?prcnam=brwxmlrpttst&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str + "&fcust=" + fcus + "&fst=" + fst + "&repid=mmhd50";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	json: false
};
var as = new bsn.AutoSuggest('rpttst', optionsrpttst);

// carrier name
var optionsa1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(0, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('a1', optionsa1);

var optionsb1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(1, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('b1', optionsb1);

var optionsc1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(2, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('c1', optionsc1);

var optionsd1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(3, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('d1', optionsd1);

var optionse1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('e1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('e1', optionse1);

var optionsf1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('f1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('f1', optionsf1);

var optionsg1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('g1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('g1', optionsg1);

var optionsh1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('h1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('h1', optionsh1);

var optionsi1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('i1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('i1', optionsi1);

var optionsj1 =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('j1', obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('j1', optionsj1);

var optionsa2 =
		{
		script: function(str){

			var getcar;
			getcar = document.main.a5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(4, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('a2', optionsa2);

var optionsb2 =
		{
		script: function(str){

			var getcar1;
			getcar1 = document.main.b5.value;
		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar1 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(5, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('b2', optionsb2);

var optionsc2 =
		{
		script: function(str){

			var getcar2;
			getcar2 = document.main.c5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar2 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(6, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('c2', optionsc2);

var optionsd2 =
		{
		script: function(str){
			var getcar3;
			getcar3 = document.main.d5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar3 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata(7, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('d2', optionsd2);

var optionse2 =
		{
		script: function(str){
			var getcare;
			getcare = document.main.e5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcare + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('e2', obj.id);
		do5 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('e2', optionse2);

var optionsf2 =
		{
		script: function(str){
			var getcarf;
			getcarf = document.main.f5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcarf + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('f2', obj.id);
		do6 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('f2', optionsf2);

var optionsg2 =
		{
		script: function(str){
			var getcarg;
			getcarg = document.main.g5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcarg + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('g2', obj.id);
		do7 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('g2', optionsg2);

var optionsh2 =
		{
		script: function(str){
			var getcar3;
			getcar3 = document.main.h5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar3 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('h2', obj.id);
		do8 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('h2', optionsh2);

var optionsi2 =
		{
		script: function(str){
			var getcar3;
			getcar3 = document.main.i5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar3 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('i2', obj.id);
		do9 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('i2', optionsi2);

var optionsj2 =
		{
		script: function(str){
			var getcar3;
			getcar3 = document.main.j5.value;

		return"/route.php?prcnam=brwxmlacc&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &carid=" + getcar3 + "&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: true,
	callback: function(obj) {
		applylocationdata('j2', obj.id);
		do10 = "y";
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('j2', optionsj2);

// carrier name
var optionscarr =
		{
		script: function(str){
//Craig 030117 job 27167 added saferwatch parameter
return "/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &rep=mmhd50&qual=gj56nb24&key=" + str+ "&hazmat=" + document.main.fld68.value + "&saferwatch=" + document.getElementById("saferwatch_enabled").value + "&fsma=" + $('#fldextra1').val() + "&tempcontrol=" + $('#fld69').val() + "&cover=Y";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(20, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fldcarr', optionscarr);

// mc #
var optionsmcno =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=86873&rep=mmhd50&qual=gj56nb24&MC=" + str + "&hazmat=" + document.main.fld68.value + "&fsma=" + $('#fldextra1').val() + "&tempcontrol=" + $('#fld69').val() + "&cover=Y";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	divwidth: 2,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(20, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('mcno', optionsmcno);

// dot #
var optionsdotno =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &rep=mmhd50&qual=gj56nb24&DOT=" + str + "&hazmat=" + document.main.fld68.value + "&fsma=" + $('#fldextra1').val() + "&tempcontrol=" + $('#fld69').val() + "&cover=Y";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	divwidth: 2,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(20, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('dotno', optionsdotno);

// scac
var optionsscac =
		{
		script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=  86873&mode=BROKERAGE &rep=mmhd50&qual=gj56nb24&SCAC=" + str + "&hazmat=" + document.main.fld68.value + "&fsma=" + $('#fldextra1').val() + "&tempcontrol=" + $('#fld69').val() + "&cover=Y";
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	divwidth: 2,
	shownoresults: false,
	callback: function(obj) {
		applylocationdata(20, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('scac', optionsscac);

//pickup country
var options334 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlctry&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld334', options334);

//consignee country
var options335 = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlctry&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld335', options335);

//required ref #
var optionspld360 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld360', optionspld360);

//required ref #
var optionspld361 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld361', optionspld361);

//required ref #
var optionspld362 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld362', optionspld362);

//required ref #
var optionspld363 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld363', optionspld363);

//required ref #
var optionspld364 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld364', optionspld364);

//required ref #
var optionspld365 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlfldref&nane=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	callback: function(obj) {
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('pld365', optionspld365);

// Craig 040716 job 20768 added code for dropdown menus in Trucking available city, state, zip
var truckOriginCity = {
	script: function(str) {
		return "/route.php?prcnam=brwxmlcs&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + document.main.drv126.value;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
callback: function(obj)
{
var data = obj.id;
var data = data.split("|");
$('#drv128').val(data[2]);//zip
$('#drv127').val(data[1]);//state
$('#drv126').val(data[0]);//city
return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('drv126', truckOriginCity);

// Craig 040716 job 20768 added code for dropdown menus in Trucking available city, state, zip
var truckOriginZip = {
script: function(str) {
var repid = 'eric';
return "/route.php?prcnam=brwxmlzip&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key="+document.main.drv128.value+"&repid="+repid;
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
callback: function(obj)
{
var data = obj.id;
var data = data.split("|");
$('#drv128').val(data[0]);//zip
$('#drv127').val(data[2]);//state
$('#drv126').val(data[1]);//city
return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('drv128', truckOriginZip);

// customer contact
var options9 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcont&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			 + "&brwname=_&id=" + $('#cuacct').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(30, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld9', options9);

// bill to contact
var optionsbtc = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcont&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			 + "&brwname=_&id=" + $('#fld171').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(31, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('btcontact', optionsbtc);

// pick up contact
var options35 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcont&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			 + "&brwname=_&id=" + $('#pacct').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(32, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld35', options35);

// consignee contact
var options43 = {
	script: function(str) {
		return "/route.php?prcnam=browsexmlcont&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&qual=gj56nb24&key=" + str
			 + "&brwname=_&id=" + $('#cacct').val();
	},
	varname: "name",
	delay: 10,
	timeout: 10000,
	cache: false,
	shownoresults: false,
	requireselect: false,
	callback: function(obj) {
		applylocationdata(33, obj.id);
		return true;
	},
	json: false
};
var as = new bsn.AutoSuggest('fld43', options43);

var optionsimcar = {
	script: function(str){
		return"/route.php?prcnam=brwxmlcar&name=mmhd50&c_tok=UAKETBDTQWMIRAHDNNE7TLMH5FAEW&pro=100002&rep=mmhd50&qual=gj56nb24&key=" + str
},
varname: "name",
delay: 10,
timeout: 10000,
cache: false,
shownoresults: false,
callback: function(obj, id) {
applylocationdata(34, obj.id, id);
return true;
},
json: false
};
var as = new bsn.AutoSuggest('im_fld2-1', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-2', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-3', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-4', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-5', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-6', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-7', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-8', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-9', optionsimcar);
var as = new bsn.AutoSuggest('im_fld2-10', optionsimcar);

}
</script>

</body>
</html>
