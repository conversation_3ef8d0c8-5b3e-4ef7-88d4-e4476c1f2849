package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenerateMcLeodTumaloID(t *testing.T) {
	tests := []struct {
		name     string
		locName  string
		city     string
		state    string
		expected string
	}{
		{
			name:     "General case - Standard lengths",
			locName:  "Tumalo Logistics",
			city:     "Portland",
			state:    "Oregon",
			expected: "TUMPOOR", // TUM (3) + PO (2) + OR (2)
		},
		{
			name:     "Standard Warehouse - Multi-word",
			locName:  "Global Logistics Hub",
			city:     "San Francisco",
			state:    "California",
			expected: "GLOSACA",
		},
		{
			name:     "Retail Location - With Suffix",
			locName:  "Walmart Supercenter #1234",
			city:     "Bentonville",
			state:    "Arkansas",
			expected: "WALBEAR",
		},
		{
			name:     "Industrial Site - With Hyphen",
			locName:  "Steel-Works Inc",
			city:     "Pittsburgh",
			state:    "Pennsylvania",
			expected: "STEPIPE",
		},
		{
			name:     "Short Name - Less than 3 chars",
			locName:  "AB",
			city:     "Portland",
			state:    "OR",
			expected: "ABPOOR", // AB (2) + PO (2) + <PERSON> (2)
		},
		{
			name:     "Short City - Less than 2 chars",
			locName:  "Tumalo",
			city:     "P",
			state:    "OR",
			expected: "TUMPOR", // TUM (3) + P (1) + OR (2)
		},
		{
			name:     "Short State - Less than 2 chars",
			locName:  "Tumalo",
			city:     "Portland",
			state:    "O",
			expected: "TUMPOO", // TUM (3) + PO (2) + O (1)
		},
		{
			name:     "Empty strings - No panic",
			locName:  "",
			city:     "",
			state:    "",
			expected: "", // Handles empty strings gracefully
		},
		{
			name:     "Special characters and spaces",
			locName:  "T.u.m.a.l.o! @Logistics",
			city:     "St. Louis",
			state:    "MO",
			expected: "TUMSTMO", // "TUM" from Tumalo, "ST" from StLouis, "MO" from MO
		},
		{
			name:     "Numeric values in strings",
			locName:  "3PL Corp",
			city:     "99 City",
			state:    "CA",
			expected: "3PL99CA",
		},
		{
			name:     "Lowercase conversion",
			locName:  "mcleod",
			city:     "bend",
			state:    "or",
			expected: "MCLBEOR",
		},
		{
			name:     "Strings with only special characters",
			locName:  "!!!",
			city:     "###",
			state:    "$$",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// The use of slicing s[:length] in getPrefix is safe because
			// the code explicitly checks if len(s) <= length first.
			result := generateMcLeodTumaloID(tt.locName, tt.city, tt.state)
			assert.Equal(t, tt.expected, result, "Generated ID should match expected output")
		})
	}
}
