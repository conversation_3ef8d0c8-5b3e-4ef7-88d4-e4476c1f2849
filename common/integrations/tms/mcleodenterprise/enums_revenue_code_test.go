package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestToSyfanRevenueCode(t *testing.T) {
	m := &McleodEnterprise{
		tms: models.Integration{
			Tenant: TenantSyfan,
		},
	}

	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string should return empty string without error",
			input:       "",
			expected:    "",
			expectError: false,
		},
		{
			name:        "whitespace only should return empty string without error",
			input:       "   ",
			expected:    "",
			expectError: false,
		},
		{
			name:        "valid revenue code - expedited",
			input:       "expedited",
			expected:    "EXP",
			expectError: false,
		},
		{
			name:        "valid revenue code - temp",
			input:       "temp",
			expected:    "TEMP",
			expectError: false,
		},
		{
			name:        "valid revenue code - dedicated",
			input:       "dedicated",
			expected:    "DED",
			expectError: false,
		},
		{
			name:        "valid revenue code with mixed case",
			input:       "EXPEDITED",
			expected:    "EXP",
			expectError: false,
		},
		{
			name:        "invalid revenue code should return error",
			input:       "invalid-code",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := m.toSyfanRevenueCode(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "unsupported revenue code")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestToRevenueCode_SyfanWithFeatureFlag(t *testing.T) {
	tests := []struct {
		name              string
		isRevenueRequired bool
		input             string
		expected          string
		expectError       bool
		errorContains     string
	}{
		{
			name:              "empty revenue code with flag=true should allow empty",
			isRevenueRequired: true,
			input:             "",
			expected:          "",
			expectError:       false,
		},
		{
			name:              "empty revenue code with flag=false should allow empty",
			isRevenueRequired: false,
			input:             "",
			expected:          "",
			expectError:       false,
		},
		{
			name:              "valid revenue code with flag=true",
			isRevenueRequired: true,
			input:             "expedited",
			expected:          "EXP",
			expectError:       false,
		},
		{
			name:              "invalid revenue code with flag=true should return error",
			isRevenueRequired: true,
			input:             "invalid",
			expected:          "",
			expectError:       true,
			errorContains:     "unsupported revenue code",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &McleodEnterprise{
				tms: models.Integration{
					Tenant: TenantSyfan,
					FeatureFlags: models.IntegrationFeatureFlags{
						IsRevenueCodeRequired: tt.isRevenueRequired,
					},
				},
			}

			result, err := m.toRevenueCode(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestToTumaloRevenueCode(t *testing.T) {
	m := &McleodEnterprise{
		tms: models.Integration{
			Tenant: TenantTumalo,
		},
	}

	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string should return empty string without error",
			input:       "",
			expected:    "",
			expectError: false,
		},
		{
			name:        "valid revenue code - SPOT",
			input:       "SPOT",
			expected:    "SPOT FREIGHT",
			expectError: false,
		},
		{
			name:        "invalid revenue code should return error",
			input:       "INVALID",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := m.toTumaloRevenueCode(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "unsupported revenue code")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}
