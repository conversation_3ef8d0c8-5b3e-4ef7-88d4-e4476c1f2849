package mcleodenterprise

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestSyfanAllocationCode(t *testing.T) {
	m := &McleodEnterprise{
		tms: models.Integration{
			Tenant: "syfn.loadtracking.com",
		},
	}

	tests := []struct {
		name      string
		input     string
		wantCode  string
		wantError bool
	}{
		{
			name:      "DEDICATED maps to DED",
			input:     "DEDICATED",
			wantCode:  "DED",
			wantError: false,
		},
		{
			name:      "MIDWEST maps to MWD",
			input:     "MIDWEST",
			wantCode:  "MWD",
			wantError: false,
		},
		{
			name:      "lowercase dedicated works",
			input:     "dedicated",
			wantCode:  "DED",
			wantError: false,
		},
		{
			name:      "empty string returns empty",
			input:     "",
			wantCode:  "",
			wantError: false,
		},
		{
			name:      "EXPEDITED 1 maps to EXP1",
			input:     "EXPEDITED 1",
			wantCode:  "EXP1",
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code, err := m.toSyfanAllocationCode(tt.input)

			if tt.wantError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantCode, code)
			}
		})
	}
}
