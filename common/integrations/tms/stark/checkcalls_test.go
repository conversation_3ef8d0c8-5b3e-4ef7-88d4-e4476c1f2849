package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// const apiKey = ""
// const username = ""
// const tenant = ""
// const testLoadID = ""

func TestLiveStarkCheckCalls(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkCheckCalls: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	t.Run("GetCheckCallsHistory", func(t *testing.T) {
		log.Info(ctx, "calling GetCheckCallsHistory for test shipment ID")

		startTime := time.Now()
		checkCalls, err := client.GetCheckCallsHistory(ctx, 1, testLoadID)
		require.NoError(t, err, "GetCheckCallsHistory should succeed")

		log.Info(
			ctx,
			"GetCheckCallsHistory completed",
			zap.Int("checkCallsCount", len(checkCalls)),
			zap.Duration("duration", time.Since(startTime)),
		)

		// Basic validation
		for i, checkCall := range checkCalls {
			assert.NotEmpty(t, checkCall.FreightTrackingID, "check call %d should have freight tracking ID", i)
			assert.Equal(
				t,
				testLoadID,
				checkCall.FreightTrackingID,
				"check call %d freight tracking ID should match",
				i,
			)
			assert.NotEmpty(t, checkCall.Status, "check call %d should have status", i)
			assert.True(t, checkCall.DateTime.Valid, "check call %d should have valid date time", i)

			log.Info(
				ctx,
				"check call validated",
				zap.Int("index", i),
				zap.String("status", checkCall.Status),
				zap.String("city", checkCall.City),
				zap.String("state", checkCall.State),
				zap.String("source", checkCall.Source),
				zap.Time("dateTime", checkCall.DateTime.Time),
			)
		}
	})
}
