package stark

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

// TODO: Implement Okta authentication flow
// This will need to:
// 1. Authenticate with <PERSON><PERSON> using username/password
// 2. Get Okta session token
// 3. Exchange session token for Stark API token
// 4. Store the token

var (
// tmsDBUpdateFunc         = integrationDB.Update
// oktaTenantURL           = "https://nfi.okta.com"
// oktaClientID            = "okta.2b1959c8-bcc0-56eb-a589-cfcfb7422f26"
// oktaRedirectURI         = "https://nfi.okta.com/enduser/callback"
// oktaEndUserHomeEndpoint = "/enduser/api/v1/home"
// oktaTokenEndpoint       = "/oauth2/v1/token"
// oktaAuthorizeEndpoint   = "/oauth2/v1/authorize"
)

// RefreshToken refreshes the Ok<PERSON> access token for Stark TMS
func (s *Stark) RefreshToken(ctx context.Context) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "RefreshTokenStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "refreshing Stark client token")

	// TODO: Implement Okta token refresh flow
	// This will need to:
	// 1. Authenticate with Okta using username/password
	// 2. Get Okta session token
	// 3. Exchange session token for Stark API token
	// 4. Store the token and expiration

	// For now, return an error indicating this needs to be implemented
	return errors.New("okta authentication not yet implemented - need to scrape Okta auth flow from browser")
}

// Authenticate performs the initial Okta authentication using OAuth2 Authorization Code flow with PKCE
// func (s *Stark) authenticate(ctx context.Context, username, password string) (models.OnboardTMSResponse, error) {
// 	ctx, metaSpan := otel.StartSpan(ctx, "AuthenticateStark", otel.IntegrationAttrs(s.tms))
// 	defer func() { metaSpan.End(nil) }()

// 	// Step 1: Generate PKCE code verifier and challenge
// 	codeVerifier, err := generateCodeVerifier()
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("failed to generate code verifier: %w", err)
// 	}
// 	codeChallenge := generateCodeChallenge(codeVerifier)

// 	// Step 2: Authenticate with Okta using standard Authentication API to get sessionToken
// 	authnResponse, err := s.oktaAuthenticate(ctx, username, password)
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("okta authentication failed: %w", err)
// 	}

// 	// Step 3: Handle MFA if required
// 	if authnResponse.Status == "MFA_REQUIRED" {
// 		// TODO: Implement MFA flow
// 		return models.OnboardTMSResponse{},
// 			errors.New("mfa is required but not yet implemented – need to scrape MFA flow")
// 	}

// 	if authnResponse.Status != "SUCCESS" {
// 		return models.OnboardTMSResponse{}, fmt.Errorf(
// 			"okta authentication failed with status: %s",
// 			authnResponse.Status,
// 		)
// 	}

// 	// Step 4: Get authorization code using sessionToken
// 	authCode, err := s.oktaGetAuthorizationCode(ctx, authnResponse.SessionToken, codeChallenge)
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("failed to get authorization code: %w", err)
// 	}

// 	// Step 5: Exchange authorization code for access token
// 	tokenResponse, err := s.oktaExchangeCodeForToken(ctx, authCode, codeVerifier)
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("failed to exchange code for token: %w", err)
// 	}

// 	// Step 6: Get user info from Okta using access token
// 	userInfo, err := s.oktaGetUserInfo(ctx, tokenResponse.AccessToken)
// 	if err != nil {
// 		log.WarnNoSentry(ctx, "failed to get user info from Okta", zap.Error(err))
// 		// Continue without user info
// 	}

// 	// Step 7: Exchange Okta access token for Stark API token
// 	// TODO: This is what needs to be scraped - how does Stark exchange the Okta token?
// 	starkToken, rogersRevision, err := s.exchangeOktaTokenForStarkToken(ctx, tokenResponse.AccessToken)
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("failed to exchange Okta token for Stark token: %w", err)
// 	}

// 	userID := ""
// 	if userInfo != nil {
// 		userID = userInfo.UserID
// 	}

// 	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
// 	if err != nil {
// 		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
// 	}

// 	// Token expiration from Okta response (defaults to 1 hour if not provided)
// 	tokenExpiration := time.Now().Add(time.Duration(tokenResponse.ExpiresIn) * time.Second)
// 	if tokenResponse.ExpiresIn == 0 {
// 		tokenExpiration = time.Now().Add(1 * time.Hour) // Default to 1 hour
// 	}

// 	// TODO: Store userID and rogersRevision somewhere accessible
// 	// For now, we'll need to extract them from tms.Note when needed
// 	// Format: "userID:xxx,rogersRevision:yyy"
// 	_ = userID
// 	_ = rogersRevision

// 	return models.OnboardTMSResponse{
// 		EncryptedPassword:         encryptedPassword,
// 		Username:                  username,
// 		AccessToken:               starkToken, // Stark API token (x-user-token)
// 		AccessTokenExpirationDate: tokenExpiration,
// 		APIKey:                    starkToken, // x-user-token
// 	}, nil
// }

// oktaAuthenticate calls Okta Authentication API
// func (s *Stark) oktaAuthenticate(ctx context.Context, username, password string) (*OktaAuthnResponse, error) {
// 	reqBody := map[string]any{
// 		"username": username,
// 		"password": password,
// 	}

// 	reqBodyJSON, err := json.Marshal(reqBody)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to marshal request body: %w", err)
// 	}

// 	url := oktaTenantURL + "/api/v1/authn"
// 	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(reqBodyJSON))
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to create request: %w", err)
// 	}

// 	req.Header.Set("Content-Type", "application/json")
// 	req.Header.Set("Accept", "application/json")

// 	client := &http.Client{
// 		Transport: otelhttp.NewTransport(
// 			http.DefaultTransport,
// 			otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
// 		),
// 		Timeout: 30 * time.Second,
// 	}

// 	resp, err := client.Do(req)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to make request: %w", err)
// 	}
// 	defer resp.Body.Close()

// 	if resp.StatusCode != http.StatusOK {
// 		body, err := io.ReadAll(resp.Body)
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to read response body: %w", err)
// 		}
// 		return nil, fmt.Errorf("okta authn failed with status %d: %s", resp.StatusCode, string(body))
// 	}

// 	var authnResponse OktaAuthnResponse
// 	if err := json.NewDecoder(resp.Body).Decode(&authnResponse); err != nil {
// 		return nil, fmt.Errorf("failed to decode response: %w", err)
// 	}

// 	return &authnResponse, nil
// }

// oktaGetAuthorizationCode gets authorization code using sessionToken
// func (s *Stark) oktaGetAuthorizationCode(ctx context.Context, sessionToken, codeChallenge string) (string, error) {
// 	// Build authorization URL with sessionToken
// 	authURL := fmt.Sprintf(
// 		"%s%s?client_id=%s&redirect_uri=%s&response_type=code&scope=openid+profile+email"+
// 			"&code_challenge=%s&code_challenge_method=S256&sessionToken=%s",
// 		oktaTenantURL,
// 		oktaAuthorizeEndpoint,
// 		url.QueryEscape(oktaClientID),
// 		url.QueryEscape(oktaRedirectURI),
// 		url.QueryEscape(codeChallenge),
// 		url.QueryEscape(sessionToken),
// 	)

// 	req, err := http.NewRequestWithContext(ctx, http.MethodGet, authURL, nil)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to create request: %w", err)
// 	}

// 	client := &http.Client{
// 		Transport: otelhttp.NewTransport(
// 			http.DefaultTransport,
// 			otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
// 		),
// 		Timeout: 30 * time.Second,
// 		CheckRedirect: func(_ *http.Request, _ []*http.Request) error {
// 			// Don't follow redirects - we want to extract the code from the redirect URL
// 			return http.ErrUseLastResponse
// 		},
// 	}

// 	resp, err := client.Do(req)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to make request: %w", err)
// 	}
// 	defer resp.Body.Close()

// 	// Extract code from redirect URL
// 	location, err := resp.Location()
// 	if err != nil {
// 		return "", fmt.Errorf("failed to get redirect location: %w", err)
// 	}

// 	queryParams, err := url.ParseQuery(location.RawQuery)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to parse query params: %w", err)
// 	}

// 	code := queryParams.Get("code")
// 	if code == "" {
// 		return "", errors.New("authorization code not found in redirect URL")
// 	}

// 	return code, nil
// }

// oktaExchangeCodeForToken exchanges authorization code for access token
// func (s *Stark) oktaExchangeCodeForToken(
// 	ctx context.Context,
// 	authCode, codeVerifier string,
// ) (*OktaTokenResponse, error) {
// 	data := url.Values{}
// 	data.Set("client_id", oktaClientID)
// 	data.Set("redirect_uri", oktaRedirectURI)
// 	data.Set("grant_type", "authorization_code")
// 	data.Set("code_verifier", codeVerifier)
// 	data.Set("code", authCode)

// 	req, err := http.NewRequestWithContext(
// 		ctx,
// 		http.MethodPost,
// 		oktaTenantURL+oktaTokenEndpoint,
// 		strings.NewReader(data.Encode()),
// 	)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to create request: %w", err)
// 	}

// 	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
// 	req.Header.Set("Accept", "application/json")

// 	client := &http.Client{
// 		Transport: otelhttp.NewTransport(
// 			http.DefaultTransport,
// 			otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
// 		),
// 		Timeout: 30 * time.Second,
// 	}

// 	resp, err := client.Do(req)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to make request: %w", err)
// 	}
// 	defer resp.Body.Close()

// 	if resp.StatusCode != http.StatusOK {
// 		body, err := io.ReadAll(resp.Body)
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to read response body: %w", err)
// 		}
// 		return nil, fmt.Errorf("token exchange failed with status %d: %s", resp.StatusCode, string(body))
// 	}

// 	var tokenResponse OktaTokenResponse
// 	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
// 		return nil, fmt.Errorf("failed to decode response: %w", err)
// 	}

// 	return &tokenResponse, nil
// }

// oktaGetUserInfo gets user information from Okta using access token
// func (s *Stark) oktaGetUserInfo(ctx context.Context, accessToken string) (*OktaEndUserInfo, error) {
// 	req, err := http.NewRequestWithContext(ctx, http.MethodGet, oktaTenantURL+oktaEndUserHomeEndpoint, nil)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to create request: %w", err)
// 	}

// 	req.Header.Set("Authorization", "Bearer "+accessToken)
// 	req.Header.Set("Accept", "application/json")

// 	client := &http.Client{
// 		Transport: otelhttp.NewTransport(
// 			http.DefaultTransport,
// 			otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
// 		),
// 		Timeout: 30 * time.Second,
// 	}

// 	resp, err := client.Do(req)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to make request: %w", err)
// 	}
// 	defer resp.Body.Close()

// 	if resp.StatusCode != http.StatusOK {
// 		body, err := io.ReadAll(resp.Body)
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to read response body: %w", err)
// 		}
// 		return nil, fmt.Errorf("user info request failed with status %d: %s", resp.StatusCode, string(body))
// 	}

// 	var userInfo OktaEndUserInfo
// 	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
// 		return nil, fmt.Errorf("failed to decode response: %w", err)
// 	}

// 	return &userInfo, nil
// }

// exchangeOktaTokenForStarkToken exchanges Okta access token for Stark API token
// TODO: This needs to be implemented based on scraping results
// First param is ctx and second is Okta access token, we need to rename it when we use it
// func (s *Stark) exchangeOktaTokenForStarkToken(
// 	_ context.Context,
// 	_ string,
// ) (starkToken, rogersRevision string, err error) {
// 	// TODO: Scrape this flow from browser
// 	// This is the critical missing piece - how does Stark exchange the Okta token?
// 	// Options:
// 	// 1. POST to Stark callback endpoint with Okta token
// 	// 2. Use Okta token to get session cookie, then call Stark API
// 	// 3. Call Stark's token exchange endpoint
// 	// 4. The Okta token might BE the Stark token (need to verify)

// 	// For now, return error indicating this needs to be scraped
// 	return "", "", errors.New("okta token to Stark token exchange not yet implemented - need to scrape from browser")
// }

// generateCodeVerifier generates a PKCE code verifier
// func generateCodeVerifier() (string, error) {
// 	bytes := make([]byte, 32)
// 	if _, err := rand.Read(bytes); err != nil {
// 		return "", err
// 	}
// 	return base64.RawURLEncoding.EncodeToString(bytes), nil
// }

// generateCodeChallenge generates a PKCE code challenge from verifier
// func generateCodeChallenge(verifier string) string {
// 	hash := sha256.Sum256([]byte(verifier))
// 	return base64.RawURLEncoding.EncodeToString(hash[:])
// }
