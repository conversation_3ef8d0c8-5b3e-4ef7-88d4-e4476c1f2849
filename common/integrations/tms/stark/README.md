# Stark TMS Integration

## Overview

This integration connects to Stark TMS (Transfix's internal TMS) which uses <PERSON><PERSON> for authentication and has an internal API at `https://app.transfix.io/stark/v8`.

## Current Implementation Status

✅ **Completed:**
- Basic integration structure
- GetLoad - retrieves a single shipment by ID
- GetLoadIDs - retrieves shipment IDs based on query parameters
- Shipment to Load model mapping
- HTTP client with proper headers
- Transport type enum mapping

⏳ **Pending (requires browser scraping):**
- Okta authentication flow
- Token refresh mechanism
- Additional API endpoints

## APIs to Scrape from Browser

To complete the integration, you need to scrape the following APIs from the browser:

### 1. **Okta Authentication Flow**

**What to capture:**
- Initial Okta login URL and parameters
- POST request to <PERSON><PERSON> login endpoint (credentials submission)
- MFA flow (if applicable)
- Session token/cookie extraction
- How to exchange Okta session for Stark API token

**Key information needed:**
- Okta domain/tenant URL
- Client ID and any OAuth parameters
- Session cookie names and values
- Token exchange endpoint

**Example locations to check:**
- Network tab when logging into `https://apps.transfix.io/`
- Look for requests to `*.okta.com` or `*.okta.com/oauth2/`
- Check for `sessionToken`, `access_token`, or similar in responses

### 2. **Stark API Authentication Headers**

**Current headers we're setting:**
- `x-user-token` - from `tms.APIKey` (needs to be extracted from Okta session)
- `x-user-email` - from `tms.Username`
- `x-user-id` - **NEED TO SCRAPE** (likely from Okta user profile or session)
- `x-rogers-revision` - **NEED TO SCRAPE** (appears to be a version/revision header)

**Where to find:**
- Check the Network tab when making API calls to `/stark/v8/shipments`
- Look at request headers in the example curl command you provided
- These may be set by JavaScript after Okta authentication

### 3. **Additional API Endpoints**

**Currently implemented:**
- `GET /stark/v8/shipments` - List shipments with filters

**Need to find:**
- `GET /stark/v8/shipments/{id}` - Get single shipment (if different from list endpoint)
- `POST /stark/v8/shipments` - Create shipment (if needed)
- `PUT /stark/v8/shipments/{id}` - Update shipment (if needed)
- Any endpoints for check calls, notes, exceptions, etc.

**How to find:**
- Open browser DevTools → Network tab
- Navigate through the Stark TMS interface
- Capture all API calls to `app.transfix.io/stark/v8/*`
- Note the HTTP method, path, request body, and response structure

### 4. **Pagination**

**Current implementation:**
- Uses `page_count` parameter (from your example)
- Need to verify if there's a `page_size` or similar parameter
- Check response structure for pagination metadata (total count, has_more, etc.)

### 5. **Filter Structure**

**Current implementation:**
- Filters are JSON-encoded in query parameter
- Based on your example, we have a comprehensive filter structure
- May need to verify all filter options work as expected

## Example API Call Structure

Based on your provided example:

```bash
GET https://app.transfix.io/stark/v8/shipments
Query Parameters:
  - filters: JSON-encoded filter object
  - sort_field: e.g., "pickups"
  - sort_order: e.g., "asc"
  - page_count: pagination offset

Headers:
  - x-user-token: <extracted from Okta session>
  - x-user-email: <user email>
  - x-user-id: <user ID from Okta>
  - x-rogers-revision: <revision number>
  - content-type: application/json
  - accept: */*
```

## Next Steps

1. **Scrape Okta Authentication:**
   - Log into the Stark TMS via browser
   - Capture the full Okta authentication flow
   - Document all cookies, tokens, and session data
   - Test token refresh mechanism

2. **Extract Required Headers:**
   - Find where `x-user-id` and `x-rogers-revision` are set
   - Determine if they're static or dynamic
   - Check if they need to be refreshed with the token

3. **Test the Integration:**
   - Once authentication is working, test `GetLoad` and `GetLoadIDs`
   - Verify data mapping is correct
   - Test with various shipment statuses and filters

4. **Implement Additional Features:**
   - Check calls (if API exists)
   - Notes (if API exists)
   - Exceptions (if API exists)
   - Update/Create operations (if needed)

## Notes

- The integration follows the same pattern as other TMS integrations (Aljex, Relay, Turvo, GlobalTranz)
- All data is mapped to the standard Drumkit Load model
- Transport type mapping is implemented for common equipment types
- The integration uses the standard error handling and logging patterns

## Technical Implementation Details

### Timezone Handling

Stark TMS's update endpoints (for appointments, ETAs, and carrier timestamps) are designed to accept raw date and time strings (e.g., `"2006-01-02"` and `"15:04"`) without any timezone information or offsets.

Because these raw strings are interpreted by Stark as being in the local time of the stop, our integration must:
1.  Identify the timezone of the specific stop (pickup or delivery).
2.  Load that timezone location using `time.LoadLocation`.
3.  Convert the internal UTC timestamps (from the `models.Load`) into the stop's local time.
4.  Format the local time into the date and time strings required by the Stark API.

This ensures that update operations correctly reflect the intended local time at each location, regardless of where the server is running or the timezone of the data source.
