package stark

import "strings"

// Stark GraphQL returns a detailed map of business hours for a facility which FE then displays a human-readable string.
// This function mimics that behavior
func formatBusinessHours(
	dayMap map[string]FacilityScheduleHour,
	title string,
) string {
	var sb strings.Builder
	sb.WriteString(title)
	sb.WriteString("\n")

	if dayMap == nil {
		sb.WriteString("No hours available")
		return strings.TrimSpace(sb.String())
	}

	type block struct {
		startDay string
		endDay   string
		value    string
	}

	var blocks []block
	var current *block

	for _, day := range dayOrder {
		h, ok := dayMap[day]

		var val string
		switch {
		case !ok || h.Closed:
			val = "Closed"
		case isOpen24Hours(h):
			val = "Open 24 Hours"
		default:
			val = h.StartTime + " - " + h.EndTime
		}

		if current == nil || current.value != val {
			current = &block{
				startDay: day,
				endDay:   day,
				value:    val,
			}
			blocks = append(blocks, *current)
		} else {
			current.endDay = day
			blocks[len(blocks)-1] = *current
		}
	}

	for _, b := range blocks {
		dayLabel := dayShort[b.startDay]
		if b.startDay != b.endDay {
			dayLabel += "-" + dayShort[b.endDay]
		}

		sb.WriteString(dayLabel)
		sb.WriteString(": ")
		sb.WriteString(b.value)
		sb.WriteString("\n")
	}

	return strings.TrimSpace(sb.String())
}
