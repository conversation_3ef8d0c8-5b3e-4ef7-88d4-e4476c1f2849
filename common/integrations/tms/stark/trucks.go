package stark

import (
	"context"
	"errors"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) GetTruckNumbers(
	ctx context.Context,
	carrierApplicationID string,
) (truckNumbers []models.TMSTruckNumber, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetTruckNumbersStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	if carrierApplicationID == "" {
		return nil, errors.New("carrierApplicationID is required for GetTruckNumbers")
	}

	variables := map[string]any{
		"carrierApplicationId": carrierApplicationID,
		"searchTerm":           "",
	}

	query := `query SearchCarrierApplicationTruckNumbers($searchTerm: String!, $carrierApplicationId: ID!) {
		searchAllCarrierApplicationTruckNumbers(
			searchTerm: $searchTerm
			carrierApplicationId: $carrierApplicationId
		) {
			edges {
				node {
					id
					p44State
					value
					__typename
				}
				__typename
			}
			__typename
		}
	}`

	var response SearchCarrierApplicationTruckNumbersResponse
	err = s.graphQLQuery(
		ctx,
		"SearchCarrierApplicationTruckNumbers",
		variables,
		query,
		&response,
		s3backup.TypeTruckLists,
	)
	if err != nil {
		return nil, errtypes.WrapNewUserFacingError(
			"failed to get truck numbers",
			err,
			cleanStarkError,
		)
	}

	truckNumbers = make([]models.TMSTruckNumber, 0, len(response.Data.SearchAllCarrierApplicationTruckNumbers.Edges))
	for _, edge := range response.Data.SearchAllCarrierApplicationTruckNumbers.Edges {
		id, parseErr := strconv.ParseUint(edge.Node.ID, 10, 64)
		if parseErr != nil {
			log.WarnNoSentry(
				ctx,
				"failed to parse truck number ID",
				zap.String("id", edge.Node.ID),
				zap.Error(parseErr),
			)
			continue
		}

		truckNumbers = append(truckNumbers, models.TMSTruckNumber{
			ID:          uint(id),
			TruckNumber: edge.Node.Value,
		})
	}

	return truckNumbers, nil
}
