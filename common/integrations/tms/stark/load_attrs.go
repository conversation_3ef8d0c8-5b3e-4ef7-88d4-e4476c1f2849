package stark

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := defaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&s.tms, &attrs)
	return attrs
}

var defaultLoadAttributes = models.LoadAttributes{
	ExternalTMSID:     models.FieldAttributes{IsReadOnly: true},
	FreightTrackingID: models.FieldAttributes{IsReadOnly: true},
	Commodities:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
	LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
		Status:           models.FieldAttributes{IsReadOnly: true},
		Mode:             models.FieldAttributes{IsReadOnly: true},
		MoreThanTwoStops: models.FieldAttributes{IsReadOnly: true},
		PONums:           models.FieldAttributes{IsReadOnly: false},
		Operator:         models.FieldAttributes{IsReadOnly: false},
		RateData:         models.InitUnsupportedRateData,
		Customer: models.CustomerAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
				Name:          models.FieldAttributes{IsReadOnly: true},
				AddressLine1:  models.FieldAttributes{IsReadOnly: true},
				AddressLine2:  models.FieldAttributes{IsReadOnly: true},
				City:          models.FieldAttributes{IsReadOnly: true},
				State:         models.FieldAttributes{IsReadOnly: true},
				Zipcode:       models.FieldAttributes{IsReadOnly: true},
				Country:       models.FieldAttributes{IsReadOnly: true},
				Contact:       models.FieldAttributes{IsReadOnly: true},
				Phone:         models.FieldAttributes{IsReadOnly: true},
				Email:         models.FieldAttributes{IsReadOnly: true},
			},
			RefNumber:           models.FieldAttributes{IsReadOnly: true},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
		},
		BillTo: models.BillToAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{IsNotSupported: true},
				AddressLine1:  models.FieldAttributes{IsNotSupported: true},
				AddressLine2:  models.FieldAttributes{IsNotSupported: true},
				City:          models.FieldAttributes{IsNotSupported: true},
				State:         models.FieldAttributes{IsNotSupported: true},
				Zipcode:       models.FieldAttributes{IsNotSupported: true},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{IsNotSupported: true},
				Email:         models.FieldAttributes{IsNotSupported: true},
			},
		},
		Pickup: models.PickupAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
				Name:          models.FieldAttributes{IsReadOnly: true},
				AddressLine1:  models.FieldAttributes{IsReadOnly: true},
				AddressLine2:  models.FieldAttributes{IsReadOnly: true},
				City:          models.FieldAttributes{IsReadOnly: true},
				State:         models.FieldAttributes{IsReadOnly: true},
				Zipcode:       models.FieldAttributes{IsReadOnly: true},
				Country:       models.FieldAttributes{IsReadOnly: true},
				Contact:       models.FieldAttributes{IsReadOnly: true},
				Phone:         models.FieldAttributes{IsReadOnly: true},
				Email:         models.FieldAttributes{IsReadOnly: true},
			},
			ExternalTMSStopID:   models.FieldAttributes{IsReadOnly: true},
			BusinessHours:       models.FieldAttributes{IsNotSupported: true},
			RefNumber:           models.FieldAttributes{IsReadOnly: false},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			ReadyTime:           models.FieldAttributes{IsReadOnly: false},
			ApptStartTime:       models.FieldAttributes{IsReadOnly: false},
			ApptEndTime:         models.FieldAttributes{IsReadOnly: false},
			ApptRequired:        models.FieldAttributes{IsNotSupported: true},
			ApptType:            models.FieldAttributes{IsReadOnly: false},
			ApptNote:            models.FieldAttributes{IsReadOnly: false},
		},
		Consignee: models.ConsigneeAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
				Name:          models.FieldAttributes{IsReadOnly: true},
				AddressLine1:  models.FieldAttributes{IsReadOnly: true},
				AddressLine2:  models.FieldAttributes{IsReadOnly: true},
				City:          models.FieldAttributes{IsReadOnly: true},
				State:         models.FieldAttributes{IsReadOnly: true},
				Zipcode:       models.FieldAttributes{IsReadOnly: true},
				Country:       models.FieldAttributes{IsReadOnly: true},
				Contact:       models.FieldAttributes{IsReadOnly: true},
				Phone:         models.FieldAttributes{IsReadOnly: true},
				Email:         models.FieldAttributes{IsReadOnly: true},
			},
			ExternalTMSStopID:   models.FieldAttributes{IsReadOnly: true},
			BusinessHours:       models.FieldAttributes{IsNotSupported: true},
			RefNumber:           models.FieldAttributes{IsReadOnly: false},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			MustDeliver:         models.FieldAttributes{IsReadOnly: false},
			ApptRequired:        models.FieldAttributes{IsNotSupported: true},
			ApptStartTime:       models.FieldAttributes{IsReadOnly: false},
			ApptEndTime:         models.FieldAttributes{IsReadOnly: false},
			ApptType:            models.FieldAttributes{IsReadOnly: false},
			ApptNote:            models.FieldAttributes{IsReadOnly: false},
		},
		Carrier: models.CarrierAttributes{
			ExternalTMSID:            models.FieldAttributes{IsReadOnly: true},
			MCNumber:                 models.FieldAttributes{IsNotSupported: true},
			DOTNumber:                models.FieldAttributes{IsNotSupported: true},
			Name:                     models.FieldAttributes{IsReadOnly: true},
			Phone:                    models.FieldAttributes{IsReadOnly: true},
			Dispatcher:               models.FieldAttributes{IsReadOnly: true},
			Notes:                    models.FieldAttributes{IsReadOnly: true},
			SealNumber:               models.FieldAttributes{IsNotSupported: true},
			SCAC:                     models.FieldAttributes{IsNotSupported: true},
			FirstDriverName:          models.FieldAttributes{IsReadOnly: true},
			FirstDriverPhone:         models.FieldAttributes{IsReadOnly: true},
			SecondDriverName:         models.FieldAttributes{IsReadOnly: true},
			SecondDriverPhone:        models.FieldAttributes{IsReadOnly: true},
			Email:                    models.FieldAttributes{IsReadOnly: true},
			DispatchCity:             models.FieldAttributes{IsNotSupported: true},
			DispatchState:            models.FieldAttributes{IsNotSupported: true},
			ExternalTMSTruckID:       models.FieldAttributes{IsReadOnly: true},
			ExternalTMSTrailerID:     models.FieldAttributes{IsReadOnly: true},
			RateConfirmationSent:     models.FieldAttributes{IsReadOnly: true},
			ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true},
			ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true},
			DispatchedTime:           models.FieldAttributes{IsNotSupported: true},
			ExpectedPickupTime:       models.FieldAttributes{IsNotSupported: true},
			PickupStart:              models.FieldAttributes{IsNotSupported: true},
			PickupEnd:                models.FieldAttributes{IsNotSupported: true},
			ExpectedDeliveryTime:     models.FieldAttributes{IsNotSupported: true},
			DeliveryStart:            models.FieldAttributes{IsNotSupported: true},
			DeliveryEnd:              models.FieldAttributes{IsNotSupported: true},
			SignedBy:                 models.FieldAttributes{IsNotSupported: true},
		},
		Specifications: models.SpecificationsAttributes{
			OrderType:           models.FieldAttributes{IsNotSupported: true},
			TotalInPalletCount:  models.FieldAttributes{IsReadOnly: false}, // Updated via UpdateShipmentBasicInfo
			TotalOutPalletCount: models.FieldAttributes{IsReadOnly: true},
			TotalPieces:         models.FieldAttributes{IsReadOnly: true},
			Commodities:         models.FieldAttributes{IsReadOnly: true},
			NumCommodities:      models.FieldAttributes{IsReadOnly: true},
			TotalWeight:         models.FieldAttributes{IsReadOnly: false}, // Updated via UpdateShipmentBasicInfo
			TotalDistance:       models.FieldAttributes{IsReadOnly: false}, // Updated via UpdateShipmentBasicInfo
			BillableWeight:      models.FieldAttributes{IsNotSupported: true},
			ServiceType:         models.FieldAttributes{IsReadOnly: true},
			TransportType:       models.FieldAttributes{IsReadOnly: false}, // Updated via UpdateShipmentEquipment
			TransportTypeEnum:   models.FieldAttributes{IsReadOnly: false}, // Updated via UpdateShipmentEquipment
			TransportSize:       models.FieldAttributes{IsReadOnly: true},
			IsRefrigerated:      models.FieldAttributes{IsNotSupported: true},
			MinTempFahrenheit:   models.FieldAttributes{IsNotSupported: true},
			MaxTempFahrenheit:   models.FieldAttributes{IsNotSupported: true},
			LiftgatePickup:      models.FieldAttributes{IsNotSupported: true},
			LiftgateDelivery:    models.FieldAttributes{IsNotSupported: true},
			InsidePickup:        models.FieldAttributes{IsNotSupported: true},
			InsideDelivery:      models.FieldAttributes{IsNotSupported: true},
			Tarps:               models.FieldAttributes{IsNotSupported: true},
			Oversized:           models.FieldAttributes{IsNotSupported: true},
			Hazmat:              models.FieldAttributes{IsReadOnly: true},
			Straps:              models.FieldAttributes{IsNotSupported: true},
			Permits:             models.FieldAttributes{IsNotSupported: true},
			Escorts:             models.FieldAttributes{IsNotSupported: true},
			Seal:                models.FieldAttributes{IsNotSupported: true},
			CustomBonded:        models.FieldAttributes{IsNotSupported: true},
			Labor:               models.FieldAttributes{IsNotSupported: true},
		},
	},
}
