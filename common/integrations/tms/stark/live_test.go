package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const apiKey = ""
const username = ""
const tenant = ""
const testLoadID = ""

func TestLiveStarkGetLoad(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkGetLoad: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	var load models.Load
	var attrs models.LoadAttributes
	t.Run("GetLoad", func(t *testing.T) {
		log.Info(ctx, "calling GetLoad for test shipment ID")

		startTime := time.Now()
		var err error
		load, attrs, err = client.GetLoad(ctx, testLoadID)
		require.NoError(t, err, "GetLoad should succeed")

		log.Info(ctx, "GetLoad completed",
			zap.Any("load", load))

		// Verify basic load structure
		assert.NotEmpty(t, load.ExternalTMSID, "load should have external TMS ID")
		assert.NotEmpty(t, load.FreightTrackingID, "load should have freight tracking ID")
		assert.Equal(t, testLoadID, load.ExternalTMSID, "external TMS ID should match requested ID")

		log.Info(ctx, "GetLoad completed",
			zap.String("externalTMSID", load.ExternalTMSID),
			zap.String("freightTrackingID", load.FreightTrackingID),
			zap.String("status", load.Status),
			zap.String("customerName", load.Customer.Name),
			zap.Int("stopsCount", len(load.Stops)),
			zap.Int("notesCount", len(load.Notes)),
			zap.Duration("duration", time.Since(startTime)))
	})

	t.Run("ValidateBasicLoadInfo", func(t *testing.T) {
		assert.NotEmpty(t, load.ExternalTMSID, "load should have external TMS ID")
		assert.NotEmpty(t, load.FreightTrackingID, "load should have freight tracking ID")
		assert.NotEmpty(t, load.Status, "load should have status")
		assert.Equal(t, uint(1), load.ServiceID, "service ID should be set")

		log.Info(ctx, "basic load info validated",
			zap.String("externalTMSID", load.ExternalTMSID),
			zap.String("freightTrackingID", load.FreightTrackingID),
			zap.String("status", load.Status))
	})

	t.Run("ValidatePickupInfo", func(t *testing.T) {
		if load.Pickup.City == "" {
			t.Skip("skipping pickup validation: no pickup data found")
			return
		}

		assert.NotEmpty(t, load.Pickup.City, "pickup should have city")
		assert.NotEmpty(t, load.Pickup.State, "pickup should have state")

		log.Info(ctx, "pickup info validated",
			zap.String("city", load.Pickup.City),
			zap.String("state", load.Pickup.State),
			zap.String("zipcode", load.Pickup.Zipcode),
			zap.String("timezone", load.Pickup.Timezone))
	})

	t.Run("ValidateConsigneeInfo", func(t *testing.T) {
		if load.Consignee.City == "" {
			t.Skip("skipping consignee validation: no consignee data found")
			return
		}

		assert.NotEmpty(t, load.Consignee.City, "consignee should have city")
		assert.NotEmpty(t, load.Consignee.State, "consignee should have state")

		log.Info(ctx, "consignee info validated",
			zap.String("city", load.Consignee.City),
			zap.String("state", load.Consignee.State),
			zap.String("zipcode", load.Consignee.Zipcode),
			zap.String("timezone", load.Consignee.Timezone))
	})

	t.Run("ValidateStopsData", func(t *testing.T) {
		if len(load.Stops) == 0 {
			t.Skip("skipping stops validation: no stops data found")
			return
		}

		for i, stop := range load.Stops {
			assert.NotEmpty(t, stop.Address.City, "stop %d should have city", i+1)
			assert.NotEmpty(t, stop.StopType, "stop %d should have stop type", i+1)

			log.Info(ctx, "stop validated",
				zap.Int("stopNumber", stop.StopNumber),
				zap.String("stopType", stop.StopType),
				zap.String("city", stop.Address.City),
				zap.String("state", stop.Address.State),
				zap.String("externalTMSStopID", stop.ExternalTMSStopID))
		}
	})

	t.Run("ValidateLoadAttributes", func(t *testing.T) {
		assert.NotNil(t, attrs, "load attributes should not be nil")

		log.Info(ctx, "load attributes validated",
			zap.Bool("pickupApptStartTimeReadOnly", attrs.Pickup.ApptStartTime.IsReadOnly),
			zap.Bool("consigneeApptStartTimeReadOnly", attrs.Consignee.ApptStartTime.IsReadOnly),
			zap.Bool("carrierNameReadOnly", attrs.Carrier.Name.IsReadOnly),
			zap.Bool("statusReadOnly", attrs.Status.IsReadOnly),
			zap.Bool("externalTMSIDReadOnly", attrs.ExternalTMSID.IsReadOnly))
	})
}

// TestLiveStarkGetLoadIDs tests the GetLoadIDs function with different date ranges
func TestLiveStarkGetLoadIDs(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkGetLoadIDs: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	// Define test date ranges
	decFirst2025 := time.Date(2025, 12, 1, 0, 0, 0, 0, time.UTC)
	decFifth2025 := time.Date(2025, 12, 5, 23, 59, 59, 0, time.UTC)
	decTenth2025 := time.Date(2025, 12, 10, 23, 59, 59, 0, time.UTC)

	t.Run("CompareDateRanges", func(t *testing.T) {
		// Test range 1: Dec 1-10, 2025 (10 days)
		query1 := models.SearchLoadsQuery{
			FromDate: models.NullTime{Time: decFirst2025, Valid: true},
			ToDate:   models.NullTime{Time: decTenth2025, Valid: true},
		}

		// Test range 2: Dec 1-5, 2025 (5 days)
		query2 := models.SearchLoadsQuery{
			FromDate: models.NullTime{Time: decFirst2025, Valid: true},
			ToDate:   models.NullTime{Time: decFifth2025, Valid: true},
		}

		log.Info(
			ctx,
			"testing date range comparison",
			zap.String("range1", "Dec 1-10, 2025"),
			zap.String("range2", "Dec 1-5, 2025"),
		)

		// Get load IDs for both ranges
		startTime1 := time.Now()
		loadIDs1, err := client.GetLoadIDs(ctx, query1)
		duration1 := time.Since(startTime1)
		require.NoError(t, err, "GetLoadIDs should succeed for range 1")

		startTime2 := time.Now()
		loadIDs2, err := client.GetLoadIDs(ctx, query2)
		duration2 := time.Since(startTime2)
		require.NoError(t, err, "GetLoadIDs should succeed for range 2")

		log.Info(
			ctx,
			"GetLoadIDs results",
			zap.Int("range1Count", len(loadIDs1)),
			zap.Int("range2Count", len(loadIDs2)),
			zap.Duration("range1Duration", duration1),
			zap.Duration("range2Duration", duration2),
		)

		// The shorter date range (Dec 1-5) should have fewer or equal IDs compared to longer range (Dec 1-10)
		assert.LessOrEqual(t, len(loadIDs2), len(loadIDs1),
			"shorter date range (Dec 1-5) should have fewer or equal load IDs than longer range (Dec 1-10)")

		// Log some sample IDs for verification
		if len(loadIDs1) > 0 {
			sampleCount := 5
			if len(loadIDs1) < sampleCount {
				sampleCount = len(loadIDs1)
			}
			log.Info(ctx, "sample load IDs from range 1", zap.Strings("sampleIDs", loadIDs1[:sampleCount]))
		}

		if len(loadIDs2) > 0 {
			sampleCount := 5
			if len(loadIDs2) < sampleCount {
				sampleCount = len(loadIDs2)
			}
			log.Info(ctx, "sample load IDs from range 2", zap.Strings("sampleIDs", loadIDs2[:sampleCount]))
		}
	})

	t.Run("SingleDayRange", func(t *testing.T) {
		// Test with single day range
		singleDay := time.Date(2025, 12, 5, 0, 0, 0, 0, time.UTC)
		singleDayEnd := time.Date(2025, 12, 5, 23, 59, 59, 0, time.UTC)

		querySingleDay := models.SearchLoadsQuery{
			FromDate: models.NullTime{Time: singleDay, Valid: true},
			ToDate:   models.NullTime{Time: singleDayEnd, Valid: true},
		}

		startTime := time.Now()
		loadIDsSingleDay, err := client.GetLoadIDs(ctx, querySingleDay)
		duration := time.Since(startTime)
		require.NoError(t, err, "GetLoadIDs should succeed for single day range")

		log.Info(
			ctx,
			"GetLoadIDs for single day (Dec 5, 2025)",
			zap.Int("count", len(loadIDsSingleDay)),
			zap.Duration("duration", duration),
		)

		// Verify all returned IDs are valid strings
		for _, id := range loadIDsSingleDay {
			assert.NotEmpty(t, id, "load ID should not be empty")
		}
	})

	t.Run("FutureDateRange", func(t *testing.T) {
		// Test with future date range (should return empty or minimal results)
		start := time.Date(2025, 12, 10, 0, 0, 0, 0, time.UTC)
		futureEnd := time.Date(2026, 1, 1, 23, 59, 59, 0, time.UTC)

		queryFuture := models.SearchLoadsQuery{
			FromDate: models.NullTime{Time: start, Valid: true},
			ToDate:   models.NullTime{Time: futureEnd, Valid: true},
		}

		startTime := time.Now()
		loadIDsFuture, err := client.GetLoadIDs(ctx, queryFuture)
		duration := time.Since(startTime)
		require.NoError(t, err, "GetLoadIDs should succeed for future date range")

		log.Info(
			ctx,
			"GetLoadIDs for future date range (Jan 2026)",
			zap.Int("count", len(loadIDsFuture)),
			zap.Duration("duration", duration),
		)

		// Future dates should typically return fewer results
		assert.GreaterOrEqual(t, len(loadIDsFuture), 0, "should return zero or more load IDs for future dates")
	})
}
