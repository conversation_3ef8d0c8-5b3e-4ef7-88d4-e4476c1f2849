package stark

import (
	"context"
	"errors"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) GetTrailerNumbers(
	ctx context.Context,
	carrierID string,
) (trailerNumbers []models.TMSTrailerNumber, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetTrailerNumbersStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	if carrierID == "" {
		return nil, errors.New("carrierID is required for GetTrailerNumbers")
	}

	variables := map[string]any{
		"carrierId":  carrierID,
		"searchTerm": "",
	}

	query := `query SearchTrailers($searchTerm: String!, $carrierId: ID!) {
		searchTrailers(searchTerm: $searchTerm, carrierId: $carrierId) {
			edges {
				node {
					id
					number
					shipment {
						id
						__typename
					}
					__typename
				}
				__typename
			}
			__typename
		}
	}`

	var response SearchTrailersResponse
	err = s.graphQLQuery(ctx, "SearchTrailers", variables, query, &response, s3backup.TypeTrailerLists)
	if err != nil {
		return nil, errtypes.WrapNewUserFacingError("failed to get trailer numbers", err, cleanStarkError)
	}

	trailerNumbers = make([]models.TMSTrailerNumber, 0, len(response.Data.SearchTrailers.Edges))
	for _, edge := range response.Data.SearchTrailers.Edges {
		id, parseErr := strconv.ParseUint(edge.Node.ID, 10, 64)
		if parseErr != nil {
			log.WarnNoSentry(
				ctx,
				"failed to parse trailer number ID",
				zap.String("id", edge.Node.ID),
				zap.Error(parseErr),
			)
			continue
		}

		trailerNumbers = append(trailerNumbers, models.TMSTrailerNumber{
			ID:            uint(id),
			TrailerNumber: edge.Node.Number,
		})
	}

	return trailerNumbers, nil
}
