package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// const apiKey = ""
// const username = ""
// const tenant = ""
// const testLoadID = ""

func TestLiveStarkExceptions(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkExceptions: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	t.Run("GetExceptionHistory", func(t *testing.T) {
		log.Info(ctx, "calling GetExceptionHistory for test shipment ID")

		startTime := time.Now()
		exceptions, err := client.GetExceptionHistory(ctx, 1, testLoadID)
		require.NoError(t, err, "GetExceptionHistory should succeed")

		log.Info(
			ctx,
			"GetExceptionHistory completed",
			zap.Int("exceptionsCount", len(exceptions)),
			zap.Duration("duration", time.Since(startTime)),
		)

		// Basic validation
		for i, exception := range exceptions {
			assert.Equal(t, uint(1), exception.LoadID, "exception %d should have correct load ID", i)
			assert.NotEmpty(t, exception.Status, "exception %d should have status", i)

			// Validate datetime fields if present
			if exception.DateTime != "" {
				_, err := time.Parse(time.RFC3339, exception.DateTime)
				assert.NoError(t, err, "exception %d should have valid RFC3339 datetime", i)
			}

			if exception.DateTimeWithoutTimezone.Valid {
				assert.False(
					t,
					exception.DateTimeWithoutTimezone.Time.IsZero(),
					"exception %d should have valid datetime without timezone",
					i,
				)
			}

			log.Info(
				ctx,
				"exception validated",
				zap.Int("index", i),
				zap.String("status", exception.Status),
				zap.String("eventCode", exception.EventCode),
				zap.String("whoEntered", exception.WhoEntered),
				zap.String("fault", exception.Fault),
				zap.String("note", exception.Note),
				zap.String("dateTime", exception.DateTime),
			)
		}
	})
}
