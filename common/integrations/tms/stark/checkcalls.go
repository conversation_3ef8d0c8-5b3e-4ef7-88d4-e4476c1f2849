package stark

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (checkCalls []models.CheckCall, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	g, gctx := errgroup.WithContext(ctx)
	var driverLocationsResponse DriverLocationsGraphQLResponse
	var shipmentProgressResponse ShipmentProgressGraphQLResponse

	g.Go(func() error {
		resp, err := s.getDriverLocations(gctx, freightTrackingID)
		if err != nil {
			return fmt.Errorf("failed to get driver locations: %w", err)
		}

		if resp.Data.DriverLocations.Nodes == nil {
			return errtypes.EntityNotFoundError(s.tms, freightTrackingID, "shipment_id")
		}

		driverLocationsResponse = resp
		return nil
	})

	// Fetch shipment progress
	g.Go(func() error {
		resp, err := s.getShipmentProgress(gctx, freightTrackingID)
		if err != nil {
			return err
		}

		if resp.Data.Shipment.ID == "" {
			return errtypes.EntityNotFoundError(s.tms, freightTrackingID, "shipment_id")
		}

		shipmentProgressResponse = resp
		return nil
	})

	// Wait for both to complete
	if err := g.Wait(); err != nil {
		return nil, err
	}

	checkCalls = make([]models.CheckCall, 0)
	for _, stop := range shipmentProgressResponse.Data.Shipment.Stops {
		timezone := "America/New_York"
		if stop.Appointment.Timezone != "" {
			timezone = stop.Appointment.Timezone
		}

		// Create check call for arrival
		if stop.ArrivedAt != nil && *stop.ArrivedAt != "" {
			arrivedTime, err := time.Parse(time.RFC3339, *stop.ArrivedAt)
			if err == nil {
				checkCall := models.CheckCall{
					LoadID:                  loadID,
					FreightTrackingID:       freightTrackingID,
					Status:                  "Arrived",
					City:                    stop.City,
					State:                   stop.StateCode,
					Zip:                     stop.Zipcode,
					DateTime:                models.NullTime{Time: arrivedTime, Valid: true},
					DateTimeWithoutTimezone: models.NullTime{Time: arrivedTime.UTC(), Valid: true},
					Timezone:                timezone,
				}
				if stop.ArrivedAtUpdatedBy.FullName != "" {
					checkCall.Author = stop.ArrivedAtUpdatedBy.FullName
				}
				if stop.ArrivedAtRelayedVia != nil && *stop.ArrivedAtRelayedVia != "" {
					if checkCall.Notes != "" {
						checkCall.Notes += " | "
					}
					checkCall.Notes += "Relayed via: " + *stop.ArrivedAtRelayedVia
				}
				checkCalls = append(checkCalls, checkCall)
			}
		}
		// Create check call for departure
		if stop.LeftAt != nil && *stop.LeftAt != "" {
			leftTime, err := time.Parse(time.RFC3339, *stop.LeftAt)
			if err == nil {
				checkCall := models.CheckCall{
					LoadID:                  loadID,
					FreightTrackingID:       freightTrackingID,
					Status:                  "Departed",
					City:                    stop.City,
					State:                   stop.StateCode,
					Zip:                     stop.Zipcode,
					DateTime:                models.NullTime{Time: leftTime, Valid: true},
					DateTimeWithoutTimezone: models.NullTime{Time: leftTime.UTC(), Valid: true},
					Timezone:                timezone,
				}
				if stop.LeftAtUpdatedBy.FullName != "" {
					checkCall.Author = stop.LeftAtUpdatedBy.FullName
				}
				if stop.LeftAtRelayedVia != nil && *stop.LeftAtRelayedVia != "" {
					if checkCall.Notes != "" {
						checkCall.Notes += " | "
					}
					checkCall.Notes += "Relayed via: " + *stop.LeftAtRelayedVia
				}
				checkCalls = append(checkCalls, checkCall)
			}
		}
	}

	// Create location cache to avoid duplicate LocationSearch calls
	locationCache := make(map[string]GeocodingResult)

	// Sort driver locations by timestamp (most recent first)
	locations := driverLocationsResponse.Data.DriverLocations.Nodes
	sort.Slice(locations, func(i, j int) bool {
		timeI, errI := time.Parse(time.RFC3339, locations[i].PhoneTimestamp)
		timeJ, errJ := time.Parse(time.RFC3339, locations[j].PhoneTimestamp)
		if errI != nil || errJ != nil {
			return false // Keep original order if parsing fails
		}
		return timeI.After(timeJ) // Most recent first
	})

	// Determine which locations to process for geocoding (last 10)
	recentLocationsCount := min(10, len(locations))

	log.Info(
		ctx,
		"Processing driver locations",
		zap.Int("totalLocations", len(locations)),
		zap.Int("recentLocationsForGeocoding", recentLocationsCount),
	)

	// Process recent locations (last 10) with parallel geocoding
	if recentLocationsCount > 0 {
		recentLocations := locations[:recentLocationsCount]
		s.processRecentLocationsParallel(ctx, recentLocations, locationCache)
	}

	// Process all locations to create check calls
	for _, location := range locations {
		// Parse phone timestamp
		phoneTime, err := time.Parse(time.RFC3339, location.PhoneTimestamp)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to parse phone timestamp",
				zap.Error(err),
				zap.String("timestamp", location.PhoneTimestamp),
			)
			continue
		}

		var locationInfo GeocodingResult

		// Check cache for location info (recent locations should be populated by parallel processing)
		if cachedInfo, exists := s.getLocationInfoFromCacheOnly(
			location.Latitude,
			location.Longitude,
			locationCache,
		); exists {
			locationInfo = cachedInfo
		}

		// Create check call from driver location
		checkCall := models.CheckCall{
			LoadID:                  loadID,
			FreightTrackingID:       freightTrackingID,
			Status:                  "Location Update",
			DateTime:                models.NullTime{Time: phoneTime, Valid: true},
			DateTimeWithoutTimezone: models.NullTime{Time: phoneTime.UTC(), Valid: true},
			Timezone:                location.Timezone,
			Source:                  cases.Title(language.English).String(location.SourceType),
			Lat:                     location.Latitude,
			Lon:                     location.Longitude,
		}

		// Use location info if available
		if locationInfo.City != "" {
			checkCall.City = locationInfo.City
			checkCall.State = locationInfo.StateCode
			checkCall.Zip = locationInfo.PostalCode
		}

		// Add driver info if available
		if location.Driver != nil {
			driverName := strings.TrimSpace(location.Driver.FirstName + " " + location.Driver.LastName)
			if driverName != "" {
				checkCall.Author = driverName
			}
		}

		// Add source type and device info to notes

		if location.PhoneModel != "" {
			checkCall.Notes += fmt.Sprintf("Device: %s", location.PhoneModel)
		}

		if location.PhoneOS != "" {
			if checkCall.Notes != "" {
				checkCall.Notes += " | "
			}
			checkCall.Notes += fmt.Sprintf("OS: %s", cases.Title(language.English).String(location.PhoneOS))
		}

		checkCalls = append(checkCalls, checkCall)
	}

	return checkCalls, nil
}

func (s *Stark) getShipmentProgress(
	ctx context.Context,
	freightTrackingID string,
) (ShipmentProgressGraphQLResponse, error) {
	query := `query ShipmentProgress($id: ID!) {
		shipment(id: $id) {
			id
			stops {
				id
				address
				addressLine2
				city
				stateCode
				zipcode
				stopType
				stopSequence
				arrivedAt
				arrivedAtUpdatedBy {
					id
					fullName
					__typename
				}
				arrivedAtRelayedVia
				leftAt
				leftAtUpdatedBy {
					id
					fullName
					__typename
				}
				leftAtRelayedVia
				appointment {
					timezone
					__typename
				}
				__typename
			}
			__typename
		}
	}`

	shipmentProgressVariables := map[string]any{
		"id": freightTrackingID,
	}

	var shipmentProgressResponse ShipmentProgressGraphQLResponse
	shipmentProgressErr := s.graphQLQuery(
		ctx,
		"ShipmentProgress",
		shipmentProgressVariables,
		query,
		&shipmentProgressResponse,
		s3backup.TypeCheckCalls,
	)
	if shipmentProgressErr != nil {
		return ShipmentProgressGraphQLResponse{}, errtypes.WrapNewUserFacingError(
			"failed to get shipment progress",
			shipmentProgressErr,
			cleanStarkError,
		)
	}

	return shipmentProgressResponse, nil
}

func (s *Stark) getDriverLocations(
	ctx context.Context,
	freightTrackingID string,
) (DriverLocationsGraphQLResponse, error) {
	driverLocationsQuery := `query DriverLocations($shipmentId: ID!) {
		driverLocations(shipmentId: $shipmentId) {
			nodes {
				id
				...TrackingNode
				__typename
			}
			__typename
		}
	}

	fragment TrackingNode on DriverLocation {
		id
		driver {
			id
			firstName
			lastName
			parentUserId
			__typename
		}
		latitude
		longitude
		timezone
		sourceType
		phoneTimestamp
		osTimestamp
		phoneModel
		phoneOs
		transfixVersion
		__typename
	}`

	variables := map[string]any{
		"shipmentId": freightTrackingID,
	}

	var driverLocationsResponse DriverLocationsGraphQLResponse
	err := s.graphQLQuery(
		ctx,
		"DriverLocations",
		variables,
		driverLocationsQuery,
		&driverLocationsResponse,
		s3backup.TypeCheckCalls,
	)
	if err != nil {
		return DriverLocationsGraphQLResponse{}, errtypes.WrapNewUserFacingError(
			"failed to get driver locations",
			err,
			cleanStarkError,
		)
	}
	return driverLocationsResponse, nil
}

func getLocationKey(lat, lng float64) string {
	return fmt.Sprintf("%.4f,%.4f", lat, lng)
}

func (s *Stark) getLocationInfo(
	ctx context.Context,
	lat float64,
	lng float64,
	locationCache map[string]GeocodingResult,
	cacheMu *sync.Mutex,
) (GeocodingResult, error) {
	locationKey := getLocationKey(lat, lng)

	// Check cache first
	cacheMu.Lock()
	if cachedLocation, exists := locationCache[locationKey]; exists {
		cacheMu.Unlock()
		return cachedLocation, nil
	}
	cacheMu.Unlock()

	// Create context with 5-second timeout for GraphQL query
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Call LocationSearch GraphQL API
	locationSearchQuery := `query LocationSearch(
			$searchString: String,
			$latitude: Float,
			$longitude: Float,
			$resultTypes: [GeocodingResultTypeEnum!],
			$countries: [GeocodingAllowedCountryEnum!]
		) {
			geocodingSearch(
				searchString: $searchString
				longitude: $longitude
				latitude: $latitude
				resultTypes: $resultTypes
				countries: $countries
			) {
				...LocationSearchItem
				__typename
			}
		}

		fragment LocationSearchItem on Geocoding {
			id
			address
			city
			coordinates
			country
			countryCode
			latitude
			longitude
			postalCode
			resultType
			state
			stateCode
			__typename
		}`

	locationVariables := map[string]any{
		"longitude": lng,
		"latitude":  lat,
	}

	var locationResponse LocationSearchGraphQLResponse
	err := s.graphQLQuery(
		timeoutCtx,
		"LocationSearch",
		locationVariables,
		locationSearchQuery,
		&locationResponse,
		s3backup.TypeCheckCalls,
	)
	if err != nil {
		return GeocodingResult{}, errtypes.WrapNewUserFacingError("failed to get location info", err, cleanStarkError)
	}

	// Use first result if available
	if len(locationResponse.Data.GeocodingSearch) > 0 {
		firstGeocodingResult := locationResponse.Data.GeocodingSearch[0]
		cacheMu.Lock()
		locationCache[locationKey] = firstGeocodingResult
		cacheMu.Unlock()
		return firstGeocodingResult, nil
	}

	// Return empty result if no location found
	return GeocodingResult{}, nil

}

// getLocationInfoFromCacheOnly returns location info only if it exists in cache
func (s *Stark) getLocationInfoFromCacheOnly(
	lat float64,
	lng float64,
	locationCache map[string]GeocodingResult,
) (GeocodingResult, bool) {
	locationKey := getLocationKey(lat, lng)
	if cachedLocation, exists := locationCache[locationKey]; exists {
		return cachedLocation, true
	}
	return GeocodingResult{}, false
}

// processRecentLocationsParallel processes recent locations in parallel with 5 workers
func (s *Stark) processRecentLocationsParallel(
	ctx context.Context,
	locations []DriverLocationNode,
	locationCache map[string]GeocodingResult,
) {
	const maxWorkers = 5
	var cacheMu sync.Mutex

	// Create channels for work distribution
	locationRequests := make(chan LocationRequest, len(locations))
	results := make(chan LocationResult, len(locations))

	// Start worker goroutines
	var wg sync.WaitGroup
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for req := range locationRequests {
				locationInfo, err := s.getLocationInfo(
					ctx,
					req.Latitude,
					req.Longitude,
					locationCache,
					&cacheMu,
				)
				results <- LocationResult{
					Index:        req.Index,
					Location:     req.Location,
					LocationInfo: locationInfo,
					Error:        err,
				}
			}
		}()
	}

	// Send work to workers
	go func() {
		defer close(locationRequests)
		for i, location := range locations {
			locationRequests <- LocationRequest{
				Index:     i,
				Location:  location,
				Latitude:  location.Latitude,
				Longitude: location.Longitude,
			}
		}
	}()

	// Close results channel when all workers are done
	go func() {
		wg.Wait()
		close(results)
	}()

	// Collect results
	processedCount := 0
	successCount := 0
	for result := range results {
		processedCount++
		if result.Error != nil {
			log.WarnNoSentry(
				ctx,
				"failed to get location info in parallel processing",
				zap.Error(result.Error),
				zap.Int("index", result.Index),
				zap.Float64("lat", result.Location.Latitude),
				zap.Float64("lng", result.Location.Longitude),
			)
		} else {
			successCount++
		}
	}

	log.Info(
		ctx,
		"Parallel location processing completed",
		zap.Int("processedCount", processedCount),
		zap.Int("successCount", successCount),
	)
}
