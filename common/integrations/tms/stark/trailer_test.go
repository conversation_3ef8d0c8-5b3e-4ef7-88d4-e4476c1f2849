package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Define test constant for trailers
const testTrailerCarrierID = "4351791" // Example carrierId

func TestLiveStarkTrailerNumbers(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkTrailerNumbers: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	t.Run("GetTrailerNumbers", func(t *testing.T) {
		log.Info(ctx, "calling GetTrailerNumbers for test carrier ID")

		startTime := time.Now()
		trailerNumbers, err := client.GetTrailerNumbers(ctx, testTrailerCarrierID)
		require.NoError(t, err, "GetTrailerNumbers should succeed")

		log.Info(
			ctx,
			"GetTrailerNumbers completed",
			zap.Int("trailerNumbersCount", len(trailerNumbers)),
			zap.Duration("duration", time.Since(startTime)),
		)

		for i, trailer := range trailerNumbers {
			assert.NotEmpty(t, trailer.ID, "trailer %d should have an ID", i)
			assert.NotEmpty(t, trailer.TrailerNumber, "trailer %d should have a trailer number", i)

			log.Info(
				ctx,
				"trailer number validated",
				zap.Int("index", i),
				zap.Uint("id", trailer.ID),
				zap.String("number", trailer.TrailerNumber),
			)
		}
	})
}
