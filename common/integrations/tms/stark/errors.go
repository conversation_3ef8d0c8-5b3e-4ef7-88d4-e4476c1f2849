package stark

import (
	"encoding/json"
	"errors"
	"regexp"
	"strings"

	"github.com/drumkitai/drumkit/common/errtypes"
)

// cleanStarkError extracts user-friendly error messages from Stark GraphQL and HTTP responses.
// This function handles both GraphQL errors and HTTP response errors to provide clean messages to users.
func cleanStarkError(origErr error) string {
	var httpErr errtypes.HTTPResponseError
	if !errors.As(origErr, &httpErr) {
		return ""
	}

	body := strings.TrimSpace(string(httpErr.ResponseBody))
	if body == "" {
		return ""
	}

	// Try to parse as GraphQL error response first
	var gqlErr struct {
		Errors []struct {
			Message string `json:"message"`
		} `json:"errors"`
	}
	if err := json.Unmarshal(httpErr.ResponseBody, &gqlErr); err == nil && len(gqlErr.Errors) > 0 {
		var messages []string
		for _, e := range gqlErr.Errors {
			if msg := strings.TrimSpace(e.Message); msg != "" {
				messages = append(messages, msg)
			}
		}
		if len(messages) > 0 {
			return strings.Join(messages, "; ")
		}
	}

	// Try to extract error from JSON response with "message" field
	var jsonErr struct {
		Message string `json:"message"`
		Error   string `json:"error"`
	}
	if err := json.Unmarshal(httpErr.ResponseBody, &jsonErr); err == nil {
		if msg := strings.TrimSpace(jsonErr.Message); msg != "" {
			return msg
		}
		if msg := strings.TrimSpace(jsonErr.Error); msg != "" {
			return msg
		}
	}

	// Clean up common HTML/XML patterns if present
	if strings.Contains(body, "<") && strings.Contains(body, ">") {
		// Remove HTML tags
		re := regexp.MustCompile(`<[^>]*>`)
		body = re.ReplaceAllString(body, " ")
		// Collapse whitespace
		body = strings.Join(strings.Fields(body), " ")
	}

	// Return cleaned body if no specific patterns matched
	return strings.TrimSpace(body)
}
