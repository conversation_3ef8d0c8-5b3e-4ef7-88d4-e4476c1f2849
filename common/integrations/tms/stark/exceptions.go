package stark

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (s *Stark) GetExceptionHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (exceptions []models.Exception, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetExceptionHistoryStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	variables := map[string]any{
		"id": freightTrackingID,
	}

	query := `query ShipmentIssues($id: ID!) {
		shipment(id: $id) {
			id
			hasOsd
			hasClaims
			issues {
				...ShipmentIssueDetails
				__typename
			}
			__typename
		}
	}

	fragment ShipmentIssueDetails on ShipmentIssue {
		id
		createdAt
		updatedAt
		createdBy {
			id
			parentUserId
			firstName
			lastName
			__typename
		}
		dueAt
		fault {
			id
			fault
			__typename
		}
		notes {
			id
			body
			__typename
		}
		owner {
			followerType
			followerId
			firstName
			lastName
			nickname
			__typename
		}
		priority
		reason {
			id
			reason
			__typename
		}
		state
		summary
		type {
			id
			categoryId
			name
			description
			__typename
		}
		updater {
			id
			parentUserId
			firstName
			lastName
			__typename
		}
		__typename
	}`

	var response ShipmentIssuesResponse
	err = s.graphQLQuery(ctx, "ShipmentIssues", variables, query, &response, s3backup.TypeExceptions)
	if err != nil {
		return nil, errtypes.WrapNewUserFacingError("failed to get shipment issues", err, cleanStarkError)
	}

	exceptions = make([]models.Exception, 0, len(response.Data.Shipment.Issues))
	for _, issue := range response.Data.Shipment.Issues {
		exception := models.Exception{
			LoadID: loadID,
			Status: issue.State,
			Note:   issue.Summary,
		}

		if issue.CreatedAt != "" {
			createdAt, err := time.Parse(time.RFC3339, issue.CreatedAt)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"failed to parse exception createdAt",
					zap.String("createdAt", issue.CreatedAt),
					zap.Error(err),
				)
			} else {
				exception.DateTimeWithoutTimezone = models.NullTime{Time: createdAt, Valid: true}
				exception.DateTime = createdAt.Format(time.RFC3339)
			}
		}

		if issue.Owner.FirstName != "" || issue.Owner.LastName != "" {
			exception.Owner = strings.TrimSpace(issue.Owner.FirstName + " " + issue.Owner.LastName)
		}

		if issue.CreatedBy.FirstName != "" || issue.CreatedBy.LastName != "" {
			exception.WhoEntered = strings.TrimSpace(issue.CreatedBy.FirstName + " " + issue.CreatedBy.LastName)
		}

		if issue.Fault.Fault != "" {
			exception.Fault = issue.Fault.Fault
		}

		if issue.Reason.Reason != "" {
			exception.EventCode = issue.Reason.Reason
		} else if issue.Type.Name != "" {
			exception.EventCode = issue.Type.Name
		}

		if len(issue.Notes) > 0 {
			if exception.Note != "" {
				exception.Note += "\n"
			}
			var noteBodies []string
			for _, note := range issue.Notes {
				if note.Body != "" {
					noteBodies = append(noteBodies, note.Body)
				}
			}
			exception.Note += strings.Join(noteBodies, "\n")
		}

		exceptions = append(exceptions, exception)
	}

	return exceptions, nil
}

func (s *Stark) PostException(
	ctx context.Context,
	load *models.Load,
	exception models.Exception,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "PostExceptionStark", otel.IntegrationAttrs(s.tms))
	defer func() { metaSpan.End(err) }()

	if load.ExternalTMSID == "" {
		return errtypes.NewUserFacingError(errors.New("ExternalTMSID is required to post exception"))
	}

	// Map exception to shipment issue
	// Default issue type ID - "34" is "Changes to Shipment" based on the example
	// In a real implementation, we might want to map EventCode to different issue types
	issueTypeID := "34"
	// if exception.EventCode != "" {
	// 	// TODO: Map EventCode to appropriate issue type ID
	// 	// For now, use default
	// }

	// Build owner - use current user if available
	owner := map[string]any{
		"followerType": "User",
	}

	// Convert userID string to int for followerId
	if s.userID != "" {
		userIDInt, err := strconv.Atoi(s.userID)
		if err != nil {
			return errtypes.NewUserFacingError(fmt.Errorf("invalid userID format: %w", err))
		}
		owner["followerId"] = userIDInt
	}

	// Build subscribers - empty for now, could be populated from exception fields
	subscribers := []map[string]any{}

	input := map[string]any{
		"shipmentId":          load.ExternalTMSID,
		"shipmentIssueTypeId": issueTypeID,
		"owner":               owner,
		"subscribers":         subscribers,
		"state":               "pending",
	}

	// Add summary if note is provided
	if exception.Note != "" {
		input["summary"] = exception.Note
	}

	variables := map[string]any{
		"input": input,
	}

	query := `mutation CreateShipmentIssue($input: CreateShipmentIssueInput!) {
		createShipmentIssue(input: $input) {
			errors {
				key
				message
				__typename
			}
			shipmentIssue {
				id
				__typename
			}
			__typename
		}
	}`

	var response CreateShipmentIssueResponse
	err = s.graphQLQuery(ctx, "CreateShipmentIssue", variables, query, &response, s3backup.TypeExceptions)
	if err != nil {
		return errtypes.WrapNewUserFacingError("failed to create shipment issue", err, cleanStarkError)
	}

	if len(response.Data.CreateShipmentIssue.Errors) > 0 {
		var errorMessages []string
		for _, e := range response.Data.CreateShipmentIssue.Errors {
			errorMessages = append(errorMessages, e.Message)
		}
		return errtypes.NewUserFacingError(fmt.Errorf("create exception failed: %s", strings.Join(errorMessages, ", ")))
	}

	return nil
}
