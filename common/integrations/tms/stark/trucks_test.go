package stark

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Define test constant for trucks
const testTruckCarrierApplicationID = "4351791" // Example carrierApplicationId

func TestLiveStarkTruckNumbers(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveStarkTruckNumbers: run with LIVE_TEST=true to enable")
		return
	}

	if apiKey == "" {
		t.Skip("No Stark API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  username,
		Tenant:    tenant,
		APIKey:    apiKey,
	}
	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")

	t.Run("GetTruckNumbers", func(t *testing.T) {
		log.Info(ctx, "calling GetTruckNumbers for test carrier application ID")

		startTime := time.Now()
		truckNumbers, err := client.GetTruckNumbers(ctx, testTruckCarrierApplicationID)
		require.NoError(t, err, "GetTruckNumbers should succeed")

		log.Info(
			ctx,
			"GetTruckNumbers completed",
			zap.Int("truckNumbersCount", len(truckNumbers)),
			zap.Duration("duration", time.Since(startTime)),
		)

		for i, truck := range truckNumbers {
			assert.NotEmpty(t, truck.ID, "truck %d should have an ID", i)
			assert.NotEmpty(t, truck.TruckNumber, "truck %d should have a truck number", i)

			log.Info(
				ctx,
				"truck number validated",
				zap.Int("index", i),
				zap.Uint("id", truck.ID),
				zap.String("number", truck.TruckNumber),
			)
		}
	})
}
