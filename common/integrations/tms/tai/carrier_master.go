package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
)

const (
	GetCarrierMasterPath = "/PublicApi/Carriers/v2/Carriers/Master/%s"
)

type CarrierMasterAddress struct {
	StreetAddress string `json:"streetAddress"`
	City          string `json:"city"`
	State         string `json:"state"`
	ZipCode       string `json:"zipCode"`
	Country       string `json:"country"`
}

type CarrierMasterMailingAddress struct {
	StreetAddress    string `json:"streetAddress"`
	StreetAddressTwo string `json:"streetAddressTwo"`
	City             string `json:"city"`
	State            string `json:"state"`
	ZipCode          string `json:"zipCode"`
	Country          string `json:"country"`
}

type CarrierMasterInfo struct {
	Address            CarrierMasterAddress        `json:"address"`
	BrokerAuthority    string                      `json:"brokerAuthority"`
	CarrierName        string                      `json:"carrierName"`
	CommonAuthority    string                      `json:"commonAuthority"`
	ContractAuthority  string                      `json:"contractAuthority"`
	DOTNumber          string                      `json:"dotNumber"`
	DOTStatus          string                      `json:"dotStatus"`
	Drivers            int                         `json:"drivers"`
	Fax                string                      `json:"fax"`
	Intrastate         bool                        `json:"intrastate"`
	MailingAddress     CarrierMasterMailingAddress `json:"mailingAddress"`
	MailingPhone       string                      `json:"mailingPhone"`
	MailingFax         string                      `json:"mailingFax"`
	MotorCarrierNumber string                      `json:"motorCarrierNumber"`
	OutOfService       bool                        `json:"outOfService"`
	Phone              string                      `json:"phone"`
	PowerUnits         int                         `json:"powerUnits"`
	Rating             string                      `json:"rating"`
	SCAC               string                      `json:"scac"`
	TSACompliance      string                      `json:"tsaCompliance"`
}

type RemitPaymentAddress struct {
	CompanyName string               `json:"companyName"`
	Address     CarrierMasterAddress `json:"address"`
	Phone       string               `json:"phone"`
	Fax         string               `json:"fax"`
	Email       string               `json:"email"`
}

type LSPCarrierContact struct {
	LSPCarrierContactID int                  `json:"lspCarrierContactId"`
	Address             CarrierMasterAddress `json:"address"`
	CompanyName         string               `json:"companyName"`
	ContactName         string               `json:"contactName"`
	ContactType         string               `json:"contactType"`
	Email               string               `json:"email"`
	IsFavorite          bool                 `json:"isFavorite"`
	Phone               string               `json:"phone"`
}

type CarrierMasterResp struct {
	MasterCarrierID                 int                 `json:"masterCarrierId"`
	LSPCarrierID                    int                 `json:"lspCarrierId"`
	CarrierMasterInfo               CarrierMasterInfo   `json:"carrierMasterInfo"`
	CarrierNotes                    string              `json:"carrierNotes"`
	LSPCarrierContacts              []LSPCarrierContact `json:"lspCarrierContacts"`
	LSPCarrierSupportedTrailerTypes []string            `json:"lspCarrierSupportedTrailerTypes"`
	Status                          string              `json:"status"`
}

func (t Tai) GetCarrierMaster(ctx context.Context, carrierMasterID int) (CarrierMasterResp, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.Int("carrier_master_id", carrierMasterID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetCarrierMasterTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	path := fmt.Sprintf(GetCarrierMasterPath, strconv.Itoa(carrierMasterID))
	queryParams := url.Values{}
	var carrierMaster CarrierMasterResp
	err = t.get(ctx, path, queryParams, &carrierMaster, s3backup.TypeLoads)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to fetch carrier master details",
			zap.Error(err),
			zap.Int("carrierMasterID", carrierMasterID),
			zap.String("path", path),
			zap.Any("queryParams", queryParams),
		)
		return CarrierMasterResp{}, fmt.Errorf("failed to fetch carrier master: %w", err)
	}

	return carrierMaster, nil
}
