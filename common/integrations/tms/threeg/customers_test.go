package threeg

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestDeduplicateCustomers(t *testing.T) {
	tests := []struct {
		name          string
		customers     []models.TMSCustomer
		expectedCount int
		expectedFirst string // Expected ExternalTMSID of first customer
		expectedLast  string // Expected ExternalTMSID of last customer
	}{
		{
			name:          "no duplicates",
			customers:     createTestCustomers([]string{"CUST001", "CUST002", "CUST003"}),
			expectedCount: 3,
			expectedFirst: "CUST001",
			expectedLast:  "CUST003",
		},
		{
			name:          "with duplicates",
			customers:     createTestCustomers([]string{"CUST001", "CUST002", "CUST001", "CUST003", "CUST002"}),
			expectedCount: 3,
			expectedFirst: "CUST001",
			expectedLast:  "CUST003",
		},
		{
			name:          "all duplicates",
			customers:     createTestCustomers([]string{"CUST001", "CUST001", "CUST001"}),
			expectedCount: 1,
			expectedFirst: "CUST001",
			expectedLast:  "CUST001",
		},
		{
			name:          "empty slice",
			customers:     []models.TMSCustomer{},
			expectedCount: 0,
		},
		{
			name:          "nil slice",
			customers:     nil,
			expectedCount: 0,
		},
		{
			name: "with empty ExternalTMSID",
			customers: []models.TMSCustomer{
				createTestCustomer("CUST001", "Customer 1"),
				createTestCustomer("", "Customer 2"), // Empty ExternalTMSID
				createTestCustomer("CUST002", "Customer 3"),
				createTestCustomer("", "Customer 4"), // Another empty ExternalTMSID - should be deduplicated
			},
			expectedCount: 3, // Only first empty ID kept to prevent unique constraint violation
			expectedFirst: "CUST001",
			expectedLast:  "CUST002",
		},
		{
			name: "duplicates at different positions",
			customers: createTestCustomers(
				[]string{"CUST001", "CUST002", "CUST003", "CUST001", "CUST004", "CUST002"},
			),
			expectedCount: 4,
			expectedFirst: "CUST001",
			expectedLast:  "CUST004",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := deduplicateCustomers(tt.customers)

			assert.Equal(t, tt.expectedCount, len(result), "deduplicated count should match")

			if tt.expectedCount > 0 {
				assert.Equal(t, tt.expectedFirst, result[0].ExternalTMSID, "first customer should match")
				if tt.expectedLast != "" {
					assert.Equal(t, tt.expectedLast, result[len(result)-1].ExternalTMSID, "last customer should match")
				}
			}

			// Verify no duplicates in result
			seen := make(map[string]bool)
			for _, customer := range result {
				if customer.ExternalTMSID != "" {
					assert.False(
						t,
						seen[customer.ExternalTMSID],
						"should not have duplicates: %s",
						customer.ExternalTMSID,
					)
					seen[customer.ExternalTMSID] = true
				}
			}
		})
	}
}

func TestDeduplicateCustomersPreservesFirstOccurrence(t *testing.T) {
	// Create customers with same ExternalTMSID but different names
	customers := []models.TMSCustomer{
		createTestCustomer("CUST001", "Customer First"),
		createTestCustomer("CUST002", "Customer Second"),
		createTestCustomer("CUST001", "Customer Duplicate"), // Duplicate of first
		createTestCustomer("CUST003", "Customer Third"),
	}

	result := deduplicateCustomers(customers)

	assert.Equal(t, 3, len(result))
	// First occurrence should be preserved
	assert.Equal(t, "Customer First", result[0].Name, "first occurrence should be preserved")
	assert.Equal(t, "CUST001", result[0].ExternalTMSID)
}

func TestDeduplicateCustomersMultipleEmptyIDs(t *testing.T) {
	// Test that multiple empty ExternalTMSID records are deduplicated
	// to prevent unique constraint violations
	customers := []models.TMSCustomer{
		createTestCustomer("", "Customer Empty 1"),
		createTestCustomer("", "Customer Empty 2"), // Should be deduplicated
		createTestCustomer("", "Customer Empty 3"), // Should be deduplicated
		createTestCustomer("CUST001", "Customer With ID"),
		createTestCustomer("", "Customer Empty 4"), // Should be deduplicated
	}

	result := deduplicateCustomers(customers)

	// Should only have 2 records: first empty ID + the one with ID
	assert.Equal(t, 2, len(result))
	// First occurrence (empty ID) should be preserved
	assert.Equal(t, "", result[0].ExternalTMSID)
	assert.Equal(t, "Customer Empty 1", result[0].Name)
	// The one with ID should be included
	assert.Equal(t, "CUST001", result[1].ExternalTMSID)
	assert.Equal(t, "Customer With ID", result[1].Name)
}

// Helper function to create test customers with given ExternalTMSIDs
func createTestCustomers(externalIDs []string) []models.TMSCustomer {
	customers := make([]models.TMSCustomer, len(externalIDs))
	for i, id := range externalIDs {
		customers[i] = createTestCustomer(id, "Customer "+id)
	}
	return customers
}

// Helper function to create a single test customer
func createTestCustomer(externalID, name string) models.TMSCustomer {
	return models.TMSCustomer{
		TMSIntegrationID: 1,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: externalID,
			Name:          name,
			AddressLine1:  "123 Test St",
			City:          "Test City",
			State:         "TS",
			Zipcode:       "12345",
			Country:       "US",
		},
		NameAddress: name + ", 123 Test St, Test City, TS 12345",
	}
}
