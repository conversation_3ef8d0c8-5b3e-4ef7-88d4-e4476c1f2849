package threeg

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestDeduplicateLocations(t *testing.T) {
	tests := []struct {
		name          string
		locations     []models.TMSLocation
		expectedCount int
		expectedFirst string // Expected ExternalTMSID of first location
		expectedLast  string // Expected ExternalTMSID of last location
	}{
		{
			name:          "no duplicates",
			locations:     createTestLocations([]string{"LOC001", "LOC002", "LOC003"}),
			expectedCount: 3,
			expectedFirst: "LOC001",
			expectedLast:  "LOC003",
		},
		{
			name:          "with duplicates",
			locations:     createTestLocations([]string{"LOC001", "LOC002", "LOC001", "LOC003", "LOC002"}),
			expectedCount: 3,
			expectedFirst: "LOC001",
			expectedLast:  "LOC003",
		},
		{
			name:          "all duplicates",
			locations:     createTestLocations([]string{"LOC001", "LOC001", "LOC001"}),
			expectedCount: 1,
			expectedFirst: "LOC001",
			expectedLast:  "LOC001",
		},
		{
			name:          "empty slice",
			locations:     []models.TMSLocation{},
			expectedCount: 0,
		},
		{
			name:          "nil slice",
			locations:     nil,
			expectedCount: 0,
		},
		{
			name: "with empty ExternalTMSID",
			locations: []models.TMSLocation{
				createTestLocation("LOC001", "Location 1"),
				createTestLocation("", "Location 2"), // Empty ExternalTMSID
				createTestLocation("LOC002", "Location 3"),
				createTestLocation("", "Location 4"), // Another empty ExternalTMSID - should be deduplicated
			},
			expectedCount: 3, // Only first empty ID kept to prevent unique constraint violation
			expectedFirst: "LOC001",
			expectedLast:  "LOC002",
		},
		{
			name:          "duplicates at different positions",
			locations:     createTestLocations([]string{"LOC001", "LOC002", "LOC003", "LOC001", "LOC004", "LOC002"}),
			expectedCount: 4,
			expectedFirst: "LOC001",
			expectedLast:  "LOC004",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := deduplicateLocations(tt.locations)

			assert.Equal(t, tt.expectedCount, len(result), "deduplicated count should match")

			if tt.expectedCount > 0 {
				assert.Equal(t, tt.expectedFirst, result[0].ExternalTMSID, "first location should match")
				if tt.expectedLast != "" {
					assert.Equal(t, tt.expectedLast, result[len(result)-1].ExternalTMSID, "last location should match")
				}
			}

			// Verify no duplicates in result
			seen := make(map[string]bool)
			for _, location := range result {
				if location.ExternalTMSID != "" {
					assert.False(
						t,
						seen[location.ExternalTMSID],
						"should not have duplicates: %s",
						location.ExternalTMSID,
					)
					seen[location.ExternalTMSID] = true
				}
			}
		})
	}
}

func TestDeduplicateLocationsPreservesFirstOccurrence(t *testing.T) {
	// Create locations with same ExternalTMSID but different names
	locations := []models.TMSLocation{
		createTestLocation("LOC001", "Location First"),
		createTestLocation("LOC002", "Location Second"),
		createTestLocation("LOC001", "Location Duplicate"), // Duplicate of first
		createTestLocation("LOC003", "Location Third"),
	}

	result := deduplicateLocations(locations)

	assert.Equal(t, 3, len(result))
	// First occurrence should be preserved
	assert.Equal(t, "Location First", result[0].Name, "first occurrence should be preserved")
	assert.Equal(t, "LOC001", result[0].ExternalTMSID)
}

func TestDeduplicateLocationsMultipleEmptyIDs(t *testing.T) {
	// Test that multiple empty ExternalTMSID records are deduplicated
	// to prevent unique constraint violations
	locations := []models.TMSLocation{
		createTestLocation("", "Location Empty 1"),
		createTestLocation("", "Location Empty 2"), // Should be deduplicated
		createTestLocation("", "Location Empty 3"), // Should be deduplicated
		createTestLocation("LOC001", "Location With ID"),
		createTestLocation("", "Location Empty 4"), // Should be deduplicated
	}

	result := deduplicateLocations(locations)

	// Should only have 2 records: first empty ID + the one with ID
	assert.Equal(t, 2, len(result))
	// First occurrence (empty ID) should be preserved
	assert.Equal(t, "", result[0].ExternalTMSID)
	assert.Equal(t, "Location Empty 1", result[0].Name)
	// The one with ID should be included
	assert.Equal(t, "LOC001", result[1].ExternalTMSID)
	assert.Equal(t, "Location With ID", result[1].Name)
}

// Helper function to create test locations with given ExternalTMSIDs
func createTestLocations(externalIDs []string) []models.TMSLocation {
	locations := make([]models.TMSLocation, len(externalIDs))
	for i, id := range externalIDs {
		locations[i] = createTestLocation(id, "Location "+id)
	}
	return locations
}

// Helper function to create a single test location
func createTestLocation(externalID, name string) models.TMSLocation {
	return models.TMSLocation{
		TMSIntegrationID: 1,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: externalID,
			Name:          name,
			AddressLine1:  "123 Test St",
			City:          "Test City",
			State:         "TS",
			Zipcode:       "12345",
			Country:       "US",
		},
		FullAddress: "123 Test St, Test City, TS 12345",
		NameAddress: name + ", 123 Test St, Test City, TS 12345",
		Latitude:    40.7128,
		Longitude:   -74.0060,
	}
}
