package threeg

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsServiceOptionMappingDB "github.com/drumkitai/drumkit/common/rds/tms_service_option_mapping"
	tmsTransportTypeMappingDB "github.com/drumkitai/drumkit/common/rds/tms_transport_type_mapping"
)

const (
	webAvailableEquipmentListPath = "/web/availableEquipmentList"
	webServiceOptionListPath      = "/web/serviceOptionList"
	equipmentPageSize             = 500
	serviceOptionPageSize         = 500
)

// AvailableEquipmentResponse represents a single equipment item from 3G TMS
type AvailableEquipmentResponse struct {
	EquipmentID             int    `json:"equipmentId"`
	EquipmentName           string `json:"equipmentName"`
	EquipmentCode           string `json:"equipmentCode"`
	EquipmentClassification string `json:"dataValue_EquipmentClassification"`
	WeightMax               string `json:"weightMax"`
	WeightMaxUOM            string `json:"weightMaxUOM"`
	VolumeMax               string `json:"volumeMax"`
	VolumeMaxUOM            string `json:"volumeMaxUOM"`
	MaxPallets              int    `json:"maxPallets"`
}

// SyncTransportTypes fetches all transport types from 3G TMS and stores them in the database
// Returns the list of transport types fetched from 3G TMS
// Uses pagination with 500 records per page
func (t *ThreeG) SyncTransportTypes(ctx context.Context) ([]models.TMSEquipmentType, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "SyncTransportTypesThreeG", otel.IntegrationAttrs(t.integration))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "starting 3G TMS transport types sync", zap.Uint("integration_id", t.integration.ID))

	var allEquipment []AvailableEquipmentResponse
	pageNum := 0

	for {
		log.Info(
			ctx,
			"3G TMS fetching transport types page",
			zap.Int("page_num", pageNum),
			zap.Int("page_size", equipmentPageSize),
		)

		equipment, fetchErr := t.getAvailableEquipmentListPaginated(ctx, pageNum, equipmentPageSize)
		if fetchErr != nil {
			log.Error(
				ctx,
				"3G TMS transport types page fetch failed",
				zap.Int("page_num", pageNum),
				zap.Error(fetchErr),
			)
			err = fetchErr
			break
		}

		log.Info(
			ctx,
			"3G TMS transport types page result",
			zap.Int("page_num", pageNum),
			zap.Int("transport_types_count", len(equipment)),
			zap.Int("total_transport_types_so_far", len(allEquipment)),
		)

		// If we get 0 records, we've reached the end
		if len(equipment) == 0 {
			if pageNum == 0 {
				log.Warn(ctx, "failed to fetch 3G TMS transport types - empty response on first page")
			} else {
				log.Info(
					ctx,
					"3G TMS reached end - no more transport types data",
					zap.Int("final_page", pageNum),
					zap.Int("total_transport_types", len(allEquipment)),
				)
			}
			break
		}

		allEquipment = append(allEquipment, equipment...)

		// If we got fewer records than page size, we've reached the end
		if len(equipment) < equipmentPageSize {
			log.Info(
				ctx,
				"3G TMS reached end - partial page",
				zap.Int("final_page", pageNum),
				zap.Int("total_transport_types", len(allEquipment)),
			)
			break
		}

		pageNum++
	}

	if err != nil {
		return nil, fmt.Errorf("error fetching transport types: %w", err)
	}

	if len(allEquipment) == 0 {
		log.Warn(ctx, "no transport types fetched from 3G TMS")
		return []models.TMSEquipmentType{}, nil
	}

	// Convert to database models and map to TransportType
	mappings := make([]models.TMSTransportTypeMapping, 0, len(allEquipment))
	for _, eq := range allEquipment {
		mapping := models.TMSTransportTypeMapping{
			TMSIntegrationID:        t.integration.ID,
			EquipmentID:             eq.EquipmentID,
			EquipmentName:           eq.EquipmentName,
			EquipmentCode:           eq.EquipmentCode,
			EquipmentClassification: eq.EquipmentClassification,
			WeightMax:               eq.WeightMax,
			WeightMaxUOM:            eq.WeightMaxUOM,
			VolumeMax:               eq.VolumeMax,
			VolumeMaxUOM:            eq.VolumeMaxUOM,
			MaxPallets:              eq.MaxPallets,
			MappedTransportType:     t.mapEquipmentToTransportType(eq),
		}

		mappings = append(mappings, mapping)
	}

	// Save to database
	if err = tmsTransportTypeMappingDB.RefreshTransportTypeMappings(ctx, t.integration.ID, mappings); err != nil {
		return nil, fmt.Errorf("error saving transport types to database: %w", err)
	}

	// Invalidate Redis cache (non-critical operation - don't fail the sync if this fails)
	if cacheErr := tmsTransportTypeMappingDB.InvalidateCache(ctx, t.integration.ID); cacheErr != nil {
		log.Warn(ctx, "failed to invalidate transport types cache", zap.Error(cacheErr))
	}

	// Convert to TMSEquipmentType format for response
	transportTypes := make([]models.TMSEquipmentType, 0, len(mappings))
	for _, mapping := range mappings {
		transportTypes = append(transportTypes, models.TMSEquipmentType{
			ID:   fmt.Sprintf("%d", mapping.EquipmentID),
			Name: mapping.EquipmentName,
		})
	}

	log.Info(
		ctx,
		"successfully synced transport types from 3G TMS",
		zap.Int("total_transport_types", len(allEquipment)),
		zap.Int("mappings_created", len(mappings)),
	)

	return transportTypes, nil
}

// getAvailableEquipmentListPaginated fetches transport types from 3G TMS Web API with pagination
func (t *ThreeG) getAvailableEquipmentListPaginated(
	ctx context.Context,
	pageNum int,
	pageSize int,
) ([]AvailableEquipmentResponse, error) {
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	// Calculate record indices based on page number and page size
	recordStartIndex := pageNum * pageSize
	recordEndIndex := recordStartIndex + pageSize - 1

	// Build query parameters (matching the locations endpoint pattern which uses GET)
	queryParams := url.Values{}
	queryParams.Set("filterscount", "0")
	queryParams.Set("groupscount", "0")
	queryParams.Set("pagenum", strconv.Itoa(pageNum))
	queryParams.Set("pagesize", strconv.Itoa(pageSize))
	queryParams.Set("recordstartindex", strconv.Itoa(recordStartIndex))
	queryParams.Set("recordendindex", strconv.Itoa(recordEndIndex))
	queryParams.Set("quickSearch", "")
	queryParams.Set("savedQueryId", "-1")

	// Construct the full URL with query parameters (like locations endpoint)
	endpoint := fmt.Sprintf("%s%s?%s", t.webBaseURL, webAvailableEquipmentListPath, queryParams.Encode())

	// Try GET first (like locations endpoint), if that doesn't work, we'll try POST
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating available equipment list request: %w", err)
	}

	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/clearAvailableEquipment", t.webBaseURL))

	// Log request details for debugging
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		log.Warn(ctx, "error parsing endpoint URL", zap.Error(err), zap.String("endpoint", endpoint))
		parsedURL = nil
	}
	var cookies []*http.Cookie
	if parsedURL != nil {
		cookies = t.client.Jar.Cookies(parsedURL)
	}
	log.Info(
		ctx,
		"3G TMS transport types API request",
		zap.String("endpoint", endpoint),
		zap.Int("page_num", pageNum),
		zap.Int("page_size", pageSize),
		zap.String("record_start", strconv.Itoa(recordStartIndex)),
		zap.String("record_end", strconv.Itoa(recordEndIndex)),
		zap.Int("cookie_count", len(cookies)),
		zap.String("full_url", endpoint),
	)

	resp, err := t.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, t.integration, err)
		log.Error(
			ctx,
			"3G TMS transport types API request failed",
			zap.Int("page_num", pageNum),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error making available transport types list request: %w", err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, t.integration, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			log.Error(ctx, "error reading error response body", zap.Error(readErr))
		}
		return nil, fmt.Errorf("error response from 3G TMS: %s - body: %s", resp.Status, string(body))
	}

	var equipment []AvailableEquipmentResponse
	if err := json.NewDecoder(resp.Body).Decode(&equipment); err != nil {
		return nil, fmt.Errorf("error decoding transport types response: %w", err)
	}

	return equipment, nil
}

// mapEquipmentToTransportType maps a 3G TMS transport type to our TransportType enum
// This uses intelligent matching based on transport type name, code, and classification
// Returns empty string if no match is found (will be stored as empty in DB)
func (t *ThreeG) mapEquipmentToTransportType(eq AvailableEquipmentResponse) models.TransportType {
	equipmentName := strings.ToLower(strings.TrimSpace(eq.EquipmentName))
	equipmentCode := strings.ToLower(strings.TrimSpace(eq.EquipmentCode))
	classification := strings.ToLower(strings.TrimSpace(eq.EquipmentClassification))

	// Check classification first (most reliable)
	switch classification {
	case "dryvan", "dry van":
		return models.VanTransportType
	case "reefer", "refrigerated":
		return models.ReeferTransportType
	case "flatbed", "flat bed":
		return models.FlatbedTransportType
	}

	// Check equipment code
	switch equipmentCode {
	case "van":
		return models.VanTransportType
	case "reefer", "refrigerated":
		return models.ReeferTransportType
	case "flatbed", "flat":
		return models.FlatbedTransportType
	case "sprinter":
		return models.SprinterTransportType
	}

	// Check equipment name patterns
	switch {
	case strings.Contains(equipmentName, "flatbed") || strings.Contains(equipmentName, "conestoga"):
		return models.FlatbedTransportType
	case strings.Contains(equipmentName, "cargo van"):
		return models.VanTransportType
	case strings.Contains(equipmentName, "sprinter"):
		return models.SprinterTransportType
	case strings.Contains(equipmentName, "van") && !strings.Contains(equipmentName, "reefer"):
		return models.VanTransportType
	case strings.Contains(equipmentName, "reefer") || strings.Contains(equipmentName, "refrigerated"):
		return models.ReeferTransportType
	case strings.Contains(equipmentName, "box truck") || strings.Contains(equipmentName, "box"):
		return models.BoxTruckTransportType
	case (strings.Contains(equipmentName, "hot") && strings.Contains(equipmentName, "shot")) ||
		strings.Contains(equipmentName, "hs"):
		return models.HotShotTransportType
	}

	// Return empty string if no match found (will be stored in DB for manual mapping later)
	return ""
}

// GetTransportTypes fetches available transport types from 3G TMS, using hybrid caching (Redis + Database)
func (t *ThreeG) GetTransportTypes(ctx context.Context) ([]models.TMSEquipmentType, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetTransportTypesThreeG", otel.IntegrationAttrs(t.integration))
	defer func() { metaSpan.End(err) }()

	// Get mappings from database (with Redis cache)
	mappings, err := tmsTransportTypeMappingDB.GetTransportTypeMappings(ctx, t.integration.ID)
	if err != nil {
		return nil, fmt.Errorf("error getting transport types: %w", err)
	}

	// Convert to TMSEquipmentType format
	transportTypes := make([]models.TMSEquipmentType, 0, len(mappings))
	for _, mapping := range mappings {
		transportTypes = append(transportTypes, models.TMSEquipmentType{
			ID:   fmt.Sprintf("%d", mapping.EquipmentID),
			Name: mapping.EquipmentName,
		})
	}

	log.Info(ctx, "fetched transport types from 3G TMS", zap.Int("count", len(transportTypes)))
	return transportTypes, nil
}

// AvailableServiceOptionResponse represents a single service option item from 3G TMS
type AvailableServiceOptionResponse struct {
	ServiceOptionID              int    `json:"serviceOptionId"`
	ServiceOptionName            string `json:"serviceOptionName"`
	Description                  string `json:"description"`
	ServiceOptionTransportModeID *int   `json:"serviceOptionTransportModeId"`
	TransportModeID              *int   `json:"transportModeId"`
	TransportModeNamesString     string `json:"transportModeNamesString"`
}

// SyncServiceOptions fetches all service options from 3G TMS and stores them in the database
// Returns the list of service options fetched from 3G TMS
// Uses pagination with 500 records per page
func (t *ThreeG) SyncServiceOptions(ctx context.Context) ([]models.TMSEquipmentType, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "SyncServiceOptionsThreeG", otel.IntegrationAttrs(t.integration))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "starting 3G TMS service options sync", zap.Uint("integration_id", t.integration.ID))

	var allServiceOptions []AvailableServiceOptionResponse
	pageNum := 0

	for {
		log.Info(
			ctx,
			"3G TMS fetching service options page",
			zap.Int("page_num", pageNum),
			zap.Int("page_size", serviceOptionPageSize),
		)

		serviceOptions, fetchErr := t.getServiceOptionListPaginated(ctx, pageNum, serviceOptionPageSize)
		if fetchErr != nil {
			log.Error(
				ctx,
				"3G TMS service options page fetch failed",
				zap.Int("page_num", pageNum),
				zap.Error(fetchErr),
			)
			err = fetchErr
			break
		}

		log.Info(
			ctx,
			"3G TMS service options page result",
			zap.Int("page_num", pageNum),
			zap.Int("service_options_count", len(serviceOptions)),
			zap.Int("total_service_options_so_far", len(allServiceOptions)),
		)

		// If we get 0 records, we've reached the end
		if len(serviceOptions) == 0 {
			if pageNum == 0 {
				log.Warn(ctx, "failed to fetch 3G TMS service options - empty response on first page")
			} else {
				log.Info(
					ctx,
					"3G TMS reached end - no more service options data",
					zap.Int("final_page", pageNum),
					zap.Int("total_service_options", len(allServiceOptions)),
				)
			}
			break
		}

		allServiceOptions = append(allServiceOptions, serviceOptions...)

		// If we got fewer records than page size, we've reached the end
		if len(serviceOptions) < serviceOptionPageSize {
			log.Info(
				ctx,
				"3G TMS reached end - partial page",
				zap.Int("final_page", pageNum),
				zap.Int("total_service_options", len(allServiceOptions)),
			)
			break
		}

		pageNum++
	}

	if err != nil {
		return nil, fmt.Errorf("error fetching service options: %w", err)
	}

	if len(allServiceOptions) == 0 {
		log.Warn(ctx, "no service options fetched from 3G TMS")
		return []models.TMSEquipmentType{}, nil
	}

	// Convert to database models and map to LoadMode
	mappings := make([]models.TMSServiceOptionMapping, 0, len(allServiceOptions))
	for _, so := range allServiceOptions {
		mapping := models.TMSServiceOptionMapping{
			TMSIntegrationID:             t.integration.ID,
			ServiceOptionID:              so.ServiceOptionID,
			ServiceOptionName:            so.ServiceOptionName,
			Description:                  so.Description,
			ServiceOptionTransportModeID: so.ServiceOptionTransportModeID,
			TransportModeID:              so.TransportModeID,
			TransportModeNamesString:     so.TransportModeNamesString,
			MappedLoadMode:               t.mapServiceOptionToLoadMode(so),
		}

		mappings = append(mappings, mapping)
	}

	// Save to database
	if err = tmsServiceOptionMappingDB.RefreshServiceOptionMappings(ctx, t.integration.ID, mappings); err != nil {
		return nil, fmt.Errorf("error saving service options to database: %w", err)
	}

	// Invalidate Redis cache (non-critical operation - don't fail the sync if this fails)
	if cacheErr := tmsServiceOptionMappingDB.InvalidateCache(ctx, t.integration.ID); cacheErr != nil {
		log.Warn(ctx, "failed to invalidate service options cache", zap.Error(cacheErr))
	}

	// Convert to TMSEquipmentType format for response
	serviceOptions := make([]models.TMSEquipmentType, 0, len(mappings))
	for _, mapping := range mappings {
		serviceOptions = append(serviceOptions, models.TMSEquipmentType{
			ID:   fmt.Sprintf("%d", mapping.ServiceOptionID),
			Name: mapping.ServiceOptionName,
		})
	}

	log.Info(
		ctx,
		"successfully synced service options from 3G TMS",
		zap.Int("total_service_options", len(allServiceOptions)),
		zap.Int("mappings_created", len(mappings)),
	)

	return serviceOptions, nil
}

// getServiceOptionListPaginated fetches service options from 3G TMS Web API with pagination
func (t *ThreeG) getServiceOptionListPaginated(
	ctx context.Context,
	pageNum int,
	pageSize int,
) ([]AvailableServiceOptionResponse, error) {
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	// Calculate record indices based on page number and page size
	recordStartIndex := pageNum * pageSize
	recordEndIndex := recordStartIndex + pageSize - 1

	// Build query parameters (matching the locations endpoint pattern which uses GET)
	queryParams := url.Values{}
	queryParams.Set("filterscount", "0")
	queryParams.Set("groupscount", "0")
	queryParams.Set("pagenum", strconv.Itoa(pageNum))
	queryParams.Set("pagesize", strconv.Itoa(pageSize))
	queryParams.Set("recordstartindex", strconv.Itoa(recordStartIndex))
	queryParams.Set("recordendindex", strconv.Itoa(recordEndIndex))
	queryParams.Set("quickSearch", "")
	queryParams.Set("savedQueryId", "-1")

	// Construct the full URL with query parameters (like locations endpoint)
	endpoint := fmt.Sprintf("%s%s?%s", t.webBaseURL, webServiceOptionListPath, queryParams.Encode())

	// Use GET method (like locations endpoint)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating service option list request: %w", err)
	}

	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/clearServiceOption", t.webBaseURL))
	req.Header.Set("Origin", t.webBaseURL)

	// Log request details for debugging
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		log.Warn(ctx, "error parsing endpoint URL", zap.Error(err), zap.String("endpoint", endpoint))
		parsedURL = nil
	}
	var cookies []*http.Cookie
	if parsedURL != nil {
		cookies = t.client.Jar.Cookies(parsedURL)
	}
	log.Info(
		ctx,
		"3G TMS service options API request",
		zap.String("endpoint", endpoint),
		zap.Int("page_num", pageNum),
		zap.Int("page_size", pageSize),
		zap.String("record_start", strconv.Itoa(recordStartIndex)),
		zap.String("record_end", strconv.Itoa(recordEndIndex)),
		zap.Int("cookie_count", len(cookies)),
		zap.String("full_url", endpoint),
	)

	resp, err := t.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, t.integration, err)
		log.Error(
			ctx,
			"3G TMS service options API request failed",
			zap.Int("page_num", pageNum),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error making service option list request: %w", err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, t.integration, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			log.Error(ctx, "error reading error response body", zap.Error(readErr))
		}
		return nil, fmt.Errorf("error response from 3G TMS: %s - body: %s", resp.Status, string(body))
	}

	var serviceOptions []AvailableServiceOptionResponse
	if err := json.NewDecoder(resp.Body).Decode(&serviceOptions); err != nil {
		return nil, fmt.Errorf("error decoding service options response: %w", err)
	}

	return serviceOptions, nil
}

// mapServiceOptionToLoadMode maps a 3G TMS service option to our LoadMode enum
// This uses intelligent matching based on service option name and description
// Returns empty string if no match is found (will be stored as empty in DB)
func (t *ThreeG) mapServiceOptionToLoadMode(so AvailableServiceOptionResponse) models.LoadMode {
	serviceOptionName := strings.ToLower(strings.TrimSpace(so.ServiceOptionName))
	description := strings.ToLower(strings.TrimSpace(so.Description))

	// Check service option name first
	switch {
	case strings.Contains(serviceOptionName, "ltl") || strings.Contains(serviceOptionName, "less than"):
		return models.LTLMode
	case strings.Contains(serviceOptionName, "ftl") ||
		strings.Contains(serviceOptionName, "truckload") ||
		strings.Contains(serviceOptionName, "full") ||
		// Check for "tl" as a standalone word (not part of "ltl" or other words like "settlement")
		(strings.Contains(serviceOptionName, " tl ") ||
			strings.HasPrefix(serviceOptionName, "tl ") ||
			strings.HasSuffix(serviceOptionName, " tl") ||
			serviceOptionName == "tl"):
		return models.TLMode
	case strings.Contains(serviceOptionName, "refrigerated") || strings.Contains(serviceOptionName, "reefer"):
		return models.RefrigeratedMode
	case strings.Contains(serviceOptionName, "flatbed"):
		return models.FlatbedMode
	case strings.Contains(serviceOptionName, "drayage"):
		return models.DrayageMode
	// Check for "rail" as a standalone word (not part of "trailer")
	case strings.Contains(serviceOptionName, " rail ") ||
		strings.HasPrefix(serviceOptionName, "rail ") ||
		strings.HasSuffix(serviceOptionName, " rail") ||
		serviceOptionName == "rail" ||
		strings.Contains(serviceOptionName, "rail transport") ||
		strings.Contains(serviceOptionName, "rail service"):
		return models.RailMode
	case strings.Contains(serviceOptionName, "air"):
		return models.AirMode
	case strings.Contains(serviceOptionName, "ocean"):
		return models.OceanMode
	case strings.Contains(serviceOptionName, "parcel"):
		return models.ParcelMode
	case strings.Contains(serviceOptionName, "intermodal"):
		return models.IntermodalMode
	case strings.Contains(serviceOptionName, "box truck"):
		return models.BoxTruckMode
	case strings.Contains(serviceOptionName, "dry van"):
		return models.DryVanMode
	case strings.Contains(serviceOptionName, "hotshot") || strings.Contains(serviceOptionName, "hot shot"):
		return models.FlatbedHotshotMode
	case strings.Contains(serviceOptionName, "power only"):
		return models.PowerOnlyMode
	case strings.Contains(serviceOptionName, "over dimensional") || strings.Contains(serviceOptionName, "overdim"):
		return models.OverDimensionalMode
	}

	// Check description as fallback
	switch {
	case strings.Contains(description, "ltl") || strings.Contains(description, "less than"):
		return models.LTLMode
	case strings.Contains(description, "ftl") ||
		strings.Contains(description, "truckload") ||
		strings.Contains(description, "full") ||
		// Check for "tl" as a standalone word (not part of "ltl" or other words like "settlement")
		(strings.Contains(description, " tl ") ||
			strings.HasPrefix(description, "tl ") ||
			strings.HasSuffix(description, " tl") ||
			description == "tl"):
		return models.TLMode
	case strings.Contains(description, "refrigerated") || strings.Contains(description, "reefer"):
		return models.RefrigeratedMode
	case strings.Contains(description, "flatbed"):
		return models.FlatbedMode
	}

	// Return empty string if no match found (will be stored in DB for manual mapping later)
	return ""
}

// GetServiceOptions fetches available service options from 3G TMS, using hybrid caching (Redis + Database)
func (t *ThreeG) GetServiceOptions(ctx context.Context) ([]models.TMSEquipmentType, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetServiceOptionsThreeG", otel.IntegrationAttrs(t.integration))
	defer func() { metaSpan.End(err) }()

	// Get mappings from database (with Redis cache)
	mappings, err := tmsServiceOptionMappingDB.GetServiceOptionMappings(ctx, t.integration.ID)
	if err != nil {
		return nil, fmt.Errorf("error getting service options: %w", err)
	}

	// Convert to TMSEquipmentType format
	serviceOptions := make([]models.TMSEquipmentType, 0, len(mappings))
	for _, mapping := range mappings {
		serviceOptions = append(serviceOptions, models.TMSEquipmentType{
			ID:   fmt.Sprintf("%d", mapping.ServiceOptionID),
			Name: mapping.ServiceOptionName,
		})
	}

	log.Info(ctx, "fetched service options from 3G TMS", zap.Int("count", len(serviceOptions)))
	return serviceOptions, nil
}
