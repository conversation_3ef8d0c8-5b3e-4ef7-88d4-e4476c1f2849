package threeg

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/tmsrefresh"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/redis"
)

// GetLocations retrieves locations from 3G TMS
func (t *ThreeG) GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsThreeG", otel.IntegrationAttrs(t.integration))
	var err error
	defer func() { metaSpan.End(err) }()

	// Apply options
	options := &models.TMSOptions{}
	options.Apply(opts...)

	var locations []models.TMSLocation
	pageNum := 0

	// Determine job type based on whether this is a poller job
	jobType := redis.LocationJob
	if options.IsPollerJob {
		jobType = redis.LocationRefreshJob
	}

	var cursor string
	// Priority for cursor: explicit option > Redis state > default (empty)
	if options.Cursor != "" {
		cursor = options.Cursor
		if resumePage, err := strconv.Atoi(cursor); err == nil {
			pageNum = resumePage
			log.Info(ctx, "using explicit page for 3G TMS location fetch", zap.Int("page", pageNum))
		}
	} else {
		// Check Redis for saved cursor from previous run
		_, savedCursor, err := redis.GetIntegrationState(ctx, t.integration.ID, jobType)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
		}
		if savedCursor != "" {
			cursor = savedCursor
			if resumePage, err := strconv.Atoi(cursor); err == nil {
				pageNum = resumePage
				log.Info(ctx, "resuming 3G TMS location fetch from page", zap.Int("page", pageNum))
			}
		}
	}

	for {
		// Save the state in the options
		if options.OnProgress != nil {
			options.OnProgress(
				"",
				strconv.Itoa(pageNum),
			)
		}

		log.Info(
			ctx,
			"3G TMS fetching location page",
			zap.Int("page_num", pageNum),
			zap.Int("page_size", pageSize),
		)

		webLocations, fetchErr := t.getLocationListPaginated(ctx, pageNum, pageSize)
		if fetchErr != nil {
			log.Error(
				ctx,
				"3G TMS location page fetch failed",
				zap.Int("page_num", pageNum),
				zap.Error(fetchErr),
			)

			tmsrefresh.SetIntegrationStateWithWarning(ctx, t.integration.ID, jobType, "", strconv.Itoa(pageNum))
			err = fetchErr
			break
		}

		log.Info(
			ctx,
			"3G TMS location page result",
			zap.Int("page_num", pageNum),
			zap.Int("locations_count", len(webLocations)),
			zap.Int("total_locations_so_far", len(locations)),
		)

		// If we get 0 records, we've reached the end
		if len(webLocations) == 0 {
			if pageNum == 0 {
				log.Warn(ctx, "Failed to fetch 3G TMS locations - empty response on first page")
			} else {
				log.Info(
					ctx,
					"3G TMS reached end - no more location data",
					zap.Int("final_page", pageNum),
					zap.Int("total_locations", len(locations)),
				)
			}

			// Job completed successfully - delete the redis state
			redisErr := redis.ClearIntegrationState(ctx, t.integration.ID, jobType)
			if redisErr != nil && !errors.Is(redisErr, redis.NilEntry) {
				log.Warn(ctx, "failed to clear integration state", zap.Error(redisErr))
			}
			break
		}

		// Map web locations to TMSLocation
		locationsToRefresh := make([]models.TMSLocation, 0, len(webLocations))

		for _, webLoc := range webLocations {
			location, parseErr := ToLocationModel(t.integration.ID, webLoc)
			if parseErr != nil {
				log.Warn(
					ctx,
					"failed to parse location coordinates, using zero values",
					zap.String("location_name", webLoc.Name),
					zap.String("location_num", webLoc.LocNum),
					zap.String("latitude", webLoc.Latitude),
					zap.String("longitude", webLoc.Longitude),
					zap.Error(parseErr),
				)
			}
			locationsToRefresh = append(locationsToRefresh, location)
		}

		// Store locations in database
		if err = tmsLocationDB.RefreshTMSLocations(ctx, &locationsToRefresh); err != nil {
			tmsrefresh.SetIntegrationStateWithWarning(ctx, t.integration.ID, jobType, "", strconv.Itoa(pageNum))
			break
		}

		// Add to return slice
		locations = append(locations, locationsToRefresh...)

		// Save current progress and continue to next page (mark as in_progress)
		tmsrefresh.SetIntegrationStateWithWarning(ctx, t.integration.ID, jobType, "", strconv.Itoa(pageNum))

		pageNum++
	}

	return locations, err

}

// getLocationListPaginated fetches locations from 3G TMS Web API with pagination
func (t *ThreeG) getLocationListPaginated(
	ctx context.Context,
	pageNum int,
	pageSize int,
) (
	[]WebLocation,
	error,
) {
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	// Calculate record indices based on page number and page size
	recordStartIndex := pageNum * pageSize
	recordEndIndex := recordStartIndex + pageSize - 1

	// Create the "Is Active" filter
	activeFilter := WebTradingPartnerRequestFilter{
		FieldName:      "isActive",
		FieldType:      "com.mysema.query.types.path.BooleanPath",
		TranslatedName: "Is Active",
		FieldNames:     nil,
		FieldIDs:       []string{"fn1", "comparisonType1"},
		UserDateFormat: "MM/dd/yyyy HH:mm",
		Values:         []string{"blank", "selected"},
	}

	// Convert filter to JSON
	filters := []WebTradingPartnerRequestFilter{activeFilter}
	filterJSON, err := json.Marshal(filters)
	if err != nil {
		return nil, fmt.Errorf("error marshaling location filters: %w", err)
	}

	// Build query parameters for GET request (matching Postman request format)
	queryParams := url.Values{}
	queryParams.Set("filterscount", "1") // We have 1 filter (active)
	queryParams.Set("groupscount", "0")
	queryParams.Set("pagenum", strconv.Itoa(pageNum))
	queryParams.Set("pagesize", strconv.Itoa(pageSize))
	queryParams.Set("recordstartindex", strconv.Itoa(recordStartIndex))
	queryParams.Set("recordendindex", strconv.Itoa(recordEndIndex))
	queryParams.Set("query", string(filterJSON))
	queryParams.Set("quicksearch", "")
	queryParams.Set("savedQueryId", "-1")

	// Construct the full URL with query parameters
	endpoint := fmt.Sprintf("%s%s?%s", t.webBaseURL, webLocationListPath, queryParams.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating location list request: %w", err)
	}

	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/clearLocations", t.webBaseURL))

	log.Debug(
		ctx,
		"3G TMS location API request",
		zap.String("endpoint", endpoint),
		zap.Int("page_num", pageNum),
		zap.Int("page_size", pageSize),
		zap.String("record_start", strconv.Itoa(recordStartIndex)),
		zap.String("record_end", strconv.Itoa(recordEndIndex)),
	)

	resp, err := t.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, t.integration, err)
		log.Error(
			ctx,
			"3G TMS location API request failed",
			zap.Int("page_num", pageNum),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error making location list request: %w", err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, t.integration, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %w", err)
		}

		log.Error(
			ctx,
			"3G TMS location list request failed",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)),
			zap.String("request_url", endpoint),
		)

		return nil, fmt.Errorf(
			"3G TMS location list request failed with status %d: %s",
			resp.StatusCode,
			string(body),
		)
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading location list response: %w", err)
	}

	log.Debug(
		ctx,
		"3G TMS location API raw response",
		zap.Int("page_num", pageNum),
		zap.Int("response_length", len(responseBody)),
	)

	var webLocations []WebLocation
	var wrappedResponse WebLocationListResponse

	// Try to unmarshal as a LocationListResponse first (wrapped in data field)
	if err := json.Unmarshal(responseBody, &wrappedResponse); err != nil {
		// Fallback: try to unmarshal as a direct array
		if unmarshalErr := json.Unmarshal(responseBody, &webLocations); unmarshalErr != nil {
			log.Error(
				ctx,
				"3G TMS location API response parsing failed",
				zap.Int("page_num", pageNum),
				zap.Error(unmarshalErr),
			)
			return nil, fmt.Errorf("error parsing location list response: %w", unmarshalErr)
		}

		log.Info(
			ctx,
			"3G TMS location API response - direct array format",
			zap.Int("page_num", pageNum),
			zap.Int("locations_count", len(webLocations)),
		)
		return webLocations, nil
	}

	// Use wrapped response data (even if empty - this is valid for final pagination page)
	webLocations = wrappedResponse.Data
	log.Info(
		ctx,
		"3G TMS location API response - wrapped format",
		zap.Int("page_num", pageNum),
		zap.Int("locations_count", len(webLocations)),
	)

	return webLocations, nil

}

// ToLocationModel converts WebLocation to TMSLocation
func ToLocationModel(tmsID uint, webLoc WebLocation) (models.TMSLocation, error) {
	// Parse latitude and longitude from string
	var latitude, longitude float64
	var parseErrors []error

	if webLoc.Latitude != "" {
		lat, err := strconv.ParseFloat(webLoc.Latitude, 64)
		if err != nil {
			parseErrors = append(parseErrors, fmt.Errorf("failed to parse latitude %q: %w", webLoc.Latitude, err))
		} else {
			latitude = lat
		}
	}
	if webLoc.Longitude != "" {
		lon, err := strconv.ParseFloat(webLoc.Longitude, 64)
		if err != nil {
			parseErrors = append(parseErrors, fmt.Errorf("failed to parse longitude %q: %w", webLoc.Longitude, err))
		} else {
			longitude = lon
		}
	}

	var err error
	if len(parseErrors) > 0 {
		err = fmt.Errorf("coordinate parsing errors: %v", parseErrors)
	}

	// Combine address lines
	addressLine2 := webLoc.AddressLine2
	if webLoc.AddressLine3 != "" {
		if addressLine2 == "" {
			addressLine2 = webLoc.AddressLine3
		} else {
			addressLine2 = addressLine2 + ", " + webLoc.AddressLine3
		}
	}

	// Determine contact information - prefer quickContact fields, fallback to contactName
	contactName := webLoc.QuickContactName
	if contactName == "" {
		contactName = webLoc.ContactName
	}
	phone := webLoc.QuickContactPhone
	email := webLoc.QuickContactEmail

	location := models.TMSLocation{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: webLoc.LocNum,
			Name:          webLoc.Name,
			AddressLine1:  webLoc.AddressLine1,
			AddressLine2:  addressLine2,
			City:          webLoc.City,
			State:         webLoc.State,
			Zipcode:       webLoc.ZipCode,
			Country:       webLoc.Country,
			Phone:         phone,
			Email:         email,
			Contact:       contactName,
		},
		LocationType: webLoc.LocationType,
		Latitude:     latitude,
		Longitude:    longitude,
		Timezone:     webLoc.TimeZone,
	}

	// Set Point if coordinates are available
	if location.Latitude != 0 || location.Longitude != 0 {
		location.Point = models.Point{
			Latitude:  float32(location.Latitude),
			Longitude: float32(location.Longitude),
		}
	}

	// Calculate FullAddress and NameAddress
	location.FullAddress = models.ConcatAddress(location.CompanyCoreInfo)
	switch {
	case location.Name != "" && location.FullAddress != "":
		location.NameAddress = location.Name + ", " + location.FullAddress
	case location.Name != "":
		location.NameAddress = location.Name
	default:
		location.NameAddress = location.FullAddress
	}

	// Set emails array if email is present
	if location.Email != "" {
		location.Emails = []string{location.Email}
	}

	return location, err

}
