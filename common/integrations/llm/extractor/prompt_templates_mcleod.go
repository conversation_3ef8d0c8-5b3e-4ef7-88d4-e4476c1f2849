package extractor

//nolint:lll
const (
	mcleodPickupTimeInstructions = `* **readyTime**: Set to the date (e.g., "mm/dd/yyyy") of the primary pickup appointment. Do not include the time component, even if one is specified.
        * If only a date is visible (e.g., "Pickup: 09/12/2025"), set this to "09/12/2025".
        * If a date and time are visible (e.g., "Pickup: 09/12/2025, 9:00AM"), set this to "09/12/2025".
        * **ERD (Estimated Ready Date)**: When you see "ERD 09/05/2025" or "Loading Time & ERD: 1430 / ERD 09/05/2025", extract the ERD date as the readyTime.

    * **apptType**: "By appointment", "FCFS", or appointment type. Infer "By appointment" if a specific time or window is provided.

    * **apptStartTime**: Set to the date AND start time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the pickup appointment.
        * If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If a date and a single time are provided, set this field to that date and time (e.g., "09/12/2025, 09:00AM").
        * If a time window is provided, use the start of that window (e.g., "09/12/2025, 09:00AM").
        * **Military Time Conversion**: If time is in 24-hour format (e.g., "1430", "0800"), convert to 12-hour format:
          * "1430" → "2:30PM"
          * "0800" → "8:00AM"
          * "APPT 10/27 @ 8:00 am" → "10/27/2025, 08:00AM"
        * **Loading Time Format**: 
          * "Loading Time: 1430" + "Loading Date: 9/10/2025" → "09/10/2025, 02:30PM"
          * "Loading Time & ERD: 1430 / ERD 09/05/2025" → apptStartTime: "09/10/2025, 02:30PM" (use Loading Date for apptStartTime, convert 1430 to 2:30PM)
          * When you see "1430" or "0800" (4-digit numbers without colons), these are ALWAYS military time and must be converted

    * **apptEndTime**: Set to the date AND end time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the pickup appointment window.
	 	* If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If no end time is provided (e.g., only a date or a single start time), set this field to be **identical** to *apptStartTime*.
        * If a time window is provided, use the end of that window (e.g., "09/12/2025, 11:00AM").
        * **Military Time Conversion**: Apply same conversion rules as apptStartTime.`

	mcleodPickupInstructions = `Here are some additional instructions for this pickup section. 
		- Pickup dates can be labeled as "Ship Date", "Ready Time", "Pickup Date", "Pickup Time", "Pickup Window", "Pickup Window Start", "Pickup Window End", "Loading Date", "Loading Time", "Loading Time & ERD", "Pick Up Appt", etc. So make sure to 
		look for any pickup related keywords to grab at least the shipping date, since documents usually include at least the date. It is usually rare to not have a shipment pickup date..
		- *readyTime*, *apptStartTime*, and *apptEndTime* fields can be either in MM/DD/YYYY format or in MM/DD/YYYY, hh:MMam/pm format.
		- **Military Time Conversion**: If you encounter military/24-hour time formats (e.g., "1430", "0800"), convert them to 12-hour format with AM/PM:
		  * "1430" → "2:30PM"
		  * "0800" → "8:00AM"
		  * "1200" → "12:00PM"
		  * "0000" → "12:00AM"
		- **ERD (Estimated Ready Date)**: When you see "ERD 09/05/2025" or "Loading Time & ERD: 1430 / ERD 09/05/2025", extract the ERD date as the readyTime.
		  * Example: "Loading Time & ERD: 1430 / ERD 09/05/2025" → readyTime: "09/05/2025" (the ERD date, not the Loading Date)
		  * The ERD date is the Estimated Ready Date, which should be used for readyTime field
		- **Facility Name Parsing**: Handle facility names with embedded locations:
		  * "Americold Logistics - Lula, GA (Gold Creek)" → name: "Americold Logistics", city: "Lula", state: "GA"
		  * "LINEAGE ALBANY" → name: "LINEAGE ALBANY"
		- **Reference Number Patterns**: Look for various reference number formats:
		  * "TR" followed by numbers (e.g., "TR246191")
		  * "Coldstore Ref:", "Warehouse Code:", "Booking #:" that appear near pickup information
		  * Customer-specific reference formats (e.g., "[Customer] Ref:", "Customer Ref:")`

	mcleodPickupExamples = `**Examples:**

### Example 1: Standard Pickup with Appointment
**Input:**
A shipping document contains:
SHIP FROM:
Mohawk Industries
1405 HWY 41 S
CALHOUN, GA 30701
Contact: Sarah Johnson
Phone: ************
Email: <EMAIL>
Ready Time: 08/26/2025, 8:00AM
Appointment Required: 9:00AM-10:00AM EDT
Special Instructions: Driver must check in at front desk

**Output:**
{
    "pickup": {
        "name": "Mohawk Industries",
        "addressLine1": "1405 HWY 41 S",
        "addressLine2": "",
        "city": "CALHOUN",
        "state": "GA",
        "zipCode": "30701",
        "country": "US",
        "contact": "Sarah Johnson",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/26/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/26/2025, 09:00AM",
        "apptEndTime": "08/26/2025, 10:00AM",
        "apptNote": "Driver must check in at front desk",
        "timezone": "EDT",
        "refNumberCandidates": []
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 1 (pickup)
Mobil, 1001 Billingsport Rd., Paulsboro, NJ 08066
Leah Scalise Phone: (*************
Pickup: 09/12/2025 07:00AM - 09/12/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)  
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "pickup": {
        "name": "Mobil",
        "addressLine1": "1001 Billingsport Rd.",
        "addressLine2": "",
        "city": "Paulsboro",
        "state": "NJ",
        "zipCode": "08066",
        "country": "US",
        "contact": "Leah Scalise",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "readyTime": "09/12/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/12/2025, 07:00AM",
        "apptEndTime": "09/12/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": [
            "2608089680",
            "4700829",
            "LD385193"
        ]
    }
}

### Example 3: Multiple Locations / Single Time
**Input:**
SHIP FROM:
Origin Warehouse LLC
500 Commerce Dr
Atlanta, GA 30309
Ready: 08/28/2025, 2:00PM

SHIP TO:
Destination Corp
789 Delivery St
Miami, FL 33101
Deliver by: 08/30/2025

**Output:**
{
    "pickup": {
        "name": "Origin Warehouse LLC",
        "addressLine1": "500 Commerce Dr",
        "addressLine2": "",
        "city": "Atlanta",
        "state": "GA",
        "zipCode": "30309",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/28/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/28/2025, 02:00PM",
        "apptEndTime": "08/28/2025, 02:00PM",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": []
    }
}

### Example 4: Ship Date Only
**Input:**
Origin:
Global Foods Inc
4500 Industrial Pkwy
Chicago, IL 60639
Ship Date: 09/30/2025
Pickup Ref: P-456123

**Output:**
{
    "pickup": {
        "name": "Global Foods Inc",
        "addressLine1": "4500 Industrial Pkwy",
        "addressLine2": "",
        "city": "Chicago",
        "state": "IL",
        "zipCode": "60639",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "P-456123",
        "readyTime": "09/30/2025",
        "apptType": "",
        "apptStartTime": "09/30/2025",
        "apptEndTime": "09/30/2025",
        "apptNote": "",
        "timezone": "",
        "refNumberCandidates": ["P-456123"]
    }
}

### Example 5: Appointment Email with Military Time & TR Numbers
**Input:**
"I have the following appointment at LINEAGE ALBANY on OCTOBER 27

TR246191 – APPT 10/27 @ 8:00 am

Please advise if you can cover."

**Output:**
{
    "pickup": {
        "name": "LINEAGE ALBANY",
        "addressLine1": "",
        "city": "",
        "state": "",
        "refNumber": "TR246191",
        "apptType": "By appointment",
        "apptStartTime": "10/27/2025, 08:00AM",
        "apptNote": ""
    }
}

### Example 6: Loading Instructions with Military Time & ERD
**Input:**
"Loading Facility:
Americold Logistics - Lula, GA (Gold Creek)
3801 Conrelia Hwy
Lula, GA *************-7100
Coldstore Ref: GC-12345

Loading Date: 9/10/2025
Loading Time & ERD: 1430 / ERD 09/05/2025
Booking #: 258026953
Customer Ref: 126404-01"

**Output:**
{
    "pickup": {
        "name": "Americold Logistics",
        "addressLine1": "3801 Conrelia Hwy",
        "city": "Lula",
        "state": "GA",
        "zipCode": "30554",
        "country": "US",
        "phone": "************",
        "refNumber": "126404-01",
        "readyTime": "09/05/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/10/2025, 02:30PM",
        "refNumberCandidates": ["258026953", "GC-12345"]
    }
}`

	mcleodConsigneeTimeInstructions = `* **mustDeliver**: Set to the date (e.g., "mm/dd/yyyy") of the primary delivery appointment. Do not include the time component, even if one is specified.
        * If only a date is visible (e.g., "Delivery: 09/12/2025"), set this to "09/12/2025".
        * If a date and time are visible (e.g., "Appt: 09/12/2025, 9:00AM"), set this to "09/12/2025".

    * **apptType**: "By appointment", "FCFS", or appointment type. Infer "By appointment" if a specific time or window is provided.

    * **apptStartTime**: Set to the date AND start time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the delivery appointment.
        * If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If a date and a single time are provided, set this field to that date and time (e.g., "09/12/2025, 09:00AM").
        * If a time window is provided, use the start of that window (e.g., "09/12/2025, 09:00AM").

    * **apptEndTime**: Set to the date AND end time (e.g., "mm/dd/yyyy, hh:MMam/pm") of the delivery appointment window.
	 	* If only a date is provided (no time), set this field to just the date (e.g., "09/12/2025").
        * If no end time is provided (e.g., only a date or a single start time), set this field to be **identical** to *apptStartTime*.
        * If a time window is provided, use the end of that window (e.g., "09/12/2025, 11:00AM").`

	mcleodConsigneeInstructions = `Here are some additional instructions for this consignee section. 
		- Delivery dates can be labeled as "Arrival Date", "Delivery Date", "Delivery Time", "Delivery Window", "Delivery Window Start", "Delivery Window End", "Delivery destination:", "Final destination:", "Deliver To Location:", "ETS" (Estimated Time of Sailing), "Cut Off Date:", etc. So make sure to 
		look for any delivery related keywords to grab at least the shipping date, since documents usually include at least the date. It is usually rare to not have a shipment delivery date..
		- *mustDeliver*, *apptStartTime*, and *apptEndTime* fields can be either in MM/DD/YYYY format or in MM/DD/YYYY, hh:MMam/pm format.
		- **International Address Handling**: Many shipments go to international destinations (COLOMBIA, MEXICO, CANADA, etc.):
		  * For international addresses, extract full address including region/province information
		  * Example: "PARQUE INDUSTRIAL TIBITOC, BODEGA 44C, TOCANCIPA CUNDINAMARCA, COLOMBIA"
		    - addressLine1: "PARQUE INDUSTRIAL TIBITOC"
		    - addressLine2: "BODEGA 44C" (look for "BODEGA", "WAREHOUSE", "UNIT" designations)
		    - city: "TOCANCIPA"
		    - state: "CUNDINAMARCA" (use full province/region name for non-US addresses)
		    - country: "CO" (convert full country names to codes: "COLOMBIA" → "CO", "MEXICO" → "MX")
		    - zipCode: "" (may be empty for international addresses)
		  * For US addresses, use standard 2-letter state codes (e.g., "GA", "TX")
		  * For international, use full province/region name in the state field
		- **International Reference Numbers**: Look for "NIT:" (Colombian tax ID) as reference numbers for international destinations
		- **Shipping Information**: Include shipping line, vessel, and certification requirements in apptNote:
		  * "Shipping Line: MAERSK"
		  * "Name of Vessel: POLAR BRASIL 537S"
		  * "USDA Certification Required for: COLOMBIA"`

	mcleodConsigneeExamples = `**Examples:**

	### Example 1: Standard Delivery with Appointment
**Input:**
A shipping document contains:
SHIP TO:
Acme Distribution Center
123 Main St, Suite 400
Springfield, IL 62704
Contact: John Doe
Phone: ************
Email: <EMAIL>
Business Hours: Mon-Fri, 8:00 AM - 5:00 PM
Delivery Appointment: 08/30/2025, 1:00PM-3:00PM CST
Special Instructions: Call 30 minutes before arrival
Delivery PO: DEL-987654
Main Shipment BOL: BOL-555123

**Output:**
{
    "consignee": {
        "name": "Acme Distribution Center",
        "addressLine1": "123 Main St",
        "addressLine2": "Suite 400",
        "city": "Springfield",
        "state": "IL",
        "zipCode": "62704",
        "country": "US",
        "contact": "John Doe",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "Mon-Fri, 8:00 AM - 5:00 PM",
        "refNumber": "DEL-987654",
        "mustDeliver": "08/30/2025",
        "apptType": "By appointment",
        "apptStartTime": "08/30/2025, 01:00PM",
        "apptEndTime": "08/30/2025, 03:00PM",
        "apptNote": "Call 30 minutes before arrival",
        "timezone": "CST",
        "externalTMSID": null,
        "refNumberCandidates": [
            "DEL-987654"
        ]
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 2 (drop)
Moove, 8120 S. Orange Avenue, Orlando, FL 32809
Mike Dvorak Phone: ************
Delivery: 09/15/2025 07:00AM - 09/15/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "consignee": {
        "name": "Moove",
        "addressLine1": "8120 S. Orange Avenue",
        "addressLine2": "",
        "city": "Orlando",
        "state": "FL",
        "zipCode": "32809",
        "country": "US",
        "contact": "Mike Dvorak",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "mustDeliver": "09/15/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/15/2025, 07:00AM",
        "apptEndTime": "09/15/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
        "externalTMSID": null,
        "refNumberCandidates": [
            "2608089680",
            "4700829",
            "LD385193"
        ]
    }
}

### Example 3: Multiple Stops - Extract Final Destination
**Input:**
Stop 1 - Intermediate Stop:
Gamma Logistics Hub
789 Transfer Ave
Denver, CO 80202

Final Destination:
Delta Manufacturing
321 Factory Road
Salt Lake City, UT 84101
Contact: Robert Kim
Phone: ************
Delivery Window: 09/02/2025, 10:00AM-12:00PM MST

**Output:**
{
    "consignee": {
        "name": "Delta Manufacturing",
        "addressLine1": "321 Factory Road",
        "addressLine2": "",
        "city": "Salt Lake City",
        "state": "UT",
        "zipCode": "84101",
        "country": "US",
        "contact": "Robert Kim",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "mustDeliver": "09/02/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/02/2025, 10:00AM",
        "apptEndTime": "09/02/2025, 12:00PM",
        "apptNote": "",
        "timezone": "MST",
        "externalTMSID": null,
        "refNumberCandidates": []
    }
}

### Example 4: Arrival Date Only
**Input:**
DELIVER TO:
Target RDC
456 Distribution Way
Phoenix, AZ 85001
Contact: Maria Garcia
Phone: ************
Arrival Date: 09/20/2025
Ref: PO-78910

**Output:**
{
    "consignee": {
        "name": "Target RDC",
        "addressLine1": "456 Distribution Way",
        "addressLine2": "",
        "city": "Phoenix",
        "state": "AZ",
        "zipCode": "85001",
        "country": "US",
        "contact": "Maria Garcia",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "PO-78910",
        "mustDeliver": "09/20/2025",
        "apptType": "By appointment",
        "apptStartTime": "09/20/2025",
        "apptEndTime": "09/20/2025",
        "apptNote": "",
        "timezone": "",
        "externalTMSID": null,
        "refNumberCandidates": [
            "PO-78910"
        ]
    }
}

### Example 5: International Destination (COLOMBIA)
**Input:**
"Consignee: 
Calypso Del Caribe
PARQUE INDUSTRIAL TIBITOC
BODEGA 44C
TOCANCIPA CUNDINAMARCA
COLOMBIA
NIT: 800055116

Shipping Line: MAERSK
Name of Vessel: POLAR BRASIL 537S
ETS: 9/13/2025
Cut Off Date: 9/11/2025
USDA Certification Required for: COLOMBIA"

**Output:**
{
    "consignee": {
        "name": "Calypso Del Caribe",
        "addressLine1": "PARQUE INDUSTRIAL TIBITOC",
        "addressLine2": "BODEGA 44C",
        "city": "TOCANCIPA",
        "state": "CUNDINAMARCA",
        "zipCode": "",
        "country": "CO",
        "refNumber": "800055116",
        "mustDeliver": "09/11/2025",
        "apptNote": "Shipping Line: MAERSK, Vessel: POLAR BRASIL 537S, ETS: 9/13/2025, USDA Certification Required for: COLOMBIA",
        "refNumberCandidates": []
    }
}`

	mcleodSpecificationsTransportTypeInstructions = `* **transportType**: Determine the correct transport type.
    * **REFRIGERATED INDICATORS**: Look for:
      - "refrigerated container", "reefer", "temp controlled", "frozen"
      - "40'' refrigerated container", "53'' reefer"
      - Temperature requirements (e.g., "-18C", "Maintain temp", "All cartons must be at -18C")
      - "40'' refrigerated container" → REEFER
    * If temperature requirements are mentioned → **REEFER**
    * Standard types: VAN, REEFER, FLATBED, STEP DECK, etc.
    * Default to VAN if not specified`

	mcleodSpecificationsTransportSizeInstructions = `* **transportSize**: Extract the vehicle size
    * Common patterns:
      - "40'' refrigerated container" → "40 ft"
      - "53'' trailer" → "53 ft"
      - If not specified but REEFER → default to "53 ft"
      - If not specified but VAN → default to "53 ft"`

	mcleodSpecificationsTotalPiecesInstructions = `* **totalPieces**: Extract the total quantity/number of items - ENHANCED
    * Common patterns:
      - "Case Count: 1,160" → totalPieces.val = 1160, unit = "Cases"
      - "Quantity: 58,000 lbs" → this is weight, not pieces
      - "585 PC" → 585 pieces
      - "24 units" → 24 units
    * Look for: "Case Count", "Total Cases", "Piece Count", "Qty", "Quantity"
    * ALWAYS use proper casing for unit type: "Cases" not "cases", "Boxes" not "boxes"`

	mcleodSpecificationsTempInstructions = `* **Temperatures - CRITICAL FOR REEFER LOADS**:
    * If temperatures are specified in Celsius, convert to Fahrenheit using the formula: (C × 9/5) + 32
    * Common patterns:
      - Single temperature: "Maintain -10C" → -10C = 14F → set both minTempFahrenheit and maxTempFahrenheit to 14
      - Temperature range: "Maintain temp between -20C and -10C" → min = -4F, max = 14F
      - Maximum temperature: "All cartons must be at -18C or lower" → -18C ≈ 0F → set both min and max to 0
    * If single temperature is given, set both min and max to that value
    * If temperature range is given, extract min and max separately
    * For REEFER loads without explicit temp, check for frozen goods → default to 0F`

	mcleodSpecificationsPlanningCommentInstructions = `* **planningComment**: Special instructions - ENHANCED
    * Extract any special instructions that affect how the load should be handled:
      - Equipment requirements (e.g., container types, seal requirements)
      - Loading instructions (e.g., floor loading, pre-cooling requirements)
      - Special handling requirements
      - Certification requirements
      - Time constraints or deadlines
      - Blocking, bracing, and securement requirements
      - Genset requirements for temperature maintenance
      - Permit requirements for overweight loads
      - Any other operational instructions mentioned in the email`
)
