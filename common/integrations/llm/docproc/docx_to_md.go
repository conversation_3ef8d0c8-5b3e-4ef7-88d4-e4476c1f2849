package docproc

import (
	"context"
	"errors"
	"fmt"
	"io"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/processor"
	"github.com/drumkitai/drumkit/common/log"
)

// DocxToMD converts a Word document (.docx) to markdown.
// It extracts text, paragraphs, tables, and formatting directly from the document.
// Returns the markdown content.
func DocxToMD(
	ctx context.Context,
	fileReader io.Reader,
	options ...Option,
) (string, error) {

	opts := &Options{
		FileName:               "NOT_PROVIDED", // Default but will be overwritten by WithFileName option
		PreserveDocxFormatting: true,
		UseGFMTables:           true,
		DocxMaxFileSizeMB:      10, // 10MB default to prevent OOM in Lambda
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	spanAttrs := otel.DocProcAttrs(opts.FileName, "docx", 1)
	if opts.Attachment != nil {
		spanAttrs = append(spanAttrs, otel.AttachmentAttrs(opts.Attachment)...)
	}

	var err error
	ctx, documentSpan := otel.StartSpan(ctx, "DocxToMD", spanAttrs)
	defer func() { documentSpan.End(err) }()

	result, err := processDocxFile(ctx, fileReader, opts)
	if err != nil {
		return "", err
	}

	log.Debug(
		ctx,
		"Markdown dump from docx conversion",
		zap.String("markdown", result.Markdown),
	)

	return result.Markdown, nil
}

// DocxToMDWithOutput converts a Word document to markdown and returns timing and content info.
func DocxToMDWithOutput(
	ctx context.Context,
	fileReader io.Reader,
	options ...Option,
) (*DocxToMDOutput, error) {

	startTime := time.Now()

	opts := &Options{
		FileName:               "NOT_PROVIDED", // Default but will be overwritten by WithFileName option
		PreserveDocxFormatting: true,
		UseGFMTables:           true,
		DocxMaxFileSizeMB:      10, // 10MB default to prevent OOM in Lambda
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	spanAttrs := otel.DocProcAttrs(opts.FileName, "docx", 1)
	if opts.Attachment != nil {
		spanAttrs = append(spanAttrs, otel.AttachmentAttrs(opts.Attachment)...)
	}

	var err error
	ctx, documentSpan := otel.StartSpan(ctx, "DocxToMDWithOutput", spanAttrs)
	defer func() { documentSpan.End(err) }()

	log.Info(
		ctx,
		"Processing Word document",
		zap.Bool("preserveFormatting", opts.PreserveDocxFormatting),
		zap.Bool("extractImages", opts.ExtractDocxImages),
		zap.Bool("includeComments", opts.IncludeComments),
		zap.Bool("useGFMTables", opts.UseGFMTables),
	)

	result, err := processDocxFile(ctx, fileReader, opts)
	if err != nil {
		return nil, err
	}

	log.Debug(
		ctx,
		"Markdown dump from docx conversion",
		zap.String("markdown", result.Markdown),
	)

	completionTime := time.Since(startTime).Milliseconds()

	log.Info(
		ctx,
		"Word document processing complete",
		zap.Int64("completionTimeMs", completionTime),
		zap.Int("contentLength", len(result.Markdown)),
		zap.Int("wordCount", result.WordCount),
	)

	return &DocxToMDOutput{
		CompletionTime: float64(completionTime),
		FileName:       opts.FileName,
		Markdown:       result.Markdown,
		ContentLength:  len(result.Markdown),
		WordCount:      result.WordCount,
	}, nil
}

// processDocxFile opens and processes a Word document.
func processDocxFile(
	_ context.Context,
	fileReader io.Reader,
	opts *Options,
) (*processor.DocxResult, error) {

	processor.SilenceDocxLogger() // Silence go-word library's internal logging (separate from our Zap logger)

	if fileReader == nil {
		return nil, errors.New("fileReader cannot be nil")
	}
	if opts == nil {
		return nil, errors.New("options cannot be nil")
	}

	// Determine max file size
	maxFileSizeMB := processor.DefaultMaxFileSizeMB
	if opts.DocxMaxFileSizeMB > 0 {
		maxFileSizeMB = opts.DocxMaxFileSizeMB
	}

	// Open the Word document with size checking to prevent OOM
	doc, err := processor.OpenDocxFromReader(fileReader, maxFileSizeMB)
	if err != nil {
		return nil, fmt.Errorf("failed to open Word document for file %q: %w", opts.FileName, err)
	}

	// Process the document
	processOpts := processor.DocxProcessOptions{
		PreserveFormatting: opts.PreserveDocxFormatting,
		ExtractImages:      opts.ExtractDocxImages,
		IncludeComments:    opts.IncludeComments,
		UseGFMTables:       opts.UseGFMTables,
		MaxFileSizeMB:      maxFileSizeMB,
	}

	result := processor.ProcessDocx(doc, processOpts)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to process Word document %q: %w", opts.FileName, result.Error)
	}

	if result.Markdown == "" {
		return nil, fmt.Errorf("no content extracted from Word document %q", opts.FileName)
	}

	return &result, nil
}
