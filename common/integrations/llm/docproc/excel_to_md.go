package docproc

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/processor"
	"github.com/drumkitai/drumkit/common/log"
)

const (
	// DefaultMaxRowsPerSheet is the default maximum rows per sheet.
	DefaultMaxRowsPerSheet = 500
	// DefaultMaxColsPerSheet is the default maximum columns per sheet.
	DefaultMaxColsPerSheet = 50
)

// ExcelToMD converts an Excel file to markdown.
// It extracts data from each sheet and converts it to markdown tables.
// Returns concatenated markdown content from all sheets.
func ExcelToMD(
	ctx context.Context,
	fileReader io.Reader,
	options ...Option,
) (string, error) {

	opts := &Options{
		FileName:           "NOT_PROVIDED",
		MaxRowsPerSheet:    DefaultMaxRowsPerSheet,
		MaxColsPerSheet:    DefaultMaxColsPerSheet,
		ExcelMaxFileSizeMB: 15, // 15MB default to prevent OOM in Lambda
		IncludeSheetNames:  true,
		ExcelOutputFormat:  ExcelFormatMarkdown,
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	spanAttrs := otel.DocProcAttrs(opts.FileName, "excel", 1)
	if opts.Attachment != nil {
		spanAttrs = append(spanAttrs, otel.AttachmentAttrs(opts.Attachment)...)
	}

	var err error
	ctx, documentSpan := otel.StartSpan(ctx, "ExcelToMD", spanAttrs)
	defer func() { documentSpan.End(err) }()

	sheets, err := processExcelFile(ctx, fileReader, opts)
	if err != nil {
		return "", err
	}

	// Extract markdown from all sheets
	var markdownSheets []string
	for _, sheet := range sheets {
		if sheet.Content != "" {
			markdownSheets = append(markdownSheets, sheet.Content)
		}
	}

	markdown := strings.Join(markdownSheets, "\n\n")

	if markdown == "" {
		err = fmt.Errorf("no content extracted from Excel file %q", opts.FileName)
		return "", err
	}

	log.Debug(
		ctx,
		"Excel to Markdown conversion complete - dumping content",
		zap.String("fileName", opts.FileName),
		zap.String("markdownContent", markdown),
		zap.Int("contentLength", len(markdown)),
	)

	return markdown, nil
}

// ExcelToMDWithOutput converts an Excel file to markdown and returns timing and per-sheet results.
func ExcelToMDWithOutput(
	ctx context.Context,
	fileReader io.Reader,
	options ...Option,
) (*ExcelToMDOutput, error) {
	startTime := time.Now()

	opts := &Options{
		FileName:           "NOT_PROVIDED",
		MaxRowsPerSheet:    DefaultMaxRowsPerSheet,
		MaxColsPerSheet:    DefaultMaxColsPerSheet,
		ExcelMaxFileSizeMB: 15, // 15MB default to prevent OOM in Lambda
		IncludeSheetNames:  true,
		ExcelOutputFormat:  ExcelFormatMarkdown,
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	spanAttrs := otel.DocProcAttrs(opts.FileName, "excel", 1)
	if opts.Attachment != nil {
		spanAttrs = append(spanAttrs, otel.AttachmentAttrs(opts.Attachment)...)
	}

	var err error
	ctx, documentSpan := otel.StartSpan(ctx, "ExcelToMDWithOutput", spanAttrs)
	defer func() { documentSpan.End(err) }()

	log.Info(
		ctx,
		"Processing Excel file",
		zap.Int("maxRowsPerSheet", opts.MaxRowsPerSheet),
		zap.Int("maxColsPerSheet", opts.MaxColsPerSheet),
		zap.Int("outputFormat", int(opts.ExcelOutputFormat)),
		zap.Bool("includeSheetNames", opts.IncludeSheetNames),
	)

	sheets, err := processExcelFile(ctx, fileReader, opts)
	if err != nil {
		return nil, err
	}

	completionTime := time.Since(startTime).Milliseconds()

	// Extract markdown from all sheets
	var markdownSheets []string
	for _, sheet := range sheets {
		if sheet.Content != "" {
			markdownSheets = append(markdownSheets, sheet.Content)
		}
	}

	markdown := strings.Join(markdownSheets, "\n\n")

	log.Debug(
		ctx,
		"Excel to Markdown conversion complete - dumping content",
		zap.String("fileName", opts.FileName),
		zap.String("markdownContent", markdown),
		zap.Int("contentLength", len(markdown)),
	)

	log.Info(
		ctx,
		"Excel processing complete",
		zap.Int("totalSheets", len(sheets)),
		zap.Int("processedSheets", len(sheets)),
		zap.Int64("completionTimeMs", completionTime),
		zap.Int("contentLength", len(markdown)),
	)

	return &ExcelToMDOutput{
		CompletionTime:  float64(completionTime),
		FileName:        opts.FileName,
		Markdown:        markdown,
		Sheets:          sheets,
		TotalSheets:     len(sheets),
		ProcessedSheets: len(sheets),
	}, nil
}

// processExcelFile opens and processes an Excel file, returning sheet data.
func processExcelFile(
	ctx context.Context,
	fileReader io.Reader,
	opts *Options,
) ([]Sheet, error) {

	maxFileSizeMB := processor.DefaultMaxExcelFileSizeMB
	if opts.ExcelMaxFileSizeMB > 0 {
		maxFileSizeMB = opts.ExcelMaxFileSizeMB
	}

	// Open the Excel file with size checking to prevent OOM
	f, err := processor.OpenExcelFromReader(fileReader, maxFileSizeMB)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %w", err)
	}
	defer f.Close()

	// Process sheets
	processOpts := processor.ExcelProcessOptions{
		MaxRowsPerSheet:   opts.MaxRowsPerSheet,
		MaxColsPerSheet:   opts.MaxColsPerSheet,
		MaxNumSheets:      opts.MaxNumSheets,
		MaxFileSizeMB:     maxFileSizeMB,
		SelectSheets:      opts.SelectSheets,
		IncludeFormulas:   opts.IncludeFormulas,
		IncludeSheetNames: opts.IncludeSheetNames,
	}

	results := processor.ProcessExcelSheets(ctx, f, processOpts)

	// Convert results to Sheet type with appropriate format
	sheets := make([]Sheet, 0, len(results))
	for _, result := range results {
		if result.Error != nil {
			log.Error(ctx, "Failed to process Excel sheet",
				zap.String("sheet", result.Name),
				zap.Error(result.Error),
			)
			continue
		}

		var content string
		var convertErr error

		switch opts.ExcelOutputFormat {
		case ExcelFormatJSON:
			content, convertErr = processor.SheetToJSON(result)
		case ExcelFormatCSV:
			content = processor.SheetToCSV(result)
		default:
			content = processor.SheetToMarkdownTable(result, opts.IncludeSheetNames)
		}

		if convertErr != nil {
			log.Error(ctx, "Failed to convert Excel sheet",
				zap.String("sheet", result.Name),
				zap.Error(convertErr),
			)
			continue
		}

		log.Debug(
			ctx,
			"Excel sheet converted - dumping content",
			zap.String("fileName", opts.FileName),
			zap.String("sheetName", result.Name),
			zap.String("content", content), // This will be logged as a string, not truncated
			zap.Int("contentLength", len(content)),
			zap.String("outputFormat", fmt.Sprintf("%d", opts.ExcelOutputFormat)),
		)

		sheets = append(sheets, Sheet{
			Name:          result.Name,
			Content:       content,
			ContentLength: len(content),
			RowCount:      result.RowCount,
			ColCount:      result.ColCount,
			Truncated:     result.Truncated,
		})
	}

	if len(sheets) == 0 {
		return nil, errors.New("no sheets were successfully processed")
	}

	return sheets, nil
}
