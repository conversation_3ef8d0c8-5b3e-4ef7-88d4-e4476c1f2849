package docproc

import (
	"errors"
	"fmt"
	"strings"
)

// SupportedFileType represents a supported document file type.
type SupportedFileType string

const (
	FileTypePDF  SupportedFileType = "pdf"
	FileTypeDocx SupportedFileType = "docx"
	FileTypeXlsx SupportedFileType = "xlsx"
	FileTypeXls  SupportedFileType = "xls"
)

// ProcessingMethod describes how a document was processed.
const (
	ProcessingMethodPDFVision     = "pdf_vision"
	ProcessingMethodPDFHTML       = "pdf_html"
	ProcessingMethodDocxDirect    = "docx_direct"
	ProcessingMethodExcelMarkdown = "excel_markdown"
	ProcessingMethodExcelJSON     = "excel_json"
	ProcessingMethodExcelCSV      = "excel_csv"
)

// ErrUnsupportedFileType is returned when a file type is not supported.
var ErrUnsupportedFileType = errors.New("unsupported file type")

// DetectFileType determines the file type from the content type and filename.
// Returns: the file type OR an error if the file type is not supported.
func DetectFileType(contentType, filename string) (SupportedFileType, error) {
	if IsPDF(contentType, filename) {
		return FileTypePDF, nil
	}

	if IsWordDocument(contentType, filename) {
		return FileTypeDocx, nil
	}

	// TODO: Add support for excel spreadsheet processing once we improve that pipeline
	// if IsExcelSpreadsheet(contentType, filename) {
	// 	// IsExcelSpreadsheet returns true for both .xlsx .xls, so check extension to distinguish
	// 	ext := strings.ToLower(filepath.Ext(filename))
	// 	if ext == ".xlsx" {
	// 		return FileTypeXlsx, nil
	// 	}
	// 	return FileTypeXls, nil
	// }

	return "", fmt.Errorf("%w: %s", ErrUnsupportedFileType, filename)

}

// IsSupportedFileType checks if a file is supported based on content type and filename.
func IsSupportedFileType(contentType, filename string) bool {
	_, err := DetectFileType(contentType, filename)
	return err == nil
}

// IsPDF returns true if the attachment is a PDF based on content type or file extension.
// It checks:
// 1. Content type contains "pdf"
// 2. Content type is "octet-stream" and filename ends with .pdf
// 3. Content type is empty/unknown and filename ends with .pdf (fallback)
func IsPDF(contentType, fileName string) bool {
	contentType = strings.ToLower(contentType)
	fileName = strings.ToLower(fileName)

	hasPDFExtension := strings.HasSuffix(fileName, ".pdf")

	// Check content type
	if strings.Contains(contentType, "pdf") {
		return true
	}

	// Fallback to extension if content type is empty, unknown, or octet-stream
	if hasPDFExtension &&
		(contentType == "" ||
			strings.Contains(contentType, "octet-stream") ||
			strings.Contains(contentType, "application/octet-stream")) {
		return true
	}

	return false
}

// IsWordDocument returns true if the attachment is a Word document (.docx).
// It checks:
// 1. Content type contains Word document MIME types
// 2. Content type is "octet-stream" and filename ends with .docx
// 3. Content type is empty/unknown and filename ends with .docx (fallback)
func IsWordDocument(contentType, fileName string) bool {
	contentType = strings.ToLower(contentType)
	fileName = strings.ToLower(fileName)

	hasDocxExtension := strings.HasSuffix(fileName, ".docx")

	// Check content type
	if strings.Contains(contentType, "wordprocessingml.document") ||
		strings.Contains(contentType, "application/vnd.openxmlformats-officedocument.wordprocessingml") {
		return true
	}

	// Fallback to extension if content type is empty, unknown, or octet-stream
	if hasDocxExtension &&
		(contentType == "" ||
			strings.Contains(contentType, "octet-stream") ||
			strings.Contains(contentType, "application/octet-stream")) {
		return true
	}

	return false
}

// IsExcelSpreadsheet returns true if the attachment is an Excel spreadsheet (.xlsx or .xls).
// It checks:
// 1. Content type contains Excel MIME types
// 2. Content type is "octet-stream" and filename ends with .xlsx or .xls
// 3. Content type is empty/unknown and filename ends with .xlsx or .xls (fallback)
func IsExcelSpreadsheet(contentType, fileName string) bool {
	contentType = strings.ToLower(contentType)
	fileName = strings.ToLower(fileName)

	hasExcelExtension := strings.HasSuffix(fileName, ".xlsx") || strings.HasSuffix(fileName, ".xls")

	// Check content type
	if strings.Contains(contentType, "spreadsheetml.sheet") ||
		strings.Contains(contentType, "application/vnd.openxmlformats-officedocument.spreadsheetml") ||
		strings.Contains(contentType, "application/vnd.ms-excel") {
		return true
	}

	// Fallback to extension if content type is empty, unknown, or octet-stream
	if hasExcelExtension &&
		(contentType == "" ||
			strings.Contains(contentType, "octet-stream") ||
			strings.Contains(contentType, "application/octet-stream")) {
		return true
	}

	return false
}
