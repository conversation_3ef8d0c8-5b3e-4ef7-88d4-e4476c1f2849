package docproc

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDetectFileType(t *testing.T) {
	tests := []struct {
		name        string
		contentType string
		filename    string
		expected    SupportedFileType
		wantErr     bool
	}{
		// PDF tests - extension only
		{
			name:        "PDF file with empty contentType",
			contentType: "",
			filename:    "document.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "PDF uppercase extension",
			contentType: "",
			filename:    "DOCUMENT.PDF",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "PDF with mixed case extension",
			contentType: "",
			filename:    "document.Pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		// PDF tests - with content type
		{
			name:        "PDF with content type",
			contentType: "application/pdf",
			filename:    "document.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "PDF with uppercase content type",
			contentType: "APPLICATION/PDF",
			filename:    "document.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "PDF with octet-stream",
			contentType: "application/octet-stream",
			filename:    "document.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "PDF with octet-stream (no application/ prefix)",
			contentType: "octet-stream",
			filename:    "document.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		// DOCX tests - extension only
		{
			name:        "DOCX file with empty contentType",
			contentType: "",
			filename:    "document.docx",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		{
			name:        "DOCX uppercase extension",
			contentType: "",
			filename:    "REPORT.DOCX",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		// DOCX tests - with content type
		{
			name:        "DOCX with full content type",
			contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			filename:    "document.docx",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		{
			name:        "DOCX with partial content type",
			contentType: "wordprocessingml.document",
			filename:    "document.docx",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		{
			name:        "DOCX with octet-stream",
			contentType: "application/octet-stream",
			filename:    "document.docx",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		// Excel support disabled - tests commented out
		// // XLSX tests - extension only
		// {
		// 	name:        "XLSX file with empty contentType",
		// 	contentType: "",
		// 	filename:    "spreadsheet.xlsx",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLSX uppercase extension",
		// 	contentType: "",
		// 	filename:    "DATA.XLSX",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		// // XLS tests - extension only
		// {
		// 	name:        "XLS file with empty contentType",
		// 	contentType: "",
		// 	filename:    "spreadsheet.xls",
		// 	expected:    FileTypeXls,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLS uppercase extension",
		// 	contentType: "",
		// 	filename:    "LEGACY.XLS",
		// 	expected:    FileTypeXls,
		// 	wantErr:     false,
		// },
		// // Excel tests - with content type
		// {
		// 	name:        "XLSX with full content type",
		// 	contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		// 	filename:    "spreadsheet.xlsx",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLSX with partial content type",
		// 	contentType: "spreadsheetml.sheet",
		// 	filename:    "spreadsheet.xlsx",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLS with ms-excel content type",
		// 	contentType: "application/vnd.ms-excel",
		// 	filename:    "spreadsheet.xls",
		// 	expected:    FileTypeXls,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLSX with octet-stream",
		// 	contentType: "application/octet-stream",
		// 	filename:    "spreadsheet.xlsx",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		// {
		// 	name:        "XLS with octet-stream",
		// 	contentType: "application/octet-stream",
		// 	filename:    "spreadsheet.xls",
		// 	expected:    FileTypeXls,
		// 	wantErr:     false,
		// },
		// Path tests
		// {
		// 	name:        "Path with directory",
		// 	contentType: "",
		// 	filename:    "/path/to/document.xlsx",
		// 	expected:    FileTypeXlsx,
		// 	wantErr:     false,
		// },
		{
			name:        "Windows path",
			contentType: "",
			filename:    "C:\\Users\\<USER>\\file.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "Relative path",
			contentType: "",
			filename:    "./files/document.docx",
			expected:    FileTypeDocx,
			wantErr:     false,
		},
		// Unsupported file types
		{
			name:        "Unsupported image file",
			contentType: "",
			filename:    "image.png",
			expected:    "",
			wantErr:     true,
		},
		{
			name:        "Unsupported text file",
			contentType: "",
			filename:    "readme.txt",
			expected:    "",
			wantErr:     true,
		},
		{
			name:        "No extension",
			contentType: "",
			filename:    "document",
			expected:    "",
			wantErr:     true,
		},
		{
			name:        "DOC file (not supported)",
			contentType: "",
			filename:    "document.doc",
			expected:    "",
			wantErr:     true,
		},
		{
			name:        "Empty filename with PDF contentType",
			contentType: "application/pdf",
			filename:    "",
			expected:    FileTypePDF,
			wantErr:     false,
		},
		{
			name:        "Empty filename with empty contentType",
			contentType: "",
			filename:    "",
			expected:    "",
			wantErr:     true,
		},
		{
			name:        "Double extension",
			contentType: "",
			filename:    "file.txt.pdf",
			expected:    FileTypePDF,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileType, err := DetectFileType(tt.contentType, tt.filename)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, fileType)
			}
		})
	}
}

func TestIsSupportedFileType(t *testing.T) {
	tests := []struct {
		name        string
		contentType string
		filename    string
		expected    bool
	}{
		// PDF files
		{name: "PDF with empty contentType", contentType: "", filename: "document.pdf", expected: true},
		{name: "PDF with contentType", contentType: "application/pdf", filename: "document.pdf", expected: true},
		{name: "PDF with octet-stream", contentType: "application/octet-stream",
			filename: "document.pdf", expected: true},
		{name: "PDF uppercase", contentType: "", filename: "FILE.PDF", expected: true},

		// DOCX files
		{name: "DOCX with empty contentType", contentType: "", filename: "document.docx", expected: true},
		{
			name:        "DOCX with contentType",
			contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			filename:    "document.docx",
			expected:    true,
		},
		{name: "DOCX with octet-stream", contentType: "application/octet-stream",
			filename: "document.docx", expected: true},

		// Excel support disabled - tests commented out
		// // Excel files
		// {name: "XLSX with empty contentType", contentType: "", filename: "spreadsheet.xlsx", expected: true},
		// {name: "XLS with empty contentType", contentType: "", filename: "spreadsheet.xls", expected: true},
		// {
		// 	name:        "XLSX with contentType",
		// 	contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		// 	filename:    "spreadsheet.xlsx",
		// 	expected:    true,
		// },
		// {name: "XLSX with octet-stream", contentType: "application/octet-stream",
		// 	filename: "spreadsheet.xlsx", expected: true},
		// {name: "XLS with octet-stream", contentType: "application/octet-stream",
		// 	filename: "spreadsheet.xls", expected: true},
		// {name: "XLS with ms-excel", contentType: "application/vnd.ms-excel", filename: "old.xls", expected: true},

		// Unsupported files
		{name: "PNG image", contentType: "", filename: "image.png", expected: false},
		{name: "DOC (unsupported)", contentType: "", filename: "document.doc", expected: false},
		{name: "TXT file", contentType: "", filename: "file.txt", expected: false},
		{name: "Empty filename", contentType: "", filename: "", expected: false},
		{name: "No extension", contentType: "", filename: "document", expected: false},
		{name: "ZIP file", contentType: "application/zip", filename: "archive.zip", expected: false},
		{name: "PPT file", contentType: "", filename: "presentation.ppt", expected: false},
		{name: "PPTX file", contentType: "", filename: "presentation.pptx", expected: false},

		// Edge cases
		{name: "Path with directory", contentType: "", filename: "/path/to/file.pdf", expected: true},
		// {name: "Windows path", contentType: "", filename: "C:\\docs\\file.xlsx", expected: true},
		{name: "Double extension", contentType: "", filename: "file.backup.docx", expected: true},
		{name: "Misleading contentType", contentType: "application/pdf",
			filename: "notapdf.txt", expected: true}, // contentType wins
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsSupportedFileType(tt.contentType, tt.filename)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProcessingMethods(t *testing.T) {
	// Verify processing method constants are defined
	assert.Equal(t, "pdf_vision", ProcessingMethodPDFVision)
	assert.Equal(t, "pdf_html", ProcessingMethodPDFHTML)
	assert.Equal(t, "docx_direct", ProcessingMethodDocxDirect)
	// Excel support disabled - tests commented out
	// assert.Equal(t, "excel_markdown", ProcessingMethodExcelMarkdown)
	// assert.Equal(t, "excel_json", ProcessingMethodExcelJSON)
	// assert.Equal(t, "excel_csv", ProcessingMethodExcelCSV)
}

func TestFileTypeConstants(t *testing.T) {
	// Verify file type constants are defined correctly
	assert.Equal(t, SupportedFileType("pdf"), FileTypePDF)
	assert.Equal(t, SupportedFileType("docx"), FileTypeDocx)
	// Excel support disabled - tests commented out
	// assert.Equal(t, SupportedFileType("xlsx"), FileTypeXlsx)
	// assert.Equal(t, SupportedFileType("xls"), FileTypeXls)
}

func TestIsPDF(t *testing.T) {
	tests := []struct {
		name        string
		contentType string
		filename    string
		expected    bool
	}{
		// Empty contentType - should rely on extension
		{name: "Empty contentType with .pdf", contentType: "", filename: "doc.pdf", expected: true},
		{name: "Empty contentType with .PDF", contentType: "", filename: "doc.PDF", expected: true},
		{name: "Empty contentType without extension", contentType: "", filename: "doc.txt", expected: false},

		// Content type with 'pdf'
		{name: "application/pdf", contentType: "application/pdf", filename: "doc.pdf", expected: true},
		{name: "APPLICATION/PDF uppercase", contentType: "APPLICATION/PDF", filename: "doc.pdf", expected: true},
		{name: "pdf contentType wrong extension", contentType: "application/pdf", filename: "doc.txt", expected: true},

		// Octet-stream
		{name: "octet-stream with .pdf", contentType: "application/octet-stream", filename: "doc.pdf", expected: true},
		{name: "octet-stream with .txt", contentType: "application/octet-stream", filename: "doc.txt", expected: false},
		{name: "octet-stream no prefix", contentType: "octet-stream", filename: "doc.pdf", expected: true},

		// Edge cases
		{name: "Path with directory", contentType: "", filename: "/path/to/file.pdf", expected: true},
		{name: "No extension", contentType: "", filename: "document", expected: false},
		{name: "Double extension", contentType: "", filename: "file.bak.pdf", expected: true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsPDF(tt.contentType, tt.filename)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsWordDocument(t *testing.T) {
	tests := []struct {
		name        string
		contentType string
		filename    string
		expected    bool
	}{
		// Empty contentType - should rely on extension
		{name: "Empty contentType with .docx", contentType: "", filename: "doc.docx", expected: true},
		{name: "Empty contentType with .DOCX", contentType: "", filename: "doc.DOCX", expected: true},
		{name: "Empty contentType with .doc", contentType: "", filename: "doc.doc", expected: false},

		// Content type with wordprocessingml
		{
			name:        "Full Word MIME type",
			contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			filename:    "doc.docx",
			expected:    true,
		},
		{name: "Partial wordprocessingml", contentType: "wordprocessingml.document",
			filename: "doc.docx", expected: true},
		{name: "Word contentType wrong ext", contentType: "wordprocessingml.document",
			filename: "doc.txt", expected: true},

		// Octet-stream
		{name: "octet-stream with .docx", contentType: "application/octet-stream",
			filename: "doc.docx", expected: true},
		{name: "octet-stream with .doc", contentType: "application/octet-stream", filename: "doc.doc", expected: false},

		// Edge cases
		{name: "Path with directory", contentType: "", filename: "/path/to/file.docx", expected: true},
		{name: "Windows path", contentType: "", filename: "C:\\docs\\file.docx", expected: true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsWordDocument(tt.contentType, tt.filename)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Excel support disabled - test commented out
// func TestIsExcelSpreadsheet(t *testing.T) {
// 	tests := []struct {
// 		name        string
// 		contentType string
// 		filename    string
// 		expected    bool
// 	}{
// 		// Empty contentType - should rely on extension
// 		{name: "Empty contentType with .xlsx", contentType: "", filename: "sheet.xlsx", expected: true},
// 		{name: "Empty contentType with .xls", contentType: "", filename: "sheet.xls", expected: true},
// 		{name: "Empty contentType with .XLSX", contentType: "", filename: "sheet.XLSX", expected: true},
// 		{name: "Empty contentType with .csv", contentType: "", filename: "sheet.csv", expected: false},
//
// 		// Content type with spreadsheetml
// 		{
// 			name:        "Full Excel MIME type",
// 			contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
// 			filename:    "sheet.xlsx",
// 			expected:    true,
// 		},
// 		{name: "Partial spreadsheetml", contentType: "spreadsheetml.sheet", filename: "sheet.xlsx", expected: true},
// 		{name: "ms-excel", contentType: "application/vnd.ms-excel", filename: "sheet.xls", expected: true},
// 		{
// 			name:        "Excel contentType wrong ext",
// 			contentType: "spreadsheetml.sheet",
// 			filename:    "doc.txt",
// 			expected:    true,
// 		},
//
// 		// Octet-stream
// 		{name: "octet-stream with .xlsx", contentType: "application/octet-stream",
// 			filename: "sheet.xlsx", expected: true},
// 		{name: "octet-stream with .xls", contentType: "application/octet-stream",
// 			filename: "sheet.xls", expected: true},
// 		{name: "octet-stream with .csv", contentType: "application/octet-stream",
// 			filename: "sheet.csv", expected: false},
//
// 		// Edge cases
// 		{name: "Path with directory", contentType: "", filename: "/data/reports/sheet.xlsx", expected: true},
// 		{name: "Legacy xls file", contentType: "", filename: "legacy_data.xls", expected: true},
// 	}
//
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			result := IsExcelSpreadsheet(tt.contentType, tt.filename)
// 			assert.Equal(t, tt.expected, result)
// 		})
// 	}
// }
