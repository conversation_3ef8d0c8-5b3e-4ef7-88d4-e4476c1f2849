package docproc

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/go-word/pkg/document"
)

func createTestDocxFile(t *testing.T) string {
	t.Helper()

	// Create a temporary directory for test files
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "test.docx")

	// Create a new Word document
	doc := document.New()

	// Add some content
	doc.AddParagraph("This is a test document.")
	doc.AddHeadingParagraph("Heading 1", 1)
	doc.AddParagraph("This is a paragraph under heading 1.")
	doc.AddHeadingParagraph("Heading 2", 2)
	doc.AddParagraph("This is a paragraph under heading 2.")

	// Save to file
	err := doc.Save(filePath)
	require.NoError(t, err)

	return filePath
}

func TestDocxToMD_BasicDocument(t *testing.T) {
	// Create a test document file
	filePath := createTestDocxFile(t)

	// Read file and convert
	fileData, err := os.ReadFile(filePath)
	require.NoError(t, err)

	// Create reader from file data
	reader := newBytesReader(fileData)

	// Convert to markdown
	ctx := context.Background()
	markdown, err := DocxToMD(ctx, reader,
		WithFileName("test.docx"),
		WithPreserveDocxFormatting(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify content structure
	assert.Contains(t, markdown, "test document")
	assert.Contains(t, markdown, "Heading 1")
	assert.Contains(t, markdown, "Heading 2")
}

func TestDocxToMDWithOutput(t *testing.T) {
	// Create a test document file
	filePath := createTestDocxFile(t)

	// Read file
	fileData, err := os.ReadFile(filePath)
	require.NoError(t, err)

	// Create reader from file data
	reader := newBytesReader(fileData)

	// Convert to markdown with output
	ctx := context.Background()
	output, err := DocxToMDWithOutput(ctx, reader,
		WithFileName("test.docx"),
	)

	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.Equal(t, "test.docx", output.FileName)
	assert.GreaterOrEqual(t, output.CompletionTime, float64(0))
	assert.NotEmpty(t, output.Markdown)
	assert.Greater(t, output.ContentLength, 0)
	assert.Greater(t, output.WordCount, 0)
}

func TestDocxToMD_WithTables(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "table_test.docx")

	// Create a document with a table
	doc := document.New()
	doc.AddParagraph("Document with table:")

	// Add a table using the library's API
	tableConfig := &document.TableConfig{
		Rows: 3,
		Cols: 3,
	}
	table, err := doc.AddTable(tableConfig)
	require.NoError(t, err)

	// Set cell values
	err = table.SetCellText(0, 0, "Header1")
	require.NoError(t, err)
	err = table.SetCellText(0, 1, "Header2")
	require.NoError(t, err)
	err = table.SetCellText(0, 2, "Header3")
	require.NoError(t, err)
	err = table.SetCellText(1, 0, "Row1Col1")
	require.NoError(t, err)
	err = table.SetCellText(1, 1, "Row1Col2")
	require.NoError(t, err)
	err = table.SetCellText(1, 2, "Row1Col3")
	require.NoError(t, err)
	err = table.SetCellText(2, 0, "Row2Col1")
	require.NoError(t, err)
	err = table.SetCellText(2, 1, "Row2Col2")
	require.NoError(t, err)
	err = table.SetCellText(2, 2, "Row2Col3")
	require.NoError(t, err)

	// Save to file
	err = doc.Save(filePath)
	require.NoError(t, err)

	// Read file
	fileData, err := os.ReadFile(filePath)
	require.NoError(t, err)

	// Create reader from file data
	reader := newBytesReader(fileData)

	// Convert to markdown
	ctx := context.Background()
	markdown, err := DocxToMD(ctx, reader,
		WithFileName("table_test.docx"),
		WithUseGFMTables(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify table content is present
	assert.Contains(t, markdown, "Header1")
	assert.Contains(t, markdown, "Row1Col1")
}

func TestDocxToMD_EmptyDocument(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "empty.docx")

	// Create an empty document
	doc := document.New()

	// Save to file
	err := doc.Save(filePath)
	require.NoError(t, err)

	// Read file
	fileData, err := os.ReadFile(filePath)
	require.NoError(t, err)

	// Create reader from file data
	reader := newBytesReader(fileData)

	// Convert to markdown - should handle empty docs gracefully
	ctx := context.Background()
	markdown, err := DocxToMD(ctx, reader, WithFileName("empty.docx"))

	// Either returns empty string or error
	if err != nil {
		assert.Contains(t, err.Error(), "no content")
	} else {
		// If no error, markdown should be empty or minimal
		assert.True(t, len(markdown) < 50, "Expected empty or minimal markdown for empty document")
	}
}

func TestDocxToMD_FormattedText(t *testing.T) {
	// Create a temporary directory for test files
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "formatted.docx")

	// Create a document with formatted text
	doc := document.New()

	// Add paragraph with formatting
	para := doc.AddParagraph("")
	para.AddFormattedText("Bold text", &document.TextFormat{Bold: true})
	para.AddFormattedText(" and ", nil)
	para.AddFormattedText("italic text", &document.TextFormat{Italic: true})

	// Save to file
	err := doc.Save(filePath)
	require.NoError(t, err)

	// Read file
	fileData, err := os.ReadFile(filePath)
	require.NoError(t, err)

	// Create reader from file data
	reader := newBytesReader(fileData)

	// Convert to markdown
	ctx := context.Background()
	markdown, err := DocxToMD(ctx, reader,
		WithFileName("formatted.docx"),
		WithPreserveDocxFormatting(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify the text is present (formatting may vary based on library)
	assert.Contains(t, markdown, "Bold text")
	assert.Contains(t, markdown, "italic text")
}

// newBytesReader creates a bytes.Reader from byte slice
func newBytesReader(data []byte) *bytesReader {
	return &bytesReader{data: data, pos: 0}
}

// bytesReader implements io.Reader for testing
type bytesReader struct {
	data []byte
	pos  int
}

func (r *bytesReader) Read(p []byte) (n int, err error) {
	if r.pos >= len(r.data) {
		return 0, io.EOF
	}
	n = copy(p, r.data[r.pos:])
	r.pos += n
	return n, nil
}
