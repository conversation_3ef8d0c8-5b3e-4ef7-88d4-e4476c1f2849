package docproc

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/xuri/excelize/v2"
)

func TestExcelToMD_BasicTable(t *testing.T) {
	// Create a test Excel file in memory
	f := excelize.NewFile()
	defer f.Close()

	// Add some data to Sheet1
	err := f.SetCellValue("Sheet1", "A1", "Name")
	require.NoError(t, err)
	err = f.Set<PERSON>ell<PERSON>alue("Sheet1", "B1", "Age")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "C1", "City")
	require.NoError(t, err)
	err = f.Set<PERSON>ellValue("Sheet1", "A2", "Alice")
	require.NoError(t, err)
	err = f.<PERSON>ell<PERSON>alue("Sheet1", "B2", 30)
	require.NoError(t, err)
	err = f.<PERSON>("Sheet1", "C2", "New York")
	require.NoError(t, err)
	err = f.<PERSON>("Sheet1", "A3", "<PERSON>")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B3", 25)
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "C3", "Los Angeles")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown
	ctx := context.Background()
	markdown, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithIncludeSheetNames(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify content structure
	assert.Contains(t, markdown, "## Sheet1")
	assert.Contains(t, markdown, "| Name |")
	assert.Contains(t, markdown, "| Alice |")
	assert.Contains(t, markdown, "| Bob |")
}

func TestExcelToMD_MultipleSheets(t *testing.T) {
	// Create a test Excel file with multiple sheets
	f := excelize.NewFile()
	defer f.Close()

	// Add data to Sheet1
	err := f.SetCellValue("Sheet1", "A1", "Product")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B1", "Price")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "A2", "Widget")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B2", 10.99)
	require.NoError(t, err)

	// Create and add data to Sheet2
	_, err = f.NewSheet("Sheet2")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet2", "A1", "Employee")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet2", "B1", "Department")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet2", "A2", "John")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet2", "B2", "Engineering")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown
	ctx := context.Background()
	markdown, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithIncludeSheetNames(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify both sheets are present
	assert.Contains(t, markdown, "## Sheet1")
	assert.Contains(t, markdown, "## Sheet2")
	assert.Contains(t, markdown, "Widget")
	assert.Contains(t, markdown, "John")
}

func TestExcelToMD_SelectSheets(t *testing.T) {
	// Create a test Excel file with multiple sheets
	f := excelize.NewFile()
	defer f.Close()

	// Add data to Sheet1
	err := f.SetCellValue("Sheet1", "A1", "Data1")
	require.NoError(t, err)

	// Create and add data to Sheet2
	_, err = f.NewSheet("Sheet2")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet2", "A1", "Data2")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown with only Sheet2 selected
	ctx := context.Background()
	markdown, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithSelectSheets([]string{"Sheet2"}),
		WithIncludeSheetNames(true),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Verify only Sheet2 is present
	assert.NotContains(t, markdown, "## Sheet1")
	assert.Contains(t, markdown, "## Sheet2")
	assert.Contains(t, markdown, "Data2")
}

func TestExcelToMD_MaxRowLimit(t *testing.T) {
	// Create a test Excel file with many rows
	f := excelize.NewFile()
	defer f.Close()

	// Add header
	err := f.SetCellValue("Sheet1", "A1", "Row")
	require.NoError(t, err)

	// Add 100 rows of data
	for i := 2; i <= 101; i++ {
		err = f.SetCellValue("Sheet1", fmt.Sprintf("A%d", i), i-1)
		require.NoError(t, err)
	}

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown with max 10 rows
	ctx := context.Background()
	markdown, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithMaxRowsPerSheet(10),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, markdown)

	// Count rows in markdown table (excluding header separator)
	lines := strings.Split(markdown, "\n")
	tableLines := 0
	for _, line := range lines {
		if strings.HasPrefix(line, "|") && !strings.Contains(line, "---") {
			tableLines++
		}
	}

	// Should have at most 10 rows (header + 9 data rows)
	assert.LessOrEqual(t, tableLines, 10)
}

func TestExcelToMDWithOutput(t *testing.T) {
	// Create a test Excel file
	f := excelize.NewFile()
	defer f.Close()

	err := f.SetCellValue("Sheet1", "A1", "Test")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "A2", "Data")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown with output
	ctx := context.Background()
	output, err := ExcelToMDWithOutput(ctx, buf,
		WithFileName("test.xlsx"),
	)

	require.NoError(t, err)
	assert.NotNil(t, output)
	assert.Equal(t, "test.xlsx", output.FileName)
	assert.Greater(t, output.CompletionTime, float64(0))
	assert.NotEmpty(t, output.Markdown)
	assert.Equal(t, 1, output.TotalSheets)
	assert.Len(t, output.Sheets, 1)
	assert.Equal(t, "Sheet1", output.Sheets[0].Name)
}

func TestExcelToMD_EmptySheet(t *testing.T) {
	// Create an empty Excel file
	f := excelize.NewFile()
	defer f.Close()

	// Write to buffer (Sheet1 exists but is empty)
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to markdown
	ctx := context.Background()
	_, err = ExcelToMD(ctx, buf, WithFileName("empty.xlsx"))

	// Should error because no content was extracted
	assert.Error(t, err)
}

func TestExcelToMD_JSONFormat(t *testing.T) {
	// Create a test Excel file
	f := excelize.NewFile()
	defer f.Close()

	err := f.SetCellValue("Sheet1", "A1", "Name")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B1", "Value")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "A2", "Key1")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B2", "Val1")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to JSON format
	ctx := context.Background()
	output, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithExcelOutputFormat(ExcelFormatJSON),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, output)

	// Verify JSON structure
	assert.Contains(t, output, `"sheet"`)
	assert.Contains(t, output, `"data"`)
	assert.Contains(t, output, `"Name"`)
	assert.Contains(t, output, `"Key1"`)
}

func TestExcelToMD_CSVFormat(t *testing.T) {
	// Create a test Excel file
	f := excelize.NewFile()
	defer f.Close()

	err := f.SetCellValue("Sheet1", "A1", "Col1")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B1", "Col2")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "A2", "Val1")
	require.NoError(t, err)
	err = f.SetCellValue("Sheet1", "B2", "Val2")
	require.NoError(t, err)

	// Write to buffer
	buf, err := f.WriteToBuffer()
	require.NoError(t, err)

	// Convert to CSV format
	ctx := context.Background()
	output, err := ExcelToMD(ctx, buf,
		WithFileName("test.xlsx"),
		WithExcelOutputFormat(ExcelFormatCSV),
	)

	require.NoError(t, err)
	assert.NotEmpty(t, output)

	// Verify CSV structure
	assert.Contains(t, output, "Col1,Col2")
	assert.Contains(t, output, "Val1,Val2")
}
