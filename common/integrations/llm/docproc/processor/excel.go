package processor

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"slices"
	"strings"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

const (
	// DefaultMaxExcelFileSizeMB is the default maximum Excel file size in MB (15MB)
	// Reasoning: Excel files are compressed ZIP archives. A 15MB compressed file could expand
	// to 45-90MB in memory after decompression and row parsing. Safe for 1GB Lambda.
	DefaultMaxExcelFileSizeMB = 15

	// MaxSafeExcelFileSizeMB is the absolute maximum we should ever allow (50MB)
	// Beyond this, memory issues are highly likely in a 1GB Lambda environment
	MaxSafeExcelFileSizeMB = 50
)

// ExcelProcessOptions configures Excel sheet processing.
type ExcelProcessOptions struct {
	MaxRowsPerSheet   int      // Maximum rows to process per sheet (0 = unlimited)
	MaxColsPerSheet   int      // Maximum columns to process per sheet (0 = unlimited)
	MaxNumSheets      int      // Maximum sheets to process (0 = unlimited)
	MaxFileSizeMB     int      // Maximum file size in MB before processing (0 = default 15MB)
	SelectSheets      []string // Process only these sheets (nil = all)
	IncludeFormulas   bool     // Include formula text alongside values
	IncludeSheetNames bool     // Include sheet names as headers
}

// ExcelSheetResult represents the result of processing a single sheet.
type ExcelSheetResult struct {
	Name      string
	Rows      [][]string
	RowCount  int
	ColCount  int
	Truncated bool
	Error     error
}

// OpenExcelFromReader opens an Excel file from an io.Reader with size checking.
// This function enforces size limits to prevent OOM in memory-constrained environments.
//
// Parameters:
//   - reader: The io.Reader providing the Excel file content
//   - maxSizeMB: Maximum file size in MB before processing (0 = use DefaultMaxExcelFileSizeMB)
//
// The function reads the file into memory to check its size, which is necessary
// because the excelize library requires the full file in memory anyway.
func OpenExcelFromReader(reader io.Reader, maxSizeMB int) (*excelize.File, error) {
	if reader == nil {
		return nil, errors.New("reader cannot be nil")
	}

	if maxSizeMB == 0 {
		maxSizeMB = DefaultMaxExcelFileSizeMB
	}

	if maxSizeMB > MaxSafeExcelFileSizeMB {
		return nil, fmt.Errorf(
			"requested max size %dMB exceeds safe limit %dMB",
			maxSizeMB,
			MaxSafeExcelFileSizeMB,
		)
	}

	// Read file into memory buffer to check size
	// This is necessary because excelize library requires full file in memory anyway
	maxBytes := int64(maxSizeMB * 1024 * 1024)
	limitedReader := io.LimitReader(reader, maxBytes+1) // +1 to detect if exceeded

	fileData, err := io.ReadAll(limitedReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	fileSize := int64(len(fileData))
	if fileSize > maxBytes {
		return nil, fmt.Errorf(
			"file size exceeds limit: %d bytes (%.2f MB) > %d MB limit",
			fileSize,
			float64(fileSize)/(1024*1024),
			maxSizeMB,
		)
	}

	// Create reader from buffer and open Excel file
	bufReader := bytes.NewReader(fileData)

	f, err := excelize.OpenReader(bufReader)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %w", err)
	}

	return f, nil
}

// OpenExcelFromPath opens an Excel file from a file path.
func OpenExcelFromPath(path string) (*excelize.File, error) {
	f, err := excelize.OpenFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %w", err)
	}
	return f, nil
}

// ProcessExcelSheets processes all sheets in an Excel file and returns their data.
func ProcessExcelSheets(ctx context.Context, f *excelize.File, opts ExcelProcessOptions) []ExcelSheetResult {
	sheetList := f.GetSheetList()
	results := make([]ExcelSheetResult, 0, len(sheetList))

	sheetCount := 0
	for _, sheetName := range sheetList {
		// Skip sheets not in the selection list (if provided)
		if len(opts.SelectSheets) > 0 && !slices.Contains(opts.SelectSheets, sheetName) {
			continue
		}

		if opts.MaxNumSheets > 0 && sheetCount >= opts.MaxNumSheets {
			log.WarnNoSentry(
				ctx,
				"Excel sheet processing stopped due to sheet limit",
				zap.Int("processedSheets", sheetCount),
				zap.Int("maxNumSheets", opts.MaxNumSheets),
			)
			break
		}

		result := ProcessExcelSheet(ctx, f, sheetName, opts)
		results = append(results, result)
		sheetCount++
	}

	return results
}

// ProcessExcelSheet processes a single Excel sheet and returns its data.
func ProcessExcelSheet(
	ctx context.Context,
	f *excelize.File,
	sheetName string,
	opts ExcelProcessOptions,
) ExcelSheetResult {

	result := ExcelSheetResult{
		Name: sheetName,
	}

	// Get all rows from the sheet
	rows, err := f.GetRows(sheetName)
	if err != nil {
		result.Error = fmt.Errorf("failed to get rows from sheet %s: %w", sheetName, err)
		return result
	}

	if len(rows) == 0 {
		return result
	}

	// Determine actual dimensions
	maxCols := 0
	for _, row := range rows {
		if len(row) > maxCols {
			maxCols = len(row)
		}
	}

	// Apply row limit
	rowLimit := len(rows)
	if opts.MaxRowsPerSheet > 0 && rowLimit > opts.MaxRowsPerSheet {
		rowLimit = opts.MaxRowsPerSheet
		result.Truncated = true
		log.WarnNoSentry(ctx, "Excel sheet truncated due to row limit",
			zap.String("sheet", sheetName),
			zap.Int("totalRows", len(rows)),
			zap.Int("maxRows", opts.MaxRowsPerSheet),
		)
	}

	// Apply column limit
	colLimit := maxCols
	if opts.MaxColsPerSheet > 0 && colLimit > opts.MaxColsPerSheet {
		colLimit = opts.MaxColsPerSheet
		result.Truncated = true
		log.WarnNoSentry(ctx, "Excel sheet truncated due to column limit",
			zap.String("sheet", sheetName),
			zap.Int("totalCols", maxCols),
			zap.Int("maxCols", opts.MaxColsPerSheet),
		)
	}

	// Extract data with limits applied
	processedRows := make([][]string, 0, rowLimit)
	for i := 0; i < rowLimit; i++ {
		row := rows[i]
		processedRow := make([]string, colLimit)

		for j := 0; j < colLimit; j++ {
			if j < len(row) {
				processedRow[j] = strings.TrimSpace(row[j])
			} else {
				processedRow[j] = ""
			}
		}

		processedRows = append(processedRows, processedRow)
	}

	result.Rows = processedRows
	result.RowCount = len(processedRows)
	result.ColCount = colLimit

	return result
}

// SheetToMarkdownTable converts sheet data to a markdown table.
func SheetToMarkdownTable(result ExcelSheetResult, includeSheetName bool) string {
	if len(result.Rows) == 0 {
		return ""
	}

	var sb strings.Builder

	// Add sheet name as header
	if includeSheetName && result.Name != "" {
		sb.WriteString(fmt.Sprintf("## %s\n\n", result.Name))
	}

	// Add truncation warning
	if result.Truncated {
		sb.WriteString("*Note: Data truncated due to size limits*\n\n")
	}

	// Build header row
	if len(result.Rows) > 0 {
		headerRow := result.Rows[0]
		sb.WriteString("| ")
		for i, cell := range headerRow {
			// Escape pipe characters in cell values
			escapedCell := strings.ReplaceAll(cell, "|", "\\|")
			if escapedCell == "" {
				escapedCell = fmt.Sprintf("Col%d", i+1)
			}
			sb.WriteString(escapedCell)
			sb.WriteString(" | ")
		}
		sb.WriteString("\n")

		// Build separator row
		sb.WriteString("| ")
		for range headerRow {
			sb.WriteString("--- | ")
		}
		sb.WriteString("\n")
	}

	// Build data rows
	for i := 1; i < len(result.Rows); i++ {
		row := result.Rows[i]
		sb.WriteString("| ")
		for _, cell := range row {
			// Escape pipe characters and newlines in cell values
			escapedCell := strings.ReplaceAll(cell, "|", "\\|")
			escapedCell = strings.ReplaceAll(escapedCell, "\n", "<br>")
			sb.WriteString(escapedCell)
			sb.WriteString(" | ")
		}
		sb.WriteString("\n")
	}

	return sb.String()
}

// SheetToJSON converts sheet data to a JSON string.
// The first row is used as keys, and subsequent rows become objects.
func SheetToJSON(result ExcelSheetResult) (string, error) {
	if len(result.Rows) == 0 {
		return "[]", nil
	}

	// First row is headers
	headers := result.Rows[0]

	// Build data objects
	data := make([]map[string]string, 0, len(result.Rows)-1)
	for i := 1; i < len(result.Rows); i++ {
		row := result.Rows[i]
		obj := make(map[string]string)
		for j, header := range headers {
			key := header
			if key == "" {
				key = fmt.Sprintf("Col%d", j+1)
			}
			if j < len(row) {
				obj[key] = row[j]
			} else {
				obj[key] = ""
			}
		}
		data = append(data, obj)
	}

	// Wrap in sheet object if name is provided
	output := map[string]any{
		"sheet":     result.Name,
		"data":      data,
		"rowCount":  result.RowCount,
		"colCount":  result.ColCount,
		"truncated": result.Truncated,
	}

	jsonBytes, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal sheet to JSON: %w", err)
	}

	return string(jsonBytes), nil
}

// SheetToCSV converts sheet data to a CSV string.
func SheetToCSV(result ExcelSheetResult) string {
	if len(result.Rows) == 0 {
		return ""
	}

	var sb strings.Builder

	for _, row := range result.Rows {
		for i, cell := range row {
			// Escape quotes and wrap in quotes if needed
			if strings.ContainsAny(cell, ",\"\n") {
				cell = "\"" + strings.ReplaceAll(cell, "\"", "\"\"") + "\""
			}
			sb.WriteString(cell)
			if i < len(row)-1 {
				sb.WriteString(",")
			}
		}
		sb.WriteString("\n")
	}

	return sb.String()
}
