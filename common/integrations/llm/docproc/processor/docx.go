package processor

import (
	"errors"
	"fmt"
	"io"
	"strings"
	"sync"

	"github.com/drumkitai/go-word/pkg/document"
	"github.com/drumkitai/go-word/pkg/markdown"
)

const (
	// DefaultMaxFileSizeMB is the default maximum file size in MB (10MB)
	// Reasoning: DOCX files are compressed. A 10MB compressed file could expand to 30-50MB
	// in memory after decompression and parsing. With 1GB Lambda limit, this is a safe default.
	DefaultMaxFileSizeMB = 10

	// MaxSafeFileSizeMB is the absolute maximum we should ever allow (50MB)
	// Beyond this, memory issues are highly likely in a 1GB Lambda environment
	MaxSafeFileSizeMB = 50
)

var silenceLoggerOnce sync.Once

// SilenceDocxLogger silences the go-word library's internal logger.
// Only affects go-word's standard library logger, not the application's Zap logger.
func SilenceDocxLogger() {
	silenceLoggerOnce.Do(func() {
		document.SetGlobalLevel(document.LogLevelSilent)
	})
}

// DocxProcessOptions configures Word document processing.
type DocxProcessOptions struct {
	PreserveFormatting bool // Keep bold/italic/underline as markdown
	ExtractImages      bool // Extract and describe embedded images
	IncludeComments    bool // Include document comments
	UseGFMTables       bool // Use GitHub Flavored Markdown tables
	MaxFileSizeMB      int  // Maximum file size in MB before processing (0 = no limit, default 10MB)
}

// DocxResult represents the result of processing a Word document.
type DocxResult struct {
	Markdown  string
	WordCount int
	Error     error
}

// OpenDocxFromReader opens a Word document from an io.Reader with size checking.
// This function enforces size limits to prevent OOM in memory-constrained environments.
// Note: The reader must provide the complete file content as the underlying
// library reads the entire content into memory.
//
// Parameters:
//   - reader: The io.Reader providing the Word document content
//   - maxSizeMB: Maximum file size in MB before processing (0 = use DefaultMaxFileSizeMB)
//
// The function reads the file into memory to check its size, which is necessary
// because the go-word library requires the full file in memory anyway.
func OpenDocxFromReader(reader io.Reader, maxSizeMB int) (*document.Document, error) {
	if reader == nil {
		return nil, errors.New("reader cannot be nil")
	}

	if maxSizeMB == 0 {
		maxSizeMB = DefaultMaxFileSizeMB
	}

	if maxSizeMB > MaxSafeFileSizeMB {
		return nil, fmt.Errorf(
			"requested max size %dMB exceeds recommended safe limit %dMB",
			maxSizeMB,
			MaxSafeFileSizeMB,
		)
	}

	// Read file into memory buffer to check size
	// This is necessary because go-word library requires full file in memory anyway
	maxBytes := int64(maxSizeMB * 1024 * 1024)
	limitedReader := io.LimitReader(reader, maxBytes+1) // +1 to detect if exceeded

	fileData, err := io.ReadAll(limitedReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %w", err)
	}

	fileSize := int64(len(fileData))
	if fileSize > maxBytes {
		return nil, fmt.Errorf(
			"file size exceeds limit: %d bytes (%.2f MB) > %d MB limit",
			fileSize,
			float64(fileSize)/(1024*1024),
			maxSizeMB,
		)
	}

	// Create reader from buffer and open document
	bufReader := io.NopCloser(strings.NewReader(string(fileData)))

	doc, err := document.OpenFromMemory(bufReader)
	if err != nil {
		return nil, fmt.Errorf("failed to open Word document from reader: %w", err)
	}

	if doc == nil {
		return nil, errors.New("document.OpenFromMemory returned nil document without error")
	}

	return doc, nil
}

// OpenDocxFromPath opens a Word document from a file path.
func OpenDocxFromPath(path string) (*document.Document, error) {
	if path == "" {
		return nil, errors.New("file path cannot be empty")
	}

	doc, err := document.Open(path)
	if err != nil {
		return nil, fmt.Errorf("failed to open Word document from path %q: %w", path, err)
	}

	if doc == nil {
		return nil, fmt.Errorf("document.Open returned nil document without error for path %q", path)
	}

	return doc, nil
}

// ProcessDocx processes a Word document and converts it to markdown.
// File size checking should be done before calling this function using OpenDocxFromReader.
func ProcessDocx(doc *document.Document, opts DocxProcessOptions) DocxResult {
	docxResult := DocxResult{}

	// Validate input: check for nil document
	if doc == nil {
		docxResult.Error = errors.New("document cannot be nil")
		return docxResult
	}

	// Configure export options based on our options
	exportOpts := markdown.DefaultExportOptions()
	exportOpts.UseGFMTables = opts.UseGFMTables
	exportOpts.ExtractImages = false // We don't extract images to files
	exportOpts.StripComments = !opts.IncludeComments
	exportOpts.IgnoreErrors = true // Fail-open approach

	// Create exporter and convert to markdown
	exporter := markdown.NewExporter(exportOpts)
	md, err := exporter.ExportToString(doc, exportOpts)
	if err != nil {
		docxResult.Error = fmt.Errorf("failed to export document to markdown: %w", err)
		return docxResult
	}

	docxResult.Markdown = md
	docxResult.WordCount = countWords(md)

	return docxResult
}

// countWords provides a simple word count estimation.
func countWords(text string) int {
	// Split on whitespace and count non-empty strings
	words := strings.Fields(text)
	return len(words)
}
