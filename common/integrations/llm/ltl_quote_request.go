package llm

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	emailHelpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/prompts"
	globalRDS "github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/integration"
)

const defaultAccessorialsList = `liftgate (pickup or delivery), residential, inside delivery, limited access,
appointment, notify before delivery, hazmat, trade show, construction site, school, church.`

//nolint:lll
type (
	LTLCommodity struct {
		Description      string   `json:"description" jsonschema_description:"Item description of the commodity."`
		Quantity         int      `json:"quantity" jsonschema_description:"Number of identical pallets with the same dims/weight."`
		HandlingQuantity int      `json:"handling_quantity" jsonschema_description:"Number of handling units (e.g., pieces, cartons) within the pallet group, if explicitly stated separately from quantity."`
		TotalPieces      int      `json:"total_pieces" jsonschema_description:"Total number of individual pieces/items if explicitly stated separately from handling units."`
		Length           *float64 `json:"length" jsonschema_description:"Pallet length in inches, if provided."`
		Width            *float64 `json:"width" jsonschema_description:"Pallet width in inches, if provided."`
		Height           *float64 `json:"height" jsonschema_description:"Pallet height in inches, if provided."`
		DimensionsUnit   string   `json:"dimensions_unit" jsonschema_description:"Unit of the dimensions, if provided. One of 'imperial' or 'metric'. Default to imperial."`
		WeightTotal      *float64 `json:"weight_total" jsonschema_description:"Total weight for the pallet group in pounds, if provided. Leave empty if unknown."`
		Density          *float64 `json:"density" jsonschema_description:"Density (PCF) only if explicitly provided as a numeric value. Do not compute or guess."`
		FreightClass     string   `json:"freight_class" jsonschema_description:"Freight class only if explicitly provided in the email (e.g., 'class 70'). Otherwise leave empty."`
		NMFC             string   `json:"nmfc" jsonschema_description:"NMFC number only if explicitly provided. Otherwise leave empty."`
		NMFCSub          string   `json:"nmfc_sub" jsonschema_description:"NMFC sub number only if provided. Otherwise leave empty."`
		PackagingGroup   string   `json:"packaging_group" jsonschema_description:"Packaging type when stated (e.g., pallet, skid, crate)."`
	}

	LTLQuoteRequest struct {
		Reasoning      string         `json:"reasoning" jsonschema_description:"A concise explanation of why the extracted values were selected for the quote request."`
		TruckType      string         `json:"truck_type" jsonschema_description:"Type of truck needed, one of 'VAN', 'REEFER', 'FLATBED', 'HOTSHOT', 'BOX TRUCK'. Defaults to '' if unclear."`
		Stops          []Stop         `json:"stops" jsonschema_description:"Ordered list of all stops (including pickup, any intermediate stops, and final dropoff)."`
		Accessorials   []string       `json:"accessorials" jsonschema_description:"For LTL only: list of explicitly requested accessorial codes."`
		LTLCommodities []LTLCommodity `json:"commodities" jsonschema_description:"For LTL only: pallet groups with dims/weight/declared value when provided including class/NMFC/density/packaging/pieces/service level if explicitly stated. Leave missing fields empty."`
	}

	// NOTE: We maintain the response type as array of quote requests instead of only one in case extraction on email
	//       labeled quote request yields multiple quote requests. We don't want to miss those quote requests as they
	//       may be valid.
	//       In the event quote request extraction results in >1 quote requests we log a warning and can update
	//       our quoting labelling prompt to label as batch quote more accurately as needed.
	LTLQuoteRequestOutput struct {
		IsLTL            bool              `json:"is_ltl" jsonschema_description:"True if the email is an ltl quote request. False otherwise."`
		LTLQuoteRequests []LTLQuoteRequest `json:"ltl_quote_requests" jsonschema_description:"Collection of ltl quote requests extracted from the email content."`
	}
)

func ExtractLTLQuoteRequestSuggestions(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textractClient textract.Client,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractLTLQuoteRequestSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	quoteRequestsFromBody, isLTL, err := promptLTLQuoteRequestLLM(
		ctx,
		email,
		nil,
		models.Attachment{},
		openaiService,
		false,
		rds,
		opts...,
	)
	if err != nil {
		return res, fmt.Errorf("LLM error extracting ltl quote request info: %w", err)
	}

	if !isLTL {
		return []models.QuoteRequest{}, nil
	}

	if email.HasPDFs {
		ltlQuoteRequestsFromAttachments, attErr := extractLTLQRSuggestionsFromAttachments(
			ctx,
			email,
			openaiService,
			textractClient,
			rds,
			opts...,
		)
		if attErr != nil {
			return res, fmt.Errorf("LLM error extracting ltl quote request from attachments: %w", attErr)
		}

		if len(ltlQuoteRequestsFromAttachments) > 0 {
			return ltlQuoteRequestsFromAttachments, nil
		}
	}

	return quoteRequestsFromBody, nil
}

func extractLTLQRSuggestionsFromAttachments(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textractClient textract.Client,
	rds RDSInterface,
	opts ...Option,
) (res []models.QuoteRequest, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "extractLTLQRSuggestionsFromAttachments", attrs)
	defer func() { metaSpan.End(err) }()

	var wg sync.WaitGroup
	var allQuotes []models.QuoteRequest

	const maxConcurrent = 5
	sem := make(chan struct{}, maxConcurrent)

	resultChan := make(chan Result, len(email.Attachments))

	// Build map of filename -> ProcessedAttachment for quick lookup
	processedMap := make(map[string]models.ProcessedAttachment)
	for _, pa := range email.ProcessedAttachments {
		processedMap[pa.OriginalFileName] = pa
	}

	for _, attachment := range email.Attachments {
		sem <- struct{}{}
		wg.Add(1)
		go func(attachment models.Attachment) {
			defer func() {
				<-sem
				wg.Done()
			}()

			ctx = log.With(
				ctx,
				zap.String("attachmentName", attachment.OriginalFileName),
				zap.String("attachmentURL", attachment.S3URL),
				zap.String("attachmentExternalID", attachment.ExternalID),
			)

			if !emailHelpers.IsPDF(attachment.MimeType, attachment.OriginalFileName) {
				log.Info(
					ctx,
					"skipping non-PDF attachment",
					zap.String("attachmentName", attachment.OriginalFileName),
				)

				return
			}

			url := attachment.S3URL
			fileName := attachment.OriginalFileName

			// Try to get markdown from email.ProcessedAttachments (preloaded via PreloadProcessedAttachments)
			var attachmentContent any
			pa, found := processedMap[attachment.OriginalFileName]
			if found {
				// Decompress markdown
				markdown, err := helpers.DecompressGzip(pa.MarkdownContent)
				if err != nil {
					log.WarnNoSentry(
						ctx,
						"failed to decompress markdown, trying Textract fallback",
						zap.String("attachmentExternalID", attachment.ExternalID),
						zap.Error(err),
					)
					// Fall through to Textract fallback below
				} else {
					// Found in processed attachments! Use the cached markdown
					attachmentContent = markdown

					log.Debug(
						ctx,
						"using cached markdown for quote request attachment",
						zap.String("attachmentExternalID", attachment.ExternalID),
					)
				}
			} else {
				// Not in processed attachments - this shouldn't happen if processing completed
				// successfully, but try Textract as fallback if available
				log.WarnNoSentry(
					ctx,
					"attachment not found in processed attachments, trying Textract fallback",
					zap.String("attachmentExternalID", attachment.ExternalID),
					zap.String("originalFileName", attachment.OriginalFileName),
				)
			}

			// If not found in cache, try Textract as fallback (if available)
			if attachmentContent == nil {
				if textractClient != nil {
					textractData, textractErr := textract.ExtractData(
						ctx,
						attachment.S3URL,
						textractClient,
					)
					if textractErr != nil {
						log.WarnNoSentry(
							ctx,
							"Textract extraction failed, skipping attachment",
							zap.Error(textractErr),
							zap.String("fileName", fileName),
						)
						resultChan <- Result{}
						return
					}

					log.Debug(
						ctx,
						"successfully extracted data using Textract fallback",
						zap.String("fileName", fileName),
						zap.String("attachmentExternalID", attachment.ExternalID),
					)

					attachmentContent = textractData
				} else {
					log.WarnNoSentry(
						ctx,
						"attachment not in cache and no Textract client available, skipping attachment",
						zap.String("fileName", fileName),
						zap.String("attachmentExternalID", attachment.ExternalID),
					)
					resultChan <- Result{}
					return
				}
			}

			quotes, _, err := promptLTLQuoteRequestLLM(
				ctx,
				email,
				attachmentContent,
				attachment,
				openaiService,
				true,
				rds,
				opts...,
			)
			if err != nil {
				log.WarnNoSentry(ctx,
					"LLM error extracting ltl quote request info:",
					zap.Error(err),
					zap.String("attachmentURL", url),
				)

				return
			}

			// Generate and store embedding for the attachment content
			func() {
				var embeddingContent string
				if markdownStr, ok := attachmentContent.(string); ok {
					embeddingContent = markdownStr
				} else {
					// For textract data, convert to string representation
					embeddingContent = fmt.Sprintf("%v", attachmentContent)
				}

				embedding, embErr := openaiService.GetEmbedding(ctx, embeddingContent)
				if embErr != nil {
					log.WarnNoSentry(
						ctx,
						"Failed to generate embedding for quote request attachment",
						zap.Error(embErr),
						zap.String("fileName", fileName),
					)

					return
				}

				vectorRepo := globalRDS.GetVectorRepository(ctx)
				if vectorRepo == nil {
					return
				}

				embErr = vectorRepo.StoreAttachmentEmbedding(
					ctx,
					&email,
					attachment.ExternalID,
					embedding,
				)
				if embErr != nil {
					log.WarnNoSentry(
						ctx,
						"Failed to store ltl quote request attachment embedding",
						zap.Error(embErr),
						zap.String("fileName", fileName),
					)

					return
				}

				log.Info(
					ctx,
					"Successfully stored ltl quote request attachment embedding",
					zap.String("fileName", fileName),
				)
			}()

			resultChan <- Result{quotes: quotes}
		}(attachment)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	for result := range resultChan {
		if len(result.quotes) > 0 {
			allQuotes = append(allQuotes, result.quotes...)
		}
	}

	return allQuotes, nil
}

func promptLTLQuoteRequestLLM(
	ctx context.Context,
	email models.Email,
	attachmentContent any, // nil, markdown string or textract
	attachment models.Attachment,
	openaiService openai.Service,
	hasAttachments bool,
	rds RDSInterface,
	opts ...Option,
) ([]models.QuoteRequest, bool, error) {

	options := &Options{
		Config: models.QuickQuoteConfig{},
	}

	for _, opt := range opts {
		opt(options)
	}

	integrations, err := rds.GetTMSListByServiceID(ctx, email.ServiceID)
	if err != nil {
		return []models.QuoteRequest{}, false, fmt.Errorf("error getting TMS list for service: %w", err)
	}

	var tmsID uint
	if len(integrations) > 0 {
		tmsID = integrations[0].ID
	}

	// Ignore service ID 1 (Drumkit) for this error
	if len(integrations) > 1 && email.ServiceID != 1 {
		log.Warn(
			ctx,
			"multiple TMS integrations found for quote request service, defaulting to first",
			zap.Uint("serviceID", email.ServiceID),
			zap.Int("countTMS", len(integrations)),
		)
	}

	emailID := strconv.FormatUint(uint64(email.ID), 10)

	subject := email.Subject
	// remove subject if we've already used the subject from this thread in a previous email
	emailCount, err := rds.GetNumberOfEmailsByThreadIDAndUserID(ctx, email.ThreadID, email.UserID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting email count", zap.Error(err))
	}

	if emailCount > 1 {
		subject = ""
		log.Info(ctx, "email subject removed for thread with multiple emails", zap.Int("emailCount", emailCount))
	}

	pricingIntegrations, err := integration.GetPricingListByServiceID(ctx, email.ServiceID)
	if err != nil {
		errMsg := fmt.Errorf("error getting pricing integration list for service: %w", err)
		return []models.QuoteRequest{}, false, errMsg
	}

	var taiIntegration models.Integration

	for _, pricingIntegration := range pricingIntegrations {
		if pricingIntegration.Name == models.TaiPricing {
			taiIntegration = pricingIntegration
			break
		}
	}

	accessorialsList := defaultAccessorialsList
	var knownAccessorials []models.Accessorial

	if taiIntegration.ID != 0 {
		client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(email.Account))
		if err != nil {
			log.Warn(ctx, "failed to create pricing client", zap.Error(err))
		} else {
			knownAccessorials = client.GetStaticCustomerAccessorials()
			accessorialsList = formatAccessorialsForPrompt(knownAccessorials)
		}
	}

	currentYear := strconv.Itoa(time.Now().Year())
	systemPrompt := strings.ReplaceAll(prompts.LTLQuoteRequestSystemPrompt, "{CURRENT_YEAR}", currentYear)
	systemPrompt = strings.ReplaceAll(systemPrompt, "{ACCESSORIALS_LIST}", accessorialsList)
	content := RemoveLinksIfString(email.BodyWithoutSignature)

	if attachmentContent != nil {
		content = RemoveLinksIfString(attachmentContent)
	}

	estTimezone, err := time.LoadLocation("America/New_York")
	if err != nil {
		log.Warn(ctx, "failed to load America/New_York location, falling back to UTC", zap.Error(err))
		estTimezone = time.UTC
	}

	userPrompt := fmt.Sprintf(
		`
		Email sent at: %s
		Subject: %s
		Email body:
		%s
		`,
		email.SentAt.In(estTimezone).Format("01/02/2006, 03:04PM"),
		subject,
		content,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LTLQRBeginConversation, hasAttachments),
		openai.ResponseOptions{
			DeveloperPrompt: systemPrompt,
			UserPrompt:      userPrompt,
			Schema:          extractor.GenerateSchema[LTLQuoteRequestOutput](),
		},
	)
	if err != nil {
		return []models.QuoteRequest{}, false, fmt.Errorf("error getting response from LLM: %w", err)
	}

	btQuoteConversationLogID := response.BraintrustLogID
	result, err := extractor.StructExtractor[LTLQuoteRequestOutput](response.Content)
	if err != nil {
		return []models.QuoteRequest{}, false, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	if !result.IsLTL {
		log.WarnNoSentry(
			ctx,
			"content is not an LTL quote request; skipping extraction",
			zap.String("emailID", emailID),
		)

		return []models.QuoteRequest{}, false, nil
	}

	if len(result.LTLQuoteRequests) == 0 {
		log.WarnNoSentry(
			ctx,
			"is_ltl=true but no ltl quote requests found for content",
			zap.String("emailID", emailID),
		)

		return []models.QuoteRequest{}, true, nil
	}

	mappedCustomer, btCustomerLogID, err := promptLLMForCustomer(
		ctx,
		tmsID,
		email,
		attachmentContent,
		openaiService,
		braintrustsdk.CreateProjectDetails(braintrustsdk.LTLQRGetCustomer, hasAttachments),
		rds,
	)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping customer", zap.Error(err))
	} else {
		log.Debug(ctx, "mapped customer", zap.Any("customer", mappedCustomer))
	}

	var res []models.QuoteRequest

	for _, qr := range result.LTLQuoteRequests {
		log.Debug(ctx, "transportType", zap.String("transportType", qr.TruckType))
		stops := make([]models.Stop, len(qr.Stops))

		for i, stop := range qr.Stops {
			dateTime, err := parseDate(
				ctx,
				stop.DateTime,
				Address{
					City:  stop.Location.City,
					State: stop.Location.State,
				},
			)
			if err != nil {
				log.WarnNoSentry(ctx, "error parsing date", zap.Error(err))
			}

			stops[i] = models.Stop{
				StopType:   stop.Type,
				StopNumber: i,
				Order:      i, // For backward compatibility
				Address: models.Address{
					City:  stop.Location.City,
					State: stop.Location.State,
					Zip:   stop.Location.Zip,
				},
			}

			// Set time fields based on stop type
			switch stop.Type {
			case string(models.PickupStop):
				stops[i].ReadyTime = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}

			case string(models.DropoffStop):
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}

			// For intermediate stops, use must deliver
			default:
				stops[i].MustDeliver = models.NullTime{
					Time:  dateTime,
					Valid: err == nil && !dateTime.IsZero(),
				}
			}
		}

		var firstPickup, lastDropoff *models.Stop
		for i := range len(stops) {
			if strings.ToLower(stops[i].StopType) == string(models.PickupStop) && firstPickup == nil {
				firstPickup = &stops[i]
			}
		}
		for i := len(stops) - 1; i >= 0; i-- {
			if strings.ToLower(stops[i].StopType) == string(models.DropoffStop) && lastDropoff == nil {
				lastDropoff = &stops[i]
			}
		}

		var pickupLocation, dropoffLocation models.Address
		var pickupDate, dropoffDate models.NullTime
		if firstPickup != nil {
			pickupLocation = firstPickup.Address
			pickupDate = firstPickup.ReadyTime
		}
		if lastDropoff != nil {
			dropoffLocation = lastDropoff.Address
			dropoffDate = lastDropoff.MustDeliver
		}

		if pickupLocation == (models.Address{}) && dropoffLocation == (models.Address{}) {
			log.Debug(ctx, "Skipping quote request with no pickup or dropoff location in stops")
			continue
		}

		if !isValidLTLQuoteRequest(ctx, userPrompt, &pickupLocation, &dropoffLocation) {
			log.Info(
				ctx,
				"LLM hallucinated, skipping ltl quote request",
				zap.Any("pickup", pickupLocation),
				zap.Any("dropoff", dropoffLocation),
			)

			continue
		}

		transportType, err := validateTransportType(
			ctx,
			email,
			attachmentContent,
			models.TransportType(qr.TruckType),
			options.Config,
		)
		if err != nil {
			if options.Config.DefaultTransportType != "" {
				transportType = options.Config.DefaultTransportType
			} else {
				transportType = models.VanTransportType
			}
		}

		suggestedRequest := models.QuoteLoadInfo{
			TransportType:    transportType,
			PickupLocation:   pickupLocation,
			PickupDate:       pickupDate,
			DeliveryLocation: dropoffLocation,
			DeliveryDate:     dropoffDate,
			Stops:            stops,
		}

		suggestedRequest.LoadMode = models.LTLMode

		if len(qr.LTLCommodities) > 0 {
			suggestedRequest.Commodities = toModelsCommodities(qr.LTLCommodities)
		}

		if len(qr.Accessorials) > 0 {
			suggestedRequest.Accessorials = toModelsAccessorials(qr.Accessorials, knownAccessorials)
		}

		if mappedCustomer != nil {
			suggestedRequest.CustomerID = mappedCustomer.ID
			suggestedRequest.Customer = mappedCustomer.CompanyCoreInfo
		}

		res = append(
			res,
			models.QuoteRequest{
				UserID:           email.UserID,
				EmailID:          email.ID,
				ThreadID:         email.ThreadID,
				ServiceID:        email.ServiceID,
				RFCMessageID:     email.RFCMessageID,
				RawLLMOutput:     suggestedRequest,
				SuggestedRequest: suggestedRequest,
				Status:           models.Pending,
				Attachment:       attachment,
				SourceCategory:   models.EmailSourceCategory,
				SourceExternalID: email.ExternalID,
				BraintrustLogIDs: createBraintrustLTLQRLogRecordList(
					btQuoteConversationLogID,
					btCustomerLogID,
				),
			},
		)
	}

	return res, true, nil
}

func createBraintrustLTLQRLogRecordList(btLTLQuoteConversationLogID, btCustomerLogID string) models.LogRecordList {
	var recordList models.LogRecordList

	if btLTLQuoteConversationLogID != "" {
		recordList = append(
			recordList,
			models.LogRecord{
				ID:              btLTLQuoteConversationLogID,
				ProjectStepName: string(braintrustsdk.LTLQRBeginConversation),
			},
		)
	}

	if btCustomerLogID != "" {
		recordList = append(
			recordList,
			models.LogRecord{
				ID:              btCustomerLogID,
				ProjectStepName: string(braintrustsdk.LTLQRGetCustomer),
			},
		)
	}

	return recordList
}

// isValidLTLQuoteRequest checking if the pickup and dropoff locations are hallucinated and mapping zipcodes
// to their corresponding city and state.
func isValidLTLQuoteRequest(ctx context.Context, userPrompt string, pickup, dropoff *models.Address) bool {
	if isHallucination(userPrompt, *pickup, *dropoff) {
		return false
	}

	validateZip(ctx, pickup)
	validateZip(ctx, dropoff)

	return true
}

func toModelsCommodities(in []LTLCommodity) models.CommodityInfoList {
	if len(in) == 0 {
		return nil
	}

	out := make(models.CommodityInfoList, 0, len(in))

	for _, r := range in {
		var unitSystem models.DimensionUnit
		switch strings.ToLower(r.DimensionsUnit) {
		case string(models.DimensionsUnitImperial):
			unitSystem = models.DimensionsUnitImperial
		case string(models.DimensionsUnitMetric):
			unitSystem = models.DimensionsUnitMetric
		default:
			unitSystem = models.DimensionsUnitImperial
		}

		var handlingQuantity int
		if r.HandlingQuantity > 0 {
			handlingQuantity = r.HandlingQuantity
		} else {
			handlingQuantity = r.Quantity
		}

		out = append(
			out,
			models.CommodityInfo{
				Description:      r.Description,
				Quantity:         r.Quantity,
				HandlingQuantity: handlingQuantity,
				TotalPieces:      r.TotalPieces,
				Length:           r.Length,
				Width:            r.Width,
				Height:           r.Height,
				DimensionsUnit:   unitSystem,
				WeightTotal:      r.WeightTotal,
				Density:          r.Density,
				FreightClass:     r.FreightClass,
				NMFC:             r.NMFC,
				NMFCSub:          r.NMFCSub,
				PackagingGroup:   r.PackagingGroup,
			},
		)
	}

	return out
}

func toModelsAccessorials(names []string, knownAccessorials []models.Accessorial) models.Accessorials {
	if len(names) == 0 {
		return nil
	}

	var byName map[string]models.Accessorial
	if len(knownAccessorials) > 0 {
		byName = make(map[string]models.Accessorial, len(knownAccessorials))
		for _, acc := range knownAccessorials {
			byName[strings.ToLower(acc.Name)] = acc
		}
	}

	out := make(models.Accessorials, 0, len(names))

	for _, n := range names {
		name := strings.TrimSpace(n)
		if name == "" {
			continue
		}

		if acc, ok := byName[strings.ToLower(name)]; ok {
			out = append(out, acc)
			continue
		}

		out = append(
			out,
			models.Accessorial{
				Name: name,
				Code: name,
			},
		)
	}

	return out
}

func formatAccessorialsForPrompt(accessorials []models.Accessorial) string {
	if len(accessorials) == 0 {
		return defaultAccessorialsList
	}

	names := make([]string, 0, len(accessorials))
	for _, acc := range accessorials {
		names = append(names, acc.Name)
	}

	return strings.Join(names, ", ")
}
