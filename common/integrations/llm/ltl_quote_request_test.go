package llm

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/models"
)

// TestOpenAIBasedLTLQuoteExtraction tests the OpenAI-based LTL quote request extraction function.
func TestOpenAIBasedLTLQuoteExtraction(t *testing.T) {
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	ctx := context.Background()

	mockOpenaiService := new(MockOpenaiService)
	mockRDS := new(MockRDS)

	testEmail := models.Email{
		UserID:    2,
		ServiceID: 1,
		ThreadID:  "thread1",
		Body: `We need an LTL quote for 3 pallets, Class 70.
			Pickup: Bayonne, NJ. Dropoff: East Windsor, CT.
			Ready 10/30/2025.`,
		Subject: "Test Subject",
	}

	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).
		Return([]models.Integration{mockTMS}, nil)
	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, testEmail.ThreadID, testEmail.UserID).
		Return(1, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	result, isLTL, err := promptLTLQuoteRequestLLM(
		ctx,
		testEmail,
		nil,
		models.Attachment{},
		mockOpenaiService,
		false,
		mockRDS,
	)

	require.NoError(t, err)
	assert.True(t, isLTL, "should detect this as an LTL quote request")
	require.NotEmpty(t, result, "Expected non-empty ltl quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
		assert.Equal(t, models.LTLMode, result[0].SuggestedRequest.LoadMode)
	}

	mockRDS.AssertExpectations(t)
}

// TestPDFAttachmentLTLQuoteExtraction tests extracting LTL quote requests from a PDF attachment.
func TestPDFAttachmentLTLQuoteExtraction(t *testing.T) {
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	ctx := context.Background()

	testEmail := models.Email{
		UserID:    2,
		ServiceID: 1,
		ThreadID:  "thread1",
		Subject:   "Mock ltl quote request",
		Body:      "This is a test email body. See attached.",
		Attachments: []models.Attachment{
			{
				OriginalFileName: "test.pdf",
				S3URL:            "s3://test.pdf",
				MimeType:         "application/pdf",
			},
		},
		HasSupportedFiles: true,
		HasPDFs:           true,
	}

	mockOpenaiService := new(MockOpenaiService)
	mockTextract := new(MockTextractClient)
	mockRDS := new(MockRDS)

	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).Return([]models.Integration{mockTMS}, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	result, err := ExtractLTLQuoteRequestSuggestions(
		ctx,
		testEmail,
		mockOpenaiService,
		mockTextract,
		mockRDS,
	)

	require.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty ltl quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
		assert.Equal(t, testEmail.Attachments[0], result[0].Attachment)
		assert.Equal(t, models.LTLMode, result[0].SuggestedRequest.LoadMode)
	}

	assertLTLQRExpectations(t, mockRDS, mockTextract, mockOpenaiService)
}

// TestDetailedLTLQuoteExtraction tests LTL quote extraction from a detailed email body.
func TestDetailedLTLQuoteExtraction(t *testing.T) {
	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	ctx := context.Background()

	mockOpenaiService := new(MockOpenaiService)
	mockOpenaiService.On("GetResponse", mock.Anything, mock.Anything).
		Return(mockLLMCustomerOutput.ShipperName, mockLLMCustomerOutput.OriginalSenderEmail, nil)

	mockTextract := new(MockTextractClient)
	mockRDS := new(MockRDS)

	detailedEmail := models.Email{
		Model:      gorm.Model{ID: 1},
		Account:    "<EMAIL>",
		UserID:     2,
		ServiceID:  1,
		ExternalID: "email1",
		ThreadID:   "thread1",
		Subject:    "Mock ltl quote request",
		Body: `
			I need an LTL quote:
			Pickup: Bayonne, NJ 07002, Ready 10/30/2025.
			Dropoff: East Windsor, CT 06088, Delivery 10/30/2025.
			Commodity: 4 pallets, electronics, 1500 lbs total, Class 70.

			Please send me a quote.
		`,
	}

	getTimezoneByZipOrCityFunc = func(context.Context, string, string, string, string) (string, error) {
		return "America/New_York", nil
	}

	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, detailedEmail.ServiceID).
		Return([]models.Integration{mockTMS}, nil).Once()
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	result, err := ExtractLTLQuoteRequestSuggestions(
		ctx,
		detailedEmail,
		mockOpenaiService,
		mockTextract,
		mockRDS,
	)

	assert.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty ltl quote request list")

	if len(result) > 0 {
		assert.Equal(t, detailedEmail.UserID, result[0].UserID)
		assert.Equal(t, detailedEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
		assert.Equal(t, models.LTLMode, result[0].SuggestedRequest.LoadMode)
	}

	assertLTLQRExpectations(t, mockRDS, mockTextract, mockOpenaiService)
}

//
// Live tests
// 1. You will need to comment out the skip in the test to run it.
// 2. You will also need to modify openai.NewService to this:
/**
func NewService(ctx context.Context) (Service, error) {

	clientOnce.Do(func() {

		apiKey := "[INSERT_API_KEY_HERE]"
		if apiKey == "" {
			clientErr = errors.New("OpenAI API key is not set")
			return
		}

		openaiClient = openaiSDK.NewClient(
			openaiOption.WithAPIKey(apiKey),
		)
	})

	return &service{client: openaiClient}, clientErr
}
**/

// TestLiveEndToEndLTLQuoteExtraction tests the full extraction pipeline with real clients.
// This test is skipped by default and only runs when explicitly enabled.
func TestLiveEndToEndLTLQuoteExtraction(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live test: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	if os.Getenv("OPENAI_API_KEY") == "" {
		t.Skip("OPENAI_API_KEY not set, skipping test")
	}

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		t.Skipf("Failed to get OpenAI client: %v", err)
	}
	textractClient := textract.GetTextractClient(context.TODO())
	mockRDS := new(MockRDS)

	testEmail := models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body: `
		We need an LTL rate.
		Pickup: 99 New Hook Rd, Bayonne, NJ 07002. Ready 10/30/2025.
		Dropoff: 10 Thompson Rd, East Windsor, CT 06088. Deliver 10/31/2025.
		Commodities:
		- 2 pallets, elecronics, 800 lbs, Class 70.
		- 3 pallets, mixed foosd, 1200 lbs, Class 100.
		Total 5 pallets, 2000 lbs.
		Please provide a quote.
		`,
	}

	mockRDS.On("GetNumberOfEmailsByThreadIDAndUserID", mock.Anything, mock.Anything, mock.Anything).Return(1, nil)
	mockRDS.On("GetTMSListByServiceID", mock.Anything, testEmail.ServiceID).
		Return([]models.Integration{{Name: models.McleodEnterprise}}, nil)
	mockRDS.On("GetCustomerByName", mock.Anything, mockTMS.ID, mockLLMCustomerOutput.ShipperName).
		Return(mockCustomer, nil)

	result, err := ExtractLTLQuoteRequestSuggestions(
		ctx,
		testEmail,
		openaiService,
		textractClient,
		mockRDS,
	)

	require.NoError(t, err)
	require.NotEmpty(t, result, "Expected non-empty ltl quote request list")

	if len(result) > 0 {
		assert.Equal(t, testEmail.UserID, result[0].UserID)
		assert.Equal(t, testEmail.ServiceID, result[0].ServiceID)
		assert.Equal(t, models.Pending, result[0].Status)
		assert.Equal(t, models.LTLMode, result[0].SuggestedRequest.LoadMode)
	}

	mockRDS.AssertExpectations(t)
}

func assertLTLQRExpectations(
	t *testing.T,
	mockRDS *MockRDS,
	mockTextract *MockTextractClient,
	mockOpenaiService *MockOpenaiService,
) {

	mockRDS.AssertExpectations(t)
	mockTextract.AssertExpectations(t)
	mockOpenaiService.AssertExpectations(t)
}
