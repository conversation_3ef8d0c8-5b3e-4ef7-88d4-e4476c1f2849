package msclient

import (
	"context"
	"fmt"
)

type DraftState string

const (
	DraftStateUnknown DraftState = "unknown"
	DraftStateDraft   DraftState = "draft"
	DraftStateSent    DraftState = "sent"
	DraftStateDeleted DraftState = "deleted"
)

type DraftDeletionFolders struct {
	DeletedItemsID              string
	RecoverableItemsDeletionsID string
	SentItemsID                 string
	DraftsID                    string
}

func (f DraftDeletionFolders) Empty() bool {
	return f.DeletedItemsID == "" &&
		f.RecoverableItemsDeletionsID == "" &&
		f.SentItemsID == "" &&
		f.DraftsID == ""
}

func (f DraftDeletionFolders) Matches(folderID string) bool {
	if folderID == "" {
		return false
	}
	return folderID == f.DeletedItemsID || folderID == f.RecoverableItemsDeletionsID
}

func (f DraftDeletionFolders) IsSentItems(folderID string) bool {
	if folderID == "" {
		return false
	}
	return folderID == f.SentItems<PERSON>
}

func GetDraftDeletionFolders(ctx context.Context, client Client) (DraftDeletionFolders, error) {
	var folders DraftDeletionFolders

	deletedItems, errDeleted := client.GetFolderByWellKnownName(ctx, "deleteditems")
	if errDeleted == nil {
		folders.DeletedItemsID = deletedItems.ID
	}

	recoverableDeletions, errRecoverable := client.GetFolderByWellKnownName(ctx, "recoverableitemsdeletions")
	if errRecoverable == nil {
		folders.RecoverableItemsDeletionsID = recoverableDeletions.ID
	}

	sentItems, errSent := client.GetFolderByWellKnownName(ctx, "sentitems")
	if errSent == nil {
		folders.SentItemsID = sentItems.ID
	}

	drafts, errDrafts := client.GetFolderByWellKnownName(ctx, "drafts")
	if errDrafts == nil {
		folders.DraftsID = drafts.ID
	}

	if folders.Empty() {
		// Return a single error with both causes for debugging.
		// Wrap the first non-nil error and include others as context.
		var firstErr error
		switch {
		case errDeleted != nil:
			firstErr = errDeleted
		case errRecoverable != nil:
			firstErr = errRecoverable
		case errSent != nil:
			firstErr = errSent
		default:
			firstErr = errDrafts
		}

		var deletedErrStr, recoverableErrStr, sentErrStr, draftsErrStr string
		if errDeleted != nil {
			deletedErrStr = errDeleted.Error()
		}
		if errRecoverable != nil {
			recoverableErrStr = errRecoverable.Error()
		}
		if errSent != nil {
			sentErrStr = errSent.Error()
		}
		if errDrafts != nil {
			draftsErrStr = errDrafts.Error()
		}

		return folders, fmt.Errorf(
			"could not resolve folder IDs (deleteditemsErr=%s, recoverableErr=%s, sentitemsErr=%s, draftsErr=%s): %w",
			deletedErrStr,
			recoverableErrStr,
			sentErrStr,
			draftsErrStr,
			firstErr,
		)
	}

	return folders, nil
}

// ClassifyDraftState determines whether a message represents a still-draft, sent, or deleted email.
//
// Notes:
//   - Deleted drafts may still have IsDraft=true; folder location takes precedence when we can resolve it.
//   - If deletion folders can't be resolved and the message isn't a draft, we return Unknown (to avoid
//     misclassifying deleted drafts as sent).
func ClassifyDraftState(found bool, msg Message, folders DraftDeletionFolders) DraftState {
	if !found {
		return DraftStateDeleted
	}

	if folders.Matches(msg.ParentFolderID) {
		return DraftStateDeleted
	}

	if msg.IsDraft {
		return DraftStateDraft
	}

	if folders.Empty() {
		return DraftStateUnknown
	}

	if folders.IsSentItems(msg.ParentFolderID) {
		return DraftStateSent
	}

	// Message exists but isn't a draft and isn't in Sent Items; don't assume "sent".
	return DraftStateUnknown
}
