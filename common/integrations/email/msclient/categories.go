package msclient

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const labelStateTTL = 24 * time.Hour

func DrumkitColorToOutlookColor(ctx context.Context, color models.Color) CategoryColor {
	ctx, span := otel.StartSpan(ctx, "msclient.DrumkitColorToOutlookColor", nil)
	defer func() { span.End(nil) }()

	switch color {
	case models.ColorRed:
		return CategoryColorRed
	case models.ColorOrange:
		return CategoryColorOrange
	case models.ColorYellow:
		return CategoryColorYellow
	case models.ColorGreen:
		return CategoryColorGreen
	case models.ColorBlue:
		return CategoryColorBlue
	case models.ColorPurple:
		return CategoryColorPurple
	default:
		log.WarnNoSentry(ctx, "unknown drumkit color, default to none", zap.String("color", string(color)))
		return CategoryColorNone
	}
}

// GetCategories retrieves all categories from the user's master category list
// https://learn.microsoft.com/en-us/graph/api/outlookuser-list-mastercategories
func (s *Service) GetCategories(ctx context.Context) ([]Category, error) {
	var resp CategoryListResponse

	err := s.do(ctx, http.MethodGet, "me/outlook/masterCategories", nil, &resp, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}

	return resp.Value, nil
}

// CreateCategory creates a new category in the user's master category list
// https://learn.microsoft.com/en-us/graph/api/outlookuser-post-mastercategories
func (s *Service) CreateCategory(ctx context.Context, displayName string, color CategoryColor) (*Category, error) {
	payload := Category{
		DisplayName: displayName,
		Color:       color,
	}

	var category Category
	err := s.do(ctx, http.MethodPost, "me/outlook/masterCategories", payload, &category, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create category %s: %w", displayName, err)
	}

	return &category, nil
}

// UpdateCategory updates the color of an existing category in the user's master category list
// https://learn.microsoft.com/en-us/graph/api/outlookcategory-update
func (s *Service) UpdateCategory(ctx context.Context, displayName string, color CategoryColor) (*Category, error) {
	payload := map[string]any{
		"color": color,
	}

	var category Category
	err := s.do(
		ctx,
		http.MethodPatch,
		fmt.Sprintf("me/outlook/masterCategories('%s')", url.PathEscape(displayName)),
		payload,
		&category,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to update category %s: %w", displayName, err)
	}

	return &category, nil
}

// AddCategoriesToMessage applies categories to a message by updating the message's categories field.
// Categories must exist in the user's master category list before applying.
// If a category doesn't exist, the API creates it with default Gray color (confirmed via testing)
// https://learn.microsoft.com/en-us/graph/api/message-update
func (s *Service) AddCategoriesToMessage(
	ctx context.Context,
	messageID string,
	labels []models.EmailClientLabel,
) (err error) {

	ctx, span := otel.StartSpan(ctx, "msclient.AddCategoriesToMessage", nil)
	defer func() { span.End(err) }()

	categoryNames := []string{}

	for _, label := range labels {
		categoryNames = append(categoryNames, label.DisplayName)
		outlookColor := DrumkitColorToOutlookColor(ctx, label.Color)

		err := checkLabelState(
			ctx,
			s.user.GetID(),
			s.user.GetEmailAddress(),
			label.DisplayName,
			outlookColor,
			s,
		)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to ensure category exists with expected color, "+
					"outlook will use default color",
				zap.String("label", label.DisplayName),
				zap.Error(err),
			)
		}
	}

	payload := map[string]any{
		"categories": categoryNames,
	}

	err = s.do(
		ctx,
		http.MethodPatch,
		fmt.Sprintf("me/messages/%s", url.PathEscape(messageID)),
		payload,
		nil,
		nil,
	)
	if err != nil {
		return fmt.Errorf("failed to add categories to message %s: %w", messageID, err)
	}

	return nil
}

// checkLabelState checks if a category exists with the correct color, using Redis cache to avoid
// unnecessary API calls. If not cached, it calls EnsureCategoryExistsOrCreate and caches the result
// for 1 day to avoid spamming the Outlook API (especially when users lack update permissions).
//
// Context: Email labels are used by Drumkit agents. Outlook fails-open when adding a label that
// doesn't yet exist to an email, creating it with the default gray color. In order to control the color of the label,
// we need to add MailboxSettings Scope to our Azure app. This ensures we gracefully handle both
// users that have relinked/re-authorized with the new scope and those that haven't.
// See ENG-5053 for more details.
func checkLabelState(
	ctx context.Context,
	userID uint,
	userEmail string,
	displayName string,
	color CategoryColor,
	client Client,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "msclient.checkLabelState", nil)
	defer func() { metaSpan.End(err) }()

	cacheKey := fmt.Sprintf("outlook-label-state:%d:%s:%s", userID, userEmail, displayName)

	// Check Redis cache
	_, found, err := redis.GetKey[string](ctx, cacheKey)
	if err == nil && found {
		log.Debug(
			ctx,
			"category state found in cache",
			zap.String("categoryName", displayName),
			zap.String("userEmail", userEmail),
		)
		return nil
	}

	// redis.Nil is expected for cache miss, other errors are unexpected
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return fmt.Errorf("failed to get category state from cache: %w", err)
	}

	// Cache miss or Redis unavailable, call EnsureCategoryExistsOrCreate
	err = EnsureCategoryExistsOrCreate(ctx, displayName, color, client)
	if err != nil {
		// Expected if user has not re-linked with additional category scopes
		if strings.Contains(strings.ToLower(err.Error()), "access denied") {
			log.Info(
				ctx,
				"access denied to get categories, user may need to re-link with additional MailboxSettings scopes",
			)
			err = nil
		}
	}

	// Cache the result for 1 day, even on error to avoid spamming Outlook API
	// This is especially important when users haven't re-linked with additional scopes
	setErr := redis.SetKey(ctx, cacheKey, "1", labelStateTTL)
	if setErr != nil {
		log.Warn(
			ctx,
			"failed to cache label state",
			zap.String("cacheKey", cacheKey),
			zap.Error(setErr),
		)
	}

	return err
}

// EnsureCategoryExistsOrCreate checks if a category exists in the user's master category list and creates it if not.
// If the category exists but has a different color, it updates the color to match the desired color.
// Returns the category displayName for use with AddCategoriesToMessage.
func EnsureCategoryExistsOrCreate(
	ctx context.Context,
	categoryName string,
	color CategoryColor,
	client Client,
) (err error) {
	ctx, span := otel.StartSpan(ctx, "msclient.EnsureCategoryExistsOrCreate", nil)
	defer func() { span.End(err) }()

	ctx = log.With(ctx, zap.String("categoryName", categoryName), zap.String("desiredColor", string(color)))

	categories, err := client.GetCategories(ctx)
	if err != nil {

		return fmt.Errorf("failed to get categories: %w", err)
	}

	for _, cat := range categories {
		if strings.EqualFold(cat.DisplayName, categoryName) {
			if cat.Color == color {
				log.Debug(
					ctx,
					"category already exists with correct color",
				)
				return nil
			}

			log.Info(
				ctx,
				"category exists but has wrong color, updating",
				zap.String("currentColor", string(cat.Color)),
				zap.String("desiredColor", string(color)),
			)

			updatedCategory, err := client.UpdateCategory(ctx, categoryName, color)
			if err != nil {
				// Expected if user has not re-linked with additional category scopes
				if strings.Contains(strings.ToLower(err.Error()), "access denied") {
					log.Info(
						ctx,
						"access denied to update category color, user may need to re-link with additional scopes",
					)
				}
				return fmt.Errorf("failed to update category %s color: %w", categoryName, err)
			}

			log.Info(
				ctx,
				"updated category color",
				zap.String("newColor", string(updatedCategory.Color)),
			)

			return nil
		}
	}

	category, err := client.CreateCategory(ctx, categoryName, color)
	if err != nil {
		return fmt.Errorf("failed to create category %s: %w", categoryName, err)
	}

	log.Info(
		ctx,
		"created new category",
		zap.String("newCategory", category.DisplayName),
	)

	return nil
}
