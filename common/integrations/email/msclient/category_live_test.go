package msclient

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

// TestLiveCategories tests category operations against a real Outlook account.
//
// There are two modes of operation:
//
// Mode 1: With Database (USE_DB=true)
//
//	LIVE_TEST=true \
//	  USE_DB=true \
//	  MICROSOFT_CLIENT_ID="<client-id>" \
//	  MICROSOFT_CLIENT_SECRET="<client-secret>" \
//	  TEST_EMAIL_ADDRESS="<outlook-email>" \
//	  DB_HOST="<host>" DB_NAME="<name>" DB_USER="<user>" DB_PASSWORD="<password>" \
//	  go test -v ./common/integrations/email/msclient -run TestLiveCategories
//
// Mode 2: Without Database (direct tokens)
//
//	LIVE_TEST=true \
//	  MICROSOFT_CLIENT_ID="<client-id>" \
//	  MICROSOFT_CLIENT_SECRET="<client-secret>" \
//	  TEST_EMAIL_ADDRESS="<outlook-email>" \
//	  TEST_TENANT_ID="<tenant-id>" \
//	  TEST_ENCRYPTED_ACCESS_TOKEN="<encrypted-access-token>" \
//	  TEST_ENCRYPTED_REFRESH_TOKEN="<encrypted-refresh-token>" \
//	  [AES_KEY="<hex-encoded-aes-key>"] \
//	  go test -v ./common/integrations/email/msclient -run TestLiveCategories
//
// Or use a .env file in the package directory or project root.
//
// NOTE: First 3 sub-tests WILL fail if you haven't re-authorized Drumkit with the MailboxSettings scope.
// See ENG-5053 for more details.
func TestLiveCategories(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveCategories: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	// Load .env file if present
	loadEnvFile(t)

	// Create client using helper
	client := createTestClient(ctx, t)

	// Generate a unique category name for testing to avoid conflicts
	testCategoryName := fmt.Sprintf("Drumkit Test Category %d", time.Now().UnixNano())

	t.Run("GetCategories", func(t *testing.T) {
		categories, err := client.GetCategories(ctx)
		require.NoError(t, err, "GetCategories should succeed")

		t.Logf("Found %d existing categories", len(categories))
		for _, cat := range categories {
			t.Logf("  - %s (color: %s)", cat.DisplayName, cat.Color)
		}
	})

	t.Run("CreateCategory", func(t *testing.T) {
		category, err := client.CreateCategory(ctx, testCategoryName, CategoryColorTeal)
		require.NoError(t, err, "CreateCategory should succeed")

		require.Equal(t, testCategoryName, category.DisplayName)
		assert.Equal(t, CategoryColorTeal, category.Color)
		t.Logf("Created category: %s (Color: %s)", category.DisplayName, category.Color)

		t.Cleanup(func() {
			// Cleanup: Note that the Microsoft Graph API doesn't provide a direct delete endpoint
			// in our current implementation. The test category will remain in the user's mailbox.
			// In a production scenario, you might want to implement a DeleteCategory method.
			t.Logf("Note: Test category '%s' was created and will remain in the mailbox", testCategoryName)
		})
	})

	t.Run("GetCategories_IncludesNewCategory", func(t *testing.T) {
		categories, err := client.GetCategories(ctx)
		require.NoError(t, err, "GetCategories should succeed")

		var found bool
		for _, cat := range categories {
			if cat.DisplayName == testCategoryName {
				found = true
				assert.Equal(t, CategoryColorTeal, cat.Color)
				break
			}
		}
		assert.True(t, found, "newly created category should be in the list")
	})

	t.Run("CreateCategory_DuplicateName_ReturnsError", func(t *testing.T) {
		_, err := client.CreateCategory(ctx, testCategoryName, CategoryColorRed)
		assert.Error(t, err, "creating a category with duplicate name should fail")
		t.Logf("Expected error for duplicate category: %v", err)

	})

	t.Run("AddCategoriesToMessage", func(t *testing.T) {
		var testMessageID string

		// Use provided message ID or create a draft
		if envMessageID := os.Getenv("TEST_OUTLOOK_MESSAGE_ID"); envMessageID != "" {
			testMessageID = envMessageID
			t.Logf("Using provided message ID: %s", testMessageID)
		} else {
			// Create a draft message to test with
			draftMsg := &Message{
				Subject: fmt.Sprintf("Drumkit Category Test Draft %d", time.Now().UnixNano()),
				Body: &Body{
					ContentType: "text",
					Content:     "This is a test draft message created for category testing. It can be deleted.",
				},
				ToRecipients: []RecipientCollection{
					{EmailAddress: EmailAddress{Address: client.GetAuthenticatedUser().GetEmailAddress()}},
				},
			}

			err := client.DraftMessage(ctx, draftMsg)
			require.NoError(t, err, "DraftMessage should succeed")
			require.NotEmpty(t, draftMsg.ID, "draft message should have an ID")

			testMessageID = draftMsg.ID
			t.Logf("Created draft message with ID: %s", testMessageID)
		}

		// Add category to the message
		err := client.AddCategoriesToMessage(
			ctx,
			testMessageID,
			[]models.EmailClientLabel{{DisplayName: testCategoryName}},
		)
		require.NoError(t, err, "AddCategoriesToMessage should succeed")

		// Verify the category was applied by fetching the message
		msg, err := client.GetMessageByID(ctx, testMessageID)
		require.NoError(t, err, "GetMessageByID should succeed")

		assert.Contains(t, msg.Categories, testCategoryName, "message should have the category applied")
		t.Logf("Message categories: %v", msg.Categories)
	})

}

// TestLiveEnsureCategoryExistsOrCreate tests the EnsureCategoryExistsOrCreate helper function.
//
// Run with the same environment variables as TestLiveCategories.
// NOTE: Test WILL fail if you haven't re-authorized Drumkit with the MailboxSettings scope.
// // See ENG-5053 for more details.
func TestLiveEnsureCategoryExistsOrCreate(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveEnsureCategoryExistsOrCreate: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	// Load .env file if present
	loadEnvFile(t)

	// Create client using helper
	client := createTestClient(ctx, t)

	t.Run("CreateNewCategory", func(t *testing.T) {
		categoryName := fmt.Sprintf("Drumkit Test New %d", time.Now().UnixNano())

		// Verify it doesn't exist yet
		categories, err := client.GetCategories(ctx)
		require.NoError(t, err)

		for _, cat := range categories {
			if cat.DisplayName == categoryName {
				t.Fatalf("category %s should not exist yet", categoryName)
			}
		}

		// Use EnsureCategoryExistsOrCreate to create it
		err = EnsureCategoryExistsOrCreate(ctx, categoryName, CategoryColorTeal, client)
		require.NoError(t, err)

		// Verify it was created with the correct color
		categories, err = client.GetCategories(ctx)
		require.NoError(t, err)

		var found bool
		var foundColor CategoryColor
		for _, cat := range categories {
			if cat.DisplayName == categoryName {
				found = true
				foundColor = cat.Color
				break
			}
		}
		assert.True(t, found, "category should exist after creation")
		assert.Equal(t, CategoryColorTeal, foundColor, "category should have the correct color")
		t.Logf("Successfully created category: %s with color: %s", categoryName, foundColor)
	})

	t.Run("FindExistingCategoryWithCorrectColor", func(t *testing.T) {
		categoryName := fmt.Sprintf("Drumkit Test Existing %d", time.Now().UnixNano())

		// First, create a category directly with a specific color
		createdCategory, err := client.CreateCategory(ctx, categoryName, CategoryColorBlue)
		require.NoError(t, err)
		require.Equal(t, CategoryColorBlue, createdCategory.Color)
		t.Logf("Pre-created category: %s with color: %s", categoryName, createdCategory.Color)

		// Now use EnsureCategoryExistsOrCreate with the same color
		err = EnsureCategoryExistsOrCreate(ctx, categoryName, CategoryColorBlue, client)
		require.NoError(t, err)

		// Verify the color is still correct (no update should have happened)
		categories, err := client.GetCategories(ctx)
		require.NoError(t, err)

		var foundColor CategoryColor
		for _, cat := range categories {
			if cat.DisplayName == categoryName {
				foundColor = cat.Color
				break
			}
		}
		assert.Equal(t, CategoryColorBlue, foundColor, "color should remain unchanged")
		t.Logf("Category found with correct color: %s", foundColor)
	})

	t.Run("UpdateExistingCategoryWithWrongColor", func(t *testing.T) {
		categoryName := fmt.Sprintf("Drumkit Test Update %d", time.Now().UnixNano())

		// First, create a category with one color
		createdCategory, err := client.CreateCategory(ctx, categoryName, CategoryColorRed)
		require.NoError(t, err)
		require.Equal(t, CategoryColorRed, createdCategory.Color)
		t.Logf("Pre-created category: %s with color: %s", categoryName, createdCategory.Color)

		// Now use EnsureCategoryExistsOrCreate with a different color
		err = EnsureCategoryExistsOrCreate(ctx, categoryName, CategoryColorGreen, client)

		// The function may fail with access denied if user hasn't re-linked with category scopes
		if err != nil {
			if strings.Contains(strings.ToLower(err.Error()), "access denied") {
				t.Skip("Skipping color update test: access denied (user may need to re-link with additional scopes)")
				return
			}
			require.NoError(t, err)
		}

		// Verify the color was updated
		categories, err := client.GetCategories(ctx)
		require.NoError(t, err)

		var foundColor CategoryColor
		for _, cat := range categories {
			if cat.DisplayName == categoryName {
				foundColor = cat.Color
				break
			}
		}
		assert.Equal(t, CategoryColorGreen, foundColor, "color should be updated to the new color")
		t.Logf("Category color successfully updated from %s to %s", CategoryColorRed, foundColor)
	})

	t.Run("CaseInsensitiveMatch", func(t *testing.T) {
		categoryName := fmt.Sprintf("Drumkit Test Case %d", time.Now().UnixNano())

		// Create with original case
		_, err := client.CreateCategory(ctx, categoryName, CategoryColorPurple)
		require.NoError(t, err)

		// Try to ensure exists with different case
		err = EnsureCategoryExistsOrCreate(
			ctx,
			strings.ToUpper(categoryName),
			CategoryColorPurple,
			client,
		)
		require.NoError(t, err)
	})
}

// loadEnvFile attempts to load .env files from package directory and project root
func loadEnvFile(t *testing.T) {
	if err := godotenv.Load(); err != nil {
		t.Log("info: no .env file found in package directory")
		// // Try loading from project root
		// if err := godotenv.Load("../../../../.env"); err != nil {
		// 	t.Log("info: no .env file found in project root")
		// }
	}
}

// createTestClient creates an msclient for testing, supporting both database and direct token modes
func createTestClient(ctx context.Context, t *testing.T) Client {
	// Validate required environment variables
	microsoftClientID := os.Getenv("MICROSOFT_CLIENT_ID")
	microsoftClientSecret := os.Getenv("MICROSOFT_CLIENT_SECRET")
	testOutlookEmail := os.Getenv("TEST_EMAIL_ADDRESS")

	require.NotEmpty(t, microsoftClientID, "MICROSOFT_CLIENT_ID environment variable is required")
	require.NotEmpty(t, microsoftClientSecret, "MICROSOFT_CLIENT_SECRET environment variable is required")
	require.NotEmpty(t, testOutlookEmail, "TEST_EMAIL_ADDRESS environment variable is required")

	var user models.User

	if os.Getenv("USE_DB") == "true" || os.Getenv("USE_DB") == "1" {
		// Mode 1: Load user from database
		t.Log("Using database mode (USE_DB=true)")

		require.NotEmpty(t, os.Getenv("DB_HOST"), "DB_HOST environment variable is required when USE_DB=true")
		require.NotEmpty(t, os.Getenv("DB_NAME"), "DB_NAME environment variable is required when USE_DB=true")
		require.NotEmpty(t, os.Getenv("DB_USER"), "DB_USER environment variable is required when USE_DB=true")
		require.NotEmpty(t, os.Getenv("DB_PASSWORD"), "DB_PASSWORD environment variable is required when USE_DB=true")

		// Connect to database
		err := rds.OpenDirect(
			ctx,
			rds.WithDBHost(os.Getenv("DB_HOST")),
			rds.WithDBName(os.Getenv("DB_NAME")),
			rds.WithDBCredentials(os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD")),
			rds.WithSSLMode("disable"),
			rds.WithGormConfig(&gorm.Config{PrepareStmt: true}),
		)
		require.NoError(t, err, "failed to connect to database")

		// Get test user from database
		dbUser, err := userDB.GetByEmail(ctx, testOutlookEmail)
		require.NoError(t, err, "failed to get test user from database: %s", testOutlookEmail)
		user = dbUser
	} else {
		// Mode 2: Use direct tokens from environment variables
		t.Log("Using direct token mode (no database)")

		encryptedAccessToken := os.Getenv("TEST_ENCRYPTED_ACCESS_TOKEN")
		encryptedRefreshToken := os.Getenv("TEST_ENCRYPTED_REFRESH_TOKEN")
		tenantID := os.Getenv("TEST_TENANT_ID")

		require.NotEmpty(t, encryptedAccessToken, "TEST_ENCRYPTED_ACCESS_TOKEN is required when USE_DB is not set")
		require.NotEmpty(t, encryptedRefreshToken, "TEST_ENCRYPTED_REFRESH_TOKEN is required when USE_DB is not set")
		require.NotEmpty(t, tenantID, "TEST_TENANT_ID is required when USE_DB is not set")

		// Set AES key if provided
		aesKey := os.Getenv("AES_KEY")
		if aesKey != "" {
			// aesKey, err := hex.DecodeString(aesKeyHex)
			// require.NoError(t, err, "failed to decode AES_KEY from hex")
			crypto.AESKey = []byte(aesKey)
			t.Log("Using provided AES_KEY for token decryption")
		} else {
			t.Log("No AES_KEY provided, using default encryption settings")
		}

		user = models.User{
			// Model:                 gorm.Model{ID: 1},
			EmailAddress:          testOutlookEmail,
			EmailProvider:         models.OutlookEmailProvider,
			TenantID:              tenantID,
			EncryptedAccessToken:  encryptedAccessToken,
			EncryptedRefreshToken: encryptedRefreshToken,
			// Force token refresh immediately to avoid token expiry race condition
			TokenExpiry: time.Now().Add(-1 * time.Hour),
			// WebhookExpiration:     time.Now().Add(24 * time.Hour),
		}
	}

	// Disable UpdateUserFunc to prevent database writes during tests in direct mode
	if os.Getenv("USE_DB") != "true" {
		originalUpdateUserFunc := oauth.UpdateUserFunc
		oauth.UpdateUserFunc = func(_ context.Context, _ models.User) error {
			t.Log("UpdateUserFunc called but skipped (direct token mode)")
			return nil
		}

		t.Cleanup(func() {
			oauth.UpdateUserFunc = originalUpdateUserFunc
		})
	}

	// Create msclient
	client, err := New(ctx, microsoftClientID, microsoftClientSecret, &user)
	require.NoError(t, err, "failed to create msclient")

	return client
}
