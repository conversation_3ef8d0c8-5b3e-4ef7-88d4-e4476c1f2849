# Microsoft Outlook Email Client

This package provides a Go client for sending emails via Microsoft Outlook using the Microsoft Graph API.

## Table of Contents

- [Overview](#overview)
- [Email Sending Flows](#email-sending-flows)
  - [Flow 1: New Email](#flow-1-new-email)
  - [Flow 2: Reply in Thread](#flow-2-reply-in-thread)
- [Key Components](#key-components)
- [API Methods](#api-methods)
- [Models](#models)
- [Architecture](#architecture)

## Overview

The `msclient` package handles all Outlook/Microsoft 365 email operations for Drumkit, including:

- Sending new emails
- Replying in existing email threads
- Managing email subscriptions (webhooks)
- Fetching messages and attachments
- Managing drafts

## Email Sending Flows

### Flow 1: New Email

**Use Case**: Sending a brand new email (not a reply)

**Entry Point**: `fn/api/routes/appt/send_request_appt_email.go`

```go
// 1. Create GeneratedEmail record in database
generatedEmail := &models.GeneratedEmail{
    FreightTrackingID: load.FreightTrackingID,
    User:              user,
    Service:           service,
    Recipients:        pq.StringArray{body.Recipient},
    CC:                pq.String<PERSON>rray(body.CC),
    BCC:               pq.StringArray(body.BCC),
    Subject:           body.Subject,
    Body:              body.EmailBody,
    Status:            models.PendingStatus,
    ScheduleSend:      scheduleTime,
}

genEmailDB.BatchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{generatedEmail})

// 2. Trigger email sending via Step Function or HTTP
apiutil.SendEmail(ctx, user, []*models.GeneratedEmail{generatedEmail})
```

**Step Function Handler**: `fn/sendemail/step_function_handler.go`

```go
// 3. Build Outlook message using helper
msg := emails.BuildOutlookMessage(generatedEmail, signature, false)

// 4. Create draft to get immutable ID
client.DraftMessage(ctx, msg)

// 5. Send the message
client.SendMessage(ctx, msg)

// 6. Update GeneratedEmail record with sent status
generatedEmail.ExternalID = msg.ID
generatedEmail.RFCMessageID = msg.InternetMessageID
generatedEmail.ThreadID = msg.ConversationID
generatedEmail.Status = models.SentStatus
genEmailDB.Update(ctx, generatedEmail)
```

**Microsoft Graph API Calls**:

```
POST https://graph.microsoft.com/v1.0/me/messages
  -> Creates draft message
  -> Returns: Message with ID

POST https://graph.microsoft.com/v1.0/me/messages/{messageId}/send
  -> Sends the draft message
```

---

### Flow 2: Reply in Thread

**Use Case**: Replying to an existing email conversation while customizing recipients

**Entry Point**: `fn/api/routes/appt/send_request_appt_email.go`

```go
// 1. Frontend passes threadItemID (the message ID to reply to)
generatedEmail := &models.GeneratedEmail{
    // ... standard fields ...
    ThreadID:         body.ThreadItemID,  // Store the message ID
    ThreadReferences: body.ThreadReferences,
    InReplyTo:        pq.StringArray(body.InReplyTo),
}
```

**Step Function Handler**: `fn/sendemail/step_function_handler.go`

```go
// 2. Check if this is a reply (ThreadID is populated)
if generatedEmail.ThreadID != "" {
    
    // 3. Get the message we're replying to (requires valid message ID)
    latestMessage, err := client.GetMessageByID(ctx, generatedEmail.ThreadID)
    if err != nil {
        // Return error - frontend must provide valid message ID
        return error
    }
    
    // 4. Create draft reply (preserves inline thread history)
    msg, err := client.DraftReplyAll(ctx, latestMessage.ID, generatedEmail.Body)

    // 5. Update recipients to match our specified recipients
    //    (not all recipients from original thread)
    toRecipients := emails.BuildMSRecipientCollection(generatedEmail.Recipients)
    ccRecipients := emails.BuildMSRecipientCollection(generatedEmail.CC)
    bccRecipients := emails.BuildMSRecipientCollection(generatedEmail.BCC)

    client.UpdateMessageRecipients(ctx, msg, toRecipients, ccRecipients, bccRecipients)

    // 6. Send the draft reply
    client.SendMessage(ctx, msg)

    // 7. Update GeneratedEmail record
    generatedEmail.ExternalID = msg.ID
    generatedEmail.RFCMessageID = msg.InternetMessageID
    generatedEmail.ThreadID = msg.ConversationID
    generatedEmail.Status = models.SentStatus
    genEmailDB.Update(ctx, generatedEmail)
}
```

**Microsoft Graph API Calls**:

```
# Get message to reply to
GET https://graph.microsoft.com/v1.0/me/messages/{messageId}
  -> Returns: Message object

# Create reply draft (preserves thread)
POST https://graph.microsoft.com/v1.0/me/messages/{messageId}/createReplyAll
  Body: {"comment": "email body with signature"}
  -> Returns: Draft Message with new ID in same thread

# Update recipients on the draft
PATCH https://graph.microsoft.com/v1.0/me/messages/{draftMessageId}
  Body: {"toRecipients": [...], "ccRecipients": [...], "bccRecipients": [...]}
  -> Returns: Updated Message

# Send the draft
POST https://graph.microsoft.com/v1.0/me/messages/{draftMessageId}/send
  -> Sends the reply
```

---

## Key Components

### Client Interface (`msclient.go`)

The `Client` interface defines all Outlook operations:

```go
type Client interface {
    // Message operations
    GetMessageByID(ctx context.Context, id string, opts ...Option) (Message, error)
    
    // Sending operations
    DraftMessage(ctx context.Context, msg *Message) error
    DraftReplyAll(ctx context.Context, msgID string, content string) (*Message, error)
    SendMessage(ctx context.Context, msg *Message) error
    UpdateMessageRecipients(ctx context.Context, msg *Message, to, cc, bcc []RecipientCollection) error

    // Other operations...
}
```

### Helper Functions (`common/helpers/emails/emails.go`)

Shared helpers for consistent email construction:

```go
// Normalizes email body (newlines -> <br> tags)
func NormalizeEmailBodyHTML(body string) string

// Builds Outlook message from GeneratedEmail model
func BuildOutlookMessage(genEmail *models.GeneratedEmail, signature string, hasAttachments bool) *Message

// Converts email strings to recipient collections
func BuildMSRecipientCollection(emails []string) []RecipientCollection

// Gets signature for user
func GetOutlookSignature(user *models.User) string
```

### Models (`models.go`)

Core data structures for Outlook messages:

```go
type Message struct {
    ID                    string
    Subject               string
    Body                  *Body
    ToRecipients          []RecipientCollection
    CcRecipients          []RecipientCollection
    BccRecipients         []RecipientCollection
    ConversationID        string    // Thread ID
    InternetMessageID     string    // RFC message ID
    InternetMessageHeaders []Header  // For threading headers
    // ... other fields
}

type Body struct {
    ContentType string  // "html" or "text"
    Content     string
}

type RecipientCollection struct {
    EmailAddress EmailAddress
}

type EmailAddress struct {
    Name    string
    Address string
}
```

## API Methods

### DraftMessage

Creates a new draft email message. Automatically appends signature if user has `UseSignatureOnRepliesForwards` enabled.

```go
func (s *Service) DraftMessage(ctx context.Context, msg *Message) error
```

**Graph API**: `POST /me/messages`

---

### DraftReplyAll

Creates a draft reply that preserves the inline thread history like the Outlook app. The `comment` parameter becomes the reply body, and the Graph API automatically includes the original message thread below it.

```go
func (s *Service) DraftReplyAll(ctx context.Context, msgID string, content string) (*Message, error)
```

**Important**:

- Only send the `comment` field in the request body
- The Graph API preserves the inline thread automatically
- Returns a complete `Message` object with a new draft ID

**Graph API**: `POST /me/messages/{messageId}/createReplyAll`

**Request Body**:

```json
{
  "comment": "<html body with signature>"
}
```

**Response**: Full `Message` object representing the draft reply

---

### UpdateMessageRecipients

Updates recipients on a draft message. This is essential for customizing who receives a reply.

```go
func (s *Service) UpdateMessageRecipients(
    ctx context.Context,
    msg *Message,
    toRecipients, ccRecipients, bccRecipients []RecipientCollection,
) error
```

**Graph API**: `PATCH /me/messages/{messageId}`

**Use Case**: When replying in a thread, `createReplyAll` includes all original recipients. Use this method to customize the recipient list before sending.

---

### SendMessage

Sends a draft message that was previously created.

```go
func (s *Service) SendMessage(ctx context.Context, msg *Message) error
```

**Graph API**: `POST /me/messages/{messageId}/send`

---

### GetMessageByID

Fetches a message by its ID. Required for getting the message to reply to.

```go
func (s *Service) GetMessageByID(ctx context.Context, id string, opts ...Option) (Message, error)
```

**Graph API**: `GET /me/messages/{messageId}`

---

## Architecture

### Email Sending Path

```
┌─────────────────────────────────────────────────────────────────┐
│ 1. API Route (send_request_appt_email.go)                       │
│    - Validates request                                          │
│    - Creates GeneratedEmail record                              │
│    - Calls apiutil.SendEmail()                                  │
└────────────────────┬────────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────────┐
│ 2. Send Email Util (send_email.go)                              │
│    - Builds payload with generatedEmailID                       │
│    - Triggers Step Function (prod) or HTTP (dev)                │
└────────────────────┬────────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────────┐
│ 3. Step Function Handler (step_function_handler.go)             │
│    - Routes to sendOutlookMessage() or sendGmailMessage()       │
│    - Fetches GeneratedEmail from DB                             │
│    - Creates MS client with user credentials                    │
│    - Executes email flow (new or reply)                         │
└────────────────────┬────────────────────────────────────────────┘
                     │
                     ▼
┌─────────────────────────────────────────────────────────────────┐
│ 4. MS Client (msclient.go)                                      │
│    - Handles OAuth2 authentication                              │
│    - Makes Graph API calls                                      │
│    - Returns message metadata                                   │
└─────────────────────────────────────────────────────────────────┘
```

### Thread Reply Flow Detail

```
Frontend                Backend API               Step Function           Graph API
   │                       │                          │                      │
   │ POST /scheduling/     │                          │                      │
   │ email/request-appt    │                          │                      │
   │ {threadItemID: "..."} │                          │                      │
   ├──────────────────────>│                          │                      │
   │                       │                          │                      │
   │                       │ Store GeneratedEmail     │                      │
   │                       │ with ThreadID            │                      │
   │                       │                          │                      │
   │                       │ Trigger Step Function    │                      │
   │                       ├─────────────────────────>│                      │
   │                       │                          │                      │
   │                       │                          │ GET /messages/{id}   │
   │                       │                          ├─────────────────────>│
   │                       │                          │<─────────────────────┤
   │                       │                          │ Message object       │
   │                       │                          │                      │
   │                       │                          │ POST /messages/{id}/ │
   │                       │                          │      createReplyAll  │
   │                       │                          ├─────────────────────>│
   │                       │                          │<─────────────────────┤
   │                       │                          │ Draft Message        │
   │                       │                          │                      │
   │                       │                          │ PATCH /messages/     │
   │                       │                          │       {draftId}      │
   │                       │                          ├─────────────────────>│
   │                       │                          │<─────────────────────┤
   │                       │                          │ Updated Message      │
   │                       │                          │                      │
   │                       │                          │ POST /messages/      │
   │                       │                          │      {draftId}/send  │
   │                       │                          ├─────────────────────>│
   │                       │                          │<─────────────────────┤
   │                       │                          │ 202 Accepted         │
   │                       │                          │                      │
   │                       │<─────────────────────────┤                      │
   │<──────────────────────┤                          │                      │
   │ 201 Created           │                          │                      │
```

---

## Important Notes

### Outlook vs Gmail Threading Differences

**Threading is handled differently** between email providers:

#### Outlook Threading

Microsoft Outlook uses provider-specific IDs:

- **Conversation ID**: Identifies an entire email thread
- **Message ID**: Identifies a specific email in that thread

**For replies, you MUST provide a valid Message ID** from the frontend. The frontend should use `threadItemID` to pass the specific message ID to reply to.

**API Request Fields (Outlook)**:

```json
{
  "threadItemID": "AAMkAG...", // Preferred: specific message ID to reply to
  "threadID": "AAQkAG...", // Fallback: conversation ID (requires resolution)
  "replyInThread": true
}
```

#### Gmail Threading

Gmail uses standard RFC email headers:

- **ThreadID**: Automatically assigned by Gmail (read-only)
- **References**: RFC header with chain of message IDs
- **In-Reply-To**: RFC header with previous message ID

**API Request Fields (Gmail)**:

```json
{
  "threadReferences": "<<EMAIL>> <<EMAIL>>", // RFC "References" header
  "inReplyTo": ["<<EMAIL>>"], // RFC "In-Reply-To" header
  "replyInThread": true
}
```

**Key Difference**:

- **Outlook**: Requires specific message/conversation ID from Graph API
- **Gmail**: Uses standard RFC threading headers that work across all email clients

**Note**: When sending via the API route, you can provide both sets of fields. The backend will use the appropriate ones based on the user's email provider.

### Immutable IDs

Always include the `Prefer: IdType="ImmutableId"` header in requests to ensure stable IDs across mailbox migrations.

### Signature Handling

- **New emails**: Signature added by `BuildOutlookMessage()` helper
- **Replies**: Signature added by `DraftReplyAll()` if `user.UseSignatureOnRepliesForwards` is true

### Error Handling

The `createReplyAll` API will return:

- `400 Bad Request` if the message ID is invalid or is actually a conversation ID
- `404 Not Found` if the message doesn't exist
- `405 Method Not Allowed` if you try to PATCH a non-draft message

### Testing

Use the mock client (`mock/mock.go`) for unit tests. It implements the `Client` interface with in-memory operations.

---

## References

- [Microsoft Graph API - Messages](https://learn.microsoft.com/en-us/graph/api/resources/message)
- [Create Reply All](https://learn.microsoft.com/en-us/graph/api/message-createreplyall)
- [Send Message](https://learn.microsoft.com/en-us/graph/api/message-send)
- [Outlook Immutable IDs](https://learn.microsoft.com/en-us/graph/outlook-immutable-id)
- [Comment vs Body in Replies](https://stackoverflow.com/a/78750255/10715467)
