# Gmail Email Client

This package provides a Go client for interacting with the Gmail API, handling email sending, retrieval, and inbox monitoring.

## Table of Contents

- [Overview](#overview)
- [Email Sending Flows](#email-sending-flows)
  - [Flow 1: New Email](#flow-1-new-email)
  - [Flow 2: Reply in Thread](#flow-2-reply-in-thread)
- [Threading Logic](#threading-logic)
- [API Methods](#api-methods)
- [Architecture](#architecture)

## Overview

The `gmailclient` package handles all Gmail operations for Drumkit, including:

- Sending new emails and replies
- Resolving threading via RFC headers
- Managing attachments (including inline images)
- Monitoring inbox changes via Pub/Sub (WatchInbox)
- Fetching messages and history

## Email Sending Flows

### Flow 1: New Email

**Use Case**: Sending a brand new email (not a reply)

```go
// 1. Create GeneratedEmail with recipients, subject, body
generatedEmail := &models.GeneratedEmail{
    Recipients: []string{"<EMAIL>"},
    Subject:    "New Load Available",
    Body:       "<h1>Hello</h1>",
    // ...
}

// 2. Send via Client
// Signature must be fetched separately and passed in
signature := client.GetGmailSignature(ctx, client, user.EmailAddress)
msg, err := client.SendMessage(ctx, generatedEmail, signature, attachments)
```

### Flow 2: Reply in Thread

**Use Case**: Replying to an existing email conversation.

Unlike Outlook which uses a "create reply" endpoint, Gmail replies are just standard emails with specific RFC headers (`In-Reply-To`, `References`) and the same Subject (prefixed with "Re:").

**Key Steps:**

1.  **Frontend**: Provides `threadReferences` (RFC header) and/or `inReplyTo` (RFC header) ID.
    *   *Fallback*: Can provides `threadID` (Gmail internal hex ID) if headers are missing.
2.  **Backend**: `SendMessage` handles the threading logic.
    *   Resolves message IDs to proper RFC format (`<<EMAIL>>`).
    *   Sets `In-Reply-To` and `References` headers.
    *   Preserves the original Subject (adding "Re:" if needed).

```go
// 1. Create GeneratedEmail with threading fields
generatedEmail := &models.GeneratedEmail{
    Recipients:       []string{"<EMAIL>"},
    Body:             "Reply content",
    ThreadReferences: "<msg1@domain> <msg2@domain>", // From frontend
    InReplyTo:        []string{"<msg2@domain>"},     // From frontend
    ThreadID:         "18c...",                      // Fallback Gmail Thread ID
}

// 2. SendMessage automatically handles the headers
client.SendMessage(ctx, generatedEmail, signature, nil)
```

## Threading Logic

Gmail threading relies on standard RFC 2822 headers. The client includes robust logic to resolve various ID formats to valid RFC Message-IDs.

### ID Resolution Strategy

When `SendMessage` receives `InReplyTo` or `ThreadReferences` IDs, it attempts to resolve them:

1.  **RFC Format**: If ID contains `@` (e.g., `<<EMAIL>>`), it uses it as-is.
2.  **Gmail Internal IDs**: If ID is a Gmail UI ID (decimal) or API ID (hex), the client:
    *   Fetches the message metadata from Gmail API.
    *   Extracts the actual `Message-ID` header.
    *   Extracts the `Subject` (to ensure reply subject matches).
3.  **ThreadID Fallback**: If explicit message IDs are invalid/missing but `ThreadID` is provided:
    *   Fetches the **latest message** in that thread.
    *   Uses its `Message-ID` for `In-Reply-To`.
    *   Uses its `Subject`.

### Valid Header Construction

The client ensures:
- `In-Reply-To`: Space-separated list of parent message IDs.
- `References`: Space-separated list of entire thread chain.
- `Subject`: Matches original thread (adds `Re:` if missing).

## API Methods

### SendMessage

Sends a generated email. Handles MIME structure, attachments, and threading headers.

```go
func (s *Service) SendMessage(
    ctx context.Context,
    genEmail *models.GeneratedEmail,
    signatureHTML string,
    newAttachments []NewAttachment,
) (*gmail.Message, error)
```

### GetSignature

Fetches the user's signature from Gmail settings. Falls back to DB if API fails.

```go
func (s *Service) GetSignature(ctx context.Context, aliasEmail string) (string, error)
```

### WatchInbox / StopWatchingInbox

Manages Gmail Pub/Sub push notifications for the user's inbox.

```go
func (s *Service) WatchInbox(ctx context.Context, req *gmail.WatchRequest) (*gmail.WatchResponse, error)
func (s *Service) StopWatchingInbox(ctx context.Context) error
```

## Architecture

### MIME Construction

`SendMessage` builds a complex MIME structure to support:
- **HTML Body**: With signature appended.
- **Attachments**: Base64 decoded and attached.
- **Inline Images**: Detected via `Content-ID` and properly embedded.
- **Forwarding**: Preserves original message structure when forwarding (via `ForwardMessage` method).

### Error Handling

- **Resolution Failures**: If an ID cannot be resolved to an RFC ID, it logs a warning and attempts to use the ID as-is (fail-open).
- **API Errors**: Logs detailed Google API errors including response codes.

