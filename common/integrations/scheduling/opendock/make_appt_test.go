package opendock

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestPrepareRefNumbers(t *testing.T) {
	t.Run("Single RefNumber without comma", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "REF123",
		}
		prepareRefNumbers(&req, false)

		assert.Equal(t, "REF123", req.RefNumber)
		assert.Nil(t, req.RefNumbers)
	})

	t.Run("RefNumber with commas - keep both fields", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "REF123, REF456, REF789",
		}
		prepareRefNumbers(&req, false)

		assert.Equal(t, "REF123, REF456, REF789", req.RefNumber)
		assert.Equal(t, []string{"REF123", "REF456", "REF789"}, req.RefNumbers)
	})

	t.Run("RefNumber with commas - use array format", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "REF123, REF456, REF789",
		}
		prepareRefNumbers(&req, true)

		assert.Equal(t, "", req.RefNumber)
		assert.Equal(t, []string{"REF123", "REF456", "REF789"}, req.RefNumbers)
	})

	t.Run("Single RefNumber - convert to array format", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "REF123",
		}
		prepareRefNumbers(&req, true)

		assert.Equal(t, "", req.RefNumber)
		assert.Equal(t, []string{"REF123"}, req.RefNumbers)
	})

	t.Run("Empty RefNumber", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "",
		}
		prepareRefNumbers(&req, false)

		assert.Equal(t, "", req.RefNumber)
		assert.Nil(t, req.RefNumbers)
	})

	t.Run("RefNumber with extra whitespace", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "REF123 ,  REF456  , REF789",
		}
		prepareRefNumbers(&req, true)

		assert.Equal(t, "", req.RefNumber)
		assert.Equal(t, []string{"REF123", "REF456", "REF789"}, req.RefNumbers)
	})

	t.Run("Single RefNumber with whitespace - convert to array format", func(t *testing.T) {
		req := models.MakeAppointmentRequest{
			RefNumber: "  REF123  ",
		}
		prepareRefNumbers(&req, true)

		assert.Equal(t, "", req.RefNumber)
		assert.Equal(t, []string{"REF123"}, req.RefNumbers)
	})
}

func TestShouldRetryWithRefNumbers(t *testing.T) {
	t.Run("Error mentions refNumbers", func(t *testing.T) {
		err := errors.New("Please provide 'refNumbers' instead of 'refNumber'")
		assert.True(t, shouldRetryWithRefNumbers(err))
	})

	t.Run("Error mentions refNumbers case insensitive", func(t *testing.T) {
		err := errors.New("Please provide 'REFNUMBERS' INSTEAD of 'refNumber'")
		assert.True(t, shouldRetryWithRefNumbers(err))
	})

	t.Run("Error does not mention refNumbers", func(t *testing.T) {
		err := errors.New("Invalid warehouse ID")
		assert.False(t, shouldRetryWithRefNumbers(err))
	})

	t.Run("Nil error", func(t *testing.T) {
		assert.False(t, shouldRetryWithRefNumbers(nil))
	})

	t.Run("Error mentions refNumbers but not 'instead'", func(t *testing.T) {
		err := errors.New("refNumbers field is invalid")
		assert.False(t, shouldRetryWithRefNumbers(err))
	})
}
