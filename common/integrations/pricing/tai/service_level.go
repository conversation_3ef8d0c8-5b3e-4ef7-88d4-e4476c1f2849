package tai

type ServiceLevel string

const (
	ServiceLevel1DayFreight                    ServiceLevel = "1Day Freight"
	ServiceLevel2DayFreight                    ServiceLevel = "2Day Freight"
	ServiceLevel2ndDay                         ServiceLevel = "2nd Day"
	ServiceLevel3To5DayDeferred                ServiceLevel = "3 to 5 Day Deferred"
	ServiceLevel3DayFreight                    ServiceLevel = "3Day Freight"
	ServiceLevelAccelerated                    ServiceLevel = "Accelerated"
	ServiceLevelAirExpress                     ServiceLevel = "Air Express"
	ServiceLevelAuto                           ServiceLevel = "Auto"
	ServiceLevelCharter                        ServiceLevel = "Charter"
	ServiceLevelCourier                        ServiceLevel = "Courier"
	ServiceLevelCrossDock                      ServiceLevel = "Cross Dock"
	ServiceLevelDeferred                       ServiceLevel = "Deferred"
	ServiceLevelDrayage                        ServiceLevel = "Drayage"
	ServiceLevelEconomy                        ServiceLevel = "Economy"
	ServiceLevelExclusiveUse                   ServiceLevel = "Exclusive Use"
	ServiceLevelExpressSaver                   ServiceLevel = "Express Saver"
	ServiceLevelFirstFreight                   ServiceLevel = "First Freight"
	ServiceLevelFirstOvernight                 ServiceLevel = "First Overnight"
	ServiceLevelFridayOnlyPickup               ServiceLevel = "Friday Only Pickup"
	ServiceLevelGeneral                        ServiceLevel = "General"
	ServiceLevelGround                         ServiceLevel = "Ground"
	ServiceLevelGuaranteed4BusinessDays        ServiceLevel = "Guaranteed 4 Business Days"
	ServiceLevelGuaranteedBy2PM                ServiceLevel = "Guaranteed By 2PM"
	ServiceLevelGuaranteedBy330PM              ServiceLevel = "Guaranteed By 3:30PM"
	ServiceLevelGuaranteedBy3PM                ServiceLevel = "Guaranteed By 3PM"
	ServiceLevelGuaranteedBy5PM                ServiceLevel = "Guaranteed By 5PM"
	ServiceLevelGuaranteedByMorning            ServiceLevel = "Guaranteed By Morning"
	ServiceLevelGuaranteedByNoon               ServiceLevel = "Guaranteed By Noon"
	ServiceLevelGuaranteedEndOfDay             ServiceLevel = "Guaranteed End of Day"
	ServiceLevelGuaranteedExclusiveUse         ServiceLevel = "Guaranteed Exclusive Use"
	ServiceLevelGuaranteedHourlyWindow         ServiceLevel = "Guaranteed Hourly Window"
	ServiceLevelGuaranteedNextDay              ServiceLevel = "Guaranteed Next Day"
	ServiceLevelHandCarry                      ServiceLevel = "Hand Carry"
	ServiceLevelHomeDelivery                   ServiceLevel = "Home Delivery"
	ServiceLevelIntermodal                     ServiceLevel = "Intermodal"
	ServiceLevelLTLReeferChilled               ServiceLevel = "LTL Reefer Chilled"
	ServiceLevelLTLReeferFrozen                ServiceLevel = "LTL Reefer Frozen"
	ServiceLevelNextDay                        ServiceLevel = "Next Day"
	ServiceLevelNextDayByNoon                  ServiceLevel = "Next Day by Noon"
	ServiceLevelNextFlightOut                  ServiceLevel = "Next Flight Out"
	ServiceLevelNormal                         ServiceLevel = "Normal"
	ServiceLevelOversizePermits                ServiceLevel = "Oversize - Permits"
	ServiceLevelParcel                         ServiceLevel = "Parcel"
	ServiceLevelPartial                        ServiceLevel = "Partial"
	ServiceLevelPremium                        ServiceLevel = "Premium"
	ServiceLevelPriority                       ServiceLevel = "Priority"
	ServiceLevelPriorityNoon                   ServiceLevel = "Priority Noon"
	ServiceLevelPriorityOvernight              ServiceLevel = "Priority Overnight"
	ServiceLevelPuertoRicoPriority             ServiceLevel = "Puerto Rico Priority"
	ServiceLevelPuertoRicoStandard             ServiceLevel = "Puerto Rico Standard"
	ServiceLevelRail                           ServiceLevel = "Rail"
	ServiceLevelSameDay                        ServiceLevel = "Same Day"
	ServiceLevelSmartPost                      ServiceLevel = "Smart Post"
	ServiceLevelStandardOvernight              ServiceLevel = "Standard Overnight"
	ServiceLevelStandardTruckload              ServiceLevel = "Standard Truckload"
	ServiceLevelTCSA                           ServiceLevel = "TCSA"
	ServiceLevelTCSP                           ServiceLevel = "TCSP"
	ServiceLevelTCSW                           ServiceLevel = "TCSW"
	ServiceLevelTeamService                    ServiceLevel = "Team Service"
	ServiceLevelThreshold                      ServiceLevel = "Threshold"
	ServiceLevelTimeCritical                   ServiceLevel = "Time-Critical"
	ServiceLevelTradeshowOnly                  ServiceLevel = "Tradeshow Only"
	ServiceLevelTransit2ndDay                  ServiceLevel = "Transit: 2nd Day"
	ServiceLevelTransit3To5Days                ServiceLevel = "Transit: 3 to 5 Days"
	ServiceLevelTransitDeferred                ServiceLevel = "Transit: Deferred"
	ServiceLevelTransitNextDay                 ServiceLevel = "Transit: Next Day"
	ServiceLevelTSTGuaranteed                  ServiceLevel = "TST Guaranteed"
	ServiceLevelTwoDay                         ServiceLevel = "Two Day"
	ServiceLevelTwoDayAM                       ServiceLevel = "Two Day AM"
	ServiceLevelUrgentCareArrivalDayGuaranteed ServiceLevel = "Urgent Care Arrival Day Guaranteed"
	ServiceLevelVolume                         ServiceLevel = "Volume"
	ServiceLevelVolumeExclusiveUse             ServiceLevel = "Volume: Exclusive Use"
	ServiceLevelVolumeGuaranteedEconomy        ServiceLevel = "Volume: Guaranteed Economy"
	ServiceLevelVolumeGuaranteedExclusiveUse   ServiceLevel = "Volume: Guaranteed Exclusive Use"
	ServiceLevelVolumeGuaranteedStandard       ServiceLevel = "Volume: Guaranteed Standard"
	ServiceLevelVolumeTruckloadExempt          ServiceLevel = "Volume: Truckload Exempt"
	ServiceLevelVolumeTruckloadService         ServiceLevel = "Volume: Truckload Service"
	ServiceLevelWhiteGlove                     ServiceLevel = "White Glove"
	ServiceLevelWhiteGloveAssembly             ServiceLevel = "White Glove Assembly"
	ServiceLevelWhiteGloveDelivery             ServiceLevel = "White Glove Delivery"
	ServiceLevelWhiteGloveOutside              ServiceLevel = "White Glove Outside"
	ServiceLevelWhiteGlovePickup               ServiceLevel = "White Glove Pickup"
	ServiceLevelWhiteGloveRoomOfChoice         ServiceLevel = "White Glove Room of Choice"
	ServiceLevelWhiteGloveThreshold            ServiceLevel = "White Glove Threshold"
	ServiceLevelWhiteGloveThreshold2ndFloor    ServiceLevel = "White Glove Threshold 2nd Floor"
	ServiceLevelWhiteGloveUnattended           ServiceLevel = "White Glove Unattended"
)

func (c *Client) GetStaticServiceLevels() []string {
	return []string{
		string(ServiceLevel1DayFreight),
		string(ServiceLevel2DayFreight),
		string(ServiceLevel2ndDay),
		string(ServiceLevel3To5DayDeferred),
		string(ServiceLevel3DayFreight),
		string(ServiceLevelAccelerated),
		string(ServiceLevelAirExpress),
		string(ServiceLevelAuto),
		string(ServiceLevelCharter),
		string(ServiceLevelCourier),
		string(ServiceLevelCrossDock),
		string(ServiceLevelDeferred),
		string(ServiceLevelDrayage),
		string(ServiceLevelEconomy),
		string(ServiceLevelExclusiveUse),
		string(ServiceLevelExpressSaver),
		string(ServiceLevelFirstFreight),
		string(ServiceLevelFirstOvernight),
		string(ServiceLevelFridayOnlyPickup),
		string(ServiceLevelGeneral),
		string(ServiceLevelGround),
		string(ServiceLevelGuaranteed4BusinessDays),
		string(ServiceLevelGuaranteedBy2PM),
		string(ServiceLevelGuaranteedBy330PM),
		string(ServiceLevelGuaranteedBy3PM),
		string(ServiceLevelGuaranteedBy5PM),
		string(ServiceLevelGuaranteedByMorning),
		string(ServiceLevelGuaranteedByNoon),
		string(ServiceLevelGuaranteedEndOfDay),
		string(ServiceLevelGuaranteedExclusiveUse),
		string(ServiceLevelGuaranteedHourlyWindow),
		string(ServiceLevelGuaranteedNextDay),
		string(ServiceLevelHandCarry),
		string(ServiceLevelHomeDelivery),
		string(ServiceLevelIntermodal),
		string(ServiceLevelLTLReeferChilled),
		string(ServiceLevelLTLReeferFrozen),
		string(ServiceLevelNextDay),
		string(ServiceLevelNextDayByNoon),
		string(ServiceLevelNextFlightOut),
		string(ServiceLevelNormal),
		string(ServiceLevelOversizePermits),
		string(ServiceLevelParcel),
		string(ServiceLevelPartial),
		string(ServiceLevelPremium),
		string(ServiceLevelPriority),
		string(ServiceLevelPriorityNoon),
		string(ServiceLevelPriorityOvernight),
		string(ServiceLevelPuertoRicoPriority),
		string(ServiceLevelPuertoRicoStandard),
		string(ServiceLevelRail),
		string(ServiceLevelSameDay),
		string(ServiceLevelSmartPost),
		string(ServiceLevelStandardOvernight),
		string(ServiceLevelStandardTruckload),
		string(ServiceLevelTCSA),
		string(ServiceLevelTCSP),
		string(ServiceLevelTCSW),
		string(ServiceLevelTeamService),
		string(ServiceLevelThreshold),
		string(ServiceLevelTimeCritical),
		string(ServiceLevelTradeshowOnly),
		string(ServiceLevelTransit2ndDay),
		string(ServiceLevelTransit3To5Days),
		string(ServiceLevelTransitDeferred),
		string(ServiceLevelTransitNextDay),
		string(ServiceLevelTSTGuaranteed),
		string(ServiceLevelTwoDay),
		string(ServiceLevelTwoDayAM),
		string(ServiceLevelUrgentCareArrivalDayGuaranteed),
		string(ServiceLevelVolume),
		string(ServiceLevelVolumeExclusiveUse),
		string(ServiceLevelVolumeGuaranteedEconomy),
		string(ServiceLevelVolumeGuaranteedExclusiveUse),
		string(ServiceLevelVolumeGuaranteedStandard),
		string(ServiceLevelVolumeTruckloadExempt),
		string(ServiceLevelVolumeTruckloadService),
		string(ServiceLevelWhiteGlove),
		string(ServiceLevelWhiteGloveAssembly),
		string(ServiceLevelWhiteGloveDelivery),
		string(ServiceLevelWhiteGloveOutside),
		string(ServiceLevelWhiteGlovePickup),
		string(ServiceLevelWhiteGloveRoomOfChoice),
		string(ServiceLevelWhiteGloveThreshold),
		string(ServiceLevelWhiteGloveThreshold2ndFloor),
		string(ServiceLevelWhiteGloveUnattended),
	}
}
