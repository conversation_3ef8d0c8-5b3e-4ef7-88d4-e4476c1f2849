package tai

import (
	"context"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

const searchCustomerStaffEndpoint = "/api/CustomerStaff/Search"

type CustomerStaff struct {
	StaffID                       int     `json:"staffId"`
	Login                         string  `json:"login"`
	ContactName                   string  `json:"contactName"`
	Email                         string  `json:"email"`
	CustomerOrganizationID        int     `json:"customerOrganizationId"`
	CustomerOrganizationName      string  `json:"customerOrganizationName"`
	BrokerOrganizationID          int     `json:"brokerOrganizationId"`
	BrokerOrganizationName        string  `json:"brokerOrganizationName"`
	BrokerOrganizationPhoneNumber string  `json:"brokerOrganizationPhoneNumber"`
	Phone                         string  `json:"phone"`
	Fax                           string  `json:"fax"`
	Enabled                       int     `json:"enabled"`
	StreetAddress                 string  `json:"streetAddress"`
	City                          string  `json:"city"`
	State                         string  `json:"state"`
	ZipCode                       string  `json:"zipCode"`
	CreditLimit                   float64 `json:"creditLimit"`
	Notes                         string  `json:"notes"`
	DimensionsRequired            bool    `json:"dimensionsRequired"`
	NmfcRequired                  bool    `json:"nmfcRequired"`
}

// SearchCustomerStaff fetches staff for a given customer organization.
// enabled=true maps to enabled=1; enabled=false maps to enabled=0.
func (c *Client) SearchCustomerStaff(
	ctx context.Context,
	customerOrganizationID int,
	customerOrganizationName string,
	enabled bool,
) ([]models.TMSCustomer, error) {

	q := url.Values{}
	q.Set("customerOrganizationId", strconv.Itoa(customerOrganizationID))
	q.Set("customerOrganizationName", customerOrganizationName)
	if enabled {
		q.Set("enabled", "1")
	} else {
		q.Set("enabled", "0")
	}

	var customerReps []CustomerStaff
	_, err := c.get(ctx, searchCustomerStaffEndpoint, q, &customerReps)
	if err != nil {
		return nil, err
	}

	out := make([]models.TMSCustomer, 0, len(customerReps))
	for _, c := range customerReps {
		if c.BrokerOrganizationID < 0 {
			continue
		}

		tmsc := models.TMSCustomer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Email: c.Email,
			},
			OwnerName:        c.ContactName,
			OwnerID:          c.StaffID,
			TMSIntegrationID: uint(c.BrokerOrganizationID),
		}

		out = append(out, tmsc)
	}

	return out, nil
}
