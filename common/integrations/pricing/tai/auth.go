package tai

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	loginEndpoint            = "/Account/Login"
	processLoginEndpoint     = "/Account/ProcessLogin"
	backOfficePath           = "/back-office"
	getAuthenticatedEndpoint = "/account/GetAuthenticated"
)

type (
	LoginCredentials struct {
		Password string `json:"Password"`
		UserName string `json:"UserName"`
	}
)

// ensureHTTPClient guarantees an http.Client with cookie jar.
func (c *Client) ensureHTTPClient() error {
	if c.HTTPClient != nil && c.HTTPClient.Jar != nil {
		return nil
	}

	jar, err := cookiejar.New(&cookiejar.Options{
		PublicSuffixList: publicsuffix.List,
	})
	if err != nil {
		return fmt.Errorf("failed to create cookie jar: %w", err)
	}

	client := otel.TracingHTTPClient(60 * time.Second)
	client.Jar = jar

	c.HTTPClient = client

	return nil
}

// buildBaseURL safely constructs a base *url.URL.
func (c *Client) buildBaseURL() (*url.URL, error) {
	if c.Integration.Tenant == "" {
		return nil, errors.New("missing tenant")
	}

	return &url.URL{
		Scheme: "https",
		Host:   c.Integration.Tenant,
	}, nil
}

// postLogin performs the initial POST /Account/Login with credentials.
// This endpoint responds 302 and sets the .ASPXAUTH cookie.
func (c *Client) postLogin(ctx context.Context, base *url.URL, username, password string) (*http.Response, error) {
	u := *base
	u.Path = loginEndpoint

	log.Debug(ctx, "Tai auth: posting to /Account/Login", zap.String("url", u.String()))

	form := url.Values{}
	form.Set("UserName", username)
	form.Set("Password", password)
	// Return to back-office after auth; harmless if ignored.
	form.Set("ReturnUrl", backOfficePath)

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u.String(), strings.NewReader(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create login POST request: %w", err)
	}
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36:")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Priority", "u=0, i")
	req.Header.Set("Origin", fmt.Sprintf("https://%s", base.Host))
	req.Header.Set("Referer", fmt.Sprintf("https://%s/", base.Host))

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("login POST request failed: %w", err)
	}

	log.Debug(
		ctx,
		"Tai auth: /Account/Login response",
		zap.Int("statusCode", resp.StatusCode),
		zap.String("location", resp.Header.Get("Location")),
	)

	return resp, nil
}

// postProcessLogin mirrors the browser's GET /Account/ProcessLogin step.
// The .ASPXAUTH cookie issued in the previous step must be present.
func (c *Client) postProcessLogin(ctx context.Context, base *url.URL) (*http.Response, error) {
	u := *base
	u.Path = processLoginEndpoint

	log.Debug(ctx, "Tai auth: GET /Account/ProcessLogin", zap.String("url", u.String()))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create ProcessLogin GET: %w", err)
	}
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36:")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Priority", "u=0, i")
	req.Header.Set("Referer", fmt.Sprintf("https://%s/", base.Host))

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("ProcessLogin GET failed: %w", err)
	}

	log.Debug(ctx, "Tai auth: ProcessLogin response", zap.Int("statusCode", resp.StatusCode))

	return resp, nil
}

// finalizeBackOffice performs GET /back-office to complete session setup.
func (c *Client) finalizeBackOffice(ctx context.Context, base *url.URL) error {
	u := *base
	u.Path = backOfficePath

	log.Debug(ctx, "Tai auth: loading back-office", zap.String("url", u.String()))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed creating back-office GET: %w", err)
	}
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36:")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Priority", "u=0, i")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("back-office GET failed: %w", err)
	}
	defer resp.Body.Close()

	log.Debug(ctx, "Tai auth: back-office response", zap.Int("statusCode", resp.StatusCode))

	//nolint:errcheck
	_, _ = io.Copy(io.Discard, resp.Body)
	return nil
}

// verifyAuthenticated performs GET /account/GetAuthenticated to confirm success.
func (c *Client) verifyAuthenticated(ctx context.Context, base *url.URL) error {
	u := *base
	u.Path = getAuthenticatedEndpoint

	log.Debug(ctx, "Tai auth: verifying session with GetAuthenticated", zap.String("url", u.String()))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed creating GetAuthenticated GET: %w", err)
	}
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Referer", base.Scheme+"://"+base.Host+backOfficePath)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("GetAuthenticated request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed reading GetAuthenticated response: %w", err)
	}

	log.Debug(
		ctx,
		"Tai auth: GetAuthenticated response",
		zap.Int("statusCode", resp.StatusCode),
		zap.String("body", string(body)),
	)

	if !strings.Contains(string(body), "true") {
		return fmt.Errorf("GetAuthenticated did not confirm success, got: %s", string(body))
	}

	return nil
}

// requireASPXAuthCookie ensures the .ASPXAUTH cookie exists either on the
// immediate response (Set-Cookie) or already persisted in the jar.
func (c *Client) requireASPXAuthCookie(ctx context.Context, base *url.URL, step string, resp *http.Response) error {
	// Check response cookies aka Set-Cookie
	if resp != nil {
		if ck := findCookie(resp.Cookies(), ".ASPXAUTH"); ck != nil {
			log.Debug(ctx, "Tai auth: detected .ASPXAUTH cookie on response", zap.String("step", step))
			return nil
		}
	}

	// Fallback: check jar aka persisted cookies
	if ck := findCookie(c.HTTPClient.Jar.Cookies(base), ".ASPXAUTH"); ck != nil {
		log.Debug(ctx, "Tai auth: detected .ASPXAUTH cookie in jar", zap.String("step", step))
		return nil
	}

	names := cookieNames(c.HTTPClient.Jar.Cookies(base))
	return fmt.Errorf("missing .ASPXAUTH cookie at step %s (have: %v)", step, names)
}

func findCookie(all []*http.Cookie, name string) *http.Cookie {
	for _, c := range all {
		if c != nil && c.Name == name {
			return c
		}
	}

	return nil
}

func cookieNames(all []*http.Cookie) []string {
	out := make([]string, 0, len(all))
	for _, c := range all {
		if c != nil {
			out = append(out, c.Name)
		}
	}

	return out
}

// authenticate performs the Tai authentication workflow
// NOTE: this only works for Trident; other customers may have a different auth flow, which we've seen.
func (c *Client) authenticate(ctx context.Context) (models.PricingOnBoardResp, error) {
	password, err := crypto.DecryptAESGCM(ctx, string(c.Integration.EncryptedPassword), nil)
	if err != nil {
		return models.PricingOnBoardResp{}, fmt.Errorf("error decrypting password: %w", err)
	}

	log.Debug(
		ctx,
		"Tai auth: starting authentication sequence",
		zap.String("tenant", c.Integration.Tenant),
		zap.String("user", c.Integration.Username),
	)

	if err := c.ensureHTTPClient(); err != nil {
		return models.PricingOnBoardResp{}, err
	}

	log.Debug(ctx, "Tai auth: HTTP client ready with cookie jar")

	base, err := c.buildBaseURL()
	if err != nil {
		return models.PricingOnBoardResp{}, err
	}

	// Temporarily disable following redirects so we can inspect the 302 and its Set-Cookie
	origRedirect := c.HTTPClient.CheckRedirect
	c.HTTPClient.CheckRedirect = func(_ *http.Request, _ []*http.Request) error {
		return http.ErrUseLastResponse
	}
	defer func() { c.HTTPClient.CheckRedirect = origRedirect }()

	loginResp, err := c.postLogin(ctx, base, c.Integration.Username, password)
	if err != nil {
		return models.PricingOnBoardResp{}, err
	}
	defer loginResp.Body.Close()
	//nolint:errcheck
	_, _ = io.Copy(io.Discard, loginResp.Body)

	// Enforce .ASPXAUTH presence.
	if err := c.requireASPXAuthCookie(ctx, base, "login", loginResp); err != nil {
		return models.PricingOnBoardResp{}, err
	}

	c.Cookies = c.HTTPClient.Jar.Cookies(base)

	plResp, err := c.postProcessLogin(ctx, base)
	if err != nil {
		return models.PricingOnBoardResp{}, err
	}
	defer plResp.Body.Close()
	//nolint:errcheck
	_, _ = io.Copy(io.Discard, plResp.Body)

	if err := c.finalizeBackOffice(ctx, base); err != nil {
		log.Warn(ctx, "back-office finalize failed", zap.Error(err))
	}

	if err := c.verifyAuthenticated(ctx, base); err != nil {
		return models.PricingOnBoardResp{}, fmt.Errorf("authentication validation failed: %w", err)
	}

	log.Debug(
		ctx,
		"Tai auth: authentication sequence completed successfully",
		zap.Int("cookieCount", len(c.Cookies)),
	)

	return models.PricingOnBoardResp{
		Username:          c.Integration.Username,
		EncryptedPassword: c.Integration.EncryptedPassword,
	}, nil
}
