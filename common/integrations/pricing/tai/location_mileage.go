package tai

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

const mileageByZipCodesEndpoint = "/api/location/mileageByZipCodes"

type MileageResponse struct {
	Mileage float64 `json:"mileage"`
}

// GetMileageByZipCodes returns the mileage between origin and destination.
func (c *Client) GetMileageByZipCodes(
	ctx context.Context,
	customerOrganizationID,
	pcmilerRoutingType int,
	origin,
	destination models.Address,
) (float64, error) {

	normalizedOrigin, err := normalizeLocation(ctx, origin.City, origin.State, origin.Zip)
	if err != nil {
		return 0, fmt.Errorf("failed to normalize origin for mileage lookup: %w", err)
	}

	normalizedDest, err := normalizeLocation(ctx, destination.City, destination.State, destination.Zip)
	if err != nil {
		return 0, fmt.Errorf("failed to normalize destination for mileage lookup: %w", err)
	}

	q := url.Values{}
	q.Set("customerOrganizationId", strconv.Itoa(customerOrganizationID))
	q.Set("pcmilerRoutingType", strconv.Itoa(pcmilerRoutingType))
	q.Set("originCountry", normalizedOrigin.Country)
	q.Set("originZipCode", normalizedOrigin.Zip)
	q.Set("destinationCountry", normalizedDest.Country)
	q.Set("destinationZipCode", normalizedDest.Zip)

	var out MileageResponse
	_, err = c.do(ctx, http.MethodGet, mileageByZipCodesEndpoint, q, nil, &out)
	if err != nil {
		return 0, err
	}

	return out.Mileage, nil
}
