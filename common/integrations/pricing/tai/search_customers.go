package tai

import (
	"context"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

const typeAheadEndpoint = "/api/customer/typeahead"

type (
	Customer struct {
		OrganizationType            int      `json:"organizationType"`
		OrganizationTypeDisplayName string   `json:"organizationTypeDisplayName"`
		Tokens                      []string `json:"tokens"`
		ShortName                   string   `json:"shortName"`
		ID                          int      `json:"id"`
		Name                        string   `json:"name"`
	}
)

// SearchCustomers searches customers by a free-text query.
func (c *Client) SearchCustomers(ctx context.Context, query string) ([]models.TMSCustomer, error) {
	q := url.Values{}
	q.Set("query", query)

	var customers []Customer
	_, err := c.get(ctx, typeAheadEndpoint, q, &customers)
	if err != nil {
		return nil, err
	}

	out := make([]models.TMSCustomer, 0, len(customers))
	for _, c := range customers {
		tmsc := models.TMSCustomer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:          c.Name,
				ExternalTMSID: strconv.Itoa(c.ID),
			},
		}

		out = append(out, tmsc)
	}

	return out, nil
}
