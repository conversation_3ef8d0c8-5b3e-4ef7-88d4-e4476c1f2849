package main

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/pricing/tai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type envVars struct {
	Username string `envconfig:"TAICLOUD_USERNAME" required:"true"`
	Password string `envconfig:"TAICLOUD_PASSWORD" required:"true"`
	Tenant   string `envconfig:"TAICLOUD_TENANT" required:"true"`
}

var env envVars

// Tests the whole Tai workflow.
// Steps:
//  1. Authenticate
//  2. Search customers
//  3. Search customer staff
//  4. Get mileage between stops
//  5. Get accessorials (POST with required IDs)
//  6. Get LTL quotes
//  7. Log all data
func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	if err := loadEnv(ctx); err != nil {
		log.Fatal(ctx, "loadEnv failed", zap.Error(err))
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, env.Password, nil)
	if err != nil {
		log.Fatal(ctx, "failed encrypting env password", zap.Error(err))
	}

	intg := models.Integration{
		Name:              models.TaiPricing,
		Tenant:            env.Tenant,
		Username:          env.Username,
		EncryptedPassword: []byte(encryptedPassword),
	}

	client, err := tai.New(ctx, intg, env.Username)
	if err != nil {
		log.Fatal(ctx, "failed initializing TAI client", zap.Error(err))
	}

	log.Info(ctx, "Auth successful, cookie jar populated", zap.Int("cookieCount", len(client.Cookies)))

	query := "food"
	customers, err := client.SearchCustomers(ctx, query)
	if err != nil {
		log.Fatal(ctx, "SearchCustomers failed", zap.Error(err))
	}

	log.Info(ctx, "SearchCustomers finished", zap.Int("results", len(customers)))
	for i, c := range customers {
		log.Info(
			ctx,
			"customer",
			zap.Int("index", i+1),
			zap.String("id", c.ExternalTMSID),
			zap.String("name", c.Name),
		)
	}

	if len(customers) == 0 {
		log.Fatal(ctx, "expected at least one customer result for query", zap.String("query", query))
	}

	selectedCustomer := customers[0]
	customerOrgID, err := strconv.Atoi(selectedCustomer.ExternalTMSID)
	if err != nil {
		log.Fatal(
			ctx,
			"failed getting customer org ID",
			zap.String("customerOrgID", selectedCustomer.ExternalTMSID),
		)
	}

	selectedName := selectedCustomer.Name
	staff, err := client.SearchCustomerStaff(ctx, customerOrgID, selectedName, true)
	if err != nil {
		log.Fatal(ctx, "SearchCustomerStaff failed", zap.Error(err))
	}

	log.Info(ctx, "SearchCustomerStaff completed", zap.Any("count", len(staff)))

	for i, s := range staff {
		log.Info(
			ctx,
			"Customer staff",
			zap.Int("index", i+1),
			zap.String("email", s.Email),
			zap.String("owner", s.OwnerName),
			zap.Uint("brokerOrgID", s.TMSIntegrationID),
		)
	}

	if len(staff) == 0 {
		log.Fatal(
			ctx,
			"no customer staff returned; this may be valid if tenant has no reps",
			zap.String("customerOrganizationName", selectedName),
			zap.Int("customerOrganizationID", customerOrgID),
		)
	}

	log.Info(ctx, "SearchCustomerStaff returned staff successfully")

	origin := models.Address{
		Zip:     "20152",
		Country: "USA",
	}
	destination := models.Address{
		Zip:     "20170",
		Country: "USA",
	}

	pcmilerRoutingType := 0

	mileage, err := client.GetMileageByZipCodes(
		ctx,
		customerOrgID,
		pcmilerRoutingType,
		origin,
		destination,
	)
	if err != nil {
		log.Fatal(ctx, "GetMileageByZipCodes failed", zap.Error(err))
	}

	if mileage <= 0 {
		log.Fatal(ctx, "expected positive mileage result", zap.Float64("mileage", mileage))
	}

	log.Info(
		ctx,
		"GetMileageByZipCodes completed",
		zap.String("originZip", origin.Zip),
		zap.String("destinationZip", destination.Zip),
		zap.Float64("mileage", mileage),
	)

	if staff[0].TMSIntegrationID > math.MaxInt {
		log.Fatal(ctx, "brokerID exceeds max int value", zap.Uint("brokerID", staff[0].TMSIntegrationID))
	}
	// BrokerID and ShipmentType may differ per tenant; defaults below mirrors Trident's access
	//nolint:gosec // G115: safe conversion, previously bounds-checked
	brokerID := int(staff[0].TMSIntegrationID)
	shipmentType := 2

	start := time.Now()
	accessorials, err := client.GetCustomerAccessorials(ctx, customerOrgID, brokerID, shipmentType)
	if err != nil {
		log.Fatal(ctx, "GetCustomerAccessorials failed", zap.Error(err))
	}

	log.Info(
		ctx,
		"GetCustomerAccessorials completed",
		zap.Int("count", len(accessorials)),
		zap.Duration("duration", time.Since(start)),
	)

	for i, a := range accessorials {
		if i > 20 {
			break
		}

		log.Info(
			ctx,
			"Accessorial item",
			zap.Int("index", i+1),
			zap.Int("id", a.ExternalID),
			zap.String("code", a.Code),
			zap.String("name", a.Name),
			zap.Any("categories", a.CategoryTags),
			zap.Any("actions", a.Actions),
		)
	}

	if len(accessorials) == 0 {
		log.Fatal(
			ctx,
			"expected at least one accessorial returned for broker/customer",
			zap.Int("brokerID", brokerID),
			zap.Int("customerOrgID", customerOrgID),
		)
	}

	now := time.Now()
	pickupReady := now.Format("2006-01-02T15:04:05")
	pickupClose := now.Add(8 * time.Hour).Format("2006-01-02T15:04:05")
	if staff[0].ID > math.MaxInt {
		log.Fatal(ctx, "staffID exceeds max int value", zap.Uint("staffID", staff[0].ID))
	}
	//nolint:gosec // G115: safe conversion, previously bounds-checked
	staffID := int(staff[0].ID)

	reqQQ := tai.LTLQuickQuoteRequest{
		ShipmentID:         0,
		OriginCity:         "CHANTILLY",
		OriginState:        "VA",
		OriginZip:          origin.Zip,
		OriginCountry:      origin.Country,
		DestinationCity:    "HERNDON",
		DestinationState:   "VA",
		DestinationZip:     destination.Zip,
		DestinationCountry: destination.Country,
		LineItems: []tai.QuickQuoteLineItem{
			{
				PackageType:           120,
				CubicFeet:             "0.00",
				CubicMeters:           "0.00",
				PoundsPerCubicFeet:    "0.00",
				EstimatedFreightClass: "500",
				FreightClass:          "70",
				ClassManuallySelected: true,
				CubicFeetChanged:      false,
				QuantityOfGoods:       1,
				TotalWeight:           12,
				CommodityDescription:  "wood",
				HandlingUnits:         0,
			},
		},
		BrokerID:            brokerID,
		CustomerStaffID:     staffID,
		WeightUnit:          0,
		DimensionUnit:       0,
		SearchByTariff:      false,
		Stackable:           false,
		ShipmentType:        2,
		ActualShipmentType:  2,
		TrailerType:         40,
		PickupReadyDateTime: pickupReady,
		PickupCloseDateTime: pickupClose,
		Mileage:             mileage,
		Accessorials:        []int{68}, // Airport PU
		IsFrontOffice:       false,
		DeclaredValue:       "",
		DeductibleValue:     0,
	}

	quotes, err := client.GetLTLQuickQuotes(ctx, reqQQ)
	if err != nil {
		log.Fatal(ctx, "GetLTLQuickQuote failed", zap.Error(err))
	}

	log.Info(ctx, "GetLTLQuickQuote", zap.Int("count", len(quotes)))

	for i, q := range quotes {
		if !q.IsError {
			log.Info(
				ctx,
				"Quote (success)",
				zap.Int("index", i+1),
				zap.String("carrier", q.CarrierName),
				zap.String("scac", q.CarrierSCAC),
				zap.Int("transitDays", q.TransitDays),
				zap.String("serviceLevel", q.ServiceLevel),
				zap.Float64("buyTotal", q.TotalCost),
				zap.Int("finalSell", q.FinalQuotePrice),
				zap.Int("finalBuy", q.FinalCarrierCost),
				zap.Int("finalMargin", q.FinalMargin),
			)
		} else {
			log.Info(
				ctx,
				"Quote (failure)",
				zap.Int("index", i+1),
				zap.Bool("isError", q.IsError),
				zap.String("errorMessage", q.ErrorMessage),
				zap.Bool("notServiced", q.NotServiced),
				zap.String("carrier", q.CarrierName),
				zap.String("scac", q.CarrierSCAC),
				zap.Int("transitDays", q.TransitDays),
				zap.String("serviceLevel", q.ServiceLevel),
				zap.Float64("buyTotal", q.TotalCost),
				zap.Int("finalSell", q.FinalQuotePrice),
				zap.Int("finalBuy", q.FinalCarrierCost),
				zap.Int("finalMargin", q.FinalMargin),
			)
		}
	}

	log.Info(
		ctx,
		"Tai flow completed (Auth -> Search -> Accessorials -> SelectCustomer -> Mileage -> LTLQuickQuote)",
	)
}

func loadEnv(ctx context.Context) error {
	if err := godotenv.Load(); err != nil {
		log.Warn(ctx, "no .env file present", zap.Error(err))
	}

	if err := envconfig.Process("", &env); err != nil {
		return fmt.Errorf("failed parsing Tai env vars: %w", err)
	}

	return nil
}
