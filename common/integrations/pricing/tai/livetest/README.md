# Tai Pricing Live Test
This script is a helpful way to quickly test the Taicloud Pricing integration directly without having to go through beacon-api.

Create an `.env` file in the same directory:

```shell
TAICLOUD_USERNAME=...
TAICLOUD_PASSWORD=...
TAICLOUD_TENANT=...
```

Fetch the Trident Transport's Taicloud username/password from 1Password.

then run the script:

```shell
FORCE_GEN_AES_KEY=true go run .
```
