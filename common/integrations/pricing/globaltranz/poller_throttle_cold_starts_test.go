package globaltranz

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestGetThrottleInfo_StartOfInterval(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Calculate what the key will be based on current time
	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()
	var intervalStart time.Time
	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)
	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
	}
	expectedKey := getCurrentOffPeakIntervalKey(intervalStart)

	// Expect Redis Get call (key doesn't exist yet)
	mock.ExpectGet(expectedKey).RedisNil()

	ctx := context.Background()
	target, processed, remaining, err := GetThrottleInfo(ctx)

	require.NoError(t, err)

	assert.GreaterOrEqual(t, target, 0, "target should be non-negative")
	assert.Equal(t, int64(0), processed, "processed should be 0 when key doesn't exist")
	assert.GreaterOrEqual(t, remaining, 0, "remaining should be non-negative")

	require.NoError(t, mock.ExpectationsWereMet())
}

func TestGetThrottleInfo_MidInterval(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Calculate what the key will be based on current time
	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()
	var intervalStart time.Time
	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)
	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
	}
	expectedKey := getCurrentOffPeakIntervalKey(intervalStart)

	mock.ExpectGet(expectedKey).SetVal("300")

	ctx := context.Background()
	target, processed, remaining, err := GetThrottleInfo(ctx)

	require.NoError(t, err)
	assert.Equal(t, int64(300), processed, "processed should match Redis value")

	expectedRemaining := target - int(processed)
	if expectedRemaining < 0 {
		expectedRemaining = 0
	}
	assert.Equal(t, expectedRemaining, remaining, "remaining should be target minus processed (clamped to 0)")
	assert.GreaterOrEqual(t, remaining, 0, "remaining should be non-negative")

	require.NoError(t, mock.ExpectationsWereMet())
}

func TestGetThrottleInfo_EndOfInterval(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Calculate what the key will be based on current time
	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()
	var intervalStart time.Time
	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)
	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
	}
	expectedKey := getCurrentOffPeakIntervalKey(intervalStart)

	mock.ExpectGet(expectedKey).SetVal("1200")

	ctx := context.Background()
	target, processed, remaining, err := GetThrottleInfo(ctx)

	require.NoError(t, err)
	assert.Equal(t, int64(1200), processed, "processed should match Redis value")
	// Target should be capped at max
	assert.LessOrEqual(t, target, maxCombinationsPerOffPeakInterval, "target should be capped at max")

	expectedRemaining := target - int(processed)
	if expectedRemaining < 0 {
		expectedRemaining = 0
	}
	assert.Equal(t, expectedRemaining, remaining, "remaining should be target minus processed (clamped to 0)")
	assert.GreaterOrEqual(t, remaining, 0, "remaining should be non-negative")

	require.NoError(t, mock.ExpectationsWereMet())
}

func TestGetThrottleInfo_DailyLimitReached(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	mockRDB, mock := redismock.NewClientMock()
	originalRDB := rediscommon.RDB
	rediscommon.RDB = mockRDB
	defer func() {
		rediscommon.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	// Calculate what the key will be based on current time
	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()
	var intervalStart time.Time
	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)
	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
	}
	expectedKey := getCurrentOffPeakIntervalKey(intervalStart)

	mock.ExpectGet(expectedKey).SetVal("1400")

	ctx := context.Background()
	target, processed, remaining, err := GetThrottleInfo(ctx)

	require.NoError(t, err)
	assert.Equal(t, int64(1400), processed, "processed should match Redis value")
	// Target should be capped at max
	assert.LessOrEqual(t, target, maxCombinationsPerOffPeakInterval, "target should be capped at max")

	assert.Equal(t, 0, remaining, "remaining should be 0 when limit is reached")

	require.NoError(t, mock.ExpectationsWereMet())
}

func TestGetCurrentOffPeakIntervalKey_After6PM(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	// Test with time after 6PM
	testTime := time.Date(2024, 1, 15, 19, 0, 0, 0, etLocation) // 7:00 PM on 2024-01-15

	key := getCurrentOffPeakIntervalKey(testTime)

	expectedKey := "globaltranz-lane-history-cold-start-count-2024-01-15"
	assert.Equal(t, expectedKey, key, "key should use today's date after 6PM")
}

func TestGetCurrentOffPeakIntervalKey_Before8AM(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	// Test with interval start from previous day (before 8AM scenario)
	// Interval started on 2024-01-15 at 6PM
	intervalStart := time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation)

	key := getCurrentOffPeakIntervalKey(intervalStart)

	expectedKey := "globaltranz-lane-history-cold-start-count-2024-01-15"
	assert.Equal(t, expectedKey, key, "key should use previous day's date before 8AM")
}

func TestGetHoursSinceIntervalStart_Calculation(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	// Test calculation: 12:00 AM - 6:00 PM = 6 hours
	intervalStart := time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation) // 6:00 PM
	nowET := time.Date(2024, 1, 16, 0, 0, 0, 0, etLocation)          // 12:00 AM (next day)

	hours := getHoursSinceIntervalStart(nowET, intervalStart)

	assert.Equal(t, 6.0, hours, "should calculate 6 hours between 6PM and midnight")
}

func TestThrottleInfo_IntervalBoundary(t *testing.T) {
	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	tests := []struct {
		name          string
		testTime      time.Time
		expectedStart time.Time
	}{
		{
			name:          "before interval (5:59 PM)",
			testTime:      time.Date(2024, 1, 15, 17, 59, 0, 0, etLocation),
			expectedStart: time.Date(2024, 1, 14, 18, 0, 0, 0, etLocation), // Previous day 6PM
		},
		{
			name:          "interval start (6:00 PM)",
			testTime:      time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation),
			expectedStart: time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation), // Today 6PM
		},
		{
			name:          "interval end (8:00 AM)",
			testTime:      time.Date(2024, 1, 16, 8, 0, 0, 0, etLocation),
			expectedStart: time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation), // Previous day 6PM
		},
		{
			name:          "after interval (8:01 AM)",
			testTime:      time.Date(2024, 1, 16, 8, 1, 0, 0, etLocation),
			expectedStart: time.Date(2024, 1, 15, 18, 0, 0, 0, etLocation), // Previous day 6PM
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hourET := tt.testTime.Hour()
			var intervalStart time.Time

			if hourET >= offPeakStartHour {
				// After 6PM, interval started today at 6PM
				intervalStart = time.Date(
					tt.testTime.Year(),
					tt.testTime.Month(),
					tt.testTime.Day(),
					offPeakStartHour,
					0,
					0,
					0,
					etLocation,
				)
			} else {
				// Before 8AM, interval started yesterday at 6PM
				intervalStart = time.Date(
					tt.testTime.Year(),
					tt.testTime.Month(),
					tt.testTime.Day(),
					offPeakStartHour,
					0,
					0,
					0,
					etLocation,
				).Add(-24 * time.Hour)
			}

			assert.Equal(t, tt.expectedStart, intervalStart, "interval start should be calculated correctly")
		})
	}
}
