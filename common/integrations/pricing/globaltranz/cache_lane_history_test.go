package globaltranz

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func TestCalculateJitteredTTL_ReturnsOffPeakHour(t *testing.T) {
	// Test multiple iterations to ensure all selections are off-peak
	iterations := 100
	offPeakHours := make(map[int]bool)

	for i := 0; i < iterations; i++ {
		ttl := calculateJitteredTTL()

		etLocation, err := time.LoadLocation("America/New_York")
		require.NoError(t, err)

		now := time.Now().In(etLocation)
		expirationTime := now.Add(ttl)
		expirationHour := expirationTime.Hour()

		// Check if hour is off-peak (6PM-8AM ET)
		isOffPeak := expirationHour >= offPeakStartHour || expirationHour < offPeakEndHour

		assert.True(t, isOffPeak, "expiration hour %d should be off-peak (6PM-8AM ET)", expirationHour)
		offPeakHours[expirationHour] = true
	}

	// Verify we got some distribution (at least 50% of off-peak hours)
	assert.Greater(t, len(offPeakHours), 8, "should have distribution across at least 8 off-peak hours")
}

func TestCalculateJitteredTTL_SelectsPollerInterval(t *testing.T) {
	// Test poller interval selection over many iterations

	validIntervals := map[int]bool{0: true, 10: true, 20: true, 30: true, 40: true, 50: true}
	foundIntervals := make(map[int]bool)

	iterations := 100
	for i := 0; i < iterations; i++ {
		ttl := calculateJitteredTTL()

		etLocation, err := time.LoadLocation("America/New_York")
		require.NoError(t, err)

		now := time.Now().In(etLocation)
		expirationTime := now.Add(ttl)
		expirationMinute := expirationTime.Minute()

		assert.True(
			t,
			validIntervals[expirationMinute],
			"expiration minute %d should be one of: 0, 10, 20, 30, 40, 50",
			expirationMinute,
		)
		foundIntervals[expirationMinute] = true
	}

	// Verify we found multiple intervals
	assert.Greater(t, len(foundIntervals), 3, "should have distribution across multiple poller intervals")
}

func TestCalculateJitteredTTL_TTLRange(t *testing.T) {
	// Test TTL range over multiple iterations

	iterations := 50
	minTTL := baseLaneHistoryTTL - time.Duration(jitterWindowHours)*time.Hour
	maxTTL := baseLaneHistoryTTL + time.Duration(jitterWindowHours)*time.Hour

	for i := 0; i < iterations; i++ {
		ttl := calculateJitteredTTL()

		// TTL should be positive
		assert.Greater(t, ttl, time.Duration(0), "TTL should always be positive")

		// TTL should be within jitter window
		// Allow some tolerance for minute-level adjustments
		assert.GreaterOrEqual(t, ttl, minTTL-time.Hour, "TTL should be within jitter window (lower bound)")
		assert.LessOrEqual(t, ttl, maxTTL+time.Hour, "TTL should be within jitter window (upper bound)")
	}
}

func TestCalculateJitteredTTL_Distribution(t *testing.T) {
	// Test distribution over many iterations

	iterations := 1000
	hourCounts := make(map[int]int)

	for i := 0; i < iterations; i++ {
		ttl := calculateJitteredTTL()

		etLocation, err := time.LoadLocation("America/New_York")
		require.NoError(t, err)

		now := time.Now().In(etLocation)
		expirationTime := now.Add(ttl)
		expirationHour := expirationTime.Hour()

		// Only count off-peak hours
		if expirationHour >= offPeakStartHour || expirationHour < offPeakEndHour {
			hourCounts[expirationHour]++
		}
	}

	// Verify distribution - no single hour should dominate (>30% of selections)
	maxCount := 0
	for _, count := range hourCounts {
		if count > maxCount {
			maxCount = count
		}
	}

	maxPercentage := float64(maxCount) / float64(iterations)
	log.Info(context.Background(), "highest hour count", zap.Int("maxCount", maxCount))

	assert.Less(
		t,
		maxPercentage,
		0.30,
		"no single hour should dominate the distribution (max: %.2f%%)",
		maxPercentage*100,
	)
}

func TestCalculateJitteredTTL_TimezoneHandling(t *testing.T) {
	// Test timezone handling

	// Test that it always uses ET timezone regardless of system timezone
	ttl := calculateJitteredTTL()

	etLocation, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	now := time.Now().In(etLocation)
	expirationTime := now.Add(ttl)
	expirationHour := expirationTime.Hour()

	// Verify expiration hour is in valid off-peak range
	isOffPeak := expirationHour >= offPeakStartHour || expirationHour < offPeakEndHour
	assert.True(t, isOffPeak, "should use ET timezone correctly, got hour %d", expirationHour)
}
