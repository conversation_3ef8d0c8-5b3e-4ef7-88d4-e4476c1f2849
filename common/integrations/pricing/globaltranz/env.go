package globaltranz

import (
	"os"
	"strconv"
)

const DefaultMaxRequestsPerMinute = 30

var (
	MaxLocationSearchRequestsPerMinute  = DefaultMaxRequestsPerMinute
	MaxCarrierHistoryRequestsPerMinute  = DefaultMaxRequestsPerMinute
	MaxOrderDetailsRequestsPerMinute    = DefaultMaxRequestsPerMinute
	MaxLaneCarrierListRequestsPerMinute = DefaultMaxRequestsPerMinute
	isEnvVarsInitialized                = false
)

// Init initializes the rate limit variables from environment variables.
func InitEnvVars() {
	if isEnvVarsInitialized {
		return
	}

	MaxLocationSearchRequestsPerMinute = getEnvInt("GTZ_LOCATION_SEARCH_RATE_LIMIT")
	MaxCarrierHistoryRequestsPerMinute = getEnvInt("GTZ_CARRIER_HISTORY_RATE_LIMIT")
	MaxOrderDetailsRequestsPerMinute = getEnvInt("GTZ_ORDER_DETAILS_RATE_LIMIT")
	MaxLaneCarrierListRequestsPerMinute = getEnvInt("GTZ_LANE_CARRIER_LIST_RATE_LIMIT")
	isEnvVarsInitialized = true
}

func getEnvInt(key string) int {
	value := os.Getenv(key)

	if value == "" {
		return DefaultMaxRequestsPerMinute
	}

	intValue, err := strconv.Atoi(value)
	if err != nil || intValue <= 0 {
		return DefaultMaxRequestsPerMinute
	}

	return intValue
}
