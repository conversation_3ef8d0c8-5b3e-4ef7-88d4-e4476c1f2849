package globaltranz

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestIsContextPolling_WithPollingKey(t *testing.T) {
	ctx := context.WithValue(context.Background(), models.IsPollingKey, true)

	result := isContextPolling(ctx)

	assert.True(t, result, "should return true when IsPollingKey is true")
}

func TestIsContextPolling_WithPollingKeyFalse(t *testing.T) {
	ctx := context.WithValue(context.Background(), models.IsPollingKey, false)

	result := isContextPolling(ctx)

	assert.False(t, result, "should return false when <PERSON>Polling<PERSON><PERSON> is false")
}

func TestIsContextPolling_WithoutPollingKey(t *testing.T) {
	ctx := context.Background()

	result := isContextPolling(ctx)

	assert.False(t, result, "should return false when IsPolling<PERSON>ey is not set")
}

func TestIsContextPolling_WithWrongType(t *testing.T) {
	ctx := context.WithValue(context.Background(), models.IsPollingKey, "not a bool")

	result := isContextPolling(ctx)

	assert.False(t, result, "should return false when IsPollingKey has wrong type")
}

func TestIsContextPolling_WithNilContext(t *testing.T) {
	ctx := context.Background()

	result := isContextPolling(ctx)

	assert.False(t, result, "should return false for nil context")
}
