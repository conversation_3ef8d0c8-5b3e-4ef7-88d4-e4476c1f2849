package globaltranz

import (
	"context"
	"errors"
	"fmt"
	"os"
	"time"

	redisv9 "github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	// maxCombinationsPerOffPeakInterval limits the number of new lane history combinations
	// that can be processed during a single off-peak interval (6PM-8AM ET).
	// This throttles cold starts to prevent overwhelming the API with initial Redis population.
	maxCombinationsPerOffPeakInterval = 1400

	offPeakIntervalDuration = 14 * time.Hour // 6PM-8AM = 14 hours
)

// getCurrentOffPeakIntervalKey returns the Redis key for tracking processed combinations
// in the current off-peak interval. The key includes the date, resetting at 6PM ET daily.
func getCurrentOffPeakIntervalKey(intervalDate time.Time) string {
	return fmt.Sprintf("globaltranz-lane-history-cold-start-count-%s", intervalDate.Format("2006-01-02"))
}

// getHoursSinceIntervalStart calculates how many hours have passed since the current
// off-peak interval started (6PM ET).
func getHoursSinceIntervalStart(nowET time.Time, intervalStart time.Time) float64 {
	return nowET.Sub(intervalStart).Hours()
}

// getThrottleInfo calculates how many new combinations can be processed
// based on time-based pacing. Returns the target count, current processed count,
// and remaining capacity.
func getThrottleInfo(ctx context.Context) (target int, processed int64, remaining int, err error) {
	etLocation, err := time.LoadLocation("America/New_York")
	if err != nil {
		return 0, 0, 0, fmt.Errorf("failed to load Eastern timezone: %w", err)
	}

	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()

	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)

	// If after 6PM, interval started today at 6PM. Otherwise, interval started yesterday at 6PM.
	var intervalStart time.Time
	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
	}

	hoursSinceStart := getHoursSinceIntervalStart(nowET, intervalStart)

	// Calculate target so we can distribute processing across the off-peak hours
	target = int((hoursSinceStart / offPeakIntervalDuration.Hours()) * float64(maxCombinationsPerOffPeakInterval))

	// Cap target at max to prevent exceeding daily limit
	if target > maxCombinationsPerOffPeakInterval {
		target = maxCombinationsPerOffPeakInterval
	}

	key := getCurrentOffPeakIntervalKey(intervalStart)

	// Try to get current count (may not exist if first run of interval)
	// IncrementKey stores as a number, so we need to use RDB.Get directly
	var currentCount int64
	if redis.RDB != nil {
		val, err := redis.RDB.Get(ctx, key).Result()
		if err != nil {
			if errors.Is(err, redisv9.Nil) {
				// Key doesn't exist yet, count is 0
				currentCount = 0
			} else {
				return 0, 0, 0, fmt.Errorf("failed to get processed count: %w", err)
			}
		} else {
			_, err := fmt.Sscanf(val, "%d", &currentCount)
			if err != nil {
				// If parsing fails, treat as 0
				currentCount = 0
			}
		}
	}

	processed = currentCount
	remaining = target - int(processed)

	// Ensure remaining is non-negative
	if remaining < 0 {
		remaining = 0
	}

	return target, processed, remaining, nil
}

// incrementProcessedCount increments the counter for processed combinations
// in the current off-peak interval. The key expires at the end of the interval (8AM ET).
func incrementProcessedCount(ctx context.Context) error {
	etLocation, err := time.LoadLocation("America/New_York")
	if err != nil {
		return fmt.Errorf("failed to load Eastern timezone: %w", err)
	}

	nowET := time.Now().In(etLocation)
	hourET := nowET.Hour()

	// If after 6PM, interval started today at 6PM. Otherwise, interval started yesterday at 6PM.
	var intervalStart, intervalEnd time.Time

	startTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakStartHour, 0, 0, 0, etLocation)
	endTimeHourOnly := time.Date(nowET.Year(), nowET.Month(), nowET.Day(), offPeakEndHour, 0, 0, 0, etLocation)

	if hourET >= offPeakStartHour {
		intervalStart = startTimeHourOnly
		intervalEnd = endTimeHourOnly.Add(24 * time.Hour)
	} else {
		intervalStart = startTimeHourOnly.Add(-24 * time.Hour)
		intervalEnd = endTimeHourOnly
	}

	ttl := intervalEnd.Sub(nowET)
	key := getCurrentOffPeakIntervalKey(intervalStart)

	_, err = redis.IncrementKey(ctx, key, ttl)
	if err != nil {
		return fmt.Errorf("failed to increment processed count: %w", err)
	}

	return nil
}

// CanProcessNewCombination checks if a new combination can be processed based on throttling limits
func CanProcessNewCombination(ctx context.Context) (bool, error) {
	// Check if cold start throttling is disabled via environment variable
	if disableThrottle := os.Getenv("DISABLE_GTZ_COLD_START_THROTTLE"); disableThrottle == "true" {
		log.Info(ctx, "cold start throttling is disabled")

		return true, nil
	}

	_, _, remaining, err := getThrottleInfo(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get throttle info: %w", err)
	}

	return remaining > 0, nil
}

// IncrementProcessedCount increments the counter for processed combinations.
func IncrementProcessedCount(ctx context.Context) error {
	if err := incrementProcessedCount(ctx); err != nil {
		log.WarnNoSentry(ctx, "failed to record processed combination", zap.Error(err))
		return err
	}
	return nil
}

// GetThrottleStatus returns the current throttle status for logging/monitoring.
func GetThrottleInfo(ctx context.Context) (target int, processed int64, remaining int, err error) {
	return getThrottleInfo(ctx)
}
