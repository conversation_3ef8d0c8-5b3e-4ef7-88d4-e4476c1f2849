package globaltranz

import (
	"context"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestEnvVariables_DefaultValues(t *testing.T) {
	// Verify default values are set correctly - defaults should be: 30, 30, 30, 30
	assert.Equal(t, 30, MaxLocationSearchRequestsPerMinute, "default MaxLocationSearchRequestsPerMinute should be 30")
	assert.Equal(t, 30, MaxCarrierHistoryRequestsPerMinute, "default MaxCarrierHistoryRequestsPerMinute should be 30")
	assert.Equal(t, 30, MaxOrderDetailsRequestsPerMinute, "default MaxOrderDetailsRequestsPerMinute should be 30")
	assert.Equal(t, 30, MaxLaneCarrierListRequestsPerMinute, "default MaxLaneCarrierListRequestsPerMinute should be 30")
}

func TestGetEnvInt_ValidValue(t *testing.T) {
	// Test getEnvInt logic by temporarily setting env vars and checking behavior
	// Note: This tests the function logic, but init() has already run, so the package
	// variables won't change. This verifies the parsing logic works correctly.

	tests := []struct {
		name         string
		envValue     string
		defaultValue int
		expected     int
	}{
		{"valid integer", "60", 30, 60},
		{"valid large integer", "100", 30, 100},
		{"empty string uses default", "", 30, 30},
		{"invalid string uses default", "not-a-number", 30, 30},
		{"zero uses default", "0", 30, 30},
		{"negative uses default", "-10", 30, 30},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Save original value
			originalValue := os.Getenv("TEST_ENV_VAR")
			defer func() {
				if originalValue == "" {
					os.Unsetenv("TEST_ENV_VAR")
				} else {
					os.Setenv("TEST_ENV_VAR", originalValue)
				}
			}()

			// Set test value
			if tt.envValue == "" {
				os.Unsetenv("TEST_ENV_VAR")
			} else {
				os.Setenv("TEST_ENV_VAR", tt.envValue)
			}

			// Test the logic (simulating getEnvInt)
			value := os.Getenv("TEST_ENV_VAR")
			result := tt.defaultValue
			if value != "" {
				intValue, err := strconv.Atoi(value)
				if err != nil || intValue <= 0 {
					result = tt.defaultValue
				} else {
					result = intValue
				}
			}

			assert.Equal(t, tt.expected, result, "getEnvInt logic should parse correctly")
		})
	}
}

func TestEnvVariables_IntegrationNote(t *testing.T) {
	// Verify that the current values are set (from init())
	assert.Greater(t, MaxLocationSearchRequestsPerMinute, 0, "MaxLocationSearchRequestsPerMinute should be set")
	assert.Greater(t, MaxCarrierHistoryRequestsPerMinute, 0, "MaxCarrierHistoryRequestsPerMinute should be set")
	assert.Greater(t, MaxOrderDetailsRequestsPerMinute, 0, "MaxOrderDetailsRequestsPerMinute should be set")
	assert.Greater(t, MaxLaneCarrierListRequestsPerMinute, 0, "MaxLaneCarrierListRequestsPerMinute should be set")
}

func TestRateLimiting_RespectsLimit(t *testing.T) {
	ctx := context.WithValue(context.Background(), models.IsPollingKey, true)

	result := isContextPolling(ctx)
	assert.True(t, result)

	// Test rate limiting calculation with MaxLocationSearchRequestsPerMinute = 30, sleep should be 2 seconds
	expectedSleep := time.Minute / time.Duration(MaxLocationSearchRequestsPerMinute)
	assert.Equal(t, 2*time.Second, expectedSleep, "sleep duration should be 2 seconds for 30 requests per minute")
}
