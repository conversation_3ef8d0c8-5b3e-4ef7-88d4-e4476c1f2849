package emailtemplates

import (
	"github.com/drumkitai/drumkit/common/models"
)

// GetAccessTier determines the access tier of an email template based on
// which level it's defined at (user, user group, service, or generic).
func GetAccessTier(template models.EmailTemplate) models.EmailTemplateAccessTier {
	if template.UserID != 0 {
		return models.EmailTemplateUserTier
	}

	if template.UserGroupID != 0 {
		return models.EmailTemplateGroupTier
	}

	if template.ServiceID != 0 {
		return models.EmailTemplateServiceTier
	}

	return models.EmailTemplateGenericTier
}
