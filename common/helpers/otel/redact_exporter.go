package otel

import (
	"context"
	"strings"

	"go.opentelemetry.io/otel/attribute"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.opentelemetry.io/otel/sdk/trace/tracetest"
)

// redactingExporter removes braintrust.input_json and braintrust.output_json from openai.responses.create spans.
type redactingExporter struct {
	exporter sdktrace.SpanExporter
}

func (r *redactingExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	redactedSpans := make([]sdktrace.ReadOnlySpan, 0, len(spans))
	for _, span := range spans {
		if strings.Contains(span.Name(), "openai.responses.create") {
			redactedSpans = append(redactedSpans, r.removeSensitiveJSON(span))
		} else {
			redactedSpans = append(redactedSpans, span)
		}
	}
	return r.exporter.ExportSpans(ctx, redactedSpans)
}

func (r *redactingExporter) removeSensitiveJSON(span sdktrace.ReadOnlySpan) sdktrace.ReadOnlySpan {
	var attrs []attribute.KeyValue
	hasSensitiveJSON := false
	for _, attr := range span.Attributes() {
		if string(attr.Key) == "braintrust.input_json" || string(attr.Key) == "braintrust.output_json" {
			hasSensitiveJSON = true
		} else {
			attrs = append(attrs, attr)
		}
	}
	if !hasSensitiveJSON {
		return span
	}
	stub := tracetest.SpanStubFromReadOnlySpan(span)
	stub.Attributes = attrs
	return stub.Snapshot()
}

func (r *redactingExporter) Shutdown(ctx context.Context) error {
	return r.exporter.Shutdown(ctx)
}
