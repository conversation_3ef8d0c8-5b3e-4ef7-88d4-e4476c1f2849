package otel

import (
	"context"
	"strings"

	"github.com/braintrustdata/braintrust-x-go/braintrust"
	bttrace "github.com/braintrustdata/braintrust-x-go/braintrust/trace"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

const DocProcProjectID string = "8982f27b-7fde-4ade-a0ed-75a4b406f3dc"

// SetupBraintrustTracing initializes Braintrust tracing with a filter for docproc spans.
// Returns a cleanup function that should be deferred, or nil if Braintrust is not configured.
func SetupBraintrustTracing(
	ctx context.Context,
	sdkProvider *sdktrace.TracerProvider,
	apiKey string,
	baseURL string,
) func() error {
	if apiKey == "" {
		log.Warn(ctx, "Braintrust API key not set, skipping Braintrust tracing")
		return nil
	}

	projectID := DocProcProjectID

	docprocFilter := func(s sdktrace.ReadOnlySpan) int {
		spanName := s.Name()
		spanNameLower := strings.ToLower(spanName)

		// Drop high-volume Axiom ingestion spans.
		// Check for both "Datasets.Ingest" (direct span name) and "datasets/ingest" (HTTP span format)
		if strings.Contains(spanNameLower, "datasets/ingest") ||
			strings.Contains(spanNameLower, "datasets.ingest") {
			return -1
		}

		if strings.HasPrefix(spanName, "page-") ||
			strings.Contains(spanName, "openai.responses.create") ||
			strings.Contains(spanName, "markdown") ||
			strings.Contains(spanName, "PDFToMD") ||
			strings.Contains(spanName, "ProcessPages") ||
			strings.Contains(spanName, "extracted_post_processing_lb_suggestion") {
			return 1
		}

		for _, attr := range s.Attributes() {
			if attr.Key == "project.step" {
				if attr.Value.AsString() == "Convert Page to Markdown" {
					return 1
				}
			}
		}

		return -1
	}

	err := bttrace.Enable(sdkProvider,
		braintrust.WithDefaultProjectID(projectID),
		braintrust.WithAPIKey(apiKey),
		braintrust.WithAPIURL(baseURL),
		braintrust.WithBlockingLogin(false),
		braintrust.WithSpanFilterFuncs(docprocFilter),
	)
	if err != nil {
		log.Warn(ctx, "failed to enable braintrust tracing", zap.Error(err))
		return nil
	}

	log.Info(ctx, "Braintrust tracing enabled with docproc filtering", zap.String("projectID", projectID))

	return func() error {
		log.Debug(ctx, "Flushing Braintrust traces")
		if sdkProvider != nil {
			return sdkProvider.Shutdown(ctx)
		}
		return nil
	}
}
