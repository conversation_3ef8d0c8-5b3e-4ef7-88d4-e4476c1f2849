package helpers

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func replaceNullBytes(s string) string {
	return strings.ReplaceAll(s, "\x00", "")
}

func sanitizeStructNullBytes(v reflect.Value, path string, sanitizedFields *[]string) int {
	if !v.IsValid() {
		return 0
	}

	// Handle pointers
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return 0
		}
		v = v.Elem()
	}

	count := 0

	switch v.Kind() {
	case reflect.String:
		// Sanitize string field
		original := v.String()
		cleaned := replaceNullBytes(original)
		if cleaned != original {
			v.SetString(cleaned)
			if sanitizedFields != nil {
				*sanitizedFields = append(*sanitizedFields, path)
			}
			count++
		}

	case reflect.Struct:
		// Recursively sanitize struct fields
		t := v.Type()
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			fieldType := t.Field(i)

			// Skip unexported fields
			if !field.CanSet() {
				continue
			}

			// Build field path for logging
			fieldPath := fieldType.Name
			if path != "" {
				fieldPath = path + "." + fieldType.Name
			}

			// Handle embedded structs
			if fieldType.Anonymous {
				count += sanitizeStructNullBytes(field, path, sanitizedFields)
			} else {
				count += sanitizeStructNullBytes(field, fieldPath, sanitizedFields)
			}
		}

	case reflect.Slice, reflect.Array:
		// Sanitize slice/array elements
		for i := 0; i < v.Len(); i++ {
			elem := v.Index(i)
			elemPath := fmt.Sprintf("%s[%d]", path, i)
			count += sanitizeStructNullBytes(elem, elemPath, sanitizedFields)
		}
	}

	return count
}

func SanitizeStructNullBytes(ctx context.Context, v any) (int, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "SanitizeStruct", nil)
	defer func() { metaSpan.End(nil) }()

	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr {
		return 0, errors.New("value must be a pointer")
	}

	if rv.IsNil() {
		return 0, errors.New("pointer must not be nil")
	}

	sanitizedFields := []string{}
	count := sanitizeStructNullBytes(rv, "", &sanitizedFields)

	if count > 0 {
		log.Info(
			ctx,
			"sanitized struct: removed invalid characters",
			zap.Int("fieldCount", count),
			zap.Strings("sanitizedFields", sanitizedFields),
		)
	}

	return count, nil
}

func SanitizeEmail(ctx context.Context, email *models.Email) (*models.Email, int, error) {
	count, err := SanitizeStructNullBytes(ctx, email)
	if err != nil {
		return email, count, fmt.Errorf("failed to sanitize email: %w", err)
	}
	return email, count, nil
}

func SanitizeOnPremEmail(ctx context.Context, email *models.OnPremEmail) (*models.OnPremEmail, int, error) {
	count, err := SanitizeStructNullBytes(ctx, email)
	if err != nil {
		return email, count, fmt.Errorf("failed to sanitize on-prem email: %w", err)
	}
	return email, count, nil
}

func SanitizeLoad(ctx context.Context, load *models.Load) (int, error) {
	count, err := SanitizeStructNullBytes(ctx, load)
	if err != nil {
		return count, fmt.Errorf("failed to sanitize load: %w", err)
	}
	return count, nil
}
