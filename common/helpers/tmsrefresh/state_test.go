package tmsrefresh

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

func TestGetRefreshState(t *testing.T) {
	ctx := context.Background()
	tmsID := uint(1001)
	jobType := redis.LocationRefreshJob
	redisKey := fmt.Sprintf("integration-id-%d-%s", tmsID, jobType)

	// Mock Redis
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := redis.RDB
	redis.RDB = mockRDB
	defer func() {
		redis.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	t.Run("Priority 1: Explicit options take precedence", func(t *testing.T) {
		options := &models.TMSOptions{
			Cursor:          "opt-cursor",
			UpdatedAtFilter: "2024-01-01T00:00:00Z",
		}

		cursor, updatedAt := GetRefreshState(
			ctx,
			tmsID,
			jobType,
			options,
			integrationDB.LastLocationUpdatedAt,
			time.RFC3339,
		)

		assert.Equal(t, "opt-cursor", cursor)
		assert.Equal(t, "2024-01-01T00:00:00Z", updatedAt)
	})

	t.Run("Priority 2: Redis fallback when options are missing", func(t *testing.T) {
		options := &models.TMSOptions{} // No explicit options

		mock.ExpectExists(redisKey).SetVal(1)
		mock.ExpectGet(redisKey).SetVal("2024-02-01T00:00:00Z;redis-cursor")

		cursor, updatedAt := GetRefreshState(
			ctx,
			tmsID,
			jobType,
			options,
			integrationDB.LastLocationUpdatedAt,
			time.RFC3339,
		)

		assert.Equal(t, "redis-cursor", cursor)
		assert.Equal(t, "2024-02-01T00:00:00Z", updatedAt)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Partial fallback: Option cursor, Redis updatedAt", func(t *testing.T) {
		options := &models.TMSOptions{
			Cursor: "opt-cursor",
		}

		mock.ExpectExists(redisKey).SetVal(1)
		mock.ExpectGet(redisKey).SetVal("2024-04-01T00:00:00Z;redis-cursor")

		cursor, updatedAt := GetRefreshState(
			ctx,
			tmsID,
			jobType,
			options,
			integrationDB.LastLocationUpdatedAt,
			time.RFC3339,
		)

		assert.Equal(t, "opt-cursor", cursor)              // Option wins
		assert.Equal(t, "2024-04-01T00:00:00Z", updatedAt) // Redis fallback wins
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Default: Returns empty when no state is found and UseLastUpdatedAt=false", func(t *testing.T) {
		// Mock Redis: no state
		mock.ExpectExists(redisKey).SetVal(0)

		options := &models.TMSOptions{
			UseLastUpdatedAt: false,
		}

		cursor, updatedAt := GetRefreshState(
			ctx,
			tmsID,
			jobType,
			options,
			integrationDB.LastLocationUpdatedAt,
			time.RFC3339,
		)

		assert.Equal(t, "", cursor)
		assert.Equal(t, "", updatedAt)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestSetIntegrationStateWithWarning(t *testing.T) {
	ctx := context.Background()
	tmsID := uint(1001)
	jobType := redis.LocationRefreshJob
	redisKey := fmt.Sprintf("integration-id-%d-%s", tmsID, jobType)

	// Mock Redis
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := redis.RDB
	redis.RDB = mockRDB
	defer func() {
		redis.RDB = originalRDB
		_ = mockRDB.Close()
	}()

	t.Run("Successfully saves state with InProgress status", func(t *testing.T) {
		updatedAt := "2024-05-01T00:00:00Z"
		cursor := "batch-cursor"
		expectedValue := fmt.Sprintf("%s;%s;%s", redis.JobStatusInProgress, updatedAt, cursor)

		mock.ExpectTxPipeline()
		mock.ExpectDel(redisKey).SetVal(1)
		mock.ExpectSet(redisKey, expectedValue, 0).SetVal("OK")
		mock.ExpectTxPipelineExec()

		SetIntegrationStateWithWarning(ctx, tmsID, jobType, updatedAt, cursor)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
