package appointments

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	// maxAppointmentDurationHours prevents unreasonably long appointments
	maxAppointmentDurationHours = 24
	// overnightCutoffHour defines the latest hour (exclusive) considered "early morning"
	// for overnight appointment detection. Hours 0-5 (00:00-05:59) are considered early morning.
	overnightCutoffHour = 6
	// timeOnlyWithoutSecondsFormat is the standard format for parsing HH:MM times
	timeOnlyWithoutSecondsFormat = "15:04"
)

var (
	// timeFormatRegex validates HH:MM format (00:00 to 23:59, or 24:00 for end of day)
	// Only 24:00 is valid for hour 24; 24:01-24:59 are invalid
	timeFormatRegex = regexp.MustCompile(`^(([01][0-9]|2[0-3]):[0-5][0-9]|24:00)$`)

	ErrEmptyAppointmentTime = errors.New("appointment time is required")
	ErrEmptyExpectedDate    = errors.New("expected date is required")
	ErrInvalidTimeFormat    = errors.New("invalid time format: expected HH:MM or HH:MM-HH:MM")
	ErrIncompleteRange      = errors.New("incomplete time range: both start and end times required")
	ErrInvalidTimeValue     = errors.New("invalid time value: hours must be 00-24, minutes 00-59")
	ErrInvalidRange         = errors.New("invalid time range: end time cannot be before start time")
	ErrAppointmentTooLong   = errors.New("appointment duration exceeds maximum allowed hours")
)

// parseAppointmentTime parses user-provided appointment time strings and combines them
// with an expected date to create full datetime values. It handles both single times
// and time ranges, with support for overnight appointments.
//
// Supported formats:
//   - Single time: "14:30"
//   - Time range: "14:30-16:00"
//   - Overnight range: "22:00-02:00" (interpreted as same day 22:00 to next day 02:00)
//   - Midnight as 24:00: "24:00-01:00" (interpreted as next day 00:00 to next day 01:00)
//
// Returns:
//   - startTime: Always populated for valid input
//   - endTime: Only populated for range inputs
//   - error: Descriptive error for invalid input
func ParseAppointmentTime(
	appointmentTime string,
	expectedDate *string,
) (models.NullTime, models.NullTime, error) {
	// Validate inputs
	if appointmentTime == "" {
		t1, t2 := invalidTimes()
		return t1, t2, ErrEmptyAppointmentTime
	}
	if expectedDate == nil || *expectedDate == "" {
		t1, t2 := invalidTimes()
		return t1, t2, ErrEmptyExpectedDate
	}

	// Normalize input: trim whitespace and remove spaces around hyphen
	appointmentTime = normalizeTimeString(appointmentTime)

	// Detect if this is a time range
	if strings.Contains(appointmentTime, "-") {
		return parseTimeRange(appointmentTime, expectedDate)
	}

	// Single time format
	return parseSingleTime(appointmentTime, expectedDate)
}

// normalizeTimeString cleans up user input by trimming whitespace and standardizing format
func normalizeTimeString(s string) string {
	s = strings.TrimSpace(s)
	// Remove spaces around hyphen: "12:00 - 15:00" -> "12:00-15:00"
	s = strings.ReplaceAll(s, " - ", "-")
	s = strings.ReplaceAll(s, " -", "-")
	s = strings.ReplaceAll(s, "- ", "-")
	return s
}

// validateTimeFormat checks if a time string matches HH:MM format
func validateTimeFormat(timeStr string) error {
	if !timeFormatRegex.MatchString(timeStr) {
		return fmt.Errorf("%w: '%s' (must be HH:MM, e.g., 09:30 or 14:00)", ErrInvalidTimeValue, timeStr)
	}
	return nil
}

// parseSingleTime handles parsing of a single time value
func parseSingleTime(
	timeStr string,
	expectedDate *string,
) (models.NullTime, models.NullTime, error) {
	if err := validateTimeFormat(timeStr); err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, err
	}

	startTime, err := parseTimeWithDate(expectedDate, timeStr)
	if err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("unable to parse appointment time: %w", err)
	}

	if !startTime.Valid {
		t1, t2 := invalidTimes()
		return t1, t2, errors.New("parsed time is invalid")
	}

	return startTime, models.NullTime{Valid: false}, nil
}

// parseTimeRange handles parsing of time ranges (HH:MM-HH:MM)
func parseTimeRange(
	rangeStr string,
	expectedDate *string,
) (models.NullTime, models.NullTime, error) {
	// Split on hyphen and validate structure
	parts := strings.Split(rangeStr, "-")

	// Reject malformed input (multiple hyphens or empty parts)
	if len(parts) != 2 {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("%w: too many hyphens in '%s'", ErrInvalidTimeFormat, rangeStr)
	}

	startStr := strings.TrimSpace(parts[0])
	endStr := strings.TrimSpace(parts[1])

	if startStr == "" || endStr == "" {
		t1, t2 := invalidTimes()
		return t1, t2, ErrIncompleteRange
	}

	// Validate both time formats before parsing
	if err := validateTimeFormat(startStr); err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("invalid start time: %w", err)
	}
	if err := validateTimeFormat(endStr); err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("invalid end time: %w", err)
	}

	// Track if start time is "24:00" for special handling
	startWas24 := startStr == "24:00"

	// Parse both times
	startTime, err := parseTimeWithDate(expectedDate, startStr)
	if err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("unable to parse start time: %w", err)
	}

	endTime, err := parseTimeWithDate(expectedDate, endStr)
	if err != nil {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("unable to parse end time: %w", err)
	}

	if !startTime.Valid || !endTime.Valid {
		t1, t2 := invalidTimes()
		return t1, t2, errors.New("parsed time range is invalid")
	}

	// Handle special case: "24:00-HH:MM" ranges
	if startWas24 {
		endTime = handleMidnightStartRange(startTime, endTime)
	}

	// Handle overnight appointments (e.g., "22:00-02:00")
	if endTime.Time.Before(startTime.Time) {
		var err error
		endTime, err = handleOvernightRange(endTime)
		if err != nil {
			t1, t2 := invalidTimes()
			return t1, t2, err
		}
	}

	// Validate reasonable duration
	duration := endTime.Time.Sub(startTime.Time)
	if duration.Hours() > maxAppointmentDurationHours {
		t1, t2 := invalidTimes()
		return t1, t2, fmt.Errorf("%w: duration is %.1f hours (max %d hours)",
			ErrAppointmentTooLong, duration.Hours(), maxAppointmentDurationHours)
	}

	return startTime, endTime, nil
}

// handleMidnightStartRange adjusts end time when start time is "24:00"
// Example: "24:00-01:00" means next day 00:00 to next day 01:00
func handleMidnightStartRange(startTime, endTime models.NullTime) models.NullTime {
	startDate := getDateOnly(startTime.Time)
	endDate := getDateOnly(endTime.Time)

	// When start is "24:00", it's already been adjusted to the next day (00:00).
	// The end time should always be adjusted to the same day as the start,
	// regardless of the hour, since "24:00-HH:MM" always means next day 00:00 to next day HH:MM.
	if endDate.Before(startDate) {
		endTime.Time = time.Date(
			startTime.Time.Year(),
			startTime.Time.Month(),
			startTime.Time.Day(),
			endTime.Time.Hour(),
			endTime.Time.Minute(),
			endTime.Time.Second(),
			endTime.Time.Nanosecond(),
			endTime.Time.Location(),
		)
	}

	return endTime
}

// handleOvernightRange adjusts end time for overnight appointments
// Example: "22:00-02:00" means 22:00 today to 02:00 tomorrow
func handleOvernightRange(endTime models.NullTime) (models.NullTime, error) {
	endHour := endTime.Time.Hour()

	// Only allow overnight if end time is early morning (prevents "15:00-12:00" being treated as overnight)
	if endHour >= overnightCutoffHour {
		return models.NullTime{Valid: false}, fmt.Errorf(
			"%w: end time %02d:%02d is before start time but not early morning "+
				"(must be 00:00-05:59 for overnight appointments)",
			ErrInvalidRange, endHour, endTime.Time.Minute(),
		)
	}

	// Advance end time by one day
	endTime.Time = endTime.Time.AddDate(0, 0, 1)
	return endTime, nil
}

// getDateOnly extracts just the date portion (year, month, day) from a time
func getDateOnly(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// invalidTimes returns two invalid NullTime values for error cases
func invalidTimes() (models.NullTime, models.NullTime) {
	return models.NullTime{Valid: false}, models.NullTime{Valid: false}
}

// parseTimeWithDate parses a single time string and combines it with a date to create a full datetime.
// Handles the special case of "24:00" by converting it to "00:00" of the next day.
func parseTimeWithDate(dateStr *string, timeStr string) (models.NullTime, error) {
	if dateStr == nil || *dateStr == "" {
		return models.NullTime{Valid: false}, errors.New("date is required")
	}

	dateParsed, err := time.Parse(time.DateOnly, *dateStr)
	if err != nil {
		return models.NullTime{Valid: false}, fmt.Errorf("unable to parse date '%s': %w", *dateStr, err)
	}

	// Handle "24:00" as a special case - convert to "00:00" of the next day
	normalizedTimeStr := strings.TrimSpace(timeStr)
	addDay := false
	if normalizedTimeStr == "24:00" {
		normalizedTimeStr = "00:00"
		addDay = true
	}

	parsedTime, err := time.Parse(timeOnlyWithoutSecondsFormat, normalizedTimeStr)
	if err != nil {
		return models.NullTime{Valid: false}, fmt.Errorf("unable to parse time '%s': %w", timeStr, err)
	}

	// If we normalized "24:00" to "00:00", add one day
	if addDay {
		dateParsed = dateParsed.AddDate(0, 0, 1)
	}

	return models.NullTime{
		Time: time.Date(
			dateParsed.Year(),
			dateParsed.Month(),
			dateParsed.Day(),
			parsedTime.Hour(),
			parsedTime.Minute(),
			0,
			0,
			dateParsed.Location(),
		),
		Valid: true,
	}, nil
}
