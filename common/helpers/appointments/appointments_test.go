package appointments

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseAppointmentTime_EmptyInputs(t *testing.T) {
	tests := []struct {
		name            string
		appointmentTime string
		expectedDate    *string
		expectedErr     error
	}{
		{
			name:            "empty appointment time",
			appointmentTime: "",
			expectedDate:    strPtr("2024-03-15"),
			expectedErr:     ErrEmptyAppointmentTime,
		},
		{
			name:            "nil expected date",
			appointmentTime: "14:30",
			expectedDate:    nil,
			expectedErr:     ErrEmptyExpectedDate,
		},
		{
			name:            "empty expected date",
			appointmentTime: "14:30",
			expectedDate:    strPtr(""),
			expectedErr:     ErrEmptyExpectedDate,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.appointmentTime, tt.expectedDate)
			assert.ErrorIs(t, err, tt.expectedErr)
			assert.False(t, start.Valid)
			assert.False(t, end.Valid)
		})
	}
}

func TestParseAppointmentTime_SingleTime(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name         string
		timeStr      string
		expectedHour int
		expectedMin  int
	}{
		{"morning time", "09:30", 9, 30},
		{"afternoon time", "14:00", 14, 0},
		{"midnight", "00:00", 0, 0},
		{"late night", "23:59", 23, 59},
		{"with spaces", "  12:45  ", 12, 45},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.timeStr, date)
			require.NoError(t, err)
			assert.True(t, start.Valid)
			assert.False(t, end.Valid)
			assert.Equal(t, 2024, start.Time.Year())
			assert.Equal(t, time.March, start.Time.Month())
			assert.Equal(t, 15, start.Time.Day())
			assert.Equal(t, tt.expectedHour, start.Time.Hour())
			assert.Equal(t, tt.expectedMin, start.Time.Minute())
		})
	}
}

func TestParseAppointmentTime_SingleTime_Special24Hour(t *testing.T) {
	date := strPtr("2024-03-15")

	start, end, err := ParseAppointmentTime("24:00", date)
	require.NoError(t, err)
	assert.True(t, start.Valid)
	assert.False(t, end.Valid)

	// 24:00 on March 15 should become 00:00 on March 16
	assert.Equal(t, 2024, start.Time.Year())
	assert.Equal(t, time.March, start.Time.Month())
	assert.Equal(t, 16, start.Time.Day())
	assert.Equal(t, 0, start.Time.Hour())
	assert.Equal(t, 0, start.Time.Minute())
}

func TestParseAppointmentTime_InvalidTimeFormats(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name    string
		timeStr string
	}{
		{"missing colon", "1430"},
		{"single digit hour", "9:30"},
		{"single digit minute", "09:5"},
		{"invalid hour", "25:00"},
		{"invalid minute", "12:60"},
		{"too many parts", "12:30:45"},
		{"non-numeric", "12:ab"},
		{"negative hour", "-01:00"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.timeStr, date)
			assert.Error(t, err)
			assert.False(t, start.Valid)
			assert.False(t, end.Valid)
		})
	}
}

func TestParseAppointmentTime_TimeRange_SameDay(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name      string
		rangeStr  string
		startHour int
		startMin  int
		endHour   int
		endMin    int
	}{
		{"morning appointment", "09:00-10:30", 9, 0, 10, 30},
		{"afternoon appointment", "14:00-16:00", 14, 0, 16, 0},
		{"with spaces", "12:00 - 15:00", 12, 0, 15, 0},
		{"with extra spaces", " 10:30  -  11:45 ", 10, 30, 11, 45},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.rangeStr, date)
			require.NoError(t, err)
			assert.True(t, start.Valid)
			assert.True(t, end.Valid)

			// Both should be on the same day
			assert.Equal(t, 15, start.Time.Day())
			assert.Equal(t, 15, end.Time.Day())

			assert.Equal(t, tt.startHour, start.Time.Hour())
			assert.Equal(t, tt.startMin, start.Time.Minute())
			assert.Equal(t, tt.endHour, end.Time.Hour())
			assert.Equal(t, tt.endMin, end.Time.Minute())
		})
	}
}

func TestParseAppointmentTime_TimeRange_Overnight(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name      string
		rangeStr  string
		startHour int
		endHour   int
		endDay    int // Expected day for end time
	}{
		{"late night to early morning", "22:00-02:00", 22, 2, 16},
		{"just before midnight", "23:30-01:30", 23, 1, 16},
		{"midnight to morning", "00:00-05:00", 0, 5, 15}, // Not overnight, same day
		{"evening to dawn", "20:00-05:59", 20, 5, 16},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.rangeStr, date)
			require.NoError(t, err)
			assert.True(t, start.Valid)
			assert.True(t, end.Valid)

			assert.Equal(t, 15, start.Time.Day())
			assert.Equal(t, tt.endDay, end.Time.Day())
			assert.Equal(t, tt.startHour, start.Time.Hour())
			assert.Equal(t, tt.endHour, end.Time.Hour())

			// Verify end is after start
			assert.True(t, end.Time.After(start.Time))
		})
	}
}

func TestParseAppointmentTime_TimeRange_24HourStart(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name     string
		rangeStr string
		endHour  int
		endDay   int
	}{
		{"24:00 to early morning", "24:00-01:00", 1, 16},
		{"24:00 to dawn", "24:00-05:00", 5, 16},
		{"24:00 to just before cutoff", "24:00-05:59", 5, 16},
		{"24:00 to morning (at cutoff)", "24:00-06:00", 6, 16},
		{"24:00 to mid-morning", "24:00-12:00", 12, 16},
		{"24:00 to afternoon", "24:00-15:30", 15, 16},
		{"24:00 to evening", "24:00-23:59", 23, 16},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.rangeStr, date)
			require.NoError(t, err)
			assert.True(t, start.Valid)
			assert.True(t, end.Valid)

			// Start should be March 16 at 00:00 (24:00 on March 15)
			assert.Equal(t, 16, start.Time.Day())
			assert.Equal(t, 0, start.Time.Hour())

			// End should also be March 16
			assert.Equal(t, tt.endDay, end.Time.Day())
			assert.Equal(t, tt.endHour, end.Time.Hour())

			// Verify end is after start
			assert.True(t, end.Time.After(start.Time))
		})
	}
}

func TestParseAppointmentTime_TimeRange_InvalidRanges(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name        string
		rangeStr    string
		expectedErr error
	}{
		{"end before start (not overnight)", "15:00-12:00", ErrInvalidRange},
		{"end before start (late morning)", "10:00-08:00", ErrInvalidRange},
		{"incomplete range - missing start", "-15:00", ErrIncompleteRange},
		{"incomplete range - missing end", "12:00-", ErrIncompleteRange},
		{"too many hyphens", "12:00-15:00-18:00", ErrInvalidTimeFormat},
		{"double hyphen", "12:00--15:00", ErrInvalidTimeFormat},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.rangeStr, date)
			assert.Error(t, err)
			if tt.expectedErr != nil {
				assert.ErrorIs(t, err, tt.expectedErr)
			}
			assert.False(t, start.Valid)
			assert.False(t, end.Valid)
		})
	}
}

func TestParseAppointmentTime_TimeRange_InvalidFormats(t *testing.T) {
	date := strPtr("2024-03-15")

	tests := []struct {
		name     string
		rangeStr string
	}{
		{"invalid start time", "25:00-15:00"},
		{"invalid end time", "12:00-25:00"},
		{"both invalid", "25:00-26:00"},
		{"malformed start", "12-15:00"},
		{"malformed end", "12:00-15"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime(tt.rangeStr, date)
			assert.Error(t, err)
			assert.False(t, start.Valid)
			assert.False(t, end.Valid)
		})
	}
}

func TestParseAppointmentTime_TimeRange_MaxDuration(t *testing.T) {
	date := strPtr("2024-03-15")

	// Valid: exactly 24 hours (00:00 to 23:59 = 23h59m, which is < 24h, so valid)
	start, end, err := ParseAppointmentTime("00:00-23:59", date)
	require.NoError(t, err)
	assert.True(t, start.Valid)
	assert.True(t, end.Valid)
	duration := end.Time.Sub(start.Time)
	assert.LessOrEqual(t, duration.Hours(), float64(maxAppointmentDurationHours))

	// Valid: 1-hour overnight appointment (23:00 to 00:00 next day)
	// This is treated as overnight, so end time is advanced by 1 day, making it 1 hour total
	start, end, err = ParseAppointmentTime("23:00-00:00", date)
	require.NoError(t, err)
	assert.True(t, start.Valid)
	assert.True(t, end.Valid)
	duration = end.Time.Sub(start.Time)
	assert.Equal(t, 1.0, duration.Hours(), "23:00-00:00 should be 1 hour when treated as overnight")
	assert.LessOrEqual(t, duration.Hours(), float64(maxAppointmentDurationHours))
}

func TestParseAppointmentTime_EdgeCases(t *testing.T) {
	date := strPtr("2024-03-15")

	t.Run("cutoff hour boundary - 05:59 is overnight", func(t *testing.T) {
		start, end, err := ParseAppointmentTime("22:00-05:59", date)
		require.NoError(t, err)
		assert.Equal(t, 15, start.Time.Day())
		assert.Equal(t, 16, end.Time.Day()) // Next day
	})

	t.Run("cutoff hour boundary - 06:00 is not overnight", func(t *testing.T) {
		_, _, err := ParseAppointmentTime("22:00-06:00", date)
		assert.Error(t, err)
		assert.ErrorIs(t, err, ErrInvalidRange)
	})

	t.Run("same start and end time", func(t *testing.T) {
		start, end, err := ParseAppointmentTime("12:00-12:00", date)
		require.NoError(t, err)
		assert.True(t, start.Time.Equal(end.Time))
	})
}

func TestParseAppointmentTime_InvalidDate(t *testing.T) {
	tests := []struct {
		name string
		date *string
	}{
		{"invalid date format", strPtr("03/15/2024")},
		{"invalid date value", strPtr("2024-13-45")},
		{"malformed date", strPtr("not-a-date")},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start, end, err := ParseAppointmentTime("14:30", tt.date)
			assert.Error(t, err)
			assert.False(t, start.Valid)
			assert.False(t, end.Valid)
		})
	}
}

func TestNormalizeTimeString(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"12:00-15:00", "12:00-15:00"},
		{"12:00 - 15:00", "12:00-15:00"},
		{"12:00 -15:00", "12:00-15:00"},
		{"12:00- 15:00", "12:00-15:00"},
		{"  12:00  -  15:00  ", "12:00-15:00"},
		{"09:30", "09:30"},
		{"  09:30  ", "09:30"},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := normalizeTimeString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidateTimeFormat(t *testing.T) {
	validTimes := []string{"00:00", "09:30", "12:45", "23:59", "24:00"}
	for _, timeStr := range validTimes {
		t.Run("valid: "+timeStr, func(t *testing.T) {
			err := validateTimeFormat(timeStr)
			assert.NoError(t, err)
		})
	}

	invalidTimes := []string{"9:30", "12:5", "25:00", "12:60", "12:00:00", "abc", "12-30", "24:01", "24:30", "24:59"}
	for _, timeStr := range invalidTimes {
		t.Run("invalid: "+timeStr, func(t *testing.T) {
			err := validateTimeFormat(timeStr)
			assert.Error(t, err)
		})
	}
}

// Helper function to create string pointers
func strPtr(s string) *string {
	return &s
}
