package user

import (
	"context"
	"database/sql"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchUsersQuery struct {
	Name      string `json:"name,omitempty"`
	Email     string `json:"email,omitempty"`
	ServiceID uint   `json:"serviceID,omitempty"` // Optional: filter by service. If 0, search all services
	Limit     int    `json:"limit,omitempty"`     // Optional: limit results
}

func FuzzySearchByNameOrEmail(ctx context.Context, query SearchUsersQuery) ([]models.User, error) {
	if query.Name == "" && query.Email == "" {
		return nil, nil
	}

	var users []models.User
	var searchThreshold = 0.3

	db := rds.WithContextReader(ctx).Model(&models.User{})

	if query.ServiceID > 0 {
		db = db.Where("service_id = ?", query.ServiceID)
	}

	searchTerm := query.Name
	if searchTerm == "" {
		searchTerm = query.Email
	}

	db = db.Scopes(fuzzyMatchUser(ctx, searchTerm, query.ServiceID, searchThreshold))

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	err := db.Find(&users).Error
	return users, err
}

func fuzzyMatchUser(
	ctx context.Context,
	searchTerm string,
	serviceID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			serviceFilter := ""
			if serviceID > 0 {
				serviceFilter = "AND service_id = @serviceID"
			}

			// Perform string-distance search on user's name and email address.
			// Order results by exact matches first, then by similarity score.
			query := fmt.Sprintf(`
				WITH user_matches AS (
					SELECT *,
						(name <-> @searchTerm) AS name_dist,
						(email_address <-> @searchTerm) AS email_dist,
						(
							name ILIKE '%%' || @searchTerm || '%%'
							OR email_address ILIKE '%%' || @searchTerm || '%%'
						) AS is_match
					FROM users
					WHERE deleted_at IS NULL
						%s
						AND (
							name %% @searchTerm
							OR email_address %% @searchTerm
							OR name ILIKE '%%' || @searchTerm || '%%'
							OR email_address ILIKE '%%' || @searchTerm || '%%'
						)
				)
				SELECT *
				FROM user_matches
				ORDER BY
					is_match DESC,
					LEAST(name_dist, email_dist) ASC
				LIMIT 100
			`, serviceFilter)

			args := []any{
				sql.Named("searchTerm", searchTerm),
			}
			if serviceID > 0 {
				args = append(args, sql.Named("serviceID", serviceID))
			}

			return tx.Raw(query, args...)
		})
	}
}
