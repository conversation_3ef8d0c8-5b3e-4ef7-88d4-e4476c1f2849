package tmsserviceoptionmapping

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	serviceOptionMappingCacheKey = "threeg:service_option_mappings:%d"
	serviceOptionMappingTTL      = 24 * time.Hour
)

// GetServiceOptionMappings gets all service option mappings for a TMS integration
// Uses hybrid pattern: Redis cache first, fallback to database
func GetServiceOptionMappings(
	ctx context.Context,
	tmsIntegrationID uint,
) ([]models.TMSServiceOptionMapping, error) {
	// Try Redis first
	cacheKey := fmt.Sprintf(serviceOptionMappingCacheKey, tmsIntegrationID)
	cachedMappings, found, err := redis.GetKey[[]models.TMSServiceOptionMapping](ctx, cacheKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting service option mappings from redis", zap.Error(err))
	}

	if found {
		return cachedMappings, nil
	}

	// Fall back to database
	var mappings []models.TMSServiceOptionMapping
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ?", tmsIntegrationID).
		Find(&mappings).Error

	if err != nil {
		return nil, err
	}

	// Cache the result in Redis
	if len(mappings) > 0 {
		if err := redis.SetKey(ctx, cacheKey, mappings, serviceOptionMappingTTL); err != nil {
			log.Warn(ctx, "error setting service option mappings in redis", zap.Error(err))
		}
	}

	return mappings, nil
}

// GetMappingByServiceOptionName gets a service option mapping by service option name
// Uses hybrid pattern: Redis cache first, fallback to database
// Performs case-insensitive matching by normalizing both the input and cached values
func GetMappingByServiceOptionName(
	ctx context.Context,
	tmsIntegrationID uint,
	serviceOptionName string,
) (*models.TMSServiceOptionMapping, error) {
	// Normalize the input for case-insensitive matching
	serviceOptionNameNormalized := strings.ToLower(strings.TrimSpace(serviceOptionName))

	// Try Redis first
	cacheKey := fmt.Sprintf(serviceOptionMappingCacheKey, tmsIntegrationID)
	cachedMappings, found, err := redis.GetKey[[]models.TMSServiceOptionMapping](ctx, cacheKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting service option mappings from redis", zap.Error(err))
	}

	if found {
		// Search in cached mappings with case-insensitive comparison
		for i := range cachedMappings {
			cachedNameNormalized := strings.ToLower(strings.TrimSpace(cachedMappings[i].ServiceOptionName))
			if cachedNameNormalized == serviceOptionNameNormalized {
				return &cachedMappings[i], nil
			}
		}
		// Not found in cache, return nil
		return nil, nil
	}

	// Fall back to database
	// Use LOWER() and TRIM() in SQL for case-insensitive matching
	var mapping models.TMSServiceOptionMapping
	err = rds.WithContextReader(ctx).
		Where(
			"tms_integration_id = ? AND LOWER(TRIM(service_option_name)) = ?",
			tmsIntegrationID,
			serviceOptionNameNormalized,
		).
		First(&mapping).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &mapping, nil
}

// GetServiceOptionMappingsByTMSID gets service option mappings from database with limit support
// Similar to GetTMSCustomersByTMSID pattern
func GetServiceOptionMappingsByTMSID(
	ctx context.Context,
	query rds.GenericGetQuery,
) ([]models.TMSServiceOptionMapping, error) {
	var mappings []models.TMSServiceOptionMapping

	db := rds.WithContextReader(ctx).
		Where("tms_integration_id = ?", query.TMSID)

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	err := db.Find(&mappings).Error
	return mappings, err
}

// InvalidateCache deletes the Redis cache for service option mappings
func InvalidateCache(ctx context.Context, tmsIntegrationID uint) error {
	cacheKey := fmt.Sprintf(serviceOptionMappingCacheKey, tmsIntegrationID)
	return redis.DeleteKey(ctx, cacheKey)
}
