package tmsserviceoptionmapping

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// RefreshServiceOptionMappings upserts service option mappings for a TMS integration.
// Updates existing mappings and inserts new ones using OnConflict, similar to RefreshTMSLocations pattern.
// Note: Items removed from the TMS will not be deleted from the database (same behavior as RefreshTMSLocations).
func RefreshServiceOptionMappings(
	ctx context.Context,
	tmsIntegrationID uint,
	mappings []models.TMSServiceOptionMapping,
) error {
	var err error
	ctx, span := otel.StartSpan(ctx, "rds.RefreshServiceOptionMappings", nil)
	defer func() { span.End(err) }()

	if len(mappings) == 0 {
		return nil
	}

	// Force set TMSIntegrationID on all mappings to ensure data consistency
	for i := range mappings {
		mappings[i].TMSIntegrationID = tmsIntegrationID
	}

	assignmentColumns := []string{
		"service_option_name",
		"description",
		"service_option_transport_mode_id",
		"transport_mode_id",
		"transport_mode_names_string",
		"mapped_load_mode",
		"updated_at",
	}

	// Upsert service option mappings to db within a transaction
	// Wrapping in transaction prevents partial state if network blip occurs during CreateInBatches
	// Updates existing records and inserts new ones based on unique constraint
	// (tms_integration_id, service_option_id)
	err = rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return tx.Clauses(
			clause.OnConflict{
				Columns: []clause.Column{
					{Name: "tms_integration_id"},
					{Name: "service_option_id"},
				},
				DoUpdates: clause.AssignmentColumns(assignmentColumns),
			},
		).
			CreateInBatches(mappings, 1000).
			Error
	})

	return err
}

// Create creates a new service option mapping
func Create(ctx context.Context, mapping *models.TMSServiceOptionMapping) error {
	return rds.WithContext(ctx).Create(mapping).Error
}
