package usergroups

import (
	"context"
	"database/sql"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchUserGroupsQuery struct {
	Name      string `json:"name,omitempty"`
	ServiceID uint   `json:"serviceID,omitempty"` // Optional: filter by service. If 0, search all services
	Limit     int    `json:"limit,omitempty"`     // Optional: limit results
}

func FuzzySearchByName(ctx context.Context, query SearchUserGroupsQuery) ([]models.UserGroup, error) {
	if query.Name == "" {
		return nil, nil
	}

	var userGroups []models.UserGroup
	var searchThreshold = 0.3

	db := rds.WithContextReader(ctx).Model(&models.UserGroup{})

	if query.ServiceID > 0 {
		db = db.Where("service_id = ?", query.ServiceID)
	}

	db = db.Scopes(fuzzyMatchUserGroup(ctx, query.Name, query.ServiceID, searchThreshold))

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	err := db.Find(&userGroups).Error
	return userGroups, err
}

func fuzzyMatchUserGroup(
	ctx context.Context,
	searchTerm string,
	serviceID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			serviceFilter := ""
			if serviceID > 0 {
				serviceFilter = "AND service_id = @serviceID"
			}

			// Perform string-distance search on user group's name.
			// Order results by exact matches first, then by similarity score.
			query := fmt.Sprintf(`
				WITH user_group_matches AS (
					SELECT *,
						name <-> @searchTerm AS name_dist,
						name ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM user_groups
					WHERE deleted_at IS NULL
						%s
						AND (name %% @searchTerm OR name ILIKE '%%' || @searchTerm || '%%')
				)
				SELECT *
				FROM user_group_matches
				ORDER BY
					is_match DESC,
					name_dist ASC
				LIMIT 100
			`, serviceFilter)

			args := []any{
				sql.Named("searchTerm", searchTerm),
			}
			if serviceID > 0 {
				args = append(args, sql.Named("serviceID", serviceID))
			}

			return tx.Raw(query, args...)
		})
	}
}
