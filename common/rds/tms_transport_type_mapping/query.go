package tmstransporttypemapping

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	transportTypeMappingCacheKey = "threeg:transport_type_mappings:%d"
	transportTypeMappingTTL      = 24 * time.Hour
)

// GetTransportTypeMappings gets all transport type mappings for a TMS integration
// Uses hybrid pattern: Redis cache first, fallback to database
func GetTransportTypeMappings(
	ctx context.Context,
	tmsIntegrationID uint,
) ([]models.TMSTransportTypeMapping, error) {
	// Try Redis first
	cacheKey := fmt.Sprintf(transportTypeMappingCacheKey, tmsIntegrationID)
	cachedMappings, found, err := redis.GetKey[[]models.TMSTransportTypeMapping](ctx, cacheKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting transport type mappings from redis", zap.Error(err))
	}

	if found {
		return cachedMappings, nil
	}

	// Fall back to database
	var mappings []models.TMSTransportTypeMapping
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ?", tmsIntegrationID).
		Find(&mappings).Error

	if err != nil {
		return nil, err
	}

	// Cache the result in Redis
	if len(mappings) > 0 {
		if err := redis.SetKey(ctx, cacheKey, mappings, transportTypeMappingTTL); err != nil {
			log.Warn(ctx, "error setting transport type mappings in redis", zap.Error(err))
		}
	}

	return mappings, nil
}

// GetMappingByEquipmentName gets a transport type mapping by equipment name
// Uses hybrid pattern: Redis cache first, fallback to database
func GetMappingByEquipmentName(
	ctx context.Context,
	tmsIntegrationID uint,
	equipmentName string,
) (*models.TMSTransportTypeMapping, error) {
	// Try Redis first
	cacheKey := fmt.Sprintf(transportTypeMappingCacheKey, tmsIntegrationID)
	cachedMappings, found, err := redis.GetKey[[]models.TMSTransportTypeMapping](ctx, cacheKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting transport type mappings from redis", zap.Error(err))
	}

	if found {
		// Search in cached mappings
		for i := range cachedMappings {
			if cachedMappings[i].EquipmentName == equipmentName {
				return &cachedMappings[i], nil
			}
		}
		// Not found in cache, return nil
		return nil, nil
	}

	// Fall back to database
	var mapping models.TMSTransportTypeMapping
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND equipment_name = ?", tmsIntegrationID, equipmentName).
		First(&mapping).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &mapping, nil
}

// GetTransportTypeMappingsByTMSID gets transport type mappings from database with limit support
// Similar to GetTMSCustomersByTMSID pattern
func GetTransportTypeMappingsByTMSID(
	ctx context.Context,
	query rds.GenericGetQuery,
) ([]models.TMSTransportTypeMapping, error) {
	var mappings []models.TMSTransportTypeMapping

	db := rds.WithContextReader(ctx).
		Where("tms_integration_id = ?", query.TMSID)

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	err := db.Find(&mappings).Error
	return mappings, err
}

// InvalidateCache deletes the Redis cache for transport type mappings
func InvalidateCache(ctx context.Context, tmsIntegrationID uint) error {
	cacheKey := fmt.Sprintf(transportTypeMappingCacheKey, tmsIntegrationID)
	return redis.DeleteKey(ctx, cacheKey)
}
