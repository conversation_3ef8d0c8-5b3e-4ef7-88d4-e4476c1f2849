package tmstransporttypemapping

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// RefreshTransportTypeMappings upserts transport type mappings for a TMS integration.
// Updates existing mappings and inserts new ones using OnConflict, similar to RefreshTMSLocations pattern.
// Note: Items removed from the TMS will not be deleted from the database (same behavior as RefreshTMSLocations).
func RefreshTransportTypeMappings(
	ctx context.Context,
	tmsIntegrationID uint,
	mappings []models.TMSTransportTypeMapping,
) error {
	var err error
	ctx, span := otel.StartSpan(ctx, "rds.RefreshTransportTypeMappings", nil)
	defer func() { span.End(err) }()

	if len(mappings) == 0 {
		return nil
	}

	// Force set TMSIntegrationID on all mappings to ensure data consistency
	for i := range mappings {
		mappings[i].TMSIntegrationID = tmsIntegrationID
	}

	assignmentColumns := []string{
		"equipment_name",
		"equipment_code",
		"equipment_classification",
		"mapped_transport_type",
		"weight_max",
		"weight_max_uom",
		"volume_max",
		"volume_max_uom",
		"max_pallets",
		"updated_at",
	}

	// Upsert transport type mappings to db within a transaction
	// Wrapping in transaction prevents partial state if network blip occurs during CreateInBatches
	// Updates existing records and inserts new ones based on unique constraint
	// (tms_integration_id, equipment_id)
	err = rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return tx.Clauses(
			clause.OnConflict{
				Columns: []clause.Column{
					{Name: "tms_integration_id"},
					{Name: "equipment_id"},
				},
				DoUpdates: clause.AssignmentColumns(assignmentColumns),
			},
		).
			CreateInBatches(mappings, 1000).
			Error
	})

	return err
}

// Create creates a new transport type mapping
func Create(ctx context.Context, mapping *models.TMSTransportTypeMapping) error {
	return rds.WithContext(ctx).Create(mapping).Error
}
