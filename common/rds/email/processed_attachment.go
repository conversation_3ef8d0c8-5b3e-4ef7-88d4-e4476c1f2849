package email

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetProcessedAttachmentByFilename queries a ProcessedAttachment by RFCMessageID + OriginalFileName.
// This is the deduplication key used across all recipients of the same email.
//
// Args:
//   - serviceID: Service ID for multi-tenancy
//   - rfcMessageID: RFC Message-ID header (same for all recipients)
//   - filename: Original filename of the attachment
//   - status: Optional status filter (empty string = any status)
//
// Returns:
//   - *models.ProcessedAttachment: Found record, or nil if not found
//   - bool: true if record was found
//   - error: Database error (not found is not an error)
func GetProcessedAttachmentByFilename(
	ctx context.Context,
	serviceID uint,
	rfcMessageID string,
	filename string,
	status string,
) (*models.ProcessedAttachment, bool, error) {
	pa := &models.ProcessedAttachment{}
	query := rds.WithContext(ctx).
		Where("service_id = ? AND rfc_message_id = ? AND original_file_name = ?",
			serviceID, rfcMessageID, filename)

	if status != "" {
		query = query.Where("processing_status = ?", status)
	}

	result := query.Take(pa)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, false, nil
	}

	if result.Error != nil {
		return nil, false, result.Error
	}

	return pa, true, nil
}

// GetProcessedAttachmentsByFilenames queries multiple ProcessedAttachments by RFCMessageID + OriginalFileName.
// This is the deduplication key used across all recipients of the same email.
//
// Args:
//   - serviceID: Service ID for multi-tenancy
//   - rfcMessageID: RFC Message-ID header (same for all recipients)
//   - filenames: List of original filenames to query
//   - status: Optional status filter (empty string = any status)
//
// Returns:
//   - []models.ProcessedAttachment: Found records (may be empty)
//   - error: Database error
func GetProcessedAttachmentsByFilenames(
	ctx context.Context,
	serviceID uint,
	rfcMessageID string,
	filenames []string,
	status string,
) ([]models.ProcessedAttachment, error) {
	if len(filenames) == 0 {
		return []models.ProcessedAttachment{}, nil
	}

	var processedAttachments []models.ProcessedAttachment
	query := rds.WithContext(ctx).
		Where("service_id = ? AND rfc_message_id = ? AND original_file_name IN ?",
			serviceID, rfcMessageID, filenames)

	if status != "" {
		query = query.Where("processing_status = ?", status)
	}

	err := query.Find(&processedAttachments).Error
	return processedAttachments, err
}
