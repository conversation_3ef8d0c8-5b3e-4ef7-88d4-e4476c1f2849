package prompts

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestResolvePrompt_WaterfallPriority(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestResolvePrompt_WaterfallPriority: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID

	// Create test user and user group
	user := rds.CreateTestUser(ctx, t, service.ID)
	userID := user.ID

	// Create user group manually
	userGroup := models.UserGroup{
		Name:      "Test Group",
		ServiceID: service.ID,
	}
	require.NoError(t, rds.WithContext(ctx).Create(&userGroup).Error)
	userGroupID := userGroup.ID
	userGroupIDs := []uint{userGroupID}

	// Create prompts at all 6 levels
	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType
	senderEmail := "<EMAIL>"
	senderDomain := "example.com"

	// Global prompt
	globalPrompt := &models.ServiceUserPrompt{
		Feature:    feature,
		Type:       promptType,
		PromptText: "Global prompt",
		IsActive:   true,
	}
	require.NoError(t, Create(ctx, globalPrompt))

	// Service-specific prompt
	servicePrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "Service prompt",
		IsActive:   true,
	}
	require.NoError(t, Create(ctx, servicePrompt))

	// Domain-specific prompt
	domainPrompt := &models.ServiceUserPrompt{
		ServiceID:    &serviceID,
		SenderDomain: &senderDomain,
		Feature:      feature,
		Type:         promptType,
		PromptText:   "Domain prompt",
		IsActive:     true,
	}
	require.NoError(t, Create(ctx, domainPrompt))

	// Sender-specific prompt
	senderPrompt := &models.ServiceUserPrompt{
		ServiceID:   &serviceID,
		SenderEmail: &senderEmail,
		Feature:     feature,
		Type:        promptType,
		PromptText:  "Sender prompt",
		IsActive:    true,
	}
	require.NoError(t, Create(ctx, senderPrompt))

	// UserGroup-specific prompt
	userGroupPrompt := &models.ServiceUserPrompt{
		ServiceID:   &serviceID,
		UserGroupID: &userGroupID,
		Feature:     feature,
		Type:        promptType,
		PromptText:  "UserGroup prompt",
		IsActive:    true,
	}
	require.NoError(t, Create(ctx, userGroupPrompt))

	// User-specific prompt
	userPrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		UserID:     &userID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "User prompt",
		IsActive:   true,
	}
	require.NoError(t, Create(ctx, userPrompt))

	// Test waterfall resolution
	tests := []struct {
		name           string
		serviceID      *uint
		userID         *uint
		userGroupIDs   []uint
		email          string
		expectedPrompt string
		expectedLevel  string
	}{
		{
			name:           "user-specific wins over everything",
			serviceID:      &serviceID,
			userID:         &userID,
			userGroupIDs:   userGroupIDs,
			email:          senderEmail,
			expectedPrompt: "User prompt",
			expectedLevel:  "user",
		},
		{
			name:           "user-group wins when no user match",
			serviceID:      &serviceID,
			userID:         nil,
			userGroupIDs:   userGroupIDs,
			email:          senderEmail,
			expectedPrompt: "UserGroup prompt",
			expectedLevel:  "user_group",
		},
		{
			name:           "sender-specific wins when no user/group match",
			serviceID:      &serviceID,
			userID:         nil,
			userGroupIDs:   nil,
			email:          senderEmail,
			expectedPrompt: "Sender prompt",
			expectedLevel:  "sender",
		},
		{
			name:           "domain-specific wins when no sender match",
			serviceID:      &serviceID,
			userID:         nil,
			userGroupIDs:   nil,
			email:          "<EMAIL>",
			expectedPrompt: "Domain prompt",
			expectedLevel:  "domain",
		},
		{
			name:           "service-specific wins when no domain match",
			serviceID:      &serviceID,
			userID:         nil,
			userGroupIDs:   nil,
			email:          "<EMAIL>",
			expectedPrompt: "Service prompt",
			expectedLevel:  "service",
		},
		{
			name:           "global wins when no service",
			serviceID:      nil,
			userID:         nil,
			userGroupIDs:   nil,
			email:          senderEmail,
			expectedPrompt: "Global prompt",
			expectedLevel:  "global",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prompt, err := ResolvePrompt(ctx, tt.serviceID, tt.userID, tt.userGroupIDs, tt.email, feature, promptType)
			require.NoError(t, err)
			require.NotNil(t, prompt)
			assert.Equal(t, tt.expectedPrompt, prompt.PromptText)
			assert.Equal(t, tt.expectedLevel, prompt.GetResolutionLevel())
		})
	}
}

func TestUpsert_RollbackSupport(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestUpsert_RollbackSupport: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID

	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType

	// Create original prompt
	originalPrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "Original prompt text",
		IsActive:   true,
	}
	require.NoError(t, Create(ctx, originalPrompt))

	// Update the prompt
	updatedPrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "Updated prompt text",
		IsActive:   true,
	}

	// Execute upsert
	err := Upsert(ctx, updatedPrompt)
	require.NoError(t, err)

	// Fetch the updated prompt and verify previous_prompt_text
	var prompt models.ServiceUserPrompt
	err = rds.WithContextReader(ctx).First(&prompt, originalPrompt.ID).Error
	require.NoError(t, err)

	assert.Equal(t, "Updated prompt text", prompt.PromptText)
	assert.NotNil(t, prompt.PreviousPromptText)
	assert.Equal(t, "Original prompt text", *prompt.PreviousPromptText)
}

func TestUpsert_ValidatesPromptTextOnUpdate(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestUpsert_ValidatesPromptTextOnUpdate: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID

	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType

	// Create original prompt with valid text
	originalPrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "Original prompt text",
		IsActive:   true,
	}
	require.NoError(t, Create(ctx, originalPrompt))

	// Try to update with empty PromptText - should fail validation
	emptyPrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "", // Empty - should fail validation
		IsActive:   true,
	}

	err := Upsert(ctx, emptyPrompt)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "prompt_text is required")

	// Verify the original prompt was not modified
	var prompt models.ServiceUserPrompt
	err = rds.WithContextReader(ctx).First(&prompt, originalPrompt.ID).Error
	require.NoError(t, err)
	assert.Equal(t, "Original prompt text", prompt.PromptText)
}

func TestPromptValidation(t *testing.T) {
	serviceID := uint(123)
	userID := uint(456)
	userGroupID := uint(789)
	senderEmail := "<EMAIL>"
	senderDomain := "example.com"

	tests := []struct {
		name        string
		prompt      *models.ServiceUserPrompt
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid user-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:  &serviceID,
				UserID:     &userID,
				Feature:    models.IsQuoteRequestAttachmentFeature,
				Type:       models.DeveloperPromptType,
				PromptText: "Test prompt",
				IsActive:   true,
			},
			expectError: false,
		},
		{
			name: "valid user-group-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				UserGroupID: &userGroupID,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: false,
		},
		{
			name: "valid sender-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				SenderEmail: &senderEmail,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: false,
		},
		{
			name: "valid domain-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:    &serviceID,
				SenderDomain: &senderDomain,
				Feature:      models.IsQuoteRequestAttachmentFeature,
				Type:         models.DeveloperPromptType,
				PromptText:   "Test prompt",
				IsActive:     true,
			},
			expectError: false,
		},
		{
			name: "user-specific without service_id",
			prompt: &models.ServiceUserPrompt{
				UserID:     &userID,
				Feature:    models.IsQuoteRequestAttachmentFeature,
				Type:       models.DeveloperPromptType,
				PromptText: "Test prompt",
				IsActive:   true,
			},
			expectError: true,
			errorMsg:    "user-specific prompts require service_id",
		},
		{
			name: "user-specific with user_group_id",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				UserID:      &userID,
				UserGroupID: &userGroupID,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: true,
			errorMsg:    "user-specific prompts cannot have user_group_id",
		},
		{
			name: "invalid mixed resolution: user + sender",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				UserID:      &userID,
				SenderEmail: &senderEmail,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: true,
			errorMsg:    "user-specific prompts cannot have sender_email or sender_domain",
		},
		{
			name: "invalid mixed resolution: user_group + domain",
			prompt: &models.ServiceUserPrompt{
				ServiceID:    &serviceID,
				UserGroupID:  &userGroupID,
				SenderDomain: &senderDomain,
				Feature:      models.IsQuoteRequestAttachmentFeature,
				Type:         models.DeveloperPromptType,
				PromptText:   "Test prompt",
				IsActive:     true,
			},
			expectError: true,
			errorMsg:    "user-group-specific prompts cannot have sender_email or sender_domain",
		},
		{
			name: "invalid global with sender_email",
			prompt: &models.ServiceUserPrompt{
				SenderEmail: &senderEmail,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: true,
			errorMsg:    "sender-specific prompts require service_id",
		},
		{
			name: "user-group-specific without service_id",
			prompt: &models.ServiceUserPrompt{
				UserGroupID: &userGroupID,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: true,
			errorMsg:    "user-group-specific prompts require service_id",
		},
		{
			name: "sender-specific without service_id",
			prompt: &models.ServiceUserPrompt{
				SenderEmail: &senderEmail,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test prompt",
				IsActive:    true,
			},
			expectError: true,
			errorMsg:    "sender-specific prompts require service_id",
		},
		{
			name: "domain-specific without service_id",
			prompt: &models.ServiceUserPrompt{
				SenderDomain: &senderDomain,
				Feature:      models.IsQuoteRequestAttachmentFeature,
				Type:         models.DeveloperPromptType,
				PromptText:   "Test prompt",
				IsActive:     true,
			},
			expectError: true,
			errorMsg:    "domain-specific prompts require service_id",
		},
		{
			name: "domain-specific with sender_email",
			prompt: &models.ServiceUserPrompt{
				ServiceID:    &serviceID,
				SenderEmail:  &senderEmail,
				SenderDomain: &senderDomain,
				Feature:      models.IsQuoteRequestAttachmentFeature,
				Type:         models.DeveloperPromptType,
				PromptText:   "Test prompt",
				IsActive:     true,
			},
			expectError: true,
			errorMsg:    "domain-specific prompts should not have sender_email",
		},
		{
			name: "missing feature",
			prompt: &models.ServiceUserPrompt{
				ServiceID:  &serviceID,
				Type:       models.DeveloperPromptType,
				PromptText: "Test prompt",
				IsActive:   true,
			},
			expectError: true,
			errorMsg:    "feature is required",
		},
		{
			name: "missing type",
			prompt: &models.ServiceUserPrompt{
				ServiceID:  &serviceID,
				Feature:    models.IsQuoteRequestAttachmentFeature,
				PromptText: "Test prompt",
				IsActive:   true,
			},
			expectError: true,
			errorMsg:    "type is required",
		},
		{
			name: "missing prompt_text",
			prompt: &models.ServiceUserPrompt{
				ServiceID: &serviceID,
				Feature:   models.IsQuoteRequestAttachmentFeature,
				Type:      models.DeveloperPromptType,
				IsActive:  true,
			},
			expectError: true,
			errorMsg:    "prompt_text is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.prompt.Validate()
			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUpsert_SenderDoesNotMatchMixedUserPrompt(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping live DB test: run with TEST_LIVE_DB=true")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID
	user := rds.CreateTestUser(ctx, t, service.ID)
	userID := user.ID

	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType
	senderEmail := "<EMAIL>"

	// Insert an invalid mixed-level prompt (user + sender_email) by skipping hooks/validation.
	mixed := &models.ServiceUserPrompt{
		ServiceID:   &serviceID,
		UserID:      &userID,
		SenderEmail: &senderEmail,
		Feature:     feature,
		Type:        promptType,
		PromptText:  "Mixed user prompt",
		IsActive:    true,
	}
	require.NoError(t, rds.WithContext(ctx).Session(&gorm.Session{SkipHooks: true}).Create(mixed).Error)

	// Upsert a sender-specific prompt with same sender_email.
	senderPrompt := &models.ServiceUserPrompt{
		ServiceID:   &serviceID,
		SenderEmail: &senderEmail,
		Feature:     feature,
		Type:        promptType,
		PromptText:  "Sender prompt",
		IsActive:    true,
	}
	require.NoError(t, Upsert(ctx, senderPrompt))

	// Ensure sender lookup returns the sender-specific prompt and does not match the mixed user prompt.
	got, err := GetPromptBySender(ctx, serviceID, senderEmail, feature, promptType)
	require.NoError(t, err)
	require.NotNil(t, got)
	assert.Equal(t, "Sender prompt", got.PromptText)
	assert.Nil(t, got.UserID)
	assert.Nil(t, got.UserGroupID)
	assert.Nil(t, got.SenderDomain)

	// Mixed prompt remains unchanged (Upsert did not update it).
	var mixedReload models.ServiceUserPrompt
	require.NoError(t, rds.WithContextReader(ctx).First(&mixedReload, mixed.ID).Error)
	assert.Equal(t, "Mixed user prompt", mixedReload.PromptText)
}

func TestPromptHelperMethods(t *testing.T) {
	serviceID := uint(123)
	userID := uint(456)
	userGroupID := uint(789)
	senderEmail := "<EMAIL>"
	senderDomain := "example.com"

	tests := []struct {
		name              string
		prompt            *models.ServiceUserPrompt
		expectedLevel     string
		expectedUser      bool
		expectedUserGroup bool
		expectedSender    bool
		expectedDomain    bool
		expectedService   bool
		expectedGlobal    bool
	}{
		{
			name: "user-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID: &serviceID,
				UserID:    &userID,
			},
			expectedLevel:     "user",
			expectedUser:      true,
			expectedUserGroup: false,
			expectedSender:    false,
			expectedDomain:    false,
			expectedService:   false,
			expectedGlobal:    false,
		},
		{
			name: "user-group-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				UserGroupID: &userGroupID,
			},
			expectedLevel:     "user_group",
			expectedUser:      false,
			expectedUserGroup: true,
			expectedSender:    false,
			expectedDomain:    false,
			expectedService:   false,
			expectedGlobal:    false,
		},
		{
			name: "sender-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				SenderEmail: &senderEmail,
			},
			expectedLevel:     "sender",
			expectedUser:      false,
			expectedUserGroup: false,
			expectedSender:    true,
			expectedDomain:    false,
			expectedService:   false,
			expectedGlobal:    false,
		},
		{
			name: "domain-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID:    &serviceID,
				SenderDomain: &senderDomain,
			},
			expectedLevel:     "domain",
			expectedUser:      false,
			expectedUserGroup: false,
			expectedSender:    false,
			expectedDomain:    true,
			expectedService:   false,
			expectedGlobal:    false,
		},
		{
			name: "service-specific prompt",
			prompt: &models.ServiceUserPrompt{
				ServiceID: &serviceID,
			},
			expectedLevel:     "service",
			expectedUser:      false,
			expectedUserGroup: false,
			expectedSender:    false,
			expectedDomain:    false,
			expectedService:   true,
			expectedGlobal:    false,
		},
		{
			name:              "global prompt",
			prompt:            &models.ServiceUserPrompt{},
			expectedLevel:     "global",
			expectedUser:      false,
			expectedUserGroup: false,
			expectedSender:    false,
			expectedDomain:    false,
			expectedService:   false,
			expectedGlobal:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expectedLevel, tt.prompt.GetResolutionLevel())
			assert.Equal(t, tt.expectedUser, tt.prompt.IsUserSpecific())
			assert.Equal(t, tt.expectedUserGroup, tt.prompt.IsUserGroupSpecific())
			assert.Equal(t, tt.expectedSender, tt.prompt.IsSenderSpecific())
			assert.Equal(t, tt.expectedDomain, tt.prompt.IsDomainSpecific())
			assert.Equal(t, tt.expectedService, tt.prompt.IsServiceSpecific())
			assert.Equal(t, tt.expectedGlobal, tt.prompt.IsGlobal())
		})
	}
}
func TestResolvePrompt_NoPromptFound(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestResolvePrompt_NoPromptFound: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID

	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType
	senderEmail := "<EMAIL>"

	// Execute
	prompt, err := ResolvePrompt(ctx, &serviceID, nil, nil, senderEmail, feature, promptType)

	// Assert - should return nil when no prompt found
	require.NoError(t, err)
	assert.Nil(t, prompt)
}

func TestEmailNormalization(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected string
	}{
		{
			name:     "uppercase email",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "mixed case email",
			email:    "<EMAIL>",
			expected: "<EMAIL>",
		},
		{
			name:     "email with spaces",
			email:    "  <EMAIL>  ",
			expected: "<EMAIL>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			serviceID := uint(123)
			prompt := &models.ServiceUserPrompt{
				ServiceID:   &serviceID,
				SenderEmail: &tt.email,
				Feature:     models.IsQuoteRequestAttachmentFeature,
				Type:        models.DeveloperPromptType,
				PromptText:  "Test",
				IsActive:    true,
			}

			err := prompt.Validate()
			require.NoError(t, err)
			assert.Equal(t, tt.expected, *prompt.SenderEmail)
		})
	}
}

func TestUpsert_ResolutionLevelChange(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping live DB test: run with TEST_LIVE_DB=true")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	serviceID := service.ID

	feature := models.IsQuoteRequestAttachmentFeature
	promptType := models.DeveloperPromptType
	senderDomain := "example.com"
	senderEmail := "<EMAIL>"

	// Step 1: Create a service-level prompt
	servicePrompt := &models.ServiceUserPrompt{
		ServiceID:  &serviceID,
		Feature:    feature,
		Type:       promptType,
		PromptText: "Service-level prompt",
		IsActive:   true,
	}
	require.NoError(t, Upsert(ctx, servicePrompt))

	// Verify service-level prompt exists
	resolved, err := ResolvePrompt(ctx, &serviceID, nil, nil, senderEmail, feature, promptType)
	require.NoError(t, err)
	require.NotNil(t, resolved)
	assert.Equal(t, "Service-level prompt", resolved.PromptText)
	assert.True(t, resolved.IsServiceSpecific())

	// Step 2: Update to domain-specific prompt (same feature/type, different resolution level)
	domainPrompt := &models.ServiceUserPrompt{
		ServiceID:    &serviceID,
		SenderDomain: &senderDomain,
		Feature:      feature,
		Type:         promptType,
		PromptText:   "Domain-level prompt",
		IsActive:     true,
	}
	require.NoError(t, Upsert(ctx, domainPrompt))

	// Step 3: Verify resolution works correctly (returns domain-specific prompt)
	resolved, err = ResolvePrompt(ctx, &serviceID, nil, nil, senderEmail, feature, promptType)
	require.NoError(t, err)
	require.NotNil(t, resolved)
	assert.Equal(t, "Domain-level prompt", resolved.PromptText)
	assert.True(t, resolved.IsDomainSpecific())
}
