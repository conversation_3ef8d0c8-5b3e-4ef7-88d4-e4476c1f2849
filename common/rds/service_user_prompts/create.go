package prompts

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Create(ctx context.Context, prompt *models.ServiceUserPrompt) error {
	return rds.WithContext(ctx).Create(prompt).Error
}

// Upsert creates or updates a prompt with rollback support
// On update, saves the current prompt_text to previous_prompt_text before overwriting
func Upsert(ctx context.Context, prompt *models.ServiceUserPrompt) error {
	// Normalize email and domain
	if prompt.SenderEmail != nil {
		normalized := strings.ToLower(strings.TrimSpace(*prompt.SenderEmail))
		if normalized == "" {
			prompt.SenderEmail = nil
		} else {
			prompt.SenderEmail = &normalized
		}
	}
	if prompt.SenderDomain != nil {
		normalized := strings.ToLower(strings.TrimSpace(*prompt.SenderDomain))
		if normalized == "" {
			prompt.SenderDomain = nil
		} else {
			prompt.SenderDomain = &normalized
		}
	}

	// Build query to find existing prompt
	query := rds.WithContext(ctx).Where("feature = ? AND type = ?", prompt.Feature, prompt.Type)

	// Add hierarchical conditions based on prompt level
	switch {
	case prompt.IsUserSpecific():
		query = query.Where(
			"service_id = ? AND user_id = ? AND user_group_id IS NULL "+
				"AND sender_email IS NULL AND sender_domain IS NULL",
			prompt.ServiceID,
			prompt.UserID,
		)
	case prompt.IsUserGroupSpecific():
		query = query.Where(
			"service_id = ? AND user_group_id = ? AND user_id IS NULL "+
				"AND sender_email IS NULL AND sender_domain IS NULL",
			prompt.ServiceID,
			prompt.UserGroupID,
		)
	case prompt.IsSenderSpecific():
		query = query.Where(
			"service_id = ? AND sender_email = ? AND user_id IS NULL "+
				"AND user_group_id IS NULL AND sender_domain IS NULL",
			prompt.ServiceID,
			prompt.SenderEmail,
		)
	case prompt.IsDomainSpecific():
		query = query.Where(
			"service_id = ? AND sender_domain = ? AND sender_email IS NULL "+
				"AND user_id IS NULL AND user_group_id IS NULL",
			prompt.ServiceID,
			prompt.SenderDomain,
		)
	case prompt.IsServiceSpecific():
		query = query.Where(
			"service_id = ? AND sender_domain IS NULL AND sender_email IS NULL "+
				"AND user_id IS NULL AND user_group_id IS NULL",
			prompt.ServiceID,
		)
	default:
		// Global
		query = query.Where(
			"service_id IS NULL AND user_id IS NULL AND user_group_id IS NULL " +
				"AND sender_email IS NULL AND sender_domain IS NULL",
		)
	}

	// Check if prompt exists
	var existing models.ServiceUserPrompt
	err := query.First(&existing).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new prompt
			return Create(ctx, prompt)
		}
		return err
	}

	// Validate the new prompt data before updating
	// This ensures validation runs on the new values, not the existing model
	if err := prompt.Validate(); err != nil {
		return err
	}

	// Save current prompt_text to previous_prompt_text for rollback
	updates := map[string]any{
		"prompt_text":          prompt.PromptText,
		"previous_prompt_text": existing.PromptText,
		"is_active":            prompt.IsActive,
		"model_name":           prompt.ModelName,
	}

	if err := rds.WithContext(ctx).Model(&existing).Updates(updates).Error; err != nil {
		return err
	}

	// Update the ID in the input prompt for caller reference
	prompt.ID = existing.ID

	return nil
}
