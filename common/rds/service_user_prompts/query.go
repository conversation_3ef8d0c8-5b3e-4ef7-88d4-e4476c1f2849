package prompts

import (
	"context"
	"errors"
	"strings"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// ResolvePrompt implements hierarchical prompt resolution via database query.
// Resolution order: User > UserGroup > Sender > Domain > Service > Global
// Returns nil if no prompt is found at any level (caller should use hardcoded default).
//
// Note: This queries the database on every call for correctness and simplicity.
// With <10K rows and proper indexes, performance is acceptable (~1-2ms).
// Similar to GetTMSListByServiceID which also queries on every invocation.
func ResolvePrompt(
	ctx context.Context,
	serviceID *uint,
	userID *uint,
	userGroupIDs []uint,
	senderEmail string,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {

	senderEmail = strings.ToLower(strings.TrimSpace(senderEmail))
	domain := extractDomain(senderEmail)

	// Run hierarchical waterfall database query
	var prompt models.ServiceUserPrompt
	query := rds.WithContextReader(ctx).
		Where("deleted_at IS NULL").
		Where("is_active = ?", true).
		Where("feature = ?", feature).
		Where("type = ?", promptType)

	// Build waterfall query conditions
	switch {
	case serviceID != nil && (userID != nil || len(userGroupIDs) > 0 ||
		senderEmail != ""):
		// Try all 6 levels in priority order
		conditions := []string{
			"(service_id IS NULL AND user_id IS NULL AND user_group_id IS NULL " +
				"AND sender_email IS NULL AND sender_domain IS NULL)",
		} // Global is always a fallback
		args := []any{}

		// User-specific
		if userID != nil {
			conditions = append([]string{
				"(service_id = ? AND user_id = ? AND user_group_id IS NULL " +
					"AND sender_email IS NULL AND sender_domain IS NULL)",
			}, conditions...)
			args = append([]any{*serviceID, *userID}, args...)
		}

		// UserGroup-specific (for any of the user's groups)
		if len(userGroupIDs) > 0 {
			conditions = append([]string{
				"(service_id = ? AND user_group_id IN ? AND user_id IS NULL " +
					"AND sender_email IS NULL AND sender_domain IS NULL)",
			}, conditions...)
			args = append([]any{*serviceID, userGroupIDs}, args...)
		}

		// Sender-specific
		if senderEmail != "" {
			conditions = append([]string{
				"(service_id = ? AND sender_email = ? AND user_id IS NULL " +
					"AND user_group_id IS NULL AND sender_domain IS NULL)",
			}, conditions...)
			args = append([]any{*serviceID, senderEmail}, args...)

			// Domain-specific
			if domain != "" {
				conditions = append([]string{
					"(service_id = ? AND sender_domain = ? AND " +
						"sender_email IS NULL AND user_id IS NULL AND " +
						"user_group_id IS NULL)",
				}, conditions...)
				args = append([]any{*serviceID, domain}, args...)
			}
		}

		// Service-specific
		conditions = append([]string{
			"(service_id = ? AND sender_domain IS NULL AND " +
				"sender_email IS NULL AND user_id IS NULL AND " +
				"user_group_id IS NULL)",
		}, conditions...)
		args = append([]any{*serviceID}, args...)

		query = query.Where(strings.Join(conditions, " OR "), args...)

	case serviceID != nil:
		// No user/group/email provided - try service and global levels
		query = query.Where(
			"(service_id = ? AND sender_domain IS NULL AND "+
				"sender_email IS NULL AND user_id IS NULL AND "+
				"user_group_id IS NULL) OR (service_id IS NULL AND "+
				"user_id IS NULL AND user_group_id IS NULL AND "+
				"sender_email IS NULL AND sender_domain IS NULL)",
			*serviceID,
		)
	default:
		// No service ID - only global level
		query = query.Where(
			"service_id IS NULL AND user_id IS NULL AND " +
				"user_group_id IS NULL AND sender_email IS NULL AND " +
				"sender_domain IS NULL",
		)
	}

	// Order by priority: user > user_group > sender > domain > service > global
	query = query.Order(`
		CASE
			WHEN user_id IS NOT NULL THEN 1
			WHEN user_group_id IS NOT NULL THEN 2
			WHEN sender_email IS NOT NULL THEN 3
			WHEN sender_domain IS NOT NULL THEN 4
			WHEN service_id IS NOT NULL THEN 5
			ELSE 6
		END
	`).Limit(1)

	err := query.First(&prompt).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // No prompt found at any level
		}
		return nil, err
	}

	return &prompt, nil
}

// GetPromptBySender gets a sender-specific prompt without fallback
func GetPromptBySender(
	ctx context.Context,
	serviceID uint,
	senderEmail string,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {
	senderEmail = strings.ToLower(strings.TrimSpace(senderEmail))

	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id = ? AND sender_email = ? AND user_id IS NULL AND user_group_id IS NULL "+
				"AND sender_domain IS NULL AND feature = ? AND type = ? AND is_active = ?",
			serviceID,
			senderEmail,
			feature,
			promptType,
			true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// GetPromptByUser gets a user-specific prompt without fallback
func GetPromptByUser(
	ctx context.Context,
	serviceID uint,
	userID uint,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {

	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id = ? AND user_id = ? AND user_group_id IS NULL AND sender_email IS NULL "+
				"AND sender_domain IS NULL AND feature = ? AND type = ? AND is_active = ?",
			serviceID,
			userID,
			feature,
			promptType,
			true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// GetPromptByUserGroup gets a user-group-specific prompt without fallback
func GetPromptByUserGroup(
	ctx context.Context,
	serviceID uint,
	userGroupID uint,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {

	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id = ? AND user_group_id = ? AND user_id IS NULL AND sender_email IS NULL "+
				"AND sender_domain IS NULL AND feature = ? AND type = ? AND is_active = ?",
			serviceID, userGroupID, feature, promptType, true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// GetPromptByDomain gets a domain-specific prompt without fallback
func GetPromptByDomain(
	ctx context.Context,
	serviceID uint,
	senderDomain string,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {

	senderDomain = strings.ToLower(strings.TrimSpace(senderDomain))

	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id = ? AND sender_domain = ? AND sender_email IS NULL AND "+
				"user_id IS NULL AND user_group_id IS NULL AND "+
				"feature = ? AND type = ? AND is_active = ?",
			serviceID, senderDomain, feature, promptType, true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// GetPromptByService gets a service-specific prompt without fallback
func GetPromptByService(
	ctx context.Context,
	serviceID uint,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {
	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id = ? AND sender_domain IS NULL AND sender_email IS NULL AND "+
				"user_id IS NULL AND user_group_id IS NULL AND "+
				"feature = ? AND type = ? AND is_active = ?",
			serviceID, feature, promptType, true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// GetGlobalPrompt gets a global prompt without fallback
func GetGlobalPrompt(
	ctx context.Context,
	feature models.PromptFeature,
	promptType models.PromptType,
) (*models.ServiceUserPrompt, error) {

	var prompt models.ServiceUserPrompt
	err := rds.WithContextReader(ctx).
		Where(
			"service_id IS NULL AND user_id IS NULL AND user_group_id IS NULL AND sender_email IS NULL "+
				"AND sender_domain IS NULL AND feature = ? AND type = ? AND is_active = ?",
			feature,
			promptType,
			true,
		).
		First(&prompt).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &prompt, nil
}

// extractDomain extracts the domain from an email address
func extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}
	return strings.ToLower(strings.TrimSpace(parts[1]))
}
