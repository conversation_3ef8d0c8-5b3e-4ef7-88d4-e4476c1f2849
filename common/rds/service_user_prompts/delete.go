package prompts

import (
	"context"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Delete performs a soft delete of a prompt by ID
func Delete(ctx context.Context, id uint) error {
	var prompt models.ServiceUserPrompt
	result := rds.WithContext(ctx).Delete(&prompt, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return nil // Already deleted or doesn't exist
	}
	return nil
}

// DeleteByIdentifiers performs a soft delete by resolution identifiers
func DeleteByIdentifiers(
	ctx context.Context,
	serviceID *uint,
	userID *uint,
	userGroupID *uint,
	senderEmail *string,
	senderDomain *string,
	feature models.PromptFeature,
	promptType models.PromptType,
) error {
	// Normalize inputs
	if senderEmail != nil {
		normalized := strings.ToLower(strings.TrimSpace(*senderEmail))
		senderEmail = &normalized
	}
	if senderDomain != nil {
		normalized := strings.ToLower(strings.TrimSpace(*senderDomain))
		senderDomain = &normalized
	}

	// Build query
	query := rds.WithContext(ctx).
		Where("feature = ? AND type = ?", feature, promptType)

	// Add hierarchical conditions
	switch {
	case userID != nil && serviceID != nil:
		// User-specific
		query = query.Where(
			"service_id = ? AND user_id = ? AND user_group_id IS NULL "+
				"AND sender_email IS NULL AND sender_domain IS NULL",
			serviceID,
			userID,
		)
	case userGroupID != nil && serviceID != nil:
		// UserGroup-specific
		query = query.Where(
			"service_id = ? AND user_group_id = ? AND user_id IS NULL "+
				"AND sender_email IS NULL AND sender_domain IS NULL",
			serviceID,
			userGroupID,
		)
	case senderEmail != nil && serviceID != nil:
		// Sender-specific
		query = query.Where(
			"service_id = ? AND sender_email = ? AND user_id IS NULL "+
				"AND user_group_id IS NULL AND sender_domain IS NULL",
			serviceID,
			senderEmail,
		)
	case senderDomain != nil && serviceID != nil:
		// Domain-specific
		query = query.Where(
			"service_id = ? AND sender_domain = ? AND sender_email IS NULL "+
				"AND user_id IS NULL AND user_group_id IS NULL",
			serviceID,
			senderDomain,
		)
	case serviceID != nil:
		// Service-specific
		query = query.Where(
			"service_id = ? AND sender_domain IS NULL AND sender_email IS NULL "+
				"AND user_id IS NULL AND user_group_id IS NULL",
			serviceID,
		)
	default:
		// Global
		query = query.Where(
			"service_id IS NULL AND user_id IS NULL AND " +
				"user_group_id IS NULL AND sender_email IS NULL AND " +
				"sender_domain IS NULL",
		)
	}

	// Soft delete
	var prompt models.ServiceUserPrompt
	result := query.Delete(&prompt)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return nil // Already deleted or doesn't exist
	}
	return nil
}
