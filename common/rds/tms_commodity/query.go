package tmscommodity

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetTMSCommoditiesByTMSID retrieves commodities by TMS ID, ordered by commodity description in ascending order
func GetTMSCommoditiesByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSCommodity, err error) {
	db := rds.WithContextReader(ctx).
		Where("tms_integration_id = ?", query.TMSID).
		Order("commodity_description ASC, commodity ASC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

// GetTMSCommodityByExternalID retrieves a single commodity by TMS ID and external TMS ID
// If customerID is provided, filters by customer ID to ensure correct commodity is returned
// (commodities are customer-scoped in systems like GlobalTranz)
func GetTMSCommodityByExternalID(
	ctx context.Context,
	tmsID uint,
	externalTMSID string,
	customerID *uint,
) (res models.TMSCommodity, err error) {
	db := rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND external_tms_id = ?", tmsID, externalTMSID)

	// Filter by customer ID if provided (required for customer-scoped commodities like GlobalTranz)
	if customerID != nil {
		db = db.Where("tms_customer_id = ?", *customerID)
	}

	err = db.First(&res).Error

	return res, err
}

// GetTMSCommoditiesByCustomerID retrieves commodities by TMS ID and customer ID
// If customerID is nil, returns commodities without a customer (for TMS systems without customer-specific commodities)
func GetTMSCommoditiesByCustomerID(
	ctx context.Context,
	tmsID uint,
	customerID *uint,
) (res []models.TMSCommodity, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", tmsID)

	if customerID != nil {
		db = db.Where("tms_customer_id = ?", *customerID)
	} else {
		db = db.Where("tms_customer_id IS NULL")
	}

	return res, db.Order("commodity_description ASC, commodity ASC").Find(&res).Error
}

// GetTMSCommoditiesByCustomerIDWithLimit retrieves commodities by TMS ID and customer ID with limit
func GetTMSCommoditiesByCustomerIDWithLimit(
	ctx context.Context,
	tmsID uint,
	customerID *uint,
	limit int,
) (res []models.TMSCommodity, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", tmsID)

	if customerID != nil {
		db = db.Where("tms_customer_id = ?", *customerID)
	} else {
		db = db.Where("tms_customer_id IS NULL")
	}

	if limit > 0 {
		db = db.Limit(limit)
	}

	return res, db.Order("commodity_description ASC, commodity ASC").Find(&res).Error
}

func GetTMSCommoditiesByCustomerIDAndCarrierTypeWithLimit(
	ctx context.Context,
	tmsID uint,
	customerID *uint,
	carrierType *int,
	limit int,
) (res []models.TMSCommodity, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", tmsID)

	if customerID != nil {
		db = db.Where("tms_customer_id = ?", *customerID)
	}

	if carrierType != nil {
		db = db.Where("carrier_type = ?", *carrierType)
	}

	if limit > 0 {
		db = db.Limit(limit)
	}

	return res, db.Order("commodity_description ASC, commodity ASC").Find(&res).Error
}
