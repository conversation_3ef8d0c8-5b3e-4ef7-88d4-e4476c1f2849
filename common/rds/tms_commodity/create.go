package tmscommodity

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

const (
	EmptyTMSCommoditiesSlice = "empty slice of TMSCommodities"
)

// RefreshTMSCommodities updates existing TMS commodities and inserts new ones.
// Database unique constraints prevent race conditions; app-level deduplication optimizes performance.
func RefreshTMSCommodities(ctx context.Context, commodities *[]models.TMSCommodity) error {
	if len(*commodities) == 0 {
		return errors.New(EmptyTMSCommoditiesSlice)
	}

	// Deduplicate commodities first
	deduped := deduplicateCommodities(commodities)

	if len(deduped) == 0 {
		return nil
	}

	// Get TMS ID from first commodity (all should have same TMS ID)
	tmsID := deduped[0].TMSIntegrationID

	// Build a map of existing commodities for efficient lookup
	// Key format: "externalTMSID:customerID" or "externalTMSID:null"
	existingMap := make(map[string]models.TMSCommodity)
	var existingCommodities []models.TMSCommodity

	// Fetch all existing commodities for this TMS that match our external IDs
	externalIDs := make([]string, 0, len(deduped))
	for _, c := range deduped {
		externalIDs = append(externalIDs, c.ExternalTMSID)
	}

	err := rds.WithContext(ctx).
		Where("tms_integration_id = ? AND external_tms_id IN ?", tmsID, externalIDs).
		Find(&existingCommodities).Error
	if err != nil {
		return fmt.Errorf("failed to fetch existing commodities: %w", err)
	}

	// Build lookup map
	for _, existing := range existingCommodities {
		key := buildCommodityKey(existing.ExternalTMSID, existing.TMSCustomerID)
		existingMap[key] = existing
	}

	// Prepare all commodities for batch upsert
	// Set ID for existing records (will be updated), leave ID as 0 for new records (will be inserted)
	allCommodities := make([]models.TMSCommodity, 0, len(deduped))
	for _, commodity := range deduped {
		key := buildCommodityKey(commodity.ExternalTMSID, commodity.TMSCustomerID)
		if existing, found := existingMap[key]; found {
			// Update existing - set ID so Save will update instead of insert
			commodity.ID = existing.ID
		}
		// Set updated_at for all commodities
		commodity.UpdatedAt = time.Now()
		allCommodities = append(allCommodities, commodity)
	}

	// Separate updates and inserts for optimal batch operations
	var toUpdate []models.TMSCommodity
	var toInsert []models.TMSCommodity
	for _, commodity := range allCommodities {
		if commodity.ID != 0 {
			toUpdate = append(toUpdate, commodity)
		} else {
			toInsert = append(toInsert, commodity)
		}
	}

	// Batch insert new records using ON CONFLICT DO NOTHING to handle concurrent inserts
	// Split by customer_id presence to match the two partial unique indexes
	if len(toInsert) > 0 {
		var withCustomerID []models.TMSCommodity
		var withoutCustomerID []models.TMSCommodity
		for _, commodity := range toInsert {
			if commodity.TMSCustomerID != nil {
				withCustomerID = append(withCustomerID, commodity)
			} else {
				withoutCustomerID = append(withoutCustomerID, commodity)
			}
		}

		// Insert commodities with customer_id using ON CONFLICT DO NOTHING
		// Matches index: idx_tms_id_tms_commodity
		// (tms_integration_id, tms_customer_id, external_tms_id) WHERE tms_customer_id IS NOT NULL
		// TargetWhere is required to match the partial unique index's WHERE condition
		if len(withCustomerID) > 0 {
			if err := rds.WithContext(ctx).
				Clauses(
					clause.OnConflict{
						Columns: []clause.Column{
							{Name: "tms_integration_id"},
							{Name: "tms_customer_id"},
							{Name: "external_tms_id"},
						},
						TargetWhere: clause.Where{
							Exprs: []clause.Expression{
								clause.Expr{SQL: "tms_customer_id IS NOT NULL"},
							},
						},
						DoNothing: true,
					},
				).
				CreateInBatches(&withCustomerID, 1000).Error; err != nil {
				return fmt.Errorf("failed to batch insert commodities with customer_id: %w", err)
			}
		}

		// Insert commodities without customer_id using ON CONFLICT DO NOTHING
		// Matches index: idx_tms_id_tms_commodity_null_customer
		// (tms_integration_id, external_tms_id) WHERE tms_customer_id IS NULL
		// TargetWhere is required to match the partial unique index's WHERE condition
		if len(withoutCustomerID) > 0 {
			if err := rds.WithContext(ctx).
				Clauses(
					clause.OnConflict{
						Columns: []clause.Column{
							{Name: "tms_integration_id"},
							{Name: "external_tms_id"},
						},
						TargetWhere: clause.Where{
							Exprs: []clause.Expression{
								clause.Expr{SQL: "tms_customer_id IS NULL"},
							},
						},
						DoNothing: true,
					},
				).
				CreateInBatches(&withoutCustomerID, 1000).Error; err != nil {
				return fmt.Errorf("failed to batch insert commodities without customer_id: %w", err)
			}
		}
	}

	// Batch update existing records in transactions
	// While still N operations, transactions allow the database to optimize and batch commits
	// This is significantly better than individual commits and avoids connection overhead
	if len(toUpdate) > 0 {
		batchSize := 1000
		for i := 0; i < len(toUpdate); i += batchSize {
			end := i + batchSize
			if end > len(toUpdate) {
				end = len(toUpdate)
			}
			batch := toUpdate[i:end]

			// Execute all Save operations in a single transaction
			// This allows the database to optimize the batch and reduces connection overhead
			if err := rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
				for _, commodity := range batch {
					if err := tx.Save(&commodity).Error; err != nil {
						return fmt.Errorf("failed to save commodity (ID: %d, ExternalTMSID: %s): %w",
							commodity.ID, commodity.ExternalTMSID, err)
					}
				}
				return nil
			}); err != nil {
				return err
			}
		}
	}

	return nil
}

func Create(ctx context.Context, commodity models.TMSCommodity) error {
	return rds.WithContext(ctx).Create(&commodity).Error
}

// buildCommodityKey creates a unique key for a commodity based on external_tms_id and tms_customer_id.
// Used for efficient lookup and deduplication.
func buildCommodityKey(externalTMSID string, tmsCustomerID *uint) string {
	if tmsCustomerID != nil {
		return fmt.Sprintf("%s:%d", externalTMSID, *tmsCustomerID)
	}
	return fmt.Sprintf("%s:null", externalTMSID)
}

func deduplicateCommodities(commodities *[]models.TMSCommodity) []models.TMSCommodity {
	if commodities == nil {
		return nil
	}

	deduplicated := make([]models.TMSCommodity, 0, len(*commodities))
	seen := make(map[string]bool)
	for _, commodity := range *commodities {
		// Create unique key: tms_integration_id:tms_customer_id:external_tms_id
		var customerIDStr string
		if commodity.TMSCustomerID != nil {
			customerIDStr = fmt.Sprintf("%d", *commodity.TMSCustomerID)
		} else {
			customerIDStr = "null"
		}
		key := fmt.Sprintf("%d:%s:%s", commodity.TMSIntegrationID, customerIDStr, commodity.ExternalTMSID)
		if !seen[key] {
			seen[key] = true
			deduplicated = append(deduplicated, commodity)
		}
	}
	return deduplicated
}
