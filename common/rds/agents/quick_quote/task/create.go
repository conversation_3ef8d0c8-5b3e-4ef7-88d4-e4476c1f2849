package quickquoteagenttask

import (
	"context"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateQuickQuoteAgentTask creates a new quick quote agent task.
func CreateQuickQuoteAgentTask(ctx context.Context, task *agentmodels.QuickQuoteAgentTask) error {
	return rds.WithContext(ctx).Create(task).Error
}

// CreateDefaultTasksForAgent creates the default task workflow (pricing + draft email reply) for a QQ agent.
// This is called when a QQ agent is matched but has no associated tasks created/configured yet.
// Uses a transaction to ensure all tasks are created atomically.
func CreateDefaultTasksForAgent(ctx context.Context, agentID uint) error {
	tasks := []agentmodels.QuickQuoteAgentTask{
		{
			QuickQuoteAgentID: agentID,
			Step:              0,
			Action:            agentmodels.PricingLookupAction,
			MaxRetriesAllowed: 1,
			Metadata: agentmodels.TaskMetadata{
				PricingLookupTask: &agentmodels.PricingLookupTask{},
			},
			CreatedByType: agentmodels.TaskCreatedBySystem,
		},
		{
			QuickQuoteAgentID: agentID,
			Step:              1,
			Action:            agentmodels.DraftReplyEmailAction,
			MaxRetriesAllowed: 1,
			Metadata: agentmodels.TaskMetadata{
				DraftEmailTask: &agentmodels.EmailTask{},
			},
			CreatedByType: agentmodels.TaskCreatedBySystem,
		},
		{
			QuickQuoteAgentID: agentID,
			Step:              2,
			Action:            agentmodels.AddEmailLabelAction,
			MaxRetriesAllowed: 1,
			FailOpen:          true,
			CreatedByType:     agentmodels.TaskCreatedBySystem,
			Metadata: agentmodels.TaskMetadata{
				AddEmailLabelTask: &agentmodels.AddEmailLabelTask{
					Label: models.DrumkitQuickQuoteAgentLabel,
				},
			},
		},
	}

	// Use a transaction to ensure all tasks are created atomically
	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for i := range tasks {
			if err := tx.Create(&tasks[i]).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
