package quickquoteagenttask

import (
	"context"
	"errors"

	"gorm.io/gorm"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateQuickQuoteAgentTaskByID updates a quick quote agent task by ID.
func UpdateQuickQuoteAgentTaskByID(
	ctx context.Context,
	taskID uint,
	updatedTask *agentmodels.QuickQuoteAgentTask,
) error {

	if updatedTask == nil {
		return errors.New("updated task is nil")
	}

	err := rds.WithContext(ctx).
		Model(&agentmodels.QuickQuoteAgentTask{}).
		Where("id = ?", taskID).Updates(updatedTask).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("task not found")
		}
		return err
	}

	return nil
}
