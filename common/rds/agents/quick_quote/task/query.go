package quickquoteagenttask

import (
	"context"
	"errors"

	"gorm.io/gorm"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetQuickQuoteAgentTaskByID gets a quick quote agent task by ID.
func GetQuickQuoteAgentTaskByID(ctx context.Context, taskID uint) (*agentmodels.QuickQuoteAgentTask, error) {
	var task agentmodels.QuickQuoteAgentTask

	err := rds.WithContextReader(ctx).First(&task, taskID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &task, nil
}
