package quickquoteagent

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetQuickQuoteAgentsForUser returns agents assigned to a user via direct association
// (quick_quote_agent_users) OR via user groups (quick_quote_agent_groups -> user_group_users).
// Results are deduplicated in case a user is assigned the same agent through multiple paths.
// Only returns agents belonging to the specified service.
func GetQuickQuoteAgentsForUser(
	ctx context.Context,
	userID uint,
	serviceID uint,
) ([]agentmodels.QuickQuoteAgent, error) {

	var agents []agentmodels.QuickQuoteAgent

	// Query agents directly assigned to user OR assigned to user's groups
	// Using subqueries to find matching agent IDs, then fetching the full agent records
	// Also filter by service_id to ensure only agents for the correct service are returned
	err := rds.WithContextReader(ctx).
		Model(&agentmodels.QuickQuoteAgent{}).
		Preload("Config.Tasks", func(db *gorm.DB) *gorm.DB {
			return db.Order("step ASC")
		}).
		Distinct().
		Where("service_id = ?", serviceID).
		Where(`
			id IN (
				SELECT quick_quote_agent_id FROM quick_quote_agent_users WHERE user_id = ?
			)
			OR id IN (
				SELECT qag.quick_quote_agent_id 
				FROM quick_quote_agent_groups qag
				INNER JOIN user_group_users ugu ON ugu.user_group_id = qag.user_group_id
				WHERE ugu.user_id = ?
			)
		`, userID, userID).
		Find(&agents).Error

	return agents, err
}

// GetServiceWideAgents returns agents marked as IsServiceWide for a given service.
// These agents apply to all users in the service regardless of direct assignment.
func GetServiceWideAgents(ctx context.Context, serviceID uint) ([]agentmodels.QuickQuoteAgent, error) {
	var agents []agentmodels.QuickQuoteAgent

	err := rds.WithContextReader(ctx).
		Model(&agentmodels.QuickQuoteAgent{}).
		Preload("Config.Tasks", func(db *gorm.DB) *gorm.DB {
			return db.Order("step ASC")
		}).
		Where("service_id = ? AND is_service_wide = ?", serviceID, true).
		Find(&agents).Error

	return agents, err
}

// GetAgentByID returns a single QuickQuoteAgent by ID.
func GetAgentByID(ctx context.Context, agentID uint) (*agentmodels.QuickQuoteAgent, error) {
	var agent agentmodels.QuickQuoteAgent

	err := rds.WithContextReader(ctx).
		Preload("Config.Tasks", func(db *gorm.DB) *gorm.DB {
			return db.Order("step ASC")
		}).
		Where("id = ?", agentID).
		First(&agent).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &agent, nil
}

// HasDuplicateLaneInThreadForUser checks if a quote request with the same lane data already
// exists in the thread FOR THE SAME USER and has been processed by an agent.
// This prevents drafting duplicate emails for the same lane within a conversation for the same user,
// while allowing different users who receive the same email to each get their own agent invocation.
func HasDuplicateLaneInThreadForUser(
	ctx context.Context,
	threadID string,
	userID uint,
	lane LaneFields,
) (bool, error) {
	var count int64

	query := rds.WithContextReader(ctx).
		Model(&models.QuoteRequest{}).
		Where("thread_id = ?", threadID).
		Where("user_id = ?", userID).
		Where("quick_quote_agent_id IS NOT NULL")

	// Match lane fields using the read-only query fields (indexed for performance)
	if lane.CustomerName != "" {
		query = query.Where("suggested_customer_name = ?", lane.CustomerName)
	}
	if lane.TransportType != "" {
		query = query.Where("suggested_transport_type = ?", lane.TransportType)
	}
	if lane.PickupCity != "" {
		query = query.Where("suggested_pickup_city = ?", lane.PickupCity)
	}
	if lane.PickupState != "" {
		query = query.Where("suggested_pickup_state = ?", lane.PickupState)
	}
	if lane.PickupZip != "" {
		query = query.Where("suggested_pickup_zip = ?", lane.PickupZip)
	}
	if lane.DropoffCity != "" {
		query = query.Where("suggested_dropoff_city = ?", lane.DropoffCity)
	}
	if lane.DropoffState != "" {
		query = query.Where("suggested_dropoff_state = ?", lane.DropoffState)
	}
	if lane.DropoffZip != "" {
		query = query.Where("suggested_dropoff_zip = ?", lane.DropoffZip)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// LaneFields contains the key fields that define a unique lane for deduplication purposes.
type LaneFields struct {
	CustomerName  string
	TransportType string
	PickupCity    string
	PickupState   string
	PickupZip     string
	DropoffCity   string
	DropoffState  string
	DropoffZip    string
}

// NewLaneFieldsFromQuoteRequest extracts lane fields from a QuoteRequest for deduplication.
func NewLaneFieldsFromQuoteRequest(qr models.QuoteRequest) LaneFields {
	return LaneFields{
		CustomerName:  qr.ReadOnlySuggestedRequest.CustomerName,
		TransportType: string(qr.ReadOnlySuggestedRequest.TransportType),
		PickupCity:    qr.ReadOnlySuggestedRequest.PickupCity,
		PickupState:   qr.ReadOnlySuggestedRequest.PickupState,
		PickupZip:     qr.ReadOnlySuggestedRequest.PickupZip,
		DropoffCity:   qr.ReadOnlySuggestedRequest.DropoffCity,
		DropoffState:  qr.ReadOnlySuggestedRequest.DropoffState,
		DropoffZip:    qr.ReadOnlySuggestedRequest.DropoffZip,
	}
}
