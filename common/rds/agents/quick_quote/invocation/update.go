package quickquoteagentinvocation

import (
	"context"
	"errors"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateQuickQuoteAgentInvocationByID updates a quick quote agent invocation.
func UpdateQuickQuoteAgentInvocationByID(
	ctx context.Context,
	invocationID uint,
	updatedInvocation *agentmodels.QuickQuoteAgentInvocation,
) error {

	if updatedInvocation == nil {
		return errors.New("updated invocation is nil")
	}

	return rds.WithContext(ctx).
		Model(&agentmodels.QuickQuoteAgentInvocation{}).
		Where("id = ?", invocationID).
		Updates(updatedInvocation).Error
}
