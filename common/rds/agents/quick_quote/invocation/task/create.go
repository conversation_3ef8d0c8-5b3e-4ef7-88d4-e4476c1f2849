package quickquoteagentinvocationtask

import (
	"context"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateQuickQuoteAgentTaskInvocation creates a new quick quote agent task invocation.
func CreateQuickQuoteAgentTaskInvocation(
	ctx context.Context,
	taskInvocation *agentmodels.QuickQuoteAgentTaskInvocation,
) error {
	return rds.WithContext(ctx).Create(taskInvocation).Error
}
