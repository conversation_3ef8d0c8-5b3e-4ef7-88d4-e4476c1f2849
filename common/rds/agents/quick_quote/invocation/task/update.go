package quickquoteagentinvocationtask

import (
	"context"
	"errors"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateQuickQuoteAgentTaskInvocationByID updates a quick quote agent task invocation.
func UpdateQuickQuoteAgentTaskInvocationByID(
	ctx context.Context,
	taskInvocationID uint,
	updatedTaskInvocation *agentmodels.QuickQuoteAgentTaskInvocation,
) error {

	if updatedTaskInvocation == nil {
		return errors.New("updated task invocation is nil")
	}

	return rds.WithContext(ctx).
		Model(&agentmodels.QuickQuoteAgentTaskInvocation{}).
		Where("id = ?", taskInvocationID).
		Updates(updatedTaskInvocation).Error
}
