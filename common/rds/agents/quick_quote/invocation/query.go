package quickquoteagentinvocation

import (
	"context"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetInvocationsByThreadID returns all quick quote agent invocations for a given thread ID.
// Additionally, if withTasks is true, it will preload the task invocations for each invocation.
func GetInvocationsByThreadID(
	ctx context.Context,
	threadID string,
	serviceID uint,
	withTasks bool,
) ([]agentmodels.QuickQuoteAgentInvocation, error) {

	var invocations []agentmodels.QuickQuoteAgentInvocation

	query := rds.WithContextReader(ctx).Where("thread_id = ? AND service_id = ?", threadID, serviceID)

	if withTasks {
		query = query.Preload("TaskInvocations")
	}

	err := query.Find(&invocations).Error
	if err != nil {
		return []agentmodels.QuickQuoteAgentInvocation{}, err
	}

	return invocations, nil
}
