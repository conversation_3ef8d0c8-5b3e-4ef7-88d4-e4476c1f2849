package quickquoteagentinvocation

import (
	"context"

	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateQuickQuoteAgentInvocation creates a new quick quote agent invocation.
// The invocation pointer is updated with the created record's ID after creation.
func CreateQuickQuoteAgentInvocation(ctx context.Context, invocation *agentmodels.QuickQuoteAgentInvocation) error {
	return rds.WithContext(ctx).Create(invocation).Error
}
