package prompts

import (
	"fmt"
	"regexp"
	"strings"
)

// Guidelines for updating DetermineQuoteRequestLabelsSystemPrompt prompt:
//  1. When making changes to this file you must test using Braintrust!!
//  2. Add updated prompt to https://www.braintrust.dev/app/Drumkit/p/Email%20Classification/prompts as a new prompt.
//     2a. Prompt naming convention follows: DetermineQuoteRequestLabelsSystemPrompt [Improvement Goal/Focus]
//     2b. Rename prompts in relation to release PR # (ex: "Release #1826")
//  3. Test against a dataset to backtest prompt accuracy. Compare with current prompt to ensure we are not regressing.
//     - Use Braintrust Experiments to test new prompt and current prod prompt against datasets.
//     - Run experiment with JSONDiff and Factuality scorers
//  4. If updated prompt performs well then you can commit.
//  5. Include links to Braintrust Experiments in the PR description.
//  6. Bonus: add more production emails to the dataset to test against. Make sure to strip system prompt and only leave
//     "body" and "subject" fields in dataset input. And make sure the expected output is accurate to behavior we want.
//
// DetermineQuoteRequestLabelsSystemPrompt is used to classify an email for batch and LTL quote requests.
// It takes two boolean arguments for string formatting:
//  1. isQuickQuoteEnabled: If true, classify is_batch (multiple distinct loads of any type: FTL and/or LTL).
//  2. isLTLQuickQuoteEnabled: If true, classify is_ltl (whether any request is LTL).
const DetermineQuoteRequestLabelsSystemPrompt = `
You are a freight broker tasked with classifying whether an email requests
one quote or multiple quotes (batch) and whether any request is LTL.

Definitions
- "Quote request" means the sender is asking for a price/rate.
- A "load" is one origin -> destination movement (FTL or LTL).
- "Bare minimum" to price a quote is pickup and dropoff locations (zip or city
  is sufficient; street address is optional).

Based on the enabled features, output a JSON object with boolean
fields 'is_batch' and 'is_ltl'.

- **Feature Configuration**:
  - IsQuickQuoteEnabled: %t
  - IsLTLQuickQuoteEnabled: %t

Hard rules about feature flags:
- If a feature flag is false, set its field to false and do NOT attempt to
  infer it. Ignore any guidance or examples for that disabled feature.

ONLY read the following sections when the corresponding feature is true.

When IsQuickQuoteEnabled is true (is_batch):
- is_batch is true only when the email asks for quotes for more than one
  distinct load (FTL and/or LTL).
- Multiple stops within a single load do NOT make it batch.
- Indicators of batch: tables of lanes; numbered/bulleted lists with multiple
  origin/destination pairs or dates; phrases like "multiple quotes",
  "several loads", "a few lanes", or "batch".
- If the email is tendering/building a shipment or not asking for a price,
  set is_batch: false.
- If ambiguous or under-specified, default is_batch: false.

When IsLTLQuickQuoteEnabled is true (is_ltl):
- Set is_ltl: true if the email is primarily requesting LTL.
- Common cues: "LTL", "pallet", "skid", "class 70", "NMFC", dimensions/weights
  typical for LTL, "partial truckload".
- Otherwise set is_ltl: false.

Outcomes (when both features are true):
- is_batch: false, is_ltl: false  -> single FTL quote
- is_batch: true,  is_ltl: false  -> multiple FTL quotes
- is_batch: false, is_ltl: true   -> single LTL quote
- is_batch: true,  is_ltl: true   -> mixture of FTL and LTL or multiple LTL

Example scenarios:

A) QuickQuote: true, LTL: false (LTL disabled -> always is_ltl: false)
Subject: LTL quote please
Email Body: 2 pallets from Dallas, TX to Houston, TX
Expected Response: {"is_batch": false, "is_ltl": false}

B) QuickQuote: true, LTL: false
Subject: Quotes for 2/10
Email Body:
give me quotes for the following:
| PU | Drop | PU Date | Transport |
| --- | --- | --- | --- |
| Philadelphia, PA | Birmingham, AL | 2/5 | Van |
| Boston, MA | NY, NY | 2/8 | flatbed |
Expected Response: {"is_batch": true, "is_ltl": false}

C) QuickQuote: false, LTL: true
Subject: Multiple FTL lanes
Email Body:
- Chicago, IL to Miami, FL
- Denver, CO to Phoenix, AZ
Expected Response: {"is_batch": false, "is_ltl": false}

D) QuickQuote: false, LTL: true
Subject: LTL request
Email Body: 3 pallets, class 60, Los Angeles, CA to Seattle, WA
Expected Response: {"is_batch": false, "is_ltl": true}

E) QuickQuote: true, LTL: true
Subject: Mixed requests
Email Body:
1. FTL from Chicago to Miami
2. 3 pallets LTL from Denver to Phoenix
Expected Response: {"is_batch": true, "is_ltl": true}

F) QuickQuote: true, LTL: true
Subject: Single FTL Quote
Email Body: Can I get a rate for a dry van from Los Angeles to Seattle?
Expected Response: {"is_batch": false, "is_ltl": false}

Strict output requirements:
- Return ONLY a single, minified JSON object on one line and nothing else
  (no prose, no code fences, no trailing text).
- The JSON MUST contain exactly the keys "is_batch" and "is_ltl".
- Do NOT output multiple JSON objects or duplicate the object.
- Do NOT include any other keys.
- If uncertain for a specific classification, default that value to false.
`

// BuildDetermineQuoteRequestLabelsSystemPrompt returns a customized version of the base prompt with sections removed
// depending on which feature flags are enabled.
func BuildDetermineQuoteRequestLabelsSystemPrompt(isQuickQuoteEnabled, isLTLQuickQuoteEnabled bool) string {
	prompt := DetermineQuoteRequestLabelsSystemPrompt

	if !isQuickQuoteEnabled {
		//nolint:lll
		reQ := regexp.MustCompile(`(?s)\nWhen IsQuickQuoteEnabled is true \(is_batch\):.*?\n\n(When IsLTLQuickQuoteEnabled is true \(is_ltl\):|Outcomes \(when both features are true\):|Example scenarios:)`)
		prompt = reQ.ReplaceAllString(prompt, "\n$1")
	}

	if !isLTLQuickQuoteEnabled {
		//nolint:lll
		reL := regexp.MustCompile(`(?s)\nWhen IsLTLQuickQuoteEnabled is true \(is_ltl\):.*?\n\n(Outcomes \(when both features are true\):|Example scenarios:)`)
		prompt = reL.ReplaceAllString(prompt, "\n$1")
	}

	if !isQuickQuoteEnabled || !isLTLQuickQuoteEnabled {
		reOut := regexp.MustCompile(`(?s)\nOutcomes \(when both features are true\):.*?\n\n(Example scenarios:)`)
		prompt = reOut.ReplaceAllString(prompt, "\n$1")
	}

	switch {
	case isQuickQuoteEnabled && isLTLQuickQuoteEnabled:
		// keep all A-F

	case isQuickQuoteEnabled && !isLTLQuickQuoteEnabled:
		// keep A, B; remove C, D, E, F
		prompt = removeExample(prompt, "C", "D")
		prompt = removeExample(prompt, "D", "E")
		prompt = removeExample(prompt, "E", "F")
		prompt = removeExampleToStrict(prompt, "F")

	case !isQuickQuoteEnabled && isLTLQuickQuoteEnabled:
		// keep C, D; remove A, B, E, F
		prompt = removeExample(prompt, "A", "B")
		prompt = removeExample(prompt, "B", "C")
		prompt = removeExample(prompt, "E", "F")
		prompt = removeExampleToStrict(prompt, "F")

	default:
		// both disabled: remove the entire Example scenarios block
		reEx := regexp.MustCompile(`(?s)\nExample scenarios:\n.*?\n\n(Strict output requirements:)`)
		prompt = reEx.ReplaceAllString(prompt, "\n$1")
	}

	for strings.Contains(prompt, "\n\n\n") {
		prompt = strings.ReplaceAll(prompt, "\n\n\n", "\n\n")
	}

	return fmt.Sprintf(prompt, isQuickQuoteEnabled, isLTLQuickQuoteEnabled)
}

// removeExample removes an example block like "\nA) ... \n\nB)" keeping the "B)" marker.
func removeExample(s, letter, next string) string {
	re := regexp.MustCompile(`(?s)\n` + letter + `\)[\s\S]*?\n\n(` + next + `\))`)
	return re.ReplaceAllString(s, "\n$1")
}

// removeExampleToStrict removes the last example block like "\nF) ... \n\nStrict output requirements: ..."
// keeping the "Strict output requirements:" marker.
func removeExampleToStrict(s, letter string) string {
	re := regexp.MustCompile(`(?s)\n` + letter + `\)[\s\S]*?\n\n(Strict output requirements:)`)
	return re.ReplaceAllString(s, "\n$1")
}
