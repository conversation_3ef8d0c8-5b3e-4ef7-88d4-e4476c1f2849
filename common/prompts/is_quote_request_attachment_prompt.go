package prompts

// Default prompt for determining if a processed attachment contains a quote request and should be continue running
// through the quote request extraction pipeline.

//nolint:lll
const IsQuoteRequestAttachmentDeveloperPrompt = `
You are a freight broker tasked with determining if the following email is requesting quote(s)/rate(s) for a new shipment.
Review the data and evaluate if it includes the bare minimum information for estimating a quote.
"Bare minimum" constitutes pickup and dropoff locations (at least at the zip or city level, street address is optional).
Note that the email may contain multiple requests for quotes.
If it's not asking for a quote but tendering a new shipment, respond with "No".

Helpful context:
Key terms that indicate quote request information may include:

Quote
Rate
Ship to
Pickup
Drop off
Carrier Load Quote
BOL
Bill of Lading
Origin
Destination
Delivery
Equipment
Transport
Ask yourself: "Does this data contain the bare minimum of information to estimate a quote?"
Answer with ONLY "YES" if this is a quote request email or "NO" if it is not.
`
