package prompts

import (
	"strings"
	"testing"
)

func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

func TestBuildDetermineQuoteRequestLabelsSystemPrompt(t *testing.T) {
	tests := []struct {
		name               string
		isQuickQuote       bool
		isLTLQuickQuote    bool
		expectQuickSection bool
		expectLTLSection   bool
	}{
		{
			name:               "both features enabled",
			isQuickQuote:       true,
			isLTLQuickQuote:    true,
			expectQuickSection: true,
			expectLTLSection:   true,
		},
		{
			name:               "only QuickQuote enabled",
			isQuickQuote:       true,
			isLTLQuickQuote:    false,
			expectQuickSection: true,
			expectLTLSection:   false,
		},
		{
			name:               "only LTL enabled",
			isQuickQuote:       false,
			isLTLQuickQuote:    true,
			expectQuickSection: false,
			expectLTLSection:   true,
		},
		{
			name:               "both disabled",
			isQuickQuote:       false,
			isLTLQuickQuote:    false,
			expectQuickSection: false,
			expectLTLSection:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			out := BuildDetermineQuoteRequestLabelsSystemPrompt(
				tt.isQuickQuote,
				tt.isLTLQuickQuote,
			)

			if !contains(out, "IsQuickQuoteEnabled: "+strings.ToLower(strings.Trim(fmtBool(tt.isQuickQuote), " "))) {
				t.Errorf("expected IsQuickQuoteEnabled to be %v", tt.isQuickQuote)
			}

			//nolint:lll
			if !contains(out, "IsLTLQuickQuoteEnabled: "+strings.ToLower(strings.Trim(fmtBool(tt.isLTLQuickQuote), " "))) {
				t.Errorf(
					"expected IsLTLQuickQuoteEnabled to be %v",
					tt.isLTLQuickQuote,
				)
			}

			quickSectionPresent := contains(out, "When IsQuickQuoteEnabled is true (is_batch):")
			if quickSectionPresent != tt.expectQuickSection {
				t.Errorf(
					"QuickQuote section presence mismatch. expected %v, got %v",
					tt.expectQuickSection,
					quickSectionPresent,
				)
			}

			ltlSectionPresent := contains(out, "When IsLTLQuickQuoteEnabled is true (is_ltl):")
			if ltlSectionPresent != tt.expectLTLSection {
				t.Errorf(
					"LTLQuickQuote section presence mismatch. expected %v, got %v",
					tt.expectLTLSection,
					ltlSectionPresent,
				)
			}

			if contains(out, "\n\n\n") {
				t.Error("result still contains triple newlines")
			}
		})
	}
}

func fmtBool(b bool) string {
	if b {
		return "true"
	}

	return "false"
}
