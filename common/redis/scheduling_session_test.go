package redis

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBuildSchedulingSessionKey(t *testing.T) {
	tests := []struct {
		name          string
		userID        uint
		integrationID uint
		expected      string
	}{
		{
			name:          "basic key format",
			userID:        123,
			integrationID: 456,
			expected:      "scheduling:session:123:456",
		},
		{
			name:          "zero values",
			userID:        0,
			integrationID: 0,
			expected:      "scheduling:session:0:0",
		},
		{
			name:          "large values",
			userID:        999999,
			integrationID: 888888,
			expected:      "scheduling:session:999999:888888",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildSchedulingSessionKey(tt.userID, tt.integrationID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetSchedulingBrowserbaseSession(t *testing.T) {
	ctx := context.Background()
	userID := uint(123)
	integrationID := uint(456)
	key := buildSchedulingSessionKey(userID, integrationID)

	tests := []struct {
		name            string
		setupMock       func(t *testing.T, mock redismock.ClientMock)
		expectedSession *SchedulingBrowserbaseSession
		expectedFound   bool
		expectedErr     bool
	}{
		{
			name: "cache hit - session found",
			setupMock: func(t *testing.T, mock redismock.ClientMock) {
				session := SchedulingBrowserbaseSession{
					BrowserbaseSessionID: "session-123",
					CreatedAt:            time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
					LastUsedAt:           time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
				}
				jsonBytes, err := json.Marshal(session)
				require.NoError(t, err)
				mock.ExpectGet(key).SetVal(string(jsonBytes))
			},
			expectedSession: &SchedulingBrowserbaseSession{
				BrowserbaseSessionID: "session-123",
				CreatedAt:            time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				LastUsedAt:           time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
			},
			expectedFound: true,
			expectedErr:   false,
		},
		{
			name: "cache miss - session not found",
			setupMock: func(_ *testing.T, mock redismock.ClientMock) {
				mock.ExpectGet(key).RedisNil()
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedErr:     false,
		},
		{
			name: "Redis error",
			setupMock: func(_ *testing.T, mock redismock.ClientMock) {
				mock.ExpectGet(key).SetErr(errors.New("connection failed"))
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedErr:     true,
		},
		{
			name: "RDB nil - returns early",
			setupMock: func(_ *testing.T, _ redismock.ClientMock) {
				// No expectations - function returns early when RDB is nil
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock Redis client
			mockRDB, mock := redismock.NewClientMock()
			originalRDB := RDB

			if tt.name == "RDB nil - returns early" {
				RDB = nil
			} else {
				RDB = mockRDB
			}

			defer func() {
				RDB = originalRDB
				if mockRDB != nil {
					_ = mockRDB.Close()
				}
			}()

			// Setup mock expectations
			tt.setupMock(t, mock)

			// Execute
			session, found, err := GetSchedulingBrowserbaseSession(ctx, userID, integrationID)

			// Assert
			if tt.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedFound, found)
			if tt.expectedSession != nil {
				assert.NotNil(t, session)
				assert.Equal(t, tt.expectedSession.BrowserbaseSessionID, session.BrowserbaseSessionID)
				assert.Equal(t, tt.expectedSession.CreatedAt, session.CreatedAt)
				assert.Equal(t, tt.expectedSession.LastUsedAt, session.LastUsedAt)
			} else {
				assert.Nil(t, session)
			}

			// Verify all Redis expectations were met (only if RDB was set)
			if tt.name != "RDB nil - returns early" {
				assert.NoError(t, mock.ExpectationsWereMet())
			}
		})
	}
}

func TestSetSchedulingBrowserbaseSession(t *testing.T) {
	ctx := context.Background()
	userID := uint(123)
	integrationID := uint(456)
	key := buildSchedulingSessionKey(userID, integrationID)
	ttl := 25 * time.Minute

	session := SchedulingBrowserbaseSession{
		BrowserbaseSessionID: "session-123",
		CreatedAt:            time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
		LastUsedAt:           time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
	}

	tests := []struct {
		name          string
		setupMock     func(t *testing.T, mock redismock.ClientMock)
		expectedError bool
		rdbNil        bool
	}{
		{
			name: "success - session stored",
			setupMock: func(t *testing.T, mock redismock.ClientMock) {
				jsonBytes, err := json.Marshal(session)
				require.NoError(t, err)
				mock.ExpectSet(key, jsonBytes, ttl).SetVal("OK")
			},
			expectedError: false,
			rdbNil:        false,
		},
		{
			name: "Redis error",
			setupMock: func(t *testing.T, mock redismock.ClientMock) {
				jsonBytes, err := json.Marshal(session)
				require.NoError(t, err)
				mock.ExpectSet(key, jsonBytes, ttl).SetErr(errors.New("connection failed"))
			},
			expectedError: true,
			rdbNil:        false,
		},
		{
			name: "RDB nil - returns early",
			setupMock: func(_ *testing.T, _ redismock.ClientMock) {
				// No expectations - function returns early when RDB is nil
			},
			expectedError: false,
			rdbNil:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock Redis client
			mockRDB, mock := redismock.NewClientMock()
			originalRDB := RDB

			if tt.rdbNil {
				RDB = nil
			} else {
				RDB = mockRDB
			}

			defer func() {
				RDB = originalRDB
				if mockRDB != nil {
					_ = mockRDB.Close()
				}
			}()

			// Setup mock expectations
			tt.setupMock(t, mock)

			// Execute
			err := SetSchedulingBrowserbaseSession(ctx, userID, integrationID, session, ttl)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "failed to cache scheduling browserbase session")
			} else {
				assert.NoError(t, err)
			}

			// Verify all Redis expectations were met (only if RDB was set)
			if !tt.rdbNil {
				assert.NoError(t, mock.ExpectationsWereMet())
			}
		})
	}
}
