package redis

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

const (
	// SchedulingSessionPrefix is the Redis key prefix for scheduling browserbase sessions.
	SchedulingSessionPrefix = "scheduling:session"
)

// SchedulingBrowserbaseSession represents a cached browserbase session for scheduling flows.
type SchedulingBrowserbaseSession struct {
	BrowserbaseSessionID string    `json:"browserbaseSessionId"`
	CreatedAt            time.Time `json:"createdAt"`
	LastUsedAt           time.Time `json:"lastUsedAt"`
}

// buildSchedulingSessionKey builds the Redis key for a user's scheduling browserbase session.
func buildSchedulingSessionKey(userID, integrationID uint) string {
	return fmt.Sprintf(
		"%s:%d:%d",
		SchedulingSessionPrefix,
		userID,
		integrationID,
	)
}

// GetSchedulingBrowserbaseSession retrieves a cached browserbase session for a user + integration.
func GetSchedulingBrowserbaseSession(
	ctx context.Context,
	userID, integrationID uint,
) (*SchedulingBrowserbaseSession, bool, error) {
	ctx, span := otel.StartSpan(ctx, "GetSchedulingBrowserbaseSession", nil)
	var err error
	defer func() {
		span.End(err)
	}()

	if RDB == nil {
		return nil, false, nil
	}

	key := buildSchedulingSessionKey(userID, integrationID)

	session, found, err := GetKey[SchedulingBrowserbaseSession](ctx, key)
	if err != nil {
		// Treat redis.Nil as "not found" rather than an error
		if errors.Is(err, redis.Nil) {
			return nil, false, nil
		}
		log.Debug(ctx, "error getting scheduling browserbase session", zap.Error(err))
		return nil, false, err
	}

	if !found {
		return nil, false, nil
	}

	return &session, true, nil
}

// SetSchedulingBrowserbaseSession stores a browserbase session for a user + integration.
func SetSchedulingBrowserbaseSession(
	ctx context.Context,
	userID, integrationID uint,
	session SchedulingBrowserbaseSession,
	ttl time.Duration,
) (err error) {
	ctx, span := otel.StartSpan(ctx, "SetSchedulingBrowserbaseSession", nil)
	defer func() { span.End(err) }()

	if RDB == nil {
		return nil
	}

	key := buildSchedulingSessionKey(userID, integrationID)

	if err := SetKey(ctx, key, session, ttl); err != nil {
		log.Error(ctx, "failed to cache scheduling browserbase session", zap.Error(err))
		return fmt.Errorf("failed to cache scheduling browserbase session: %w", err)
	}

	log.Info(
		ctx,
		"cached scheduling browserbase session",
		zap.Uint("userID", userID),
		zap.Uint("integrationID", integrationID),
	)

	return nil
}
