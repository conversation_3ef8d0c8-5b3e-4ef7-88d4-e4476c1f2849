name: Run Narrative Tests
on:
  schedule:
    - cron: "0 13 * * *" # 8AM EST / 9AM EDT
    - cron: "0 21 * * *" # 4PM EST / 5PM EDT
  workflow_dispatch:

jobs:
  run-narrative-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Trigger narrative tests & wait
        env:
          TRYNARRATIVE_API_TOKEN: ${{ secrets.TRYNARRATIVE_API_TOKEN }}
          SERVICE_URL: https://backend-web.trynarrative.com
        run: |
          # 1) Read and base64-encode your YAML
          cfg=$(base64 -w0 < trynarrative.yml)
          # 2) Kick off a test run
          resp=$(curl -s -X POST $SERVICE_URL/api/v1/cicd/github/runs \
            -H "Authorization: Bearer $TRYNARRATIVE_API_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "repo": "'"${{ github.repository }}"'",
                "commit": "'"${{ github.sha }}"'",
                "config": "'"$cfg"'"
              }')
          run_id=$(echo "$resp" | jq -r .run_id)
          echo "👉 Test run ID: $run_id"

          # 3) Poll status up to 5 minutes
          for i in {1..30}; do
            response=$(curl -s \
              -H "Authorization: Bearer $TRYNARRATIVE_API_TOKEN" \
              "$SERVICE_URL/api/v1/cicd/github/runs/$run_id/status")
            status=$(echo "$response" | jq -r .status)
            
            # Clear previous output and show current state
            echo "----------------------------------------"
            echo "$response" | jq -r '.runs[]? | "  \(.test_name // .test_id): \(.status) (\(.current_step)/\(.total_steps))"'
            
            if [ "$status" = "success" ]; then
              echo "✅ All tests passed!"
              exit 0
            fi
            
            if [ "$status" = "failed" ]; then
              echo "❌ Tests failed"
              exit 1
            fi
            sleep 10
          done
          echo "❌ Timed out waiting for test results"
          exit 1
