name: Refresh materialized view

on:
  schedule:
    - cron: "0 7 * * *"
  workflow_dispatch:

jobs:
  refresh-mv:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      # NOTE: Order matters!
      # mv_qr_qq_users is the base MV; all other MVs depending on this
      # Opportunities
      # mv_daily_opportunities_customer_breakdown and mv_daily_opportunities_user_breakdown depend on mv_daily_opportunities
      # Daily QQ
      # mv_daily_qq_search_customer_breakdown and mv_daily_qq_search_user_breakdown depend on mv_daily_qq_search
      # Response Time
      # mv_qq_response_time_customer_breakdown and mv_qq_response_time_user_breakdown depend on mv_qq_response_time
      MATERIALIZED_VIEWS: >-
        mv_qr_qq_users
        mv_daily_opportunities
        mv_daily_opportunities_customer_breakdown
        mv_daily_opportunities_user_breakdown
        mv_daily_qq_search
        mv_daily_qq_search_customer_breakdown
        mv_daily_qq_search_user_breakdown
        mv_qq_response_time
        mv_qq_response_time_customer_breakdown
        mv_qq_response_time_user_breakdown
    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Install dependencies
        run: sudo apt-get update && sudo apt-get install -y postgresql-client

      - name: Refresh materialized views
        env:
          PGHOST: drumkit-prod.cluster-c7kpei57f4sp.us-east-1.rds.amazonaws.com
          PGPORT: 5432
          PGDATABASE: beacon
          PGUSER: ${{ secrets.MV_REFRESH_USER }}
          PGPASSWORD: ${{ secrets.MV_REFRESH_PASSWORD }}
        run: |
          set -euo pipefail
          : "${PGUSER:?MV_REFRESH_USER secret is not set}"
          : "${PGPASSWORD:?MV_REFRESH_PASSWORD secret is not set}"
          PGPORT="${PGPORT:-5432}"

          read -ra MVS <<< "$MATERIALIZED_VIEWS"

          # Build connection string with timeout settings
          CONN_STRING="host=${PGHOST} port=${PGPORT} dbname=${PGDATABASE} user=${PGUSER} sslmode=require connect_timeout=10 keepalives=1 keepalives_idle=30 keepalives_interval=10 keepalives_count=3"

          for mv in "${MVS[@]}"; do
            echo "Refreshing ${mv} on ${PGHOST}:${PGPORT}/${PGDATABASE}"
            echo "Start time: $(date -u +%Y-%m-%dT%H:%M:%SZ)"

            # Create temporary SQL file with proper escaping
            SQL_FILE=$(mktemp)
            cat > "${SQL_FILE}" <<SQL
          SET statement_timeout = '1 h';
          SET lock_timeout = '1 min';
          SET idle_in_transaction_session_timeout = '1 h';

          -- Check if view exists first
          DO \$\$
          BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = '${mv}') THEN
              RAISE EXCEPTION 'Materialized view ${mv} does not exist';
            END IF;
          END \$\$;

          -- Refresh the materialized view
          REFRESH MATERIALIZED VIEW ${mv};

          SQL

            # Execute SQL with proper connection settings
            psql "${CONN_STRING}" -v ON_ERROR_STOP=1 -f "${SQL_FILE}" || {
              echo "ERROR: Failed to refresh ${mv}"
              rm -f "${SQL_FILE}"
              exit 1
            }

            rm -f "${SQL_FILE}"
            echo "Refresh of ${mv} completed at $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            echo "----------------------------------------"
          done

      - name: Check for and refresh additional materialized views
        id: check_additional_mvs
        env:
          PGHOST: drumkit-prod.cluster-c7kpei57f4sp.us-east-1.rds.amazonaws.com
          PGPORT: 5432
          PGDATABASE: beacon
          PGUSER: ${{ secrets.MV_REFRESH_USER }}
          PGPASSWORD: ${{ secrets.MV_REFRESH_PASSWORD }}
        run: |
          set -euo pipefail
          PGPORT="${PGPORT:-5432}"

          # Known MVs from the workflow environment variable
          read -ra KNOWN_MVS <<< "$MATERIALIZED_VIEWS"

          CONN_STRING="host=${PGHOST} port=${PGPORT} dbname=${PGDATABASE} user=${PGUSER} sslmode=require connect_timeout=10 keepalives=1 keepalives_idle=30 keepalives_interval=10 keepalives_count=3"

          # Get all materialized views from the database
          ALL_MVS=$(psql "${CONN_STRING}" -t -A -c "SELECT matviewname FROM pg_matviews ORDER BY matviewname;")

          # Find MVs that are not in the known list
          ADDITIONAL_MVS=()
          while IFS= read -r mv; do
            if [[ -n "$mv" ]]; then
              is_known=false
              for known in "${KNOWN_MVS[@]}"; do
                if [[ "$mv" == "$known" ]]; then
                  is_known=true
                  break
                fi
              done
              if [[ "$is_known" == false ]]; then
                ADDITIONAL_MVS+=("$mv")
              fi
            fi
          done <<< "$ALL_MVS"

          if [[ ${#ADDITIONAL_MVS[@]} -eq 0 ]]; then
            echo "No additional materialized views found. All MVs are tracked."
            echo "found=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "WARNING: Found ${#ADDITIONAL_MVS[@]} additional materialized view(s) not in the tracked list:"
          printf '  - %s\n' "${ADDITIONAL_MVS[@]}"
          echo ""
          echo "These will be refreshed sequentially, but dependency order may not be correct! Please add these to the tracked list in the workflow if they should be refreshed in a specific order or delete the materialized view if it is no longer needed."
          echo ""

          # Set outputs for Slack notification
          echo "found=true" >> $GITHUB_OUTPUT
          echo "count=${#ADDITIONAL_MVS[@]}" >> $GITHUB_OUTPUT
          MV_LIST=$(printf '%s, ' "${ADDITIONAL_MVS[@]}" | sed 's/, $//')
          echo "list=${MV_LIST}" >> $GITHUB_OUTPUT

          # Refresh additional MVs
          for mv in "${ADDITIONAL_MVS[@]}"; do
            echo "Refreshing additional MV: ${mv}"
            echo "Start time: $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            
            SQL_FILE=$(mktemp)
            cat > "${SQL_FILE}" <<SQL
          SET statement_timeout = '1 h';
          SET lock_timeout = '1 min';
          SET idle_in_transaction_session_timeout = '1 h';

          REFRESH MATERIALIZED VIEW ${mv};

          SQL
            
            psql "${CONN_STRING}" -v ON_ERROR_STOP=1 -f "${SQL_FILE}" || {
              echo "ERROR: Failed to refresh ${mv}"
              rm -f "${SQL_FILE}"
              exit 1
            }
            
            rm -f "${SQL_FILE}"
            echo "Refresh of ${mv} completed at $(date -u +%Y-%m-%dT%H:%M:%SZ)"
            echo "----------------------------------------"
          done

      - name: Notify about additional materialized views
        uses: ravsamhq/notify-slack-action@v2
        if: steps.check_additional_mvs.outputs.found == 'true'
        with:
          status: success
          notify_when: "success"
          notification_title: "⚠️ Additional Materialized Views Found and Refreshed"
          message_format: |
            *${{ steps.check_additional_mvs.outputs.count }} materialized view(s)* were found that are not in the tracked list.

            They have been refreshed, but *dependency order may not be correct*.

            *Additional MVs:* ${{ steps.check_additional_mvs.outputs.list }}

            _Please add these to the tracked list in the workflow if they should be refreshed in a specific order._
          icon_success: ":warning:"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}

      - name: Notify on workflow failure
        uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
