package main

import (
	"context"
	"os"
	"path/filepath"
	"runtime"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
)

const appTmpDir = "/tmp/drumkit-processor"

// cleanupTmpDirectory removes all files and directories from /tmp/drumkit-processor to prevent memory accumulation
// across Lambda invocations. This is particularly important for MuPDF temporary files created
// during PDF processing that may persist when Lambda execution environments are reused.
// Only runs cleanup when in a lambda environment.
func cleanupTmpDirectory(ctx context.Context) {
	// Only cleanup temporary directory when running in Lambda environment
	if !helpers.IsLambda() {
		return
	}

	// Ensure directory exists before attempting cleanup
	if _, err := os.Stat(appTmpDir); os.IsNotExist(err) {
		if err := os.MkdirAll(appTmpDir, 0755); err != nil {
			log.Warn(
				ctx,
				"Failed to create app temp directory for cleanup",
				zap.String("path", appTmpDir),
				zap.Error(err),
			)
			return
		}
	}

	items, err := filepath.Glob(appTmpDir + "/*")
	if err != nil {
		log.Warn(
			ctx,
			"Error reading app temp directory",
			zap.String("path", appTmpDir),
			zap.Error(err),
		)
		return
	}

	if len(items) == 0 {
		log.Debug(ctx, "No temporary files to clean up")
		return
	}

	cleanedCount := 0
	errorCount := 0

	for _, item := range items {
		if err := os.RemoveAll(item); err != nil {
			log.Warn(ctx, "Error deleting temp file", zap.String("path", item), zap.Error(err))
			errorCount++
		} else {
			log.Debug(ctx, "Successfully deleted temp file", zap.String("path", item))
			cleanedCount++
		}
	}

	if cleanedCount > 0 || errorCount > 0 {
		log.Info(
			ctx,
			"Completed app temp directory cleanup",
			zap.String("path", appTmpDir),
			zap.Int("cleaned", cleanedCount),
			zap.Int("errors", errorCount),
		)
	}
}

// logMemoryStats logs current memory usage statistics for monitoring.
// Includes both Go heap memory and total process RSS to track C allocations.
func logMemoryStats(ctx context.Context, stage string) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// TODO: Change this to .Debug after initial monitoring period
	log.Info(
		ctx,
		"Memory statistics",
		zap.String("stage", stage),
		zap.Uint64("alloc_mb", bToMb(m.Alloc)),
		zap.Uint64("total_alloc_mb", bToMb(m.TotalAlloc)),
		zap.Uint64("sys_mb", bToMb(m.Sys)),
		zap.Uint32("num_gc", m.NumGC),
	)
}

// bToMb converts bytes to megabytes
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
