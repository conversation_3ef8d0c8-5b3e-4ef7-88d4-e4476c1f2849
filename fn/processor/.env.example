APP_ENV=dev
DB_USER=postgres
DB_HOST=localhost
DB_NAME=drumkit_dev
DB_PASSWORD=postgres

# Set to false to allow duplicate email processing
COPY_DUPLICATE_EMAILS=false

GOOGLE_CLIENT_ID=local
MICROSOFT_CLIENT_ID=""
MICROSOFT_WEBHOOK_URL="[ngrok_link]/inboxWebhook"

REDIS_URL=redis://127.0.0.1:6379
USPS_USER_ID="70AXLETA37809"

AWS_ENDPOINT_URL=http://localhost:4567
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
S3_BUCKET_NAME=drumkit-local

SQS_QUEUE_URL="http://localhost:5005"

OPENAI_API_KEY=""
BRAINTRUST_API_KEY=""
BRAINTRUST_BASE_URL=""
# Dev only env var to toggle sending logs to braintrust
SEND_LOGS_TO_BRAINTRUST=TRUE

GOMEMLIMIT=1GiB

# Tracing
AXIOM_ORG_ID=axle-xqz9
AXIOM_TOKEN="Generate on Axiom"
AXIOM_TRACE_DATASET=beacon-otel-dev