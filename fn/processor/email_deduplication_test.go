package main

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
)

func TestIsEmailDeduplicationDisabledForUser(t *testing.T) {
	tests := []struct {
		name                        string
		userID                      uint
		service                     *models.Service
		mockGetQuickQuoteAgents     func(ctx context.Context, userID uint, serviceID uint) ([]agentmodels.QuickQuoteAgent, error) //nolint:lll
		mockGetServiceWideAgents    func(ctx context.Context, serviceID uint) ([]agentmodels.QuickQuoteAgent, error)
		expectedDisabled            bool
		expectedReason              string
		expectedError               bool
		expectedErrorMessageContain string
	}{
		// Add test for feature flag disabled
		{
			name:   "Feature flag disabled - deduplication enabled",
			userID: 1,
			service: &models.Service{
				Model: gorm.Model{ID: 10},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: false,
				},
			},
			expectedDisabled: false,
			expectedReason:   "",
			expectedError:    false,
		},
		{
			name:   "User has quick quote agents - deduplication disabled",
			userID: 1,
			service: &models.Service{
				Model: gorm.Model{ID: 10},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(
				_ context.Context,
				_ uint,
				serviceID uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{
					{
						Model:     gorm.Model{ID: 1},
						ServiceID: serviceID,
					},
				}, nil
			},
			mockGetServiceWideAgents: func(context.Context, uint) ([]agentmodels.QuickQuoteAgent, error) {
				t.Fatal("should not call GetServiceWideAgents when user agents exist")
				return nil, nil
			},
			expectedDisabled: true,
			expectedReason:   "user agents enabled",
			expectedError:    false,
		},
		{
			name:   "Multiple user agents - deduplication disabled",
			userID: 2,
			service: &models.Service{
				Model: gorm.Model{ID: 20},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(
				_ context.Context,
				_ uint,
				serviceID uint,
			) ([]agentmodels.QuickQuoteAgent, error) {

				return []agentmodels.QuickQuoteAgent{
					{Model: gorm.Model{ID: 1}, ServiceID: serviceID},
					{Model: gorm.Model{ID: 2}, ServiceID: serviceID},
					{Model: gorm.Model{ID: 3}, ServiceID: serviceID},
				}, nil
			},
			mockGetServiceWideAgents: func(context.Context, uint) ([]agentmodels.QuickQuoteAgent, error) {
				t.Fatal("should not call GetServiceWideAgents when user agents exist")
				return nil, nil
			},
			expectedDisabled: true,
			expectedReason:   "user agents enabled",
			expectedError:    false,
		},
		{
			name:   "Error getting user agents - fail open (disabled)",
			userID: 3,
			service: &models.Service{
				Model: gorm.Model{ID: 30},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(context.Context, uint, uint) ([]agentmodels.QuickQuoteAgent, error) {
				return nil, errors.New("database connection error")
			},
			mockGetServiceWideAgents: func(context.Context, uint) ([]agentmodels.QuickQuoteAgent, error) {
				t.Fatal("should not call GetServiceWideAgents when user agents query fails")
				return nil, nil
			},
			expectedDisabled:            true,
			expectedReason:              "",
			expectedError:               true,
			expectedErrorMessageContain: "database connection error",
		},
		{
			name:   "No user agents but service-wide agents exist - deduplication disabled",
			userID: 4,
			service: &models.Service{
				Model: gorm.Model{ID: 40},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(_ context.Context, _ uint, _ uint) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{}, nil
			},
			mockGetServiceWideAgents: func(_ context.Context, serviceID uint) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{
					{
						Model:         gorm.Model{ID: 10},
						ServiceID:     serviceID,
						IsServiceWide: true,
					},
				}, nil
			},
			expectedDisabled: true,
			expectedReason:   "service agents enabled",
			expectedError:    false,
		},
		{
			name:   "No user agents and error getting service-wide agents - fail open (disabled)",
			userID: 5,
			service: &models.Service{
				Model: gorm.Model{ID: 50},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(
				context.Context,
				uint,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{}, nil
			},
			mockGetServiceWideAgents: func(
				context.Context,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return nil, errors.New("service agents query failed")
			},
			expectedDisabled:            true,
			expectedReason:              "",
			expectedError:               true,
			expectedErrorMessageContain: "failed to get service-wide quick quote agents",
		},
		{
			name:   "No user agents and no service-wide agents - deduplication enabled",
			userID: 6,
			service: &models.Service{
				Model: gorm.Model{ID: 60},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(
				context.Context,
				uint,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{}, nil
			},
			mockGetServiceWideAgents: func(
				context.Context,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return []agentmodels.QuickQuoteAgent{}, nil
			},
			expectedDisabled: false,
			expectedReason:   "",
			expectedError:    false,
		},
		{
			name:   "No user agents (nil) and no service-wide agents (nil) - deduplication enabled",
			userID: 7,
			service: &models.Service{
				Model: gorm.Model{ID: 70},
				FeatureFlags: models.FeatureFlags{
					IsQuickQuoteAgentEnabled: true,
				},
			},
			mockGetQuickQuoteAgents: func(
				context.Context,
				uint,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return nil, nil
			},
			mockGetServiceWideAgents: func(
				context.Context,
				uint,
			) ([]agentmodels.QuickQuoteAgent, error) {
				return nil, nil
			},
			expectedDisabled: false,
			expectedReason:   "",
			expectedError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			originalGetQuickQuoteAgentsForUser := dbGetQuickQuoteAgentsForUser
			originalGetServiceWideAgents := dbGetServiceWideAgents
			defer func() {
				dbGetQuickQuoteAgentsForUser = originalGetQuickQuoteAgentsForUser
				dbGetServiceWideAgents = originalGetServiceWideAgents
			}()

			dbGetQuickQuoteAgentsForUser = tt.mockGetQuickQuoteAgents
			dbGetServiceWideAgents = tt.mockGetServiceWideAgents

			ctx := context.Background()
			disabled, reason, err := isEmailDeduplicationDisabledForUser(ctx, tt.userID, tt.service)

			assert.Equal(t, tt.expectedDisabled, disabled, "disabled flag mismatch")
			assert.Equal(t, tt.expectedReason, reason, "reason mismatch")

			if tt.expectedError {
				assert.Error(t, err, "expected an error but got nil")
				if tt.expectedErrorMessageContain != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorMessageContain, "error message mismatch")
				}
			} else {
				assert.NoError(t, err, "expected no error but got: %v", err)
			}
		})
	}
}
