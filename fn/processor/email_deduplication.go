package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	quickQuoteAgentDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

const (
	redisLockExtension = 5 * time.Second
	copyAction         = "copy"
	processAction      = "process"
	deferAndExitAction = "defer"
)

var (
	// For unit testing
	dbGetQuickQuoteAgentsForUser = quickQuoteAgentDB.GetQuickQuoteAgentsForUser
	dbGetServiceWideAgents       = quickQuoteAgentDB.GetServiceWideAgents
)

// determineAction determines the action to take based on the email's status in Redis--process, copy, or retry later
func determineAction(
	ctx context.Context,
	msg models.IngestedEmail,
	rfcIDRedisKey string,
	service *models.Service,
) (existingEmail *models.Email, action string) {
	ctx, span := otel.StartSpan(ctx, "determineAction", nil)
	defer func() { span.End(nil) }()

	// TODO: remove this after SDS and Trifecta are fixed to support carrier quote
	if service.IsCarrierNetworkQuotingEnabled {
		log.Info(ctx, "turn off email de-dupe due to carrier quoting feature")
		return nil, processAction
	}

	shouldProcess, reason, err := isEmailDeduplicationDisabledForUser(ctx, msg.UserID, service)
	if err != nil {
		// Fail-open; occasional duplicate processing is fine
		log.WarnNoSentry(
			ctx,
			"failed to check if email deduplication is disabled for user, processing email",
			zap.Error(err),
			zap.Uint("userID", msg.UserID),
			zap.Uint("serviceID", service.ID),
		)

		return nil, processAction
	}

	if shouldProcess {
		log.Info(ctx, "email deduplication is disabled for user, processing email", zap.String("reason", reason))
		return nil, processAction
	}

	redisVal, found, err := redis.GetKey[string](ctx, rfcIDRedisKey)

	switch {
	case !env.Vars.CopyDuplicateEmails:
		log.Info(ctx, "email deduplication is disabled, processing email")
		return nil, processAction

	case err != nil && !errors.Is(err, redis.NilEntry):
		// Fail-open; occasional duplicate processing is fine
		log.WarnNoSentry(ctx, "failed to get message RFC lock, processing email", zap.Error(err))
		return nil, processAction

	case found && redisVal == string(models.InFlight):
		log.Info(ctx, "email already being processed by another instance, deferring this message")
		siblingKey := rfcIDRedisKey + ":siblings"

		// Create a smaller representation of the sibling message to save on Redis memory.
		deferredMsg := DeferredSiblingMessage{
			UserID:             msg.UserID,
			Account:            msg.Account,
			ServiceID:          msg.ServiceID,
			ExternalID:         msg.ExternalID,
			ThreadID:           msg.ThreadID,
			RFCMessageID:       msg.RFCMessageID,
			ProcessingMetadata: msg.ProcessingMetadata,
		}

		msgJSON, err := json.Marshal(deferredMsg)
		if err != nil {
			// If we can't marshal, we can't defer. Process it to avoid losing the email.
			log.Error(ctx, "failed to marshal sibling message for deferral, processing instead", zap.Error(err))
			return nil, processAction
		}

		// Use a pipeline to atomically add the sibling and set a TTL on the list
		pipe := redis.RDB.Pipeline()
		pipe.RPush(ctx, siblingKey, msgJSON)
		pipe.Expire(ctx, siblingKey, 24*time.Hour)
		if _, err := pipe.Exec(ctx); err != nil {
			// If we can't write to redis, we can't defer. Process it to avoid losing the email.
			log.Error(ctx, "failed to push sibling message to redis, processing instead", zap.Error(err))
			return nil, processAction
		}

		log.Info(ctx, "successfully deferred sibling message", zap.String("siblingKey", siblingKey))
		return nil, deferAndExitAction

	case !found:
		// Lock the RFC message ID to prevent duplicate processing
		// Cannot check the DB because email is added to DB before all processing (i.e. LLM pipelines) finishes
		// According to AWS CW logs, avg. processing time is ~10s, so we initially lock for 5s and extendRedisLock()
		// will extend it every 5s until processing finishes or times out
		log.Info(
			ctx,
			"email not found in Redis, processing email",
			zap.String("key", rfcIDRedisKey),
			zap.String("redisVal", redisVal),
		)
		emails.SetRedisWithWarn(ctx, rfcIDRedisKey, string(models.InFlight), redisLockExtension)

		return nil, processAction

	default:
		var emailIDStr string
		if strings.HasPrefix(redisVal, "done:") {
			emailIDStr = strings.TrimPrefix(redisVal, "done:")
		} else {
			emailIDStr = redisVal
		}

		id, convErr := strconv.Atoi(emailIDStr)
		if convErr != nil || id <= 0 {
			log.WarnNoSentry(
				ctx,
				"failed to convert redis value to int, processing email",
				zap.Error(convErr),
				zap.String("redisVal", redisVal),
			)

			return nil, processAction
		}

		existingEmail, err := dbGetEmailByIDFunc(ctx, uint(id))
		if err != nil {
			// Fail-open; occasional duplicate processing is fine
			log.WarnNoSentry(ctx, "failed to get email by ID, processing email", zap.Error(err))

			return nil, processAction
		}

		if strings.Contains(existingEmail.Labels, string(emails.CarrierQuoteResponseLabel)) &&
			service.IsDelegatedInboxEnabled &&
			service.IsCarrierNetworkQuotingEnabled {

			log.Info(ctx, "email may belong to shared carrier quoting thread, processing email")

			return nil, processAction
		}

		log.Info(ctx, "email already processed", zap.String("redisVal", redisVal))
		return &existingEmail, copyAction
	}

}

// isEmailDeduplicationDisabledForUser checks if email deduplication is disabled for a user.
// This should be used only temporarily and sparingly to support new features that require deduplication to be disabled.
// TODO: ENG-5092
func isEmailDeduplicationDisabledForUser(
	ctx context.Context,
	userID uint,
	service *models.Service,
) (bool, string, error) {

	var err error
	ctx, span := otel.StartSpan(ctx, "isEmailDeduplicationDisabledForUser", nil)
	defer func() { span.End(err) }()

	if service == nil || !service.IsQuickQuoteAgentEnabled {
		return false, "", nil
	}

	// Check user/user's groups agents
	agents, err := dbGetQuickQuoteAgentsForUser(ctx, userID, service.ID)
	if err != nil {
		// Fail-open; occasional duplicate processing is fine
		return true, "", err
	}

	if len(agents) > 0 {
		return true, "user agents enabled", nil
	}

	// Check service-wide agents
	agents, err = dbGetServiceWideAgents(ctx, service.ID)
	if err == nil && len(agents) > 0 {
		return true, "service agents enabled", nil
	}

	if err != nil {
		// Fail-open; occasional duplicate processing is fine
		return true, "", fmt.Errorf("failed to get service-wide quick quote agents: %w", err)
	}

	return false, "", nil
}

// handleDuplicateEmail copies processed associations from the existingEmail to the msg, namely:
//   - Loads
//   - QuoteRequest suggestions
//   - LoadBuilding suggestions
//   - Check call & appointment suggestions
//   - Truck list suggestions
//   - Vector associations for emails and suggestions
//
// NOTES:
//   - Applied fields are *not* copied bc those are specific to each user and must be separate for accurate metrics.
//   - As pipelines are added, this should be updated to copy the appropriate fields.
//   - CarrierQuotes are not copied because even if carrier CC's multiple users, the carrier quote DB record
//     is associated only with the user and thread who sent the carrier email.
//   - Attachments are still stored by user email in ingestion step
func handleDuplicateEmail(
	ctx context.Context,
	existingEmail *models.Email,
	deferredMsg DeferredSiblingMessage,
	service *models.Service,
	sqsClient sqsclient.API,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "handleDuplicateEmail", nil)
	defer func() { metaSpan.End(err) }()

	vectorRepo := dbGetVectorRepositoryFunc(ctx)
	if vectorRepo == nil {
		return errors.New("failed to get vector repository")
	}

	// Small optimization to skip if this email has already been processed; this also preserves prevents false positive
	// on "copied_from_email_id" metadata
	if existingEmail.ExternalID == deferredMsg.ExternalID {
		log.Info(
			ctx,
			"skipping copy of already processed email",
			zap.Uint("existingEmailID", existingEmail.ID),
			zap.String("existingEmailAccount", existingEmail.Account),
			zap.String("ingestedExternalID", deferredMsg.ExternalID),
		)
		return nil
	}

	log.Info(
		ctx,
		"copying already processed email",
		zap.String("rfcId", deferredMsg.RFCMessageID),
	)

	// Attachments are copied from the existing email, not from the deferred message.
	newMsg := &models.Email{
		UserID:             deferredMsg.UserID,
		Account:            deferredMsg.Account,
		ServiceID:          deferredMsg.ServiceID,
		ExternalID:         deferredMsg.ExternalID,
		ThreadID:           deferredMsg.ThreadID,
		RFCMessageID:       deferredMsg.RFCMessageID,
		Attachments:        existingEmail.Attachments,
		ProcessingMetadata: deferredMsg.ProcessingMetadata,
	}

	newMsg = existingEmail.CopyProcessedDuplicate(newMsg)

	if err := dbUpsertEmailFunc(ctx, newMsg); err != nil {
		return fmt.Errorf("failed to upsert email and loads: %w", err)
	}
	log.Info(ctx, "successfully upserted copied email and loads", zap.Uint("emailID", newMsg.ID))

	// Process forwarding rules synchronously; if error, fail-close and process email from scratch
	if service.IsEmailForwardingEnabled {
		if err := processForwardingRulesFunc(ctx, *newMsg, service, sqsClient); err != nil {
			return fmt.Errorf("failed to process forwarding rules:%w", err)
		}
	}

	// Copy email vector associations
	if len(existingEmail.Vectors) > 0 {
		log.Debug(ctx, "copying email vector associations",
			zap.Uint("emailID", existingEmail.ID),
			zap.Int("vectorCount", len(existingEmail.Vectors)))

		if err := vectorRepo.BatchAssociateWithEmail(ctx, existingEmail.Vectors, newMsg); err != nil {
			log.WarnNoSentry(ctx, "failed to batch associate vectors with copied email",
				zap.Error(err),
				zap.Uint("emailID", newMsg.ID),
				zap.Int("vectorCount", len(existingEmail.Vectors)))
		}
	}

	// Copy load suggestions; fail-open on failures as we do for pipelines
	loadSuggestions, err := dbGetLoadSuggestionsFunc(ctx, existingEmail.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WarnNoSentry(ctx, "failed to get load suggestions by email ID", zap.Error(err))
	}

	shouldCopyQuoteRequestSeparately := true

	for _, existingSug := range loadSuggestions {
		if existingSug.QuoteRequestSuggestionID != nil && existingSug.QuoteRequestSuggestion == nil {
			log.Warn(
				ctx,
				"Quote Request Suggestion should be preloaded for copying",
				zap.Uint("suggestionID", existingSug.ID))
		}

		newSug := existingSug.CopyProcessedDuplicate(newMsg)

		if existingSug.QuoteRequestSuggestionID != nil {
			log.Debug(ctx, "setting shouldCopyQuoteRequestSeparately to false")
			shouldCopyQuoteRequestSeparately = false
		}

		if err := dbCreateLoadSuggestionFunc(ctx, newSug); err != nil {
			log.WarnNoSentry(ctx, "failed to store copied load suggestion",
				zap.Error(err), zap.Uint("suggestionID", existingSug.ID))
		} else if len(existingSug.Vectors) > 0 {
			// Copy vector associations for the load suggestion
			log.Debug(ctx, "copying load suggestion vector associations",
				zap.Uint("suggestionID", existingSug.ID),
				zap.Int("vectorCount", len(existingSug.Vectors)))

			if err := vectorRepo.BatchAssociateWithLoadSuggestion(ctx, existingSug.Vectors, newSug); err != nil {
				log.WarnNoSentry(ctx, "failed to batch associate vectors with copied load suggestion",
					zap.Error(err),
					zap.Uint("suggestionID", newSug.ID),
					zap.Int("vectorCount", len(existingSug.Vectors)))
			}

		}
	}

	// Copy quote request suggestions generated
	if shouldCopyQuoteRequestSeparately {
		existingQRs, err := dbGetQuoteRequestsFunc(ctx, existingEmail.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to get quote request by email ID: %w", err)
		}

		for _, existingQR := range existingQRs {
			newQR := existingQR.CopyProcessedDuplicate(newMsg)

			err = dbCreateQuoteRequestFunc(ctx, newQR)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"failed to store copied quote request",
					zap.Error(err),
					zap.Uint("quoteRequestID", existingQR.ID))
			} else if len(existingQR.Vectors) > 0 {
				// Copy vector associations for the quote request
				log.Debug(ctx, "copying quote request vector associations",
					zap.Uint("quoteRequestID", existingQR.ID),
					zap.Int("vectorCount", len(existingQR.Vectors)))

				if err := vectorRepo.BatchAssociateWithQuoteRequest(ctx, existingQR.Vectors, newQR); err != nil {
					log.WarnNoSentry(ctx, "failed to batch associate vectors with copied quote request",
						zap.Error(err),
						zap.Uint("quoteRequestID", newQR.ID),
						zap.Int("vectorCount", len(existingQR.Vectors)))
				}

			}
		}
	}

	// Copy truck list suggestions
	existingTruckList, err := dbGetTruckListFunc(ctx, fmt.Sprint(existingEmail.ID))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to get truck list by email ID: %w", err)
	}

	if existingTruckList != nil {
		newTruckList := existingTruckList.CopyProcessedDuplicate(newMsg)

		err = dbCreateTruckListFunc(ctx, newTruckList)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to store copied truck list",
				zap.Error(err),
				zap.Uint("truckListID", existingTruckList.ID),
			)
		}
	}

	log.Info(ctx, "successfully copied processed email info")
	return nil
}

// processSiblingEmails retrieves deferred sibling emails from Redis and processes them by copying
// the results from the already-processed primary email.
func processSiblingEmails(ctx context.Context, processedEmailID uint, rfcIDRedisKey string) (err error) {
	ctx, span := otel.StartSpan(ctx, "processSiblingEmails", nil)
	defer span.End(err)

	siblingsKey := rfcIDRedisKey + ":siblings"
	log.Info(ctx, "checking for and processing sibling emails", zap.String("siblingsKey", siblingsKey))

	// Fetch the original, fully processed email to use as a template for copies.
	processedEmail, err := dbGetEmailByIDFunc(ctx, processedEmailID)
	if err != nil {
		return fmt.Errorf("failed to get processed email by ID %d to copy for siblings: %w", processedEmailID, err)
	}

	// Use a slice to hold all siblings to avoid modifying the list while iterating if we used LRem
	siblingsJSON, err := redis.RDB.LRange(ctx, siblingsKey, 0, -1).Result()
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return fmt.Errorf("failed to get sibling emails from redis list %s: %w", siblingsKey, err)
	}

	if len(siblingsJSON) == 0 {
		log.Info(ctx, "no sibling emails to process")
		return nil
	}

	log.Info(ctx, "found deferred sibling emails to process", zap.Int("count", len(siblingsJSON)))

	service, err := dbGetServiceFunc(ctx, processedEmail.ServiceID)
	if err != nil {
		return fmt.Errorf("failed to get service by ID %d: %w", processedEmail.ServiceID, err)
	}

	for _, siblingJSON := range siblingsJSON {
		var siblingMsg DeferredSiblingMessage
		if err := json.Unmarshal([]byte(siblingJSON), &siblingMsg); err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to unmarshal sibling email from redis, skipping",
				zap.Error(err),
				zap.String("json", siblingJSON),
			)
			// Remove the malformed entry to prevent repeated processing attempts.
			if remErr := redis.RDB.LRem(ctx, siblingsKey, 1, siblingJSON).Err(); remErr != nil {
				log.Warn(ctx, "failed to remove malformed sibling email from redis list", zap.Error(remErr))
			}
			continue
		}

		// Use a new context for each sibling to avoid log pollution, while inheriting
		// the parent trace for observability.
		siblingCtx := log.NewFromEnv(ctx)
		siblingCtx = log.With(siblingCtx,
			zap.String("account", siblingMsg.Account),
			zap.String("externalId", siblingMsg.ExternalID),
			zap.String("threadId", siblingMsg.ThreadID),
			zap.String("rfcId", siblingMsg.RFCMessageID),
			zap.String("subject", processedEmail.Subject), // Subject from processed email
			zap.String("sender", processedEmail.Sender),   // Sender from processed email
			zap.Uint("copied_from_email_id", processedEmail.ID),
		)

		log.Info(siblingCtx, "processing deferred sibling email")
		if err := handleDuplicateEmail(siblingCtx, &processedEmail, siblingMsg, &service, sqsClient); err != nil {
			// Don't let one failed copy stop the others. Sibling remains in the list for future retry.
			log.Warn(siblingCtx, "failed to copy processed info to sibling email, leaving in list", zap.Error(err))
		} else {
			// Successfully processed, so remove it from the list.
			if remErr := redis.RDB.LRem(ctx, siblingsKey, 1, siblingJSON).Err(); remErr != nil {
				log.Warn(ctx, "failed to remove processed sibling email from redis list", zap.Error(remErr))
			}
			ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", siblingMsg.ThreadID)
			if delErr := redis.DeleteKey(ctx, ingestionStatusKey); delErr != nil && !errors.Is(delErr, redis.NilEntry) {
				log.Error(ctx, "error deleting email ingestion status from redis", zap.Error(delErr))
			}
		}
	}

	log.Info(ctx, "finished processing sibling emails", zap.String("siblingsKey", siblingsKey))
	return nil
}

// extendRedisLock extends the Redis lock for the email every 5 seconds until processing is complete (error or success).
// Processing duration varies widely due to dependencies, transient network lags, email labels & pipelines,
// (e.g. PDF load building takes longer than check call pipeline), etc. We want the lock to account for this in order
// to meaningfully reduce the chances of duplicate processing. However, we don't want to use a high TTL for all emails
// in order to minimize user's wait time. Hence, we continuously extend the lock until processing finishes or times out.
// as a compromise b/w reducing duplicate processing and minimizing user wait time.
//
// (Optional reading)
// Upper bound on artificially inflated user wait time:
// X = processing duration of Copy A
// C = ~50-300ms = time to process copy B after copy A completes
// R = 10s = SQS Retry configuration (see Parthenon)
// E = 5s = Redis lock extension interval
//
// Suppose Lambda extends the Redis lock a split second before Copy A processing completes,
// and SQS *just* started the 10s retry countdown for Copy B. Then, User B has to wait a maximum of
//
//	R + E + C = 5 + 10 + ~100ms = 15.1s
//
// *in addition to X* for their email available to them, compared to User A who waits just X.
// But on average, the delay will be much lower than this; furthermore, deduplicating reduces strain on network and LLM,
// reducing X itself which lowers overall user wait time for both A & B.
func extendRedisLock(ctx context.Context, msg models.IngestedEmail, rfcIDRedisKey string, done <-chan struct{}) {
	ticker := time.NewTicker(4 * time.Second)
	timeout := time.After(2 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			// Timeout if OpenAI is slow; retrying on the copy may be faster
			log.WarnNoSentry(ctx, "redis lock extension timed out after 2 minutes",
				zap.String("rfcId", msg.RFCMessageID))
			return
		case <-done:
			return
		case <-ticker.C:
			// Extend TTL by 5 seconds
			log.Debug(ctx, "extending redis lock")
			emails.SetRedisWithWarn(ctx, rfcIDRedisKey, string(models.InFlight), 5*time.Second)
		case <-ctx.Done():
			return
		}
	}
}
