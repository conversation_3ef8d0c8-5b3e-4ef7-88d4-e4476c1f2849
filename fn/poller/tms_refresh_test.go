package main

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

// Mock TMS implementation
type mockTMS struct {
	tms.Interface
	mock.Mock
}

func (m *mockTMS) GetCustomers(ctx context.Context, opts ...models.TMSOption) ([]models.TMSCustomer, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).([]models.TMSCustomer), args.Error(1)
}

func (m *mockTMS) GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).([]models.TMSLocation), args.Error(1)
}

func TestShouldRunTMSRefresh(t *testing.T) {
	ctx := context.Background()

	t.Run("New job - no last run", func(t *testing.T) {
		integration := models.Integration{
			Name: models.McleodEnterprise, // Partial sync
		}
		integration.ID = 1

		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		redisFuncGetTMSRefreshLastRun = func(_ context.Context, _ uint, _ string) (time.Time, error) {
			return time.Time{}, nil
		}
		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			redisFuncGetTMSRefreshLastRun = redis.GetTMSRefreshLastRun
		}()

		assert.True(t, shouldRunTMSRefresh(ctx, integration, redis.CustomerRefreshJob))
	})

	t.Run("Unsupported TMS - will not run", func(t *testing.T) {
		for unsupportedTMS := range unsupportedTMSes {
			t.Run(string(unsupportedTMS), func(t *testing.T) {
				integrationUnsupported := models.Integration{Name: unsupportedTMS}
				integrationUnsupported.ID = 3
				assert.False(t, shouldRunTMSRefresh(ctx, integrationUnsupported, redis.CustomerRefreshJob))
			})
		}
	})

	t.Run("Unknown TMS - will not run", func(t *testing.T) {
		redisFuncGetTMSRefreshLastRun = func(_ context.Context, _ uint, _ string) (time.Time, error) {
			return time.Time{}, nil
		}
		defer func() {
			redisFuncGetTMSRefreshLastRun = redis.GetTMSRefreshLastRun
		}()
		integrationUnknown := models.Integration{Name: "unknown"}
		integrationUnknown.ID = 4
		assert.False(t, shouldRunTMSRefresh(ctx, integrationUnknown, redis.CustomerRefreshJob))
	})

	t.Run("In progress job should always run", func(t *testing.T) {
		integration := models.Integration{
			Name: models.McleodEnterprise, // Partial sync
		}
		integration.ID = 1

		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return redis.JobStatusInProgress, "", "", nil
		}
		defer func() { redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus }()

		assert.True(t, shouldRunTMSRefresh(ctx, integration, redis.CustomerRefreshJob))
	})

	t.Run("Full sync - in time window - should run", func(t *testing.T) {
		integrationFull := models.Integration{Name: models.Aljex}
		integrationFull.ID = 2

		estLoc, err := time.LoadLocation(FullSyncTimeWindowTimezone)
		require.NoError(t, err)

		timeNowFunc = func() time.Time {
			return time.Date(2024, 1, 1, 19, 5, 47, 0, estLoc)
		}
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		// Set last run to > 24 hours ago
		redisFuncGetTMSRefreshLastRun = func(_ context.Context, _ uint, _ string) (time.Time, error) {
			return time.Now().Add(-25 * time.Hour), nil
		}
		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			redisFuncGetTMSRefreshLastRun = redis.GetTMSRefreshLastRun
			timeNowFunc = time.Now
		}()

		assert.True(t, shouldRunTMSRefresh(ctx, integrationFull, redis.CustomerRefreshJob))
	})

	t.Run("Full sync - out of time window -should not run", func(t *testing.T) {
		integrationFull := models.Integration{Name: models.Aljex}
		integrationFull.ID = 2

		estLoc, err := time.LoadLocation(FullSyncTimeWindowTimezone)
		require.NoError(t, err)

		timeNowFunc = func() time.Time {
			return time.Date(2024, 1, 1, 20, 5, 47, 0, estLoc)
		}
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		// Set last run to > 24 hours ago
		redisFuncGetTMSRefreshLastRun = func(_ context.Context, _ uint, _ string) (time.Time, error) {
			return time.Now().Add(-25 * time.Hour), nil
		}
		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			redisFuncGetTMSRefreshLastRun = redis.GetTMSRefreshLastRun
			timeNowFunc = time.Now
		}()

		assert.False(t, shouldRunTMSRefresh(ctx, integrationFull, redis.CustomerRefreshJob))
	})

	t.Run("In progress job on full sync - should run even though out of original time window", func(t *testing.T) {
		integrationFull := models.Integration{Name: models.Aljex}
		integrationFull.ID = 2

		estLoc, err := time.LoadLocation(FullSyncTimeWindowTimezone)
		require.NoError(t, err)

		timeNowFunc = func() time.Time {
			return time.Date(2024, 1, 1, 20, 5, 47, 0, estLoc)
		}
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return redis.JobStatusInProgress, "", "", nil
		}
		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			timeNowFunc = time.Now
		}()

		assert.True(t, shouldRunTMSRefresh(ctx, integrationFull, redis.CustomerRefreshJob))
	})
}

func TestHandleTMSRefresh_Unit(t *testing.T) {
	ctx := context.Background()

	t.Run("Found integrations and triggered refresh", func(_ *testing.T) {
		integration := models.Integration{Name: models.McleodEnterprise}
		integration.ID = 1

		dbFuncGetAllValidTMS = func(_ context.Context) ([]models.Integration, error) {
			return []models.Integration{integration}, nil
		}
		// Mock shouldRun to return true
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return redis.JobStatusInProgress, "", "", nil
		}

		defer func() {
			dbFuncGetAllValidTMS = integrationDB.GetAllValidTMS
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
		}()

		// We just want to see it doesn't panic and calls the dependencies
		go handleTMSRefresh(ctx)
		// Since it runs in goroutines, we might need a small sleep if we wanted to assert more
		time.Sleep(100 * time.Millisecond)
	})
}

func TestRefreshTMSJob_Timeout(t *testing.T) {
	ctx := context.Background()
	integration := models.Integration{
		Name: models.McleodEnterprise,
	}
	integration.ID = 1

	jobConfig := tmsJobConfig{
		jobType:      redis.CustomerRefreshJob,
		jobName:      "customers",
		updateColumn: integrationDB.LastCustomerUpdatedAt,
		runFn: func(_ context.Context, _ tms.Interface, _ ...models.TMSOption) error {
			return context.DeadlineExceeded
		},
	}

	t.Run("Saves state on timeout", func(t *testing.T) {
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		var savedStatus string
		redisFuncSetIntegrationStateStatus = func(_ context.Context, _ uint, _, status, _, _ string) error {
			savedStatus = status
			return nil
		}
		tmsFuncNew = func(_ context.Context, _ models.Integration, _ ...models.TMSOption) (tms.Interface, error) {
			return new(mockTMS), nil
		}

		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			redisFuncSetIntegrationStateStatus = redis.SetIntegrationStateWithStatus
			tmsFuncNew = tms.New
		}()

		err := refreshTMSJob(ctx, integration, jobConfig)
		assert.NoError(t, err)
		assert.Equal(t, redis.JobStatusInProgress, savedStatus)
	})
}

func TestRefreshTMSJob_Unit(t *testing.T) {
	ctx := context.Background()
	integration := models.Integration{
		Name: models.McleodEnterprise,
	}
	integration.ID = 1
	jobConfig := tmsJobConfig{
		jobType:      redis.CustomerRefreshJob,
		jobName:      "customers",
		updateColumn: integrationDB.LastCustomerUpdatedAt,
		runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
			_, err := client.GetCustomers(ctx, opts...)
			return err
		},
	}

	t.Run("Successful run", func(t *testing.T) {
		// Mock dependencies
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		redisFuncSetIntegrationStateStatus = func(_ context.Context, _ uint, _, _, _, _ string) error {
			return nil
		}
		redisFuncSetTMSRefreshLastRun = func(_ context.Context, _ uint, _ string) error {
			return nil
		}
		redisFuncClearIntegrationState = func(_ context.Context, _ uint, _ string) error {
			return nil
		}
		dbFuncSetColumn = func(_ context.Context, _ uint, _ integrationDB.Column, _ any) error {
			return nil
		}

		mockClient := new(mockTMS)
		mockClient.On("GetCustomers", mock.Anything, mock.Anything).Return([]models.TMSCustomer{}, nil)

		tmsFuncNew = func(_ context.Context, _ models.Integration, _ ...models.TMSOption) (tms.Interface, error) {
			return mockClient, nil
		}

		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			redisFuncSetIntegrationStateStatus = redis.SetIntegrationStateWithStatus
			redisFuncSetTMSRefreshLastRun = redis.SetTMSRefreshLastRun
			redisFuncClearIntegrationState = redis.ClearIntegrationState
			dbFuncSetColumn = integrationDB.SetColumn
			tmsFuncNew = tms.New
		}()

		err := refreshTMSJob(ctx, integration, jobConfig)
		assert.NoError(t, err)
		mockClient.AssertExpectations(t)
	})

	t.Run("TMS client creation failure", func(t *testing.T) {
		redisFuncGetIntegrationStateStatus = func(_ context.Context, _ uint, _ string) (string, string, string, error) {
			return "", "", "", nil
		}
		tmsFuncNew = func(_ context.Context, _ models.Integration, _ ...models.TMSOption) (tms.Interface, error) {
			return nil, errors.New("tms error")
		}

		defer func() {
			redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
			tmsFuncNew = tms.New
		}()

		err := refreshTMSJob(ctx, integration, jobConfig)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error creating TMS client")
	})
}
