package main

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
)

func TestExtractDollarAmounts(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "simple dollar amount",
			input:    "The rate would be $2,951.38",
			expected: []string{"2951.38"},
		},
		{
			name:     "HTML formatted amount",
			input:    "<p>Thank you for your request. The rate would be $2,951.38.</p>",
			expected: []string{"2951.38"},
		},
		{
			name:     "multiple amounts",
			input:    "Base rate: $1,500.00, fuel surcharge: $450.50, total: $1,950.50",
			expected: []string{"1500.00", "450.50", "1950.50"},
		},
		{
			name:     "amount without comma",
			input:    "The total is $500.00",
			expected: []string{"500.00"},
		},
		{
			name:     "amount without cents",
			input:    "The rate is $1000",
			expected: []string{"1000"},
		},
		{
			name:     "amount with space after dollar sign",
			input:    "Total: $ 1,234.56",
			expected: []string{"1234.56"},
		},
		{
			name:     "no dollar amounts",
			input:    "Please provide a quote for this shipment",
			expected: nil,
		},
		{
			name:     "empty string",
			input:    "",
			expected: nil,
		},
		{
			name: "complex HTML with quoted message",
			input: `Thank you for your flatbed request from Oakland, NJ to Miami, FL. 
The rate would be $2,951.38.

________________________________
From: Winston Hsiao <<EMAIL>>
Sent: Thursday, January 15, 2026 4:58:03 PM
To: Winston <<EMAIL>>
Subject: Hey need a quote

Can you give a quote for Oakland, NJ to Miami, FL

Best,
Winston`,
			expected: []string{"2951.38"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractDollarAmounts(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAmountsMatch(t *testing.T) {
	tests := []struct {
		name      string
		suggested []string
		sent      []string
		expected  bool
	}{
		{
			name:      "identical single amounts",
			suggested: []string{"2951.38"},
			sent:      []string{"2951.38"},
			expected:  true,
		},
		{
			name:      "different single amounts",
			suggested: []string{"2951.38"},
			sent:      []string{"3000.00"},
			expected:  false,
		},
		{
			name:      "identical multiple amounts",
			suggested: []string{"1500.00", "450.50"},
			sent:      []string{"1500.00", "450.50"},
			expected:  true,
		},
		{
			name:      "different order but has overlap",
			suggested: []string{"1500.00", "450.50"},
			sent:      []string{"450.50", "1500.00"},
			expected:  true,
		},
		{
			name:      "partial overlap - should match",
			suggested: []string{"1000.00", "354.10"},
			sent:      []string{"10.00", "1000.00"},
			expected:  true,
		},
		{
			name:      "no overlap - different amounts",
			suggested: []string{"1000.00"},
			sent:      []string{"2000.00"},
			expected:  false,
		},
		{
			name:      "suggested has more amounts but one matches",
			suggested: []string{"1500.00", "450.50", "2000.00"},
			sent:      []string{"450.50"},
			expected:  true,
		},
		{
			name:      "sent has more amounts but one matches",
			suggested: []string{"2951.38"},
			sent:      []string{"2951.38", "100.00", "500.00"},
			expected:  true,
		},
		{
			name:      "both empty",
			suggested: []string{},
			sent:      []string{},
			expected:  true,
		},
		{
			name:      "both nil",
			suggested: nil,
			sent:      nil,
			expected:  true,
		},
		{
			name:      "one empty one nil",
			suggested: []string{},
			sent:      nil,
			expected:  true,
		},
		{
			name:      "suggested has amounts, sent is empty",
			suggested: []string{"1000.00"},
			sent:      []string{},
			expected:  false,
		},
		{
			name:      "suggested is empty, sent has amounts",
			suggested: []string{},
			sent:      []string{"1000.00"},
			expected:  false,
		},
		{
			name:      "user example - partial match should return true",
			suggested: []string{"1000.00", "354.10"},
			sent:      []string{"10.00", "1000.00"},
			expected:  true,
		},
		{
			name:      "user example reversed - still partial match",
			suggested: []string{"10.00", "1000.00"},
			sent:      []string{"1000.00", "354.10"},
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := amountsMatch(tt.suggested, tt.sent)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareMessageBodies(t *testing.T) {
	tests := []struct {
		name                   string
		suggestedBody          string
		sentBody               string
		expectQuoteChanged     bool
		expectSentBodyReturned bool
	}{
		{
			name: "same quote amount in different formats",
			suggestedBody: `<p>Thank you for your flatbed request from Oakland, NJ to Miami, FL. 
The rate would be $2,951.38.</p>`,
			sentBody: `Thank you for your flatbed request from Oakland, NJ to Miami, FL. 
The rate would be $2,951.38.

________________________________
From: Winston Hsiao <<EMAIL>>
Sent: Thursday, January 15, 2026 4:58:03 PM`,
			expectQuoteChanged:     false,
			expectSentBodyReturned: true,
		},
		{
			name: "same quote amount in different formats (space added after dollar sign)",
			suggestedBody: `Thank you for your flatbed request from Oakland, NJ to Miami, FL. 
The rate would be $2,951.38.`,
			sentBody: `Thank you for your flatbed request from Oakland, NJ to Miami, FL. 
The rate would be $ 2,951.38.`,
			expectQuoteChanged:     false,
			expectSentBodyReturned: true,
		},
		{
			name:                   "different quote amounts",
			suggestedBody:          "<p>The rate is $2,951.38</p>",
			sentBody:               "The rate is $3,000.00",
			expectQuoteChanged:     true,
			expectSentBodyReturned: true,
		},
		{
			name:                   "no amounts in either",
			suggestedBody:          "Thank you for your inquiry",
			sentBody:               "Thank you for your inquiry. I'll get back to you soon.",
			expectQuoteChanged:     false,
			expectSentBodyReturned: true,
		},
		{
			name:                   "amount added in sent email",
			suggestedBody:          "We can help with that shipment",
			sentBody:               "We can help with that shipment for $1,500.00",
			expectQuoteChanged:     true,
			expectSentBodyReturned: true,
		},
		{
			name:                   "amount removed in sent email",
			suggestedBody:          "The rate is $1,500.00",
			sentBody:               "Please call me to discuss pricing",
			expectQuoteChanged:     true,
			expectSentBodyReturned: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			body := &msclient.Body{Content: tt.sentBody}
			quoteChanged, sentBody := compareMessageBodies(tt.suggestedBody, body)

			assert.Equal(t, tt.expectQuoteChanged, quoteChanged)
			if tt.expectSentBodyReturned {
				assert.Equal(t, tt.sentBody, sentBody)
			} else {
				assert.Empty(t, sentBody)
			}
		})
	}
}
