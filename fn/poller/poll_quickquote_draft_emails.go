package main

import (
	"context"
	"fmt"
	"math"
	"net/mail"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/k3a/html2text"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteinvocationDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/invocation"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/poller/env"
)

const (
	quickQuoteAgentDraftPollerKey      = "quickquote_agent_draft_email_poller_last_run"
	quickQuoteAgentDraftPollerInterval = 1 * time.Hour
	// Slightly less than 1 hours to ensure key remains available for poller every 10 min run.
	quickQuoteAgentDraftPollerTTL = 59 * time.Minute
	// TODO: Turn this back to 7 days in next PR
	quickQuoteAgentDraftPollerLookback = 24 * time.Hour * 30 // 30 days lookback for backfilling

	quickQuoteAgentDraftPollerBatchSize = 5000
	// Microsoft Graph API batch limit
	msgraphBatchSize = 20
	// Max concurrent user processing goroutines
	maxUserConcurrency = 10
	// Lambda timeout buffer - stop processing this many minutes before Lambda timeout
	lambdaTimeoutBuffer = 30 * time.Second
	// Poller Lambda timeout
	pollerLambdaTimeout = 10 * time.Minute
	// Max failures before circuit breaker stops processing
	maxFailuresBeforeCircuitBreak = 50

	dollarAmountPattern = `\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?|\d+(?:\.\d{2})?)\b`
)

var dollarAmountRegex = regexp.MustCompile(dollarAmountPattern)

func shouldRunQuickQuoteAgentDraftPoller(ctx context.Context) bool {
	lastRun, found, err := redis.GetKey[string](ctx, quickQuoteAgentDraftPollerKey)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"could not get last quick quote agent draft poller run time, will run",
			zap.Error(err),
		)
		return true
	}

	if !found || lastRun == "" {
		log.Info(ctx, "no previous quick quote agent draft poller run recorded, will run")
		return true
	}

	lastRunTime, err := time.Parse(time.RFC3339, lastRun)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"could not parse last quick quote agent draft poller run time, will run",
			zap.Error(err),
		)
		return true
	}

	return time.Since(lastRunTime) >= quickQuoteAgentDraftPollerInterval
}

func updateQuickQuoteAgentDraftPollerLastRun(ctx context.Context) error {
	now := time.Now().Format(time.RFC3339)
	return redis.SetKey(ctx, quickQuoteAgentDraftPollerKey, now, quickQuoteAgentDraftPollerTTL)
}

func pollQuickQuoteAgentDraftEmails(ctx context.Context) (err error) {
	ctx, span := otel.StartSpan(ctx, "pollQuickQuoteAgentDraftEmails", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "quick quote agent draft poller failed", zap.Error(err))
		}
		span.End(err)
	}()

	// Timeout aware context
	timeoutCtx, cancel := context.WithTimeout(ctx, pollerLambdaTimeout-lambdaTimeoutBuffer)
	defer cancel()

	emails, err := genEmailDB.GetQuickQuoteAgentOutlookDrafts(
		timeoutCtx,
		time.Now().Add(-quickQuoteAgentDraftPollerLookback),
		quickQuoteAgentDraftPollerBatchSize,
	)
	if err != nil {
		return fmt.Errorf("failed to fetch quick quote agent draft emails: %w", err)
	}

	if len(emails) == 0 {
		log.Info(timeoutCtx, "no quick quote agent draft emails to check")
		return nil
	}

	log.Info(
		timeoutCtx,
		"checking quick quote agent draft emails",
		zap.Int("count", len(emails)),
	)

	// Group emails by user for batch processing
	emailsByUser := groupEmailsByUser(timeoutCtx, emails)
	if len(emailsByUser) == 0 {
		log.Info(timeoutCtx, "no valid emails to process after filtering")
		return nil
	}

	var successCount, failCount, skippedCount atomic.Int32

	// Process users concurrently
	var wg sync.WaitGroup
	sem := make(chan struct{}, maxUserConcurrency)

	for userID, userEmails := range emailsByUser {
		// Check circuit breaker before processing more users
		if failCount.Load() >= maxFailuresBeforeCircuitBreak {
			skippedCount.Add(safeInt32(len(userEmails.emails)))
			log.Warn(
				timeoutCtx,
				"circuit breaker triggered, skipping remaining users in quick quote agent draft poller",
				zap.Uint("userID", userID),
				zap.Int32("totalFailures", failCount.Load()),
			)
			continue
		}

		// Check for timeout before starting new user
		select {
		case <-timeoutCtx.Done():
			skippedCount.Add(safeInt32(len(userEmails.emails)))
			log.Warn(
				ctx,
				"timeout reached, skipping remaining users in quick quote agent draft poller",
				zap.Uint("userID", userID),
				zap.Int("remainingUsers", len(emailsByUser)),
			)
		default:
		}

		wg.Add(1)
		go func(uid uint, ue userEmailGroup) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					err := fmt.Errorf("%v", r)
					log.Error(
						timeoutCtx,
						"draft email processing goroutine panicked",
						zap.Uint("userID", uid),
						zap.Error(err),
					)
					failCount.Add(safeInt32(len(ue.emails)))
				}
				<-sem
			}()

			sem <- struct{}{}
			processUserEmails(timeoutCtx, uid, ue, &successCount, &failCount)
		}(userID, userEmails)
	}

	wg.Wait()

	log.Info(
		ctx,
		"quick quote agent draft poller completed",
		zap.Int32("successCount", successCount.Load()),
		zap.Int32("failCount", failCount.Load()),
		zap.Int32("skippedCount", skippedCount.Load()),
		zap.Int("totalEmails", len(emails)),
		zap.Int("totalUsers", len(emailsByUser)),
	)

	return nil
}

// userEmailGroup holds the user and their generated emails for draft status checks
type userEmailGroup struct {
	user   models.User
	emails []*models.GeneratedEmail
}

// groupEmailsByUser groups valid emails by their user ID
func groupEmailsByUser(ctx context.Context, emails []*models.GeneratedEmail) map[uint]userEmailGroup {
	emailsByUser := make(map[uint]userEmailGroup)

	for _, email := range emails {
		if email == nil {
			continue
		}

		if email.ExternalID == "" {
			log.Warn(ctx, "generated email missing external ID", zap.Uint("generatedEmailID", email.ID))
			continue
		}

		user := email.User
		if user.ID == 0 {
			log.Warn(ctx, "generated email missing user preload", zap.Uint("generatedEmailID", email.ID))
			continue
		}

		group, exists := emailsByUser[email.UserID]
		if !exists {
			group = userEmailGroup{user: user, emails: []*models.GeneratedEmail{}}
		}
		group.emails = append(group.emails, email)
		emailsByUser[email.UserID] = group
	}

	return emailsByUser
}

// processUserEmails processes all emails for a single user using batch API
func processUserEmails(
	ctx context.Context,
	userID uint,
	group userEmailGroup,
	successCount, failCount *atomic.Int32,
) {

	client, err := msclient.New(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		&group.user,
	)
	if err != nil {
		log.Warn(
			ctx,
			"failed to create Outlook client for quick quote agent draft poller",
			zap.Uint("userID", userID),
			zap.Error(err),
		)
		failCount.Add(safeInt32(len(group.emails)))
		return
	}

	deletionFolders, folderErr := msclient.GetDraftDeletionFolders(ctx, client)
	if folderErr != nil {
		log.Warn(
			ctx,
			"could not resolve Outlook deletion folders, draft classification may be incomplete",
			zap.Uint("userID", userID),
			zap.Error(folderErr),
		)
	}

	expectedSender := group.user.EmailAddress

	// Process emails in batches of msgraphBatchSize (20)
	for i := 0; i < len(group.emails); i += msgraphBatchSize {
		// Check for timeout before processing next batch
		select {
		case <-ctx.Done():
			remaining := len(group.emails) - i
			failCount.Add(safeInt32(remaining))
			log.Warn(
				ctx,
				"timeout reached during user processing, stopping",
				zap.Uint("userID", userID),
				zap.Int("processedEmails", i),
				zap.Int("remainingEmails", remaining),
			)
			return
		default:
		}

		end := i + msgraphBatchSize
		if end > len(group.emails) {
			end = len(group.emails)
		}
		batch := group.emails[i:end]
		processEmailBatch(ctx, client, deletionFolders, expectedSender, batch, successCount, failCount)
	}
}

// processEmailBatch processes a batch of emails (up to 20) using MS Graph batch API
func processEmailBatch(
	ctx context.Context,
	client msclient.Client,
	deletionFolders msclient.DraftDeletionFolders,
	expectedSender string,
	emails []*models.GeneratedEmail,
	successCount, failCount *atomic.Int32,
) {
	// Build list of external IDs for batch request
	ids := make([]string, 0, len(emails))
	emailByID := make(map[string]*models.GeneratedEmail, len(emails))
	for _, email := range emails {
		ids = append(ids, email.ExternalID)
		emailByID[email.ExternalID] = email
	}

	// Batch fetch messages
	results, err := client.BatchGetMessages(ctx, ids, msclient.WithContentType(msclient.PlaintextContentType))
	if err != nil {
		log.Warn(ctx, "batch fetch failed, falling back to individual fetches", zap.Error(err))
		// Fallback to individual fetches if batch fails
		for _, email := range emails {
			if err := processSingleEmail(ctx, client, deletionFolders, expectedSender, email); err != nil {
				log.Warn(ctx, "failed to process email", zap.Uint("generatedEmailID", email.ID), zap.Error(err))
				failCount.Add(1)
			} else {
				successCount.Add(1)
			}
		}
		return
	}

	// Process all emails and handle fallbacks for not-found messages or missing batch results
	for _, email := range emails {
		batchResult, exists := results[email.ExternalID]
		if !exists {
			// Email not in batch results (possibly due to unparseable batch response ID)
			if err := processSingleEmail(ctx, client, deletionFolders, expectedSender, email); err != nil {
				log.Warn(
					ctx,
					"failed to process email for draft status check",
					zap.Uint("generatedEmailID", email.ID),
					zap.Error(err),
				)
				failCount.Add(1)
			} else {
				successCount.Add(1)
			}
			continue
		}

		if batchResult.Error != nil {
			log.Warn(
				ctx,
				"batch item error, falling back to individual fetch",
				zap.Uint("generatedEmailID", email.ID),
				zap.Error(batchResult.Error),
			)
			// Fallback to single-message processing for this email
			if err := processSingleEmail(ctx, client, deletionFolders, expectedSender, email); err != nil {
				log.Warn(
					ctx,
					"failed to process email for draft status check after batch error",
					zap.Uint("generatedEmailID", email.ID),
					zap.Error(err),
				)
				failCount.Add(1)
			} else {
				successCount.Add(1)
			}
			continue
		}

		if batchResult.Found {
			updateEmailFromMessage(ctx, email, batchResult.Message, true, deletionFolders)
			successCount.Add(1)
			continue
		}

		// Message not found by ID in batch; use single-message logic which includes fallback searches.
		if err := processSingleEmail(ctx, client, deletionFolders, expectedSender, email); err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to process email for draft status check",
				zap.Uint("generatedEmailID", email.ID),
				zap.Error(err),
			)
			failCount.Add(1)
		} else {
			successCount.Add(1)
		}
	}
}

// processSingleEmail processes a single email (used as fallback when batch fails)
func processSingleEmail(
	ctx context.Context,
	client msclient.Client,
	deletionFolders msclient.DraftDeletionFolders,
	expectedSender string,
	email *models.GeneratedEmail,
) error {

	msg, found, err := getMessageForDraft(ctx, client, deletionFolders, expectedSender, email)
	if err != nil {
		log.Warn(
			ctx,
			"failed to fetch draft email message",
			zap.Uint("generatedEmailID", email.ID),
			zap.Error(err),
		)
		return fmt.Errorf("failed to fetch draft email message for ID %d: %w", email.ID, err)
	}

	updateEmailFromMessage(ctx, email, msg, found, deletionFolders)
	return nil
}

// updateEmailFromMessage updates the email status based on the fetched message
func updateEmailFromMessage(
	ctx context.Context,
	email *models.GeneratedEmail,
	msg msclient.Message,
	found bool,
	deletionFolders msclient.DraftDeletionFolders,
) {

	now := time.Now()
	draftCheckedAt := models.NullTime{Time: now, Valid: true}

	updates := genEmailDB.DraftStatusUpdate{
		DraftCheckedAt: &draftCheckedAt,
	}

	state := msclient.ClassifyDraftState(found, msg, deletionFolders)

	switch state {
	case msclient.DraftStateDeleted:
		draftStatus := models.DraftStatusDeleted
		status := models.UserDeletedStatus
		updates.DraftStatus = &draftStatus
		updates.Status = &status
		if err := updateInvocationUserResponse(ctx, email, agentmodels.RejectedUserResponse); err != nil {
			log.Warn(
				ctx,
				"failed to update agent invocation to rejected",
				zap.Uint("generatedEmailID", email.ID),
				zap.Error(err),
			)
		}
	case msclient.DraftStateDraft:
		draftStatus := models.DraftStatusDraft
		updates.DraftStatus = &draftStatus
	case msclient.DraftStateSent:
		draftStatus := models.DraftStatusSent
		status := models.SentStatus
		updates.DraftStatus = &draftStatus
		updates.Status = &status
		if !msg.SentDateTime.IsZero() {
			sentAt := models.NullTime{Time: msg.SentDateTime, Valid: true}
			updates.SentAt = &sentAt
		}
		if msg.WebLink != "" {
			webLink := msg.WebLink
			updates.WebLink = &webLink
		}

		quoteAmountChanged, sentBody := compareMessageBodies(email.SuggestedBody, msg.Body)
		updates.QuoteAmountChanged = &quoteAmountChanged
		if sentBody != "" {
			appliedBody := sentBody
			updates.AppliedBody = &appliedBody
		}

		acceptanceStatus := agentmodels.AcceptedNoEditsUserResponse
		if quoteAmountChanged {
			acceptanceStatus = agentmodels.AcceptedWithEditsUserResponse
		}
		if err := updateInvocationUserResponse(ctx, email, acceptanceStatus); err != nil {
			log.Warn(
				ctx,
				"failed to update agent invocation user response status",
				zap.Uint("generatedEmailID", email.ID),
				zap.Error(err),
			)
		}
	case msclient.DraftStateUnknown:
		draftStatus := models.DraftStatusUnknown
		updates.DraftStatus = &draftStatus
	}

	if err := genEmailDB.UpdateDraftStatusFields(ctx, email.ID, updates); err != nil {
		log.Warn(
			ctx,
			"failed to update generated email draft status",
			zap.Uint("generatedEmailID", email.ID),
			zap.Error(err),
		)
	}
}

func getMessageForDraft(
	ctx context.Context,
	client msclient.Client,
	deletionFolders msclient.DraftDeletionFolders,
	expectedSender string,
	email *models.GeneratedEmail,
) (msg msclient.Message, found bool, err error) {

	msg, err = client.GetMessageByID(
		ctx,
		email.ExternalID,
		msclient.WithContentType(msclient.PlaintextContentType),
	)
	if err == nil {
		return msg, true, nil
	}

	if !strings.Contains(strings.ToLower(err.Error()), "not found") {
		return msg, false, err
	}

	if email.RFCMessageID == "" {
		return findMessageByConversationID(
			ctx,
			client,
			deletionFolders,
			email.ThreadID,
			coalesceSender(email.Sender, expectedSender),
		)
	}

	filter := fmt.Sprintf("internetMessageId eq '%s'", escapeODataString(email.RFCMessageID))
	matches, searchErr := client.SearchMessages(
		ctx,
		filter,
		msclient.WithContentType(msclient.PlaintextContentType),
	)
	if searchErr != nil {
		return msg, false, searchErr
	}

	if len(matches) == 0 {
		return findMessageByConversationID(
			ctx,
			client,
			deletionFolders,
			email.ThreadID,
			coalesceSender(email.Sender, expectedSender),
		)
	}

	return matches[0], true, nil
}

func findMessageByConversationID(
	ctx context.Context,
	client msclient.Client,
	deletionFolders msclient.DraftDeletionFolders,
	conversationID string,
	expectedSender string,
) (msg msclient.Message, found bool, err error) {

	if conversationID == "" {
		return msg, false, nil
	}

	filter := fmt.Sprintf("conversationId eq '%s'", escapeODataString(conversationID))
	matches, searchErr := client.SearchMessages(
		ctx,
		filter,
		msclient.WithSearchOrderBy(""),
		msclient.WithContentType(msclient.PlaintextContentType),
	)
	if searchErr != nil {
		return msg, false, searchErr
	}
	if len(matches) == 0 {
		return msg, false, nil
	}

	return pickBestConversationMessage(matches, expectedSender, deletionFolders)
}

// pickBestConversationMessage picks the most likely message from a list of messages by conversation ID that would be
// the sent quick quote agent draft email.
func pickBestConversationMessage(
	matches []msclient.Message,
	expectedSender string,
	deletionFolders msclient.DraftDeletionFolders,
) (msg msclient.Message, found bool, err error) {

	if len(matches) == 0 {
		return msg, false, nil
	}

	// Prefer deleted drafts in Deleted Items / Recoverable Deletions.
	if candidate, ok := findBestInFolder(matches, deletionFolders.DeletedItemsID); ok {
		return candidate, true, nil
	}
	if candidate, ok := findBestInFolder(matches, deletionFolders.RecoverableItemsDeletionsID); ok {
		return candidate, true, nil
	}

	// Prefer Sent Items to avoid choosing the original inbound message in Inbox.
	if candidate, ok := findBestInFolder(matches, deletionFolders.SentItemsID); ok {
		return candidate, true, nil
	}

	// Prefer Drafts if still present.
	if candidate, ok := findBestInFolder(matches, deletionFolders.DraftsID); ok {
		return candidate, true, nil
	}

	normalizedSender := normalizeEmailAddress(expectedSender)
	if normalizedSender != "" {
		if candidate, ok := findBestNonDraftFromSender(matches, normalizedSender); ok {
			return candidate, true, nil
		}
	}

	if candidate, ok := findBestNonDraft(matches); ok {
		return candidate, true, nil
	}

	return matches[0], true, nil
}

func coalesceSender(primary, fallback string) string {
	primary = strings.TrimSpace(primary)
	if primary != "" {
		return primary
	}
	return strings.TrimSpace(fallback)
}

func findBestInFolder(matches []msclient.Message, folderID string) (msclient.Message, bool) {
	folderID = strings.TrimSpace(folderID)
	if folderID == "" {
		return msclient.Message{}, false
	}

	var best msclient.Message
	var bestTime time.Time
	for _, candidate := range matches {
		if candidate.ParentFolderID != folderID {
			continue
		}
		if isNewerMessage(candidate, bestTime) {
			best = candidate
			bestTime = messageSortTime(candidate)
		}
	}

	return best, !bestTime.IsZero()
}

func findBestNonDraftFromSender(matches []msclient.Message, sender string) (msclient.Message, bool) {
	var best msclient.Message
	var bestTime time.Time
	for _, candidate := range matches {
		if candidate.IsDraft || !senderMatches(candidate, sender) {
			continue
		}
		if isNewerMessage(candidate, bestTime) {
			best = candidate
			bestTime = messageSortTime(candidate)
		}
	}

	return best, !bestTime.IsZero()
}

func findBestNonDraft(matches []msclient.Message) (msclient.Message, bool) {
	var best msclient.Message
	var bestTime time.Time
	for _, candidate := range matches {
		if candidate.IsDraft {
			continue
		}
		if isNewerMessage(candidate, bestTime) {
			best = candidate
			bestTime = messageSortTime(candidate)
		}
	}

	return best, !bestTime.IsZero()
}

func isNewerMessage(candidate msclient.Message, bestTime time.Time) bool {
	candidateTime := messageSortTime(candidate)
	if candidateTime.IsZero() {
		return false
	}
	return bestTime.IsZero() || candidateTime.After(bestTime)
}

func messageSortTime(candidate msclient.Message) time.Time {
	if !candidate.SentDateTime.IsZero() {
		return candidate.SentDateTime
	}
	if !candidate.LastModifiedDateTime.IsZero() {
		return candidate.LastModifiedDateTime
	}
	return candidate.ReceivedDateTime
}

func senderMatches(candidate msclient.Message, sender string) bool {
	fromAddress := strings.TrimSpace(candidate.From.EmailAddress.Address)
	if fromAddress != "" && strings.EqualFold(fromAddress, sender) {
		return true
	}

	senderAddress := strings.TrimSpace(candidate.Sender.EmailAddress.Address)
	return senderAddress != "" && strings.EqualFold(senderAddress, sender)
}

func normalizeEmailAddress(raw string) string {
	raw = strings.TrimSpace(raw)
	if raw == "" {
		return ""
	}

	parsed, err := mail.ParseAddress(raw)
	if err != nil {
		return raw
	}

	return strings.TrimSpace(parsed.Address)
}

func compareMessageBodies(
	suggestedBody string,
	body *msclient.Body,
) (quoteAmountChanged bool, sentBody string) {
	if body == nil || body.Content == "" {
		return false, ""
	}

	sentBody = body.Content
	suggestedAmounts := extractDollarAmounts(suggestedBody)
	sentAmounts := extractDollarAmounts(sentBody)

	return !amountsMatch(suggestedAmounts, sentAmounts), sentBody
}

// extractDollarAmounts extracts all dollar amounts from text (HTML or plain text)
// Returns normalized amounts as strings (e.g., "2951.38", "1000.00")
func extractDollarAmounts(text string) []string {
	text = strings.TrimSpace(text)
	if text == "" {
		return nil
	}

	// Convert HTML to plain text if needed
	if strings.Contains(text, "<") && strings.Contains(text, ">") {
		text = html2text.HTML2Text(text)
	}

	matches := dollarAmountRegex.FindAllStringSubmatch(text, -1)
	if len(matches) == 0 {
		return nil
	}

	amounts := make([]string, 0, len(matches))
	for _, match := range matches {
		if len(match) > 1 {
			// Remove commas and normalize the amount
			normalized := strings.ReplaceAll(match[1], ",", "")
			amounts = append(amounts, normalized)
		}
	}

	return amounts
}

// amountsMatch checks if there's any overlap between two sets of dollar amounts.
// Returns true if at least one amount appears in both sets, false otherwise.
// If both sets are empty, returns true (no amounts means no quote change).
func amountsMatch(suggested, sent []string) bool {
	if len(suggested) == 0 && len(sent) == 0 {
		return true
	}

	if len(suggested) == 0 || len(sent) == 0 {
		return false
	}

	// Create a set of suggested amounts for O(n) lookup
	suggestedSet := make(map[string]bool, len(suggested))
	for _, amount := range suggested {
		suggestedSet[amount] = true
	}

	// Check if any sent amount exists in suggested set
	for _, amount := range sent {
		if suggestedSet[amount] {
			return true
		}
	}

	return false
}

func escapeODataString(value string) string {
	return strings.ReplaceAll(value, "'", "''")
}

func updateInvocationUserResponse(
	ctx context.Context,
	email *models.GeneratedEmail,
	status agentmodels.AgentUserResponseStatus,
) error {

	if email.QuickQuoteAgentInvocationID == nil || *email.QuickQuoteAgentInvocationID == 0 {
		return nil
	}

	updates := &agentmodels.QuickQuoteAgentInvocation{
		UserResponseStatus: status,
	}

	return quickquoteinvocationDB.UpdateQuickQuoteAgentInvocationByID(
		ctx,
		*email.QuickQuoteAgentInvocationID,
		updates,
	)
}

// safeInt32 converts an int to int32, clamping to math.MaxInt32 if overflow would occur.
// This prevents integer overflow when converting slice lengths to int32 for atomic counters.
func safeInt32(n int) int32 {
	if n > math.MaxInt32 {
		return math.MaxInt32
	}
	//nolint:gosec // Safe conversion: we check n <= math.MaxInt32 above
	return int32(n)
}
