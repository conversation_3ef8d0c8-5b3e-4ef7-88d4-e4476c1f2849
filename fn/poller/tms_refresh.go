package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/poller/env"
)

const (
	// Full sync time window is 7-8 pm ET, off peak hours to minimize TMS load across Drumkit services
	FullSyncTimeWindowTimezone        = "America/New_York"
	defaultFullSyncTimeWindowESTStart = 19 // 7 pm ET, off peak hours
	defaultFullSyncTimeWindowESTEnd   = 20 // 8 pm ET, off peak hour
)

var (
	// For unit testing
	dbFuncGetAllValidTMS               = integrationDB.GetAllValidTMS
	dbFuncSetColumn                    = integrationDB.SetColumn
	redisFuncGetIntegrationStateStatus = redis.GetIntegrationStateWithStatus
	redisFuncGetTMSRefreshLastRun      = redis.GetTMSRefreshLastRun
	redisFuncSetTMSRefreshLastRun      = redis.SetTMSRefreshLastRun
	redisFuncSetIntegrationStateStatus = redis.SetIntegrationStateWithStatus
	redisFuncClearIntegrationState     = redis.ClearIntegrationState
	redisFuncGetKey                    = redis.GetKey[retryContext]
	redisFuncSetKey                    = redis.SetKey[any]
	redisFuncDeleteKey                 = redis.DeleteKey
	tmsFuncNew                         = tms.New
	tmsFuncNewStatic                   = tms.NewStatic
	timeNowFunc                        = time.Now
)

type tmsJobConfig struct {
	jobType      string
	jobName      string
	runFn        func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error
	updateColumn integrationDB.Column
}

type retryContext struct {
	LastCursor string `json:"last_cursor"`
	RetryCount int    `json:"retry_count"`
	SkipCount  int    `json:"skip_count"`
}

const (
	// Poller lambda timeout configured to 10 minutes in Parthenon/AWS Console
	PollerMaxDuration = 10 * time.Minute
	// TMS job timeout is 1 minute less than the poller timeout to account for graceful shutdown of TMS object jobs
	TMSJobMaxDuration = PollerMaxDuration - 1*time.Minute
)

// shouldRunTMSRefresh checks if TMS refresh should run based on cadence
func shouldRunTMSRefresh(ctx context.Context, integration models.Integration, jobType string) bool {
	ctx = log.With(
		ctx,
		zap.String("tmsName", string(integration.Name)),
		zap.String("jobType", jobType),
		zap.Uint("integrationID", integration.ID),
		zap.Uint("serviceID", integration.ServiceID),
	)

	ctx, metaSpan := otel.StartSpan(ctx, "shouldRunTMSRefresh", nil)
	defer metaSpan.End(nil)

	// Check if poller is disabled for this job type
	if (jobType == redis.CustomerRefreshJob && integration.TMSPollerCustomersDisabled) ||
		(jobType == redis.LocationRefreshJob && integration.TMSPollerLocationsDisabled) {
		log.Info(
			ctx,
			"TMS poller disabled for this job type, will not run",
			zap.String("jobType", jobType),
		)
		return false
	}

	// check if we have any in progress job for the integration
	status, _, _, err := redisFuncGetIntegrationStateStatus(
		ctx,
		integration.ID,
		jobType,
	)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to read job state", zap.Error(err))
	}

	// Always resume in-progress jobs
	if status == redis.JobStatusInProgress {
		log.Info(
			ctx,
			"resuming in-progress TMS job",
		)
		return true
	}

	staticTMS, err := tmsFuncNewStatic(ctx, integration)
	if err != nil {
		log.Info(
			ctx,
			"TMS type not supported, will not run TMS refresh",
			zap.String("tmsName", string(integration.Name)),
			zap.Error(err),
		)
		return false
	}

	var approach models.TMSSyncApproach
	switch jobType {
	case redis.CustomerRefreshJob:
		approach = staticTMS.TMSCustomerSyncApproach().Approach
	case redis.LocationRefreshJob:
		approach = staticTMS.TMSLocationSyncApproach().Approach
	}

	if approach == models.TMSSyncApproachNotSupported {
		log.Info(
			ctx,
			"TMS sync approach not supported for this job type, will not run TMS refresh",
			zap.String("tmsName", string(integration.Name)),
			zap.String("jobType", jobType),
		)
		return false
	}

	lastRun, err := redisFuncGetTMSRefreshLastRun(ctx, integration.ID, jobType)
	if err != nil {
		log.WarnNoSentry(ctx, "could not get last TMS refresh run time, will run", zap.Error(err))
		return true
	}

	if lastRun.IsZero() {
		log.Info(ctx, "no previous TMS refresh run recorded, will run")
		return true
	}

	var interval time.Duration
	switch approach {
	case models.TMSSyncApproachPartial:
		// Partial sync TMSes run every hour
		interval = time.Hour

	case models.TMSSyncApproachFull:
		// Full sync TMSes run once a day during off hours (7-8 PM EST)
		interval = 24 * time.Hour

		// For full sync TMSes, also check if we're in the right time window (7-8 PM EST)
		now := timeNowFunc()
		est, err := time.LoadLocation("America/New_York")
		if err != nil {
			log.Error(ctx, "error loading EST timezone, will not run TMS refresh", zap.Error(err))
			return false
		}

		nowEST := now.In(est)
		hour := nowEST.Hour()

		windowStart, windowEnd := getFullSyncTimeWindow(ctx)

		if hour < windowStart || hour >= windowEnd {
			log.Info(
				ctx,
				"not in TMS full sync time window",
				zap.Int("currentHour", hour),
				zap.Int("windowStart", windowStart),
				zap.Int("windowEnd", windowEnd),
			)
			return false
		}

	default:
		log.WarnNoSentry(
			ctx,
			"TMS sync approach not recognized, will not run TMS refresh",
			zap.String("approach", string(approach)),
		)
		return false
	}

	timeSinceLastRun := time.Since(lastRun)
	shouldRun := timeSinceLastRun >= interval

	log.Info(
		ctx,
		"TMS refresh timing check",
		zap.Duration("timeSinceLastRun", timeSinceLastRun),
		zap.Duration("interval", interval),
		zap.Bool("shouldRun", shouldRun),
	)

	return shouldRun
}

// handleTMSRefresh handles TMS customer and location refresh for all integrations
// It spawns goroutines for each job and waits for all of them to complete before returning.
func handleTMSRefresh(ctx context.Context) {
	ctx, metaSpan := otel.StartSpan(ctx, "handleTMSRefresh", nil)
	defer metaSpan.End(nil)

	sentry.WithHub(ctx, func(ctx context.Context) {
		log.Info(ctx, "starting TMS refresh check")

		// Get all TMS integrations
		integrations, err := dbFuncGetAllValidTMS(ctx)
		if err != nil {
			log.Error(ctx, "failed to get TMS integrations", zap.Error(err))
			return
		}

		var wg sync.WaitGroup

		for _, integration := range integrations {
			// GlobalTranz.GetLocations depends on GetCustomers being complete since
			// locations are associated with customers;
			// handle this sequentiality correctly for GlobalTranzTMS
			if integration.Name == models.GlobalTranzTMS {
				wg.Add(1)
				go func(integ models.Integration) {
					defer wg.Done()

					// Sequence: Customers then Locations
					jobs := []tmsJobConfig{
						{
							jobType:      redis.CustomerRefreshJob,
							jobName:      "customers",
							updateColumn: integrationDB.LastCustomerUpdatedAt,
							runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
								_, err := client.GetCustomers(ctx, opts...)
								return err
							},
						},
						{
							jobType:      redis.LocationRefreshJob,
							jobName:      "locations",
							updateColumn: integrationDB.LastLocationUpdatedAt,
							runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
								_, err := client.GetLocations(ctx, opts...)
								return err
							},
						},
					}

					for _, job := range jobs {
						if !shouldRunTMSRefresh(ctx, integ, job.jobType) {
							continue
						}

						err := func() (jobErr error) {
							defer func() {
								if r := recover(); r != nil {
									panicErr := fmt.Errorf("%v", r)
									log.Error(
										ctx,
										fmt.Sprintf("%s refresh worker panicked", job.jobName),
										zap.Error(panicErr),
										zap.Any("job", job),
									)
									jobErr = panicErr
								}
							}()

							return refreshTMSJob(ctx, integ, job)
						}()

						if err != nil {
							log.Error(
								ctx,
								fmt.Sprintf("TMS %s refresh failed", job.jobName),
								zap.Error(err),
								zap.Uint("integrationID", integ.ID),
								zap.String("tmsName", string(integ.Name)),
							)
							break
						}
					}
				}(integration)
				continue
			}

			// Check if we should refresh customers
			if shouldRunTMSRefresh(ctx, integration, redis.CustomerRefreshJob) {
				wg.Add(1)
				go func(integ models.Integration) {
					defer wg.Done()

					customerJob := tmsJobConfig{
						jobType:      redis.CustomerRefreshJob,
						jobName:      "customers",
						updateColumn: integrationDB.LastCustomerUpdatedAt,
						runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
							_, err := client.GetCustomers(ctx, opts...)
							return err
						},
					}

					defer func() {
						if r := recover(); r != nil {
							panicErr := fmt.Errorf("%v", r)
							log.Error(
								ctx,
								"customer refresh worker panicked",
								zap.Error(panicErr),
								zap.Any("customerJob", customerJob),
							)
						}
					}()

					if err := refreshTMSJob(ctx, integ, customerJob); err != nil {
						log.Error(
							ctx,
							"TMS customer refresh failed",
							zap.Error(err),
							zap.Uint("integrationID", integ.ID),
							zap.String("tmsName", string(integ.Name)),
						)
					}
				}(integration)
			}

			// Check if we should refresh locations
			if shouldRunTMSRefresh(ctx, integration, redis.LocationRefreshJob) {
				wg.Add(1)
				go func(integ models.Integration) {

					defer wg.Done()

					locationJob := tmsJobConfig{
						jobType:      redis.LocationRefreshJob,
						jobName:      "locations",
						updateColumn: integrationDB.LastLocationUpdatedAt,
						runFn: func(ctx context.Context, client tms.Interface, opts ...models.TMSOption) error {
							_, err := client.GetLocations(ctx, opts...)
							return err
						},
					}

					defer func() {
						if r := recover(); r != nil {
							panicErr := fmt.Errorf("%v", r)
							log.Error(
								ctx, "location refresh worker panicked",
								zap.Error(panicErr),
								zap.Any("locationJob", locationJob),
							)
						}
					}()

					if err := refreshTMSJob(ctx, integ, locationJob); err != nil {
						log.Error(
							ctx,
							"TMS location refresh failed",
							zap.Error(err),
							zap.Uint("integrationID", integ.ID),
							zap.String("tmsName", string(integ.Name)),
						)
					}
				}(integration)
			}
		}

		// Wait for all goroutines to complete
		wg.Wait()

		log.Info(ctx, "TMS refresh check completed")
	})
}

func refreshTMSJob(
	ctx context.Context,
	integration models.Integration,
	jobConfig tmsJobConfig,
) (err error) {
	ctx, cancel := context.WithTimeout(ctx, TMSJobMaxDuration) // Leave 1 minute buffer before poller timeout
	defer cancel()

	ctx, metaSpan := otel.StartSpan(ctx, "refreshTMSJob", nil)
	defer func() { metaSpan.End(err) }()

	jobType := jobConfig.jobType
	runFn := jobConfig.runFn
	updateColumn := jobConfig.updateColumn
	jobName := jobConfig.jobName

	ctx = log.With(
		ctx,
		zap.Uint("integrationID", integration.ID),
		zap.String("tmsName", string(integration.Name)),
		zap.String("jobName", jobName),
		zap.String("jobType", jobType),
	)

	log.Info(ctx, "starting TMS refresh")

	// Check for existing job state to continue (using status-aware function)
	savedStatus, savedUpdatedAt, savedCursor, err := redisFuncGetIntegrationStateStatus(ctx, integration.ID, jobType)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			fmt.Sprintf("failed to get integration state for %s refresh", jobName),
			zap.Error(err))
	}

	log.Info(
		ctx,
		"TMS refresh job state",
		zap.String("savedStatus", savedStatus),
		zap.String("savedUpdatedAt", savedUpdatedAt),
		zap.String("savedCursor", savedCursor),
		zap.Uint("integrationID", integration.ID),
	)

	client, err := tmsFuncNew(ctx, integration)
	if err != nil {
		return fmt.Errorf("error creating TMS client: %w", err)
	}

	var approach models.TMSSyncApproach
	switch jobType {
	case redis.CustomerRefreshJob:
		approach = client.TMSCustomerSyncApproach().Approach
	case redis.LocationRefreshJob:
		approach = client.TMSLocationSyncApproach().Approach
	}

	// Set lastRun at job start for full sync TMSes
	if approach == models.TMSSyncApproachFull && savedStatus != redis.JobStatusInProgress {
		if err := redisFuncSetTMSRefreshLastRun(ctx, integration.ID, jobType); err != nil {
			log.WarnNoSentry(ctx, "failed to set lastRun at job start", zap.Error(err))
		}
	}

	var (
		lastUpdatedAt string
		lastCursor    string
	)
	// Build options for TMS client
	opts := []models.TMSOption{
		models.WithPollerJob(true),
		models.WithProgressCallback(func(u, c string) {
			lastUpdatedAt = u
			lastCursor = c
		}),
	}

	// Aljex specific: always pass retry option as requested by client
	if integration.Name == models.Aljex {
		opts = append(opts, models.WithRetryFailedPages(true))
	}

	// CIRCUIT BREAKER LOGIC
	retryKey := fmt.Sprintf("tms-refresh-retry-%d-%s", integration.ID, jobType)
	var retryCtx retryContext
	if val, exists, err := redisFuncGetKey(ctx, retryKey); err == nil && exists {
		retryCtx = val
	}

	// If we have a saved cursor, we are in a retry/continuation state
	if savedCursor != "" {
		if retryCtx.LastCursor == savedCursor {
			retryCtx.RetryCount++
		} else {
			// First time seeing this specific cursor, start count at 1
			retryCtx.LastCursor = savedCursor
			retryCtx.RetryCount = 1
		}

		log.Debug(
			ctx,
			fmt.Sprintf("TMS %s refresh retry tracking", jobName),
			zap.String("cursor", savedCursor),
			zap.Int("retryCount", retryCtx.RetryCount),
		)

		if retryCtx.RetryCount >= 3 {
			log.Error(
				ctx,
				fmt.Sprintf("Circuit breaker tripped for TMS %s refresh", jobName),
				zap.String("cursor", savedCursor),
				zap.Uint("integrationID", integration.ID),
			)

			// SMART SKIP for Aljex
			if integration.Name == models.Aljex && strings.Contains(savedCursor, ",") {
				retryCtx.SkipCount++
				if retryCtx.SkipCount < 3 {
					parts := strings.Split(savedCursor, ",")
					newCursor := strings.Join(parts[1:], ",")
					log.Warn(
						ctx,
						"Smart Skip: skipping failed prefix and continuing with remaining queue",
						zap.String("skipped", parts[0]),
						zap.String("next", parts[1]),
						zap.Int("skipCount", retryCtx.SkipCount),
					)

					// Update cursor in Redis but don't clear the whole job
					if err := redisFuncSetIntegrationStateStatus(
						ctx,
						integration.ID,
						jobType,
						redis.JobStatusInProgress,
						savedUpdatedAt,
						newCursor,
					); err != nil {
						log.Error(ctx, "failed to update cursor for smart skip", zap.Error(err))
					}

					// Update retry tracking for the new cursor
					retryCtx.LastCursor = newCursor
					retryCtx.RetryCount = 0
					if err := redisFuncSetKey(ctx, retryKey, retryCtx, 24*time.Hour); err != nil {
						log.WarnNoSentry(ctx, "failed to save retry context after skip", zap.Error(err))
					}
					return nil // Finish this run, next run will pick up from newCursor
				}

				// If SkipCount >= 3, we fall through to the global fallback below
				log.Error(
					ctx,
					"Aljex Smart Skip limit reached (3 skips), disabling poller",
					zap.Uint("integrationID", integration.ID),
				)
			}

			// Disable the poller for this category to prevent further spamming
			var disableColumn integrationDB.Column
			if jobType == redis.CustomerRefreshJob {
				disableColumn = integrationDB.TMSPollerCustomersDisabled
			} else {
				disableColumn = integrationDB.TMSPollerLocationsDisabled
			}

			if err := dbFuncSetColumn(ctx, integration.ID, disableColumn, true); err != nil {
				log.Error(ctx, "failed to disable TMS poller", zap.Error(err))
				// Don't delete retry key or clear state if we failed to disable the poller
				// This ensures the circuit breaker will trip again on the next attempt
				return fmt.Errorf("circuit breaker tripped but failed to disable poller: %w", err)
			}

			// Only clear state and delete retry key after successfully disabling the poller
			if err := redisFuncClearIntegrationState(ctx, integration.ID, jobType); err != nil {
				log.WarnNoSentry(ctx, "failed to clear integration state", zap.Error(err))
			}
			if err := redisFuncDeleteKey(ctx, retryKey); err != nil {
				log.WarnNoSentry(ctx, "failed to delete retry key", zap.Error(err))
			}

			return fmt.Errorf("circuit breaker tripped: too many failed attempts on cursor %s", savedCursor)
		}
	} else {
		// Fresh start - reset retry tracking
		retryCtx.LastCursor = ""
		retryCtx.RetryCount = 0
		retryCtx.SkipCount = 0
	}
	// Persist retry context
	if err := redisFuncSetKey(ctx, retryKey, retryCtx, 24*time.Hour); err != nil {
		log.WarnNoSentry(ctx, "failed to save retry context", zap.Error(err))
	}

	// If we have a saved job state, use it (job continuation)
	if savedUpdatedAt != "" || savedCursor != "" {
		log.Info(
			ctx,
			"continuing previous refresh job",
			zap.String("savedStatus", savedStatus),
			zap.String("savedUpdatedAt", savedUpdatedAt),
			zap.String("savedCursor", savedCursor),
			zap.Uint("integrationID", integration.ID),
		)

		if savedUpdatedAt != "" {
			opts = append(opts, models.WithUpdatedAtFilter(savedUpdatedAt))
		}
		if savedCursor != "" {
			opts = append(opts, models.WithCursor(savedCursor))
		}
	} else if approach == models.TMSSyncApproachPartial {
		// New job - use timestamp filtering for partial sync TMSes
		opts = append(opts, models.WithUseLastUpdatedAt(true))
	}

	// Mark job as in progress (at start or continuation)
	// TMS implementation will update this during execution
	if savedCursor == "" {
		// Only set in_progress at the very start (no cursor yet)
		if setErr := redisFuncSetIntegrationStateStatus(
			ctx,
			integration.ID,
			jobType,
			redis.JobStatusInProgress,
			"", // updatedAt will be set by TMS implementation
			"", // cursor will be set by TMS implementation
		); setErr != nil {
			log.WarnNoSentry(ctx, "failed to set initial in_progress state", zap.Error(setErr))
		}
	}

	// Execute the refresh - the TMS implementation will handle pagination and timeout
	if err := runFn(ctx, client, opts...); err != nil {
		// Save state on ANY error (timeout or transient) so we can resume
		saveCtx, cancel := context.WithTimeout(
			context.WithoutCancel(ctx),
			10*time.Second,
		)
		defer cancel()

		if setErr := redisFuncSetIntegrationStateStatus(
			saveCtx,
			integration.ID,
			jobType,
			redis.JobStatusInProgress,
			lastUpdatedAt,
			lastCursor,
		); setErr != nil {
			log.WarnNoSentry(ctx, "failed to set integration state on error", zap.Error(setErr))
		}

		if errors.Is(err, context.DeadlineExceeded) {
			return fmt.Errorf("failed to refresh %s: %w due to timeout", jobName, err)
		}
		return fmt.Errorf("failed to refresh %s: %w", jobName, err)
	}

	// SUCCESS - Clear retry context
	if err := redisFuncDeleteKey(ctx, retryKey); err != nil {
		log.WarnNoSentry(ctx, "failed to delete retry key", zap.Error(err))
	}

	// Only partial sync TMSes should update lastRun on completion
	if approach == models.TMSSyncApproachPartial {
		if err := redisFuncSetTMSRefreshLastRun(ctx, integration.ID, jobType); err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to update refresh last run time",
				zap.Error(err),
			)
		}
	}

	// Clear the job state from Redis (successful completion)
	if err := redisFuncClearIntegrationState(ctx, integration.ID, jobType); err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to clear refresh state",
			zap.Error(err))
	}

	// Update integration timestamp only on full completion
	if err := dbFuncSetColumn(
		ctx,
		integration.ID,
		updateColumn,
		time.Now(),
	); err != nil {
		log.Warn(
			ctx,
			"failed to update integration timestamp",
			zap.Error(err))
	}

	log.Info(
		ctx,
		"TMS refresh completed successfully",
	)

	return nil
}

// getFullSyncTimeWindow returns the start and end hours for the full sync time window.
// Values can be overridden via FULL_SYNC_TIME_WINDOW_EST_START and FULL_SYNC_TIME_WINDOW_EST_END
// environment variables (loaded via env.Vars).
// If env.Vars is not initialized (AppEnv is empty), uses default values.
func getFullSyncTimeWindow(ctx context.Context) (start, end int) {
	start = defaultFullSyncTimeWindowESTStart
	end = defaultFullSyncTimeWindowESTEnd

	envStart := env.Vars.FullSyncTimeWindowESTStart
	envEnd := env.Vars.FullSyncTimeWindowESTEnd

	if envStart != nil {
		if *envStart < 0 || *envStart > 23 {
			log.WarnNoSentry(
				ctx,
				"FULL_SYNC_TIME_WINDOW_EST_START out of range (0-23), using default",
				zap.Int("providedValue", *envStart),
				zap.Int("default", defaultFullSyncTimeWindowESTStart),
			)
		} else {
			start = *envStart
		}
	}

	if envEnd != nil {
		if *envEnd < 0 || *envEnd > 23 {
			log.WarnNoSentry(
				ctx,
				"FULL_SYNC_TIME_WINDOW_EST_END out of range (0-23), using default",
				zap.Int("providedValue", *envEnd),
				zap.Int("default", defaultFullSyncTimeWindowESTEnd),
			)
		} else {
			end = *envEnd
		}
	}

	return start, end
}
