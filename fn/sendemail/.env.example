# Example env file for to run sendemail lambda in locally/in dev
# .env.example is NOT gitignored DO NOT commit secrets here
APP_ENV=dev

DB_HOST=localhost
DB_NAME=drumkit_dev
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password

REDIS_URL="redis://localhost:6379"

# OAuth Client IDs (Required)
# Associated with the Drumkit Google/Microsoft apps
GOOGLE_CLIENT_ID="968651685013-i120oufqf06lonr2lj3ahh92il7j67qo.apps.googleusercontent.com"
MICROSOFT_CLIENT_ID="7ef2023f-908c-4cd7-99ba-866a75fa15d0"

# OAuth Client Secrets
GOOGLE_CLIENT_SECRET="<SEE 1PASSWORD>"
MICROSOFT_CLIENT_SECRET="<SEE 1PASSWORD>"
