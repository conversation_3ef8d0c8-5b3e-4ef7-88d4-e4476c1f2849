package load

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

const preNotificationBuffer = -30

type (
	CarrierSOPPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	MilestoneDetails struct {
		// CheckIn represents whether an email should be sent for this milestone (based on checkbox input on FE)
		CheckIn       bool      `json:"checkIn"`
		SendTime      time.Time `json:"sendTime"`
		Duration      int       `json:"duration"`
		Frequency     int       `json:"frequency"` // Specific to "In Transit" emails
		TimeUnit      string    `json:"timeUnit"`
		Recipients    []string  `json:"recipients" validate:"required_if=CheckIn true"`
		CC            []string  `json:"cc"`
		BCC           []string  `json:"bcc"`
		Subject       string    `json:"subject"` // Validated manually based on ReplyInThread
		EmailDraft    string    `json:"emailDraft" validate:"required_if=CheckIn true"`
		ReplyInThread bool      `json:"replyInThread"`
		// Threading fields for reply mode
		ThreadID         string   `json:"threadID"`
		ThreadItemID     string   `json:"threadItemID"`
		ThreadReferences string   `json:"threadReferences"`
		InReplyTo        []string `json:"inReplyTo"`
	}

	CarrierSOPBody struct {
		FreightTrackingID string           `json:"freightTrackingID"`
		Dispatch          MilestoneDetails `json:"dispatch"`
		Pickup            MilestoneDetails `json:"pickup"`
		AfterPickup       MilestoneDetails `json:"afterPickup"`
		InTransit         MilestoneDetails `json:"inTransit"`
		Dropoff           MilestoneDetails `json:"dropoff"`
		AfterDropoff      MilestoneDetails `json:"afterDropoff"`
		PODCollection     MilestoneDetails `json:"podCollection"`
	}

	SubmitCarrierSOPsResponse struct {
		Message            string `json:"message"`
		NumEmailsScheduled int    `json:"numEmailsScheduled"`
	}
)

func SubmitCarrierSOPs(c *fiber.Ctx) error {
	var path CarrierSOPPath
	var body CarrierSOPBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	// Validate ReplyInThread and subject requirements
	if err := validateCarrierSOPEmails(ctx, &body, &user); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx = log.With(ctx, zap.String("freightTrackingID", body.FreightTrackingID))

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID),
			)
		}

		log.Error(ctx, "submit carrier sops loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var generatedEmails []*models.GeneratedEmail
	if user.EmailSignature == "" {
		log.Warn(ctx, "user without signature trying to generate carrier emails")
	}

	var pickupApptTime, dropoffApptTime time.Time

	if body.Pickup.SendTime.Equal(load.Pickup.ApptStartTime.Time) {
		pickupApptTime = load.Pickup.ApptStartTime.Time
	} else {
		pickupApptTime = body.Pickup.SendTime
	}

	if body.Dropoff.SendTime.Equal(load.Consignee.ApptStartTime.Time) {
		dropoffApptTime = load.Consignee.ApptStartTime.Time
	} else {
		dropoffApptTime = body.Dropoff.SendTime
	}

	// If this is an Aljex load, convert the timezone-agnostic pickup or dropoff timestamp to the warehouse's locale.
	//
	// Example: load.Pickup.ApptStartTime is originally 2024-07-01T10:00:00Z and the location is Boston.
	//
	// Conversion means that pickupApptTime is actually 2024-07-01T6:00:00+04:00 (EDT).
	if load.TMS.Name == models.Aljex {
		originalPickupTime := pickupApptTime
		pickupApptTime, err = timezone.DenormalizeUTC(pickupApptTime, load.Pickup.Timezone)
		if err != nil {
			log.Error(
				ctx,
				"error converting aljex pickup appt time to its timezone",
				zap.Error(err),
				zap.String("timezone", load.Pickup.Timezone),
				zap.Time("originalTime", originalPickupTime),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if load.TMS.Name == models.Aljex {
		originalDropoffTime := dropoffApptTime
		dropoffApptTime, err = timezone.DenormalizeUTC(dropoffApptTime, load.Consignee.Timezone)
		if err != nil {
			log.Error(
				ctx,
				"error converting aljex dropoff appt time to its timezone",
				zap.Error(err),
				zap.String("timezone", load.Consignee.Timezone),
				zap.Time("originalTime", originalDropoffTime),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if body.Dispatch.CheckIn {
		timeUnit := getTimeUnit(body.Dispatch.TimeUnit)
		delay := getDelay(body.Dispatch.Duration, timeUnit, models.DispatchMilestone)
		scheduledTime := body.Dispatch.SendTime.Add(delay)

		genEmail := createMilestoneEmail(
			ctx,
			&body.Dispatch,
			load,
			user,
			service,
			models.DispatchMilestone,
			models.NullTime{Time: scheduledTime, Valid: true},
			models.PendingStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if body.Pickup.CheckIn {
		if !load.Pickup.ApptStartTime.Valid {
			log.Error(ctx, "no pickup appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
		timeUnit := getTimeUnit(body.Pickup.TimeUnit)
		delay := getDelay(body.Pickup.Duration, timeUnit, models.PickupMilestone)
		scheduledTime := pickupApptTime.Add(delay)

		genEmail := createMilestoneEmail(
			ctx,
			&body.Pickup,
			load,
			user,
			service,
			models.PickupMilestone,
			models.NullTime{Time: scheduledTime, Valid: true},
			models.PendingStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if body.AfterPickup.CheckIn {
		if !load.Pickup.ApptStartTime.Valid {
			log.Error(ctx, "no pickup appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
		timeUnit := getTimeUnit(body.AfterPickup.TimeUnit)
		delay := getDelay(body.AfterPickup.Duration, timeUnit, models.LoadedMilestone)
		scheduledTime := pickupApptTime.Add(delay)

		genEmail := createMilestoneEmail(
			ctx,
			&body.AfterPickup,
			load,
			user,
			service,
			models.LoadedMilestone,
			models.NullTime{Time: scheduledTime, Valid: true},
			models.PendingStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if body.InTransit.CheckIn {
		if !load.Pickup.ApptStartTime.Valid || !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no appointment times set for pickup or dropoff", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		timeUnit := getTimeUnit(body.InTransit.TimeUnit)

		var scheduleTimes []time.Time
		var currentTime time.Time

		now := time.Now()

		if pickupApptTime.After(now) {
			currentTime = pickupApptTime.Add(time.Duration(body.AfterPickup.Duration) * timeUnit)
		} else {
			currentTime = now
		}

		endTime := dropoffApptTime

		for currentTime.Before(endTime) {
			scheduleTimes = append(scheduleTimes, currentTime)
			currentTime = currentTime.Add(time.Duration(body.InTransit.Frequency) * timeUnit)
		}

		for _, scheduleTime := range scheduleTimes {
			genEmail := createMilestoneEmail(
				ctx,
				&body.InTransit,
				load,
				user,
				service,
				models.InTransitMilestone,
				models.NullTime{Time: scheduleTime, Valid: true},
				models.PendingStatus,
			)
			generatedEmails = append(generatedEmails, genEmail)
		}
	}

	if body.Dropoff.CheckIn {
		if !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no dropoff appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
		timeUnit := getTimeUnit(body.Dropoff.TimeUnit)
		delay := getDelay(body.Dropoff.Duration, timeUnit, models.DropoffMilestone)
		scheduledTime := dropoffApptTime.Add(delay)

		genEmail := createMilestoneEmail(
			ctx,
			&body.Dropoff,
			load,
			user,
			service,
			models.DropoffMilestone,
			models.NullTime{Time: scheduledTime, Valid: true},
			models.PendingStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if body.AfterDropoff.CheckIn {
		if !load.Consignee.ApptStartTime.Valid {
			log.Error(ctx, "no dropoff appointment time set", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
		timeUnit := getTimeUnit(body.AfterDropoff.TimeUnit)
		delay := getDelay(body.AfterDropoff.Duration, timeUnit, models.UnloadedMilestone)
		scheduledTime := dropoffApptTime.Add(delay)

		genEmail := createMilestoneEmail(
			ctx,
			&body.AfterDropoff,
			load,
			user,
			service,
			models.UnloadedMilestone,
			models.NullTime{Time: scheduledTime, Valid: true},
			models.PendingStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if body.PODCollection.CheckIn {
		emailStatus := models.PendingStatus
		scheduledTime := models.NullTime{Time: time.Now().Add(time.Second * 5), Valid: true}
		if strings.ToLower(load.Status) != "delivered" {
			emailStatus = models.WaitingTriggerStatus
			scheduledTime = models.NullTime{Time: time.Time{}, Valid: false}
		}

		genEmail := createMilestoneEmail(
			ctx,
			&body.PODCollection,
			load,
			user,
			service,
			models.PODCollectionMilestone,
			scheduledTime,
			emailStatus,
		)
		generatedEmails = append(generatedEmails, genEmail)
	}

	if err = genEmailDB.BatchCreateGeneratedEmails(ctx, generatedEmails); err != nil {
		log.Error(ctx, "error storing generated emails", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("There was an issue saving the emails that will be sent. Please try again.")
	}

	// NOTE: If in dev, lambda will hang until email's send time
	errs := apiutil.SendEmail(ctx, user, generatedEmails)
	if len(errs) > 0 {
		for _, err := range errs {
			log.Error(ctx, "error sending email", zap.Error(err))
		}
	}

	log.Info(
		ctx,
		"successfully completed carrier SOPs submission",
		zap.Int("totalEmailsScheduled", len(generatedEmails)),
	)

	return c.Status(http.StatusCreated).JSON(SubmitCarrierSOPsResponse{
		Message:            "Carrier SOPs submitted successfully",
		NumEmailsScheduled: len(generatedEmails),
	})
}

func getTimeUnit(unit string) time.Duration {
	switch unit {
	case "Minutes":
		return time.Minute
	case "Hours":
		return time.Hour
	case "Days":
		return 24 * time.Hour
	default:
		return time.Hour
	}
}

func getDelay(duration int, timeUnit time.Duration, milestone models.MilestoneStatus) time.Duration {
	if duration == 0 && (milestone == models.PickupMilestone || milestone == models.DropoffMilestone) {
		return preNotificationBuffer * time.Minute
	}

	return time.Duration(duration) * timeUnit
}

func createMilestoneEmail(
	ctx context.Context,
	details *MilestoneDetails,
	load models.Load,
	user models.User,
	service models.Service,
	milestone models.MilestoneStatus,
	scheduleSend models.NullTime,
	status models.SendStatus,
) *models.GeneratedEmail {
	genEmail := &models.GeneratedEmail{
		FreightTrackingID: load.FreightTrackingID,
		User:              user,
		Service:           service,
		Loads:             []models.Load{load},
		Recipients:        pq.StringArray(details.Recipients),
		CC:                pq.StringArray(details.CC),
		BCC:               pq.StringArray(details.BCC),
		AppliedBody:       details.EmailDraft,
		Milestone:         milestone,
		Status:            status,
		TriggeredByUserID: user.ID,
		ScheduleSend:      scheduleSend,
	}

	// Set subject: empty string for replies (they inherit thread subject), required for new emails
	if !details.ReplyInThread {
		genEmail.Subject = details.Subject
	} else {
		genEmail.Subject = "" // Replies don't need a subject - email client will use thread subject
	}

	setThreadingFields(
		ctx,
		genEmail,
		details.ReplyInThread,
		details.ThreadID,
		details.ThreadItemID,
		details.ThreadReferences,
		details.InReplyTo,
	)

	return genEmail
}

func validateCarrierSOPEmails(ctx context.Context, body *CarrierSOPBody, user *models.User) error {
	checkMilestone := func(m MilestoneDetails) error {
		if !m.CheckIn {
			return nil
		}

		// Validate that dev users can't send to external emails
		if err := emails.ValidateDevUserRecipients(
			user.EmailAddress,
			m.Recipients,
			m.CC,
			m.BCC,
		); err != nil {
			log.Warn(ctx, "dev user attempted to send email to external address", zap.Error(err))
			return err
		}

		if !m.ReplyInThread && m.Subject == "" {
			return errors.New("subject is required when replyInThread is false")
		}

		return nil
	}

	if err := checkMilestone(body.Dispatch); err != nil {
		return err
	}
	if err := checkMilestone(body.Pickup); err != nil {
		return err
	}
	if err := checkMilestone(body.AfterPickup); err != nil {
		return err
	}
	if err := checkMilestone(body.InTransit); err != nil {
		return err
	}
	if err := checkMilestone(body.Dropoff); err != nil {
		return err
	}
	if err := checkMilestone(body.AfterDropoff); err != nil {
		return err
	}
	return checkMilestone(body.PODCollection)
}

func setThreadingFields(
	ctx context.Context,
	genEmail *models.GeneratedEmail,
	replyInThread bool,
	threadID,
	threadItemID,
	threadReferences string,
	inReplyTo []string,
) {
	if !replyInThread {
		return
	}

	// Validate that we have at least some threading information
	hasOutlookOrFrontThreading := !helpers.IsBlank(threadID) || !helpers.IsBlank(threadItemID)
	hasGmailThreading := !helpers.IsBlank(threadReferences) || len(inReplyTo) > 0

	if !hasOutlookOrFrontThreading && !hasGmailThreading {
		log.Warn(
			ctx,
			"replyInThread is true but no threading fields provided - email will be sent as new message",
			zap.Bool("replyInThread", replyInThread),
			zap.String("threadID", threadID),
			zap.String("threadItemID", threadItemID),
			zap.String("threadReferences", threadReferences),
			zap.Strings("inReplyTo", inReplyTo),
		)
		// Continue anyway - the email will be sent but as a new message, not a reply
		return
	}

	// Outlook: Use ThreadItemID (message ID) if provided, otherwise ThreadID (conversation ID)
	// Gmail: ThreadID will be automatically assigned by Gmail API
	if !helpers.IsBlank(threadItemID) {
		genEmail.ThreadID = threadItemID
		log.Info(ctx, "using ThreadItemID for reply", zap.String("threadItemID", threadItemID))
	} else if !helpers.IsBlank(threadID) {
		genEmail.ThreadID = threadID
		log.Info(ctx, "using ThreadID for reply", zap.String("threadID", threadID))
	}

	// Gmail RFC threading headers (ignored by Outlook)
	genEmail.ThreadReferences = threadReferences
	if len(inReplyTo) > 0 {
		genEmail.InReplyTo = pq.StringArray(inReplyTo)
	}

	log.Info(
		ctx,
		"set threading fields for reply email",
		zap.String("threadID", genEmail.ThreadID),
		zap.String("threadReferences", genEmail.ThreadReferences),
		zap.Strings("inReplyTo", genEmail.InReplyTo),
	)
}
