package load

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/stark"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

// GetTruckNumbersQuery defines query parameters for fetching truck numbers
type GetTruckNumbersQuery struct {
	TMSID                uint   `query:"tmsID" validate:"required"`
	CarrierApplicationID string `query:"carrierApplicationID" validate:"required"`
}

// GetTruckNumbers returns a list of available truck numbers from the TMS
func GetTruckNumbers(c *fiber.Ctx) error {
	var query GetTruckNumbersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to fetch truck numbers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	tmsClient, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	// Check if the TMS client supports GetTruckNumbers (specifically Stark)
	if starkClient, ok := tmsClient.(*stark.Stark); ok {
		truckNumbers, err := starkClient.GetTruckNumbers(ctx, query.CarrierApplicationID)
		if err != nil {
			log.Error(ctx, "failed to get truck numbers from Stark", zap.Error(err))
			return c.SendStatus(http.StatusServiceUnavailable)
		}
		log.Info(ctx, "fetched truck numbers for Stark", zap.Int("count", len(truckNumbers)))
		return c.Status(http.StatusOK).JSON(truckNumbers)
	}

	log.Warn(ctx, "GetTruckNumbers called for unsupported TMS", zap.String("tms", string(tmsIntegration.Name)))
	// For other TMS integrations, return an empty list (not implemented)
	return c.Status(http.StatusOK).JSON([]models.TMSTruckNumber{})
}
