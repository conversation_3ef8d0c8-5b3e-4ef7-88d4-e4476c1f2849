package load

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

// GetEquipmentTypesQuery defines query parameters for fetching equipment types
type GetEquipmentTypesQuery struct {
	TMSID uint `query:"tmsID" validate:"required"`
}

// GetEquipmentTypes returns a list of available equipment types from the TMS
func GetEquipmentTypes(c *fiber.Ctx) error {
	var query GetEquipmentTypesQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch equipment types for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	tmsClient, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	// Check if the TMS client supports GetEquipmentTypes
	switch client := tmsClient.(type) {
	case *revenova.Revenova:
		equipmentTypes, err := client.GetEquipmentTypes(ctx)
		if err != nil {
			log.Error(ctx, "failed to get equipment types from Revenova", zap.Error(err))
			return c.SendStatus(http.StatusServiceUnavailable)
		}
		log.Info(ctx, "fetched equipment types for Revenova", zap.Int("count", len(equipmentTypes)))
		return c.Status(http.StatusOK).JSON(equipmentTypes)

	case *threeg.ThreeG:
		transportTypes, err := client.GetTransportTypes(ctx)
		if err != nil {
			log.Error(ctx, "failed to get transport types from 3G TMS", zap.Error(err))
			return c.SendStatus(http.StatusServiceUnavailable)
		}
		log.Info(ctx, "fetched transport types for 3G TMS", zap.Int("count", len(transportTypes)))
		return c.Status(http.StatusOK).JSON(transportTypes)
	}

	log.Warn(ctx, "GetEquipmentTypes called for unsupported TMS", zap.String("tms", string(tmsIntegration.Name)))
	// For other TMS integrations, return an empty list (not implemented)
	return c.Status(http.StatusOK).JSON([]models.TMSEquipmentType{})
}
