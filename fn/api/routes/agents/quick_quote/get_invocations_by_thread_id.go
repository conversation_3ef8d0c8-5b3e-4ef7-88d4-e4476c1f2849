package quickquoteagent

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	agentmodels "github.com/drumkitai/drumkit/common/models/agents"
	quickquoteagentinvocationDB "github.com/drumkitai/drumkit/common/rds/agents/quick_quote/invocation"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type GetQuickQuoteAgentInvocationsByThreadIDPath struct {
	ThreadID string `params:"threadID" validate:"required"`
}

// TODO: This route is returning full QuickQuoteAgentInvocation objects, we could strip it down to only what FE needs.
type GetQuickQuoteAgentInvocationsByThreadIDResult struct {
	Invocations []agentmodels.QuickQuoteAgentInvocation `json:"invocations"`
}

// Returns quick quote agent invocation and invocation task details for a given thread ID.
// Used in FE where we display agent runtime info in the sidebar to compliment/provide sources from the actions
// taken by QQ Agent.
func GetQuickQuoteAgentInvocationsByThreadID(c *fiber.Ctx) error {
	var path GetQuickQuoteAgentInvocationsByThreadIDPath
	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("threadID", path.ThreadID))

	path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID in GetQuickQuoteAgentInvocationsByThreadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	serviceID := middleware.ServiceIDFromContext(c)

	preloadTaskInvocations := true
	invocations, err := quickquoteagentinvocationDB.GetInvocationsByThreadID(
		ctx,
		path.ThreadID,
		serviceID,
		preloadTaskInvocations,
	)
	if err != nil {
		log.Error(ctx, "error fetching quick quote agent invocations by thread ID", zap.Error(err))
		return apiutil.HandleDBError(
			c,
			err,
			false,
			"quick quote agent invocations not found for thread ID %s",
			path.ThreadID,
		)
	}

	if len(invocations) == 0 {
		log.Info(ctx, "no quick quote agent invocations found for thread ID")
		return c.SendStatus(http.StatusNotFound)
	}

	return c.Status(http.StatusOK).JSON(GetQuickQuoteAgentInvocationsByThreadIDResult{
		Invocations: invocations,
	})
}
