package emailtemplates

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	emailTemplatesHelpers "github.com/drumkitai/drumkit/common/helpers/emailtemplates"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/emailtemplates"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type GetEmailTemplatePath struct {
	ID uint `params:"id" validate:"required"`
}

type GetEmailTemplateResponse struct {
	ID                 uint                           `json:"id"`
	TemplateType       models.EmailTemplateType       `json:"templateType"`
	TemplateAccessTier models.EmailTemplateAccessTier `json:"templateAccessTier"`
	Name               string                         `json:"name"`
	Subject            string                         `json:"subject"`
	Body               string                         `json:"body"`
	CC                 []string                       `json:"cc"`
	ServiceID          *uint                          `json:"serviceId,omitempty"`
	UserGroupID        *uint                          `json:"userGroupId,omitempty"`
	UserID             *uint                          `json:"userId,omitempty"`
	CreatedAt          string                         `json:"createdAt,omitempty"`
	UpdatedAt          string                         `json:"updatedAt,omitempty"`
}

// GetEmailTemplateByID returns a single email template by its ID
// Admin users can access any template, while regular users must have access
// through user, user group, or service level permissions
func GetEmailTemplateByID(c *fiber.Ctx) error {
	var path GetEmailTemplatePath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "failed to get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(
		ctx,
		zap.Uint("userId", user.ID),
		zap.String("userEmail", claims.Email),
		zap.Uint("templateId", path.ID),
	)

	template, err := emailtemplates.GetEmailTemplateByID(ctx, path.ID)
	if err != nil {
		log.Error(ctx, "failed to get email template", zap.Uint("templateId", path.ID), zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(api.ErrorResponse{
			Error: "Template not found",
		})
	}

	// Admin users have unrestricted access
	if perms.IsAdmin(user.EmailAddress) {
		log.Info(
			ctx,
			"admin user accessed email template",
			zap.String("templateType", string(template.TemplateType)),
			zap.String("userEmail", claims.Email),
		)
		return c.Status(http.StatusOK).JSON(buildEmailTemplateResponse(template))
	}

	// Regular users need access validation
	if err := validateUserTemplateAccess(ctx, user, template); err != nil {
		log.Warn(
			ctx, "user denied access to email template",
			zap.Error(err),
		)
		return c.Status(http.StatusForbidden).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	return c.Status(http.StatusOK).JSON(buildEmailTemplateResponse(template))
}

// validateUserTemplateAccess checks if a user has access to a specific template
// returns nil if they have access, otherwise returns an error
func validateUserTemplateAccess(
	ctx context.Context,
	user models.User,
	template models.EmailTemplate,
) error {

	isUserTierTemplate := template.UserID == user.ID && template.ServiceID == user.ServiceID
	isGroupTierTemplate := template.UserID == 0 && template.UserGroupID != 0 && template.ServiceID == user.ServiceID
	isServiceTierTemplate := template.ServiceID == user.ServiceID &&
		template.UserID == 0 &&
		template.UserGroupID == 0

	// User-tier: template belongs to the user and same service
	if isUserTierTemplate {
		return nil
	}

	// Group-tier: template belongs to a group the user is in, and same service
	if isGroupTierTemplate {
		userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
		if err != nil {
			log.Error(ctx, "failed to get user groups", zap.Error(err))
			// Continue - absence of groups means no group-level access
		}

		for _, groupID := range userGroupIDs {
			if groupID == template.UserGroupID {
				return nil
			}
		}
	}

	// Service-tier: template belongs to user's service with no user/group restrictions
	if isServiceTierTemplate {
		return nil
	}

	return errors.New("access denied to this template")
}

// buildEmailTemplateResponse converts a template model to the API response format
func buildEmailTemplateResponse(template models.EmailTemplate) GetEmailTemplateResponse {
	response := GetEmailTemplateResponse{
		ID:                 template.ID,
		TemplateType:       template.TemplateType,
		TemplateAccessTier: emailTemplatesHelpers.GetAccessTier(template),
		Name:               template.Name,
		Subject:            template.Subject,
		Body:               template.Body,
		CC:                 template.CC,
		CreatedAt:          template.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:          template.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	if template.ServiceID != 0 {
		response.ServiceID = &template.ServiceID
	}
	if template.UserGroupID != 0 {
		response.UserGroupID = &template.UserGroupID
	}
	if template.UserID != 0 {
		response.UserID = &template.UserID
	}

	return response
}
