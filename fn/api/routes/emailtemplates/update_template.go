package emailtemplates

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailtemplatesDB "github.com/drumkitai/drumkit/common/rds/emailtemplates"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type UpdateEmailTemplatePath struct {
	ID uint `params:"id" validate:"required"`
}

type UpdateEmailTemplateRequest struct {
	Name     *string  `json:"name,omitempty"`
	Subject  *string  `json:"subject,omitempty"`
	Body     *string  `json:"body,omitempty"`
	CC       []string `json:"cc,omitempty"`
	TargetID *uint    `json:"targetId,omitempty"`
}

type UpdateEmailTemplateResponse struct {
	Message string `json:"message"`
}

// UpdateEmailTemplate updates an existing email template with validation and permission checks
func UpdateEmailTemplate(c *fiber.Ctx) error {
	var path UpdateEmailTemplatePath
	var req UpdateEmailTemplateRequest
	if err := api.Parse(c, &path, nil, &req); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	if err := validateUpdateRequest(&req); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "failed to get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}
	ctx = log.With(ctx, zap.Uint("userId", user.ID), zap.String("userEmail", claims.Email))

	templateID := path.ID
	existingTemplate, err := emailtemplatesDB.GetEmailTemplateByID(ctx, templateID)
	if err != nil {
		log.Error(ctx, "failed to get email template", zap.Error(err))
		return c.Status(http.StatusNotFound).JSON(api.ErrorResponse{
			Error: "Template not found",
		})
	}

	if err := verifyUpdateAccess(ctx, user, &existingTemplate); err != nil {
		log.Warn(ctx, "user denied access to update email template", zap.Uint("templateId", templateID))

		return c.Status(http.StatusForbidden).JSON(api.ErrorResponse{
			Error: "Access denied to update this template",
		})
	}

	log.InfoNoTrace(
		ctx,
		"updating email template",
		zap.Uint("templateId", templateID),
		zap.Any("requestBody", req),
	)

	// Handle targetID update separately with proper validation
	if req.TargetID != nil {
		if err := updateTemplateTarget(ctx, &existingTemplate, user, *req.TargetID); err != nil {
			return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
				Error: err.Error(),
			})
		}
	}

	applyUpdates(&existingTemplate, &req)

	if req.Subject != nil || req.Body != nil {
		if err := validateTemplateSyntax(existingTemplate.Subject, existingTemplate.Body); err != nil {
			return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
				Error: "Template syntax error: " + err.Error(),
			})
		}
	}

	// Sanitize and validate HTML
	if req.Body != nil {
		existingTemplate.Body = sanitizeHTML(existingTemplate.Body)
	}
	if err := validateTemplateHTMLSyntax(existingTemplate.Body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: "Template HTML syntax error: " + err.Error(),
		})
	}

	if err := emailtemplatesDB.UpdateEmailTemplate(ctx, &existingTemplate); err != nil {
		log.Error(ctx, "failed to update email template", zap.Uint("templateId", templateID), zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(api.ErrorResponse{
			Error: "Failed to update email template",
		})
	}

	log.InfoNoTrace(
		ctx,
		"successfully updated email template",
		zap.Uint("templateId", templateID),
		zap.Any("requestBody", req),
	)

	return c.Status(http.StatusOK).JSON(UpdateEmailTemplateResponse{
		Message: "Email template updated successfully",
	})
}

// validateUpdateRequest validates that the update request contains at least one field to update
func validateUpdateRequest(req *UpdateEmailTemplateRequest) error {
	if req.Name == nil && req.Subject == nil && req.Body == nil && req.CC == nil && req.TargetID == nil {
		return errors.New("at least one field must be provided to update email template")
	}

	return nil
}

// applyUpdates applies the requested changes to the existing template
func applyUpdates(existingTemplate *models.EmailTemplate, req *UpdateEmailTemplateRequest) {
	if req.Name != nil {
		existingTemplate.Name = *req.Name
	}
	if req.Subject != nil {
		existingTemplate.Subject = *req.Subject
	}
	if req.Body != nil {
		existingTemplate.Body = *req.Body
	}
	if req.CC != nil {
		existingTemplate.CC = req.CC
	}
}

// verifyUpdateAccess checks if the user has permission to update the template
func verifyUpdateAccess(
	ctx context.Context,
	user models.User,
	template *models.EmailTemplate,
) error {
	// Admin users have unrestricted access
	if perms.IsAdmin(user.EmailAddress) {
		log.Info(
			ctx,
			"admin user updating email template",
			zap.String("templateType", string(template.TemplateType)),
		)
		return nil
	}

	// Check if user has access to this template
	hasAccess, err := checkTemplateAccess(ctx, user, template)
	if err != nil {
		return err
	}

	if !hasAccess {
		return errors.New("insufficient permissions")
	}

	return nil
}

// checkTemplateAccess determines if a non-admin user has access to update the template
func checkTemplateAccess(
	ctx context.Context,
	user models.User,
	template *models.EmailTemplate,
) (bool, error) {

	if user.ID == 0 {
		return false, errors.New("user not found")
	}

	// Check user-tier template access
	if template.UserID == user.ID && template.ServiceID == user.ServiceID {
		return true, nil
	}

	// Check group-tier template access
	if template.UserID == 0 && template.UserGroupID != 0 && template.ServiceID == user.ServiceID {
		userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
		if err != nil {
			log.Error(ctx, "failed to get user groups", zap.Error(err))
			// Don't fail on error - user might have group access but we can't verify
			return false, nil
		}

		for _, groupID := range userGroupIDs {
			if groupID == template.UserGroupID {
				return true, nil
			}
		}
		return false, nil
	}

	// Check service-tier template access
	if isServiceTierTemplate(template) && template.ServiceID == user.ServiceID {
		return true, nil
	}

	return false, nil
}

// isServiceTierTemplate checks if a template is service-tier
func isServiceTierTemplate(template *models.EmailTemplate) bool {
	return template.UserID == 0 && template.UserGroupID == 0
}

// updateTemplateTarget updates the template's target ID (UserGroupID or ServiceID) based on template tier
func updateTemplateTarget(
	ctx context.Context,
	template *models.EmailTemplate,
	user models.User,
	targetID uint,
) error {

	isAdmin := perms.IsAdmin(user.EmailAddress)

	// Check UserID first - user-tier templates have UserID != 0
	// Note: User-tier templates also have ServiceID set, so we must check UserID before ServiceID
	switch {
	case template.UserID != 0: // User tier template
		// For non-admin users: only allow keeping the same target (no-op)
		if !isAdmin {
			if targetID == template.UserID {
				return nil
			}
			return errors.New("cannot change targetId for user tier templates")
		}

		// For admin users: allow changing to any valid user ID
		if targetID == template.UserID {
			// No change needed
			return nil
		}

		// Admin is changing the target user - fetch the new target user
		targetUser, err := userDB.GetByID(ctx, targetID)
		if err != nil {
			log.Error(ctx, "failed to get target user", zap.Error(err), zap.Uint("targetId", targetID))
			return errors.New("failed to get target user")
		}

		// Update both UserID and ServiceID to match the new target user
		template.UserID = targetUser.ID
		template.ServiceID = targetUser.ServiceID
		return nil
	case template.UserGroupID != 0: // Group tier template
		return updateGroupTierTarget(ctx, template, user, targetID)
	case template.ServiceID != 0: // Service tier template
		return updateServiceTierTarget(ctx, template, user, targetID, isAdmin)
	default:
		return errors.New("invalid template tier")
	}
}

// updateGroupTierTarget updates the UserGroupID for a group tier template
func updateGroupTierTarget(
	ctx context.Context,
	template *models.EmailTemplate,
	user models.User,
	targetID uint,
) error {

	if targetID == 0 {
		return errors.New("targetId cannot be zero for group tier templates")
	}

	// Verify user has access to the new target group
	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil {
		log.Error(ctx, "failed to get user groups", zap.Error(err))
		return errors.New("failed to verify user group access")
	}

	found := false
	for _, groupID := range userGroupIDs {
		if groupID == targetID {
			found = true
			break
		}
	}

	if !found && !perms.IsAdmin(user.EmailAddress) {
		return errors.New("access denied to specified user group")
	}

	// Fetch the target group to get its ServiceID
	targetGroup, err := userGroupsDB.GetByID(ctx, targetID)
	if err != nil {
		log.Error(ctx, "failed to get target group", zap.Error(err), zap.Uint("targetId", targetID))
		return errors.New("failed to get target group")
	}

	template.UserGroupID = targetID
	template.ServiceID = targetGroup.ServiceID
	return nil
}

// updateServiceTierTarget updates the ServiceID for a service tier template
func updateServiceTierTarget(
	ctx context.Context,
	template *models.EmailTemplate,
	user models.User,
	targetID uint,
	isAdmin bool,
) error {

	if targetID == 0 {
		return errors.New("targetId cannot be zero for service tier templates")
	}

	// If user is not an admin, verify it matches user's service
	if !isAdmin && targetID != user.ServiceID {
		log.Error(
			ctx,
			"user is not an admin and cannot update service tier template to a different service",
			zap.Uint("targetID", targetID),
			zap.Uint("userServiceID", user.ServiceID),
			zap.String("userEmail", user.EmailAddress),
		)
		return errors.New("insufficient permissions to change service tier template to different service")
	}

	template.ServiceID = targetID
	return nil
}
