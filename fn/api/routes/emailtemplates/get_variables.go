package emailtemplates

import (
	"net/http"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/models"
)

type GetVariablesRequest struct {
	TemplateType models.EmailTemplateType `query:"templateType" validate:"required"`
}

type GetVariablesResponse struct {
	Variables []string `json:"variables"`
}

// GetVariables returns the available template variables for a given template type
func GetVariables(c *fiber.Ctx) error {
	var req GetVariablesRequest
	if err := api.Parse(c, nil, &req, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	variables := models.GetTemplateVariables(req.TemplateType)

	return c.Status(http.StatusOK).JSON(GetVariablesResponse{
		Variables: variables,
	})
}
