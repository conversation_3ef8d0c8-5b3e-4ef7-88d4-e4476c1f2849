package emailtemplates

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"text/template"

	"github.com/gofiber/fiber/v2"
	"github.com/microcosm-cc/bluemonday"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailTemplatesDB "github.com/drumkitai/drumkit/common/rds/emailtemplates"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type CreateEmailTemplateRequest struct {
	TemplateType models.EmailTemplateType       `json:"templateType" validate:"required"`
	Name         string                         `json:"name" validate:"required"`
	Subject      string                         `json:"subject" validate:"required"`
	Body         string                         `json:"body" validate:"required"`
	CC           []string                       `json:"cc,omitempty"`
	AccessTier   models.EmailTemplateAccessTier `json:"accessTier" validate:"required"`
	TargetID     uint                           `json:"targetId"`
}

type CreateEmailTemplateResponse struct {
	ID uint `json:"id"`
}

// CreateEmailTemplate creates a new email template with validation and sanitization
func CreateEmailTemplate(c *fiber.Ctx) error {
	var req CreateEmailTemplateRequest
	if err := api.Parse(c, nil, nil, &req); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	ctx := c.UserContext()
	ctx = log.With(ctx, zap.Any("requestBody", req))
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "failed to get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := validateTemplateSyntax(req.Subject, req.Body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: "Template syntax error: " + err.Error(),
		})
	}

	sanitizedBody := sanitizeHTML(req.Body)
	if err := validateTemplateHTMLSyntax(sanitizedBody); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: "Template HTML syntax error: " + err.Error(),
		})
	}

	emailTemplate := models.EmailTemplate{
		Name:            req.Name,
		Subject:         req.Subject,
		Body:            sanitizedBody,
		CC:              req.CC,
		TemplateType:    req.TemplateType,
		CreatedByUserID: &user.ID,
	}

	isAdmin := perms.IsAdmin(user.EmailAddress)

	// Set the appropriate ID based on access tier
	switch req.AccessTier {
	case models.EmailTemplateUserTier:
		if err := prepareUserTierTemplate(ctx, &emailTemplate, user, req.TargetID, isAdmin); err != nil {
			return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
				Error: err.Error(),
			})
		}
	case models.EmailTemplateGroupTier:
		if err := prepareGroupTierTemplate(ctx, &emailTemplate, user, req.TargetID, isAdmin); err != nil {
			return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
				Error: err.Error(),
			})
		}
	case models.EmailTemplateServiceTier:
		if err := prepareServiceTierTemplate(ctx, &emailTemplate, user, req.TargetID, isAdmin); err != nil {
			return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
				Error: err.Error(),
			})
		}
	default:
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: "Invalid access tier",
		})
	}

	if err := emailTemplatesDB.CreateEmailTemplate(ctx, &emailTemplate); err != nil {
		log.Error(ctx, "failed to create email template", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.InfoNoTrace(
		ctx,
		"successfully created email template",
		zap.Uint("templateId", emailTemplate.ID),
		zap.String("templateType", string(emailTemplate.TemplateType)),
		zap.String("accessTier", string(req.AccessTier)),
		zap.Uint("userId", user.ID),
		zap.String("userEmail", claims.Email),
	)

	return c.Status(http.StatusCreated).JSON(CreateEmailTemplateResponse{
		ID: emailTemplate.ID,
	})
}

// prepareUserTierTemplate sets up the email template for user tier access
// Admins can create templates for other users by providing targetId
func prepareUserTierTemplate(
	ctx context.Context,
	emailTemplate *models.EmailTemplate,
	user models.User,
	targetID uint,
	isAdmin bool,
) error {

	log.Info(ctx, "attempting to create user tier template")

	// If targetID is provided and user is an admin, create template for target user
	if targetID != 0 && isAdmin {
		targetUser, err := userDB.GetByID(ctx, targetID)
		if err != nil {
			log.Error(ctx, "failed to get target user", zap.Error(err), zap.Uint("targetId", targetID))
			return errors.New("failed to get target user")
		}

		emailTemplate.UserID = targetUser.ID
		emailTemplate.ServiceID = targetUser.ServiceID
		return nil
	}

	// If targetID is provided but user is not admin, deny access
	if targetID != 0 && !isAdmin && targetID != user.ID {
		log.Error(
			ctx,
			"user is not an admin and cannot create user tier template for another user",
			zap.Uint("targetID", targetID),
			zap.Uint("userId", user.ID),
			zap.String("userEmail", user.EmailAddress),
		)
		return errors.New("insufficient permissions to create template for another user")
	}

	// Default case: create template for the requesting user
	emailTemplate.UserID = user.ID
	emailTemplate.ServiceID = user.ServiceID
	return nil
}

// prepareGroupTierTemplate sets up the email template for group tier access
// Admins can create templates for any group they specify
func prepareGroupTierTemplate(
	ctx context.Context,
	emailTemplate *models.EmailTemplate,
	user models.User,
	targetID uint,
	isAdmin bool,
) error {

	log.Info(ctx, "attempting to create group tier template")

	// Validate targetID is provided
	if targetID == 0 {
		return errors.New("targetId is required for group tier templates")
	}

	// Verify user has access to this group
	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil {
		log.Error(ctx, "failed to get user groups", zap.Error(err))
		return errors.New("failed to get user groups")
	}

	found := false
	for _, groupID := range userGroupIDs {
		if groupID == targetID {
			found = true
			break
		}
	}

	// Allow if user is in the group OR if user is an admin
	if !found && !isAdmin {
		log.Error(
			ctx,
			"user is not in the target group and is not an admin",
			zap.Uint("targetID", targetID),
			zap.Uint("userId", user.ID),
			zap.String("userEmail", user.EmailAddress),
		)
		return errors.New("access denied to specified user group")
	}

	// Fetch the target group to get its ServiceID
	targetGroup, err := userGroupsDB.GetByID(ctx, targetID)
	if err != nil {
		log.Error(ctx, "failed to get target group", zap.Error(err), zap.Uint("targetId", targetID))
		return errors.New("failed to get target group")
	}

	emailTemplate.UserGroupID = targetID
	emailTemplate.ServiceID = targetGroup.ServiceID
	return nil
}

// prepareServiceTierTemplate sets up the email template for service tier access
func prepareServiceTierTemplate(
	ctx context.Context,
	emailTemplate *models.EmailTemplate,
	user models.User,
	targetID uint,
	isAdmin bool,
) error {

	log.Info(ctx, "attempting to create service tier template")

	// If TargetID is provided and user is not an admin, verify it matches user's service
	if targetID != 0 && !isAdmin && targetID != user.ServiceID {
		log.Error(
			ctx,
			"user is not an admin and cannot create service tier template for a different service",
			zap.Uint("targetID", targetID),
			zap.Uint("userServiceID", user.ServiceID),
			zap.String("userEmail", user.EmailAddress),
		)
		return errors.New("insufficient permissions to create company wide template")
	}

	// Admin can specify any target service ID, non-admin uses their own service ID
	if targetID != 0 && isAdmin {
		emailTemplate.ServiceID = targetID
	} else {
		emailTemplate.ServiceID = user.ServiceID
	}

	return nil
}

// validateTemplateSyntax validates that the subject and body contain valid Go template syntax
func validateTemplateSyntax(subject, body string) error {
	if _, err := template.New("subject").Parse(subject); err != nil {
		return err
	}

	if _, err := template.New("body").Parse(body); err != nil {
		return err
	}

	return nil
}

// validateTemplateHTMLSyntax validates that the HTML content contains valid Go template syntax
func validateTemplateHTMLSyntax(html string) error {
	if _, err := template.New("html").Parse(html); err != nil {
		return err
	}

	return nil
}

// sanitizeHTML sanitizes HTML content to prevent XSS attacks while preserving formatting
func sanitizeHTML(html string) string {
	// Create a bluemonday policy that allows basic formatting but removes dangerous elements
	p := bluemonday.UGCPolicy()

	// Allow common formatting tags
	p.AllowElements("p", "br", "strong", "b", "em", "i", "u", "h1", "h2", "h3", "h4", "h5", "h6")
	p.AllowElements("ul", "ol", "li")
	p.AllowElements("table", "thead", "tbody", "tr", "th", "td")
	p.AllowAttrs("href").OnElements("a")
	p.AllowAttrs("src", "alt").OnElements("img")

	// Allow style attributes for basic styling
	p.AllowAttrs("style").OnElements("p", "span", "div", "table", "tr", "td", "th")

	return strings.TrimSpace(p.Sanitize(html))
}
