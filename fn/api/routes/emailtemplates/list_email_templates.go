package emailtemplates

import (
	"context"
	"net/http"
	"sort"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	emailtemplatesHelpers "github.com/drumkitai/drumkit/common/helpers/emailtemplates"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/emailtemplates"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type EmailTemplateResponse struct {
	ID                 uint                           `json:"id"`
	TemplateType       models.EmailTemplateType       `json:"templateType"`
	TemplateAccessTier models.EmailTemplateAccessTier `json:"templateAccessTier"`
	Name               string                         `json:"name"`
	Subject            string                         `json:"subject"`
	Body               string                         `json:"body"`
	CC                 []string                       `json:"cc"`
	ServiceID          uint                           `json:"serviceId,omitempty"`
	UserGroupID        uint                           `json:"userGroupId,omitempty"`
	UserID             uint                           `json:"userId,omitempty"`
	CreatedAt          string                         `json:"createdAt,omitempty"`
	UpdatedAt          string                         `json:"updatedAt,omitempty"`
}

type ListEmailTemplatesRequest struct {
	TemplateType models.EmailTemplateType `query:"templateType"`
}

type ListEmailTemplatesResponse struct {
	Templates []EmailTemplateResponse `json:"templates"`
}

// ListEmailTemplates returns all email templates accessible to the user
// Admin users can access all templates across services and groups
// Regular users only see templates accessible through user, user group, or service level permissions
// Includes generic templates and results are sorted by template type and access tier
// Query parameters:
//   - templateType: (optional) filter templates by type
func ListEmailTemplates(c *fiber.Ctx) error {
	var req ListEmailTemplatesRequest
	if err := api.Parse(c, nil, &req, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(api.ErrorResponse{
			Error: err.Error(),
		})
	}

	ctx := c.UserContext()
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "failed to get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Admin users have unrestricted access
	if perms.IsAdmin(user.EmailAddress) {
		templates, err := fetchAllTemplatesForAdmin(ctx, req.TemplateType)
		if err != nil {
			log.Error(ctx, "failed to get email templates for admin", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON(api.ErrorResponse{
				Error: "Failed to get email templates",
			})
		}
		return c.Status(http.StatusOK).JSON(ListEmailTemplatesResponse{
			Templates: templates,
		})
	}

	// Regular users need access filtering
	templates, err := fetchTemplatesForUser(ctx, user, req.TemplateType)
	if err != nil {
		log.Error(ctx, "failed to get email templates for user", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(api.ErrorResponse{
			Error: "Failed to get email templates",
		})
	}

	return c.Status(http.StatusOK).JSON(ListEmailTemplatesResponse{
		Templates: templates,
	})
}

// fetchAllTemplatesForAdmin retrieves all templates across services and groups for admin users
func fetchAllTemplatesForAdmin(
	ctx context.Context,
	templateType models.EmailTemplateType,
) ([]EmailTemplateResponse, error) {

	var dbTemplates []models.EmailTemplate
	var err error

	switch templateType {
	case "":
		dbTemplates, err = emailtemplates.GetAllEmailTemplates(ctx)
	default:
		dbTemplates, err = emailtemplates.GetAllEmailTemplatesByType(ctx, templateType)
	}

	if err != nil {
		return nil, err
	}

	templates := convertToResponseFormat(dbTemplates)
	templates = appendGenericTemplates(templates, templateType)
	sortTemplates(templates)

	return templates, nil
}

// fetchTemplatesForUser retrieves templates accessible to a regular user
func fetchTemplatesForUser(
	ctx context.Context,
	user models.User,
	templateType models.EmailTemplateType,
) ([]EmailTemplateResponse, error) {

	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil {
		log.Error(ctx, "failed to get user groups", zap.Error(err))
		// Continue without user groups, can still get non-group templates - not a fatal error
	}

	var dbTemplates []models.EmailTemplate

	if templateType != "" {
		dbTemplates, err = emailtemplates.GetAllAccessibleEmailTemplatesByTypeForUser(
			ctx,
			user.ID,
			userGroupIDs,
			user.ServiceID,
			templateType,
		)
	} else {
		dbTemplates, err = emailtemplates.GetAllAccessibleEmailTemplatesForUser(
			ctx,
			user.ID,
			userGroupIDs,
			user.ServiceID,
		)
	}

	if err != nil {
		return nil, err
	}

	templates := convertToResponseFormat(dbTemplates)
	templates = appendGenericTemplates(templates, templateType)
	sortTemplates(templates)

	return templates, nil
}

// convertToResponseFormat converts database models to API response format
func convertToResponseFormat(dbTemplates []models.EmailTemplate) []EmailTemplateResponse {
	templates := make([]EmailTemplateResponse, 0, len(dbTemplates))
	for _, tmpl := range dbTemplates {
		templates = append(templates, EmailTemplateResponse{
			ID:                 tmpl.ID,
			TemplateType:       tmpl.TemplateType,
			TemplateAccessTier: emailtemplatesHelpers.GetAccessTier(tmpl),
			Name:               tmpl.Name,
			Subject:            tmpl.Subject,
			Body:               tmpl.Body,
			CC:                 tmpl.CC,
			ServiceID:          tmpl.ServiceID,
			UserGroupID:        tmpl.UserGroupID,
			UserID:             tmpl.UserID,
			CreatedAt:          tmpl.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:          tmpl.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		})
	}
	return templates
}

// appendGenericTemplates adds generic templates to the response
func appendGenericTemplates(
	templates []EmailTemplateResponse,
	templateType models.EmailTemplateType,
) []EmailTemplateResponse {
	if templateType == "" {
		// Add all generic templates with deterministic ordering
		genericTemplateTypes := make([]models.EmailTemplateType, 0, len(models.GenericEmailTemplates))
		for tmplType := range models.GenericEmailTemplates {
			genericTemplateTypes = append(genericTemplateTypes, tmplType)
		}
		sort.Slice(genericTemplateTypes, func(i, j int) bool {
			return string(genericTemplateTypes[i]) < string(genericTemplateTypes[j])
		})

		for _, tmplType := range genericTemplateTypes {
			genericTmpl := models.GenericEmailTemplates[tmplType]
			templates = append(templates, EmailTemplateResponse{
				ID:                 0,
				TemplateType:       tmplType,
				TemplateAccessTier: models.EmailTemplateGenericTier,
				Name:               "Generic " + string(tmplType),
				Subject:            genericTmpl.Subject,
				Body:               genericTmpl.Body,
				CC:                 genericTmpl.CC,
			})
		}
	} else if genericTmpl, exists := models.GenericEmailTemplates[templateType]; exists {
		// Add the specific generic template if it exists
		templates = append(templates, EmailTemplateResponse{
			ID:                 0,
			TemplateType:       templateType,
			TemplateAccessTier: models.EmailTemplateGenericTier,
			Name:               "Generic " + string(templateType) + " Template",
			Subject:            genericTmpl.Subject,
			Body:               genericTmpl.Body,
			CC:                 genericTmpl.CC,
		})
	}
	return templates
}

// sortTemplates sorts templates by template type and access tier
func sortTemplates(templates []EmailTemplateResponse) {
	sort.Slice(templates, func(i, j int) bool {
		if templates[i].TemplateType != templates[j].TemplateType {
			return string(templates[i].TemplateType) < string(templates[j].TemplateType)
		}
		return getAccessTierOrder(templates[i].TemplateAccessTier) < getAccessTierOrder(templates[j].TemplateAccessTier)
	})
}

// getAccessTierOrder returns a numeric order for access tiers
// Lower numbers indicate higher priority (more specific tiers)
func getAccessTierOrder(tier models.EmailTemplateAccessTier) int {
	switch tier {
	case models.EmailTemplateUserTier:
		return 0
	case models.EmailTemplateGroupTier:
		return 1
	case models.EmailTemplateServiceTier:
		return 2
	case models.EmailTemplateGenericTier:
		return 3
	default:
		return 4
	}
}
