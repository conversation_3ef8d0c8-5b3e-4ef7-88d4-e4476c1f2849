package integrations

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

// TaiLoadStatusUpdateWebhookHandler handles ShipmentStatusUpdateURL webhook events from Tai TMS.
// This webhook is triggered when a load status is updated in the TMS. It filters for "Delivered" status
// and sends any generated emails that are waiting for trigger for the associated load.
func TaiLoadStatusUpdateWebhookHandler(c *fiber.Ctx) error {
	var body tai.WebhookShipmentStatusUpdate
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	userEmail := middleware.ClaimsFromContext(c).Email

	ctx := log.With(
		c.UserContext(),
		zap.String("userEmail", userEmail),
		zap.Uint("userServiceID", userServiceID),
	)

	// Filter out any load status updates that are not "Delivered"
	if strings.ToLower(body.Status) != "delivered" {
		log.InfoNoTrace(
			ctx,
			"load status update is not delivered, skipping",
			zap.String("status", body.Status),
		)
		return c.SendStatus(http.StatusOK)
	}

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	load, err := loadDB.GetLoadByFreightIDAndService(ctx, userServiceID, body.ShipmentID.String())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.InfoNoTrace(
				ctx,
				"load not found in DB, skipping",
				zap.String("shipmentID", body.ShipmentID.String()),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error getting load in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Check if there are generated emails waiting for trigger
	generatedEmails, err := genEmailDB.GetEmailsByStatusAndLoadID(
		ctx,
		userServiceID,
		load.ID,
		models.WaitingTriggerStatus,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error getting generated emails", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(generatedEmails) == 0 {
		log.InfoNoTrace(
			ctx,
			"no generated emails found for load, skipping",
			zap.Uint("loadID", load.ID),
		)
		return c.SendStatus(http.StatusOK)
	}

	// Send emails one by one for better error/update handling, though we shouldn't ever have more than one here.
	for _, email := range generatedEmails {
		email.Status = models.PendingStatus
		email.ScheduleSend = models.NullTime{
			Time:  time.Now().Add(time.Second * 5),
			Valid: true,
		}

		// NOTE: If in dev, lambda will hang until email's send time
		errs := apiutil.SendEmail(ctx, user, []*models.GeneratedEmail{email})
		if len(errs) > 0 {
			for _, err := range errs {
				log.Error(ctx, "error sending email", zap.Error(err))
			}

			continue // Fail-open: continue updating other emails if one fails
		}

		email.Status = models.SentStatus
		email.SentAt = models.NullTime{
			Time:  time.Now(),
			Valid: true,
		}

		err = genEmailDB.Update(ctx, email)
		if err != nil {
			log.Error(ctx, "error updating generated email", zap.Error(err))
			continue
		}
	}

	log.InfoNoTrace(ctx, "successfully completed load status update webhook handler")

	return c.SendStatus(http.StatusOK)
}
