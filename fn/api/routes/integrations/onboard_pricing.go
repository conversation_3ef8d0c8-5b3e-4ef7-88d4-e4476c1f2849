package integrations

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/integrations/pricing/globaltranz"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/integrations/pricing/tai"
	"github.com/drumkitai/drumkit/common/integrations/pricing/truckstop"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func OnboardPricing(c *fiber.Ctx) error {
	var body models.OnboardPricingRequest
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed.", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "Fetching user service failed", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newIntegration := models.Integration{
		Name:              models.IntegrationName(body.Name),
		Type:              models.Pricing,
		APIKey:            body.APIKey,
		EncryptedPassword: []byte(encryptedPassword),
		AppID:             body.AppID,
		ServiceID:         userServiceID,
		Username:          body.Username,
		Tenant:            body.Tenant,
		Note:              body.Note,
	}

	var onBoardResp models.PricingOnBoardResp

	switch newIntegration.Name {
	case models.DAT:
		_, onBoardResp, err = dat.NewOnboard(ctx, newIntegration)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.DAT)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

	case models.GlobalTranz:
		_, onBoardResp, err = globaltranz.New(ctx, newIntegration)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.GlobalTranz)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

	case models.Greenscreens:
		_, onBoardResp, err = greenscreens.New(ctx, newIntegration)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.Greenscreens)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

	case models.TaiPricing:
		_, err := tai.New(ctx, newIntegration, newIntegration.Username)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.TaiPricing)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

		onBoardResp.Username = body.Username
		onBoardResp.EncryptedPassword = []byte(encryptedPassword)

	case models.Truckstop:
		truckstopClient, err := truckstop.New(ctx, newIntegration)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.Truckstop)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
		onBoardResp, err = truckstopClient.InitialOnboard(ctx, service, body)
		if err != nil {
			log.Error(
				ctx,
				"Failed to onboard pricing integration",
				zap.String("integrationName", string(models.Truckstop)),
				zap.Error(err),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}

	default:
		log.Error(ctx, "Unknown pricing integration", zap.String("integrationName", body.Name))
		return c.SendStatus(http.StatusInternalServerError)
	}

	newIntegration.Username = onBoardResp.Username
	newIntegration.EncryptedPassword = onBoardResp.EncryptedPassword
	newIntegration.AccessToken = onBoardResp.AccessToken
	newIntegration.RefreshToken = onBoardResp.RefreshToken
	newIntegration.APIKey = onBoardResp.APIKey
	newIntegration.AccessTokenExpirationDate = onBoardResp.AccessTokenExpirationDate
	newIntegration.ServiceID = userServiceID
	newIntegration.AppID = onBoardResp.AppID
	newIntegration.Tenant = body.Tenant
	newIntegration.Note = onBoardResp.Note

	if err = integrationDB.Create(ctx, &newIntegration); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusCreated)
}
