package outbox

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	UndoCancelEmailPath struct {
		GeneratedEmailID uint `params:"generatedEmailID" validate:"required"`
	}

	UndoCancelEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

func UndoCancelEmail(c *fiber.Ctx) error {
	var path UndoCancelEmailPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(UndoCancelEmailResponse{Message: err.Error()})
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	existingEmail, err := generatedemails.GetEmail(ctx, path.GeneratedEmailID)
	if err != nil {
		log.Error(ctx, "generatedemails query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Authorization check: ensure user belongs to same service as the email
	if existingEmail.ServiceID != user.ServiceID {
		log.WarnNoSentry(
			ctx,
			"unauthorized attempt to undo cancel email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.Uint("emailServiceID", existingEmail.ServiceID),
			zap.Uint("requestUserServiceID", user.ServiceID),
		)

		return c.Status(http.StatusForbidden).JSON(
			UndoCancelEmailResponse{Message: "Not authorized to undo cancellation of this email"},
		)
	}

	// Only allow undo of canceled emails
	if existingEmail.Status != models.CanceledStatus {
		log.WarnNoSentry(
			ctx,
			"attempt to undo delete non-canceled email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.String("status", string(existingEmail.Status)),
		)

		return c.Status(http.StatusConflict).JSON(UndoCancelEmailResponse{
			Message: "Can not undo cancellation of email that is not in canceled state",
		})
	}

	err = generatedemails.UndoCancelPendingEmail(ctx, existingEmail)
	if err != nil {
		log.Error(ctx, "error undoing deletion of generated email in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	result := UndoCancelEmailResponse{
		Message: "Email successfully restored",
	}

	return c.Status(http.StatusOK).JSON(result)
}
