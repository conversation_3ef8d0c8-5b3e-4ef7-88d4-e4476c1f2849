package outbox

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	CancelEmailPath struct {
		GeneratedEmailID uint `params:"generatedEmailID" validate:"required"`
	}

	CancelEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

// CancelEmail cancels a carrier email (sets status to canceled) does not actually delete the email from db
func CancelEmail(c *fiber.Ctx) error {
	var path CancelEmailPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(CancelEmailResponse{
			Message: "There was an error canceling this email",
		})
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	existingEmail, err := generatedemails.GetEmail(ctx, path.GeneratedEmailID)
	if err != nil {
		log.Error(ctx, "generatedemails query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Authorization check: ensure user belongs to same service as the email
	if existingEmail.ServiceID != user.ServiceID {
		log.WarnNoSentry(
			ctx,
			"unauthorized attempt to delete email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.Uint("emailServiceID", existingEmail.ServiceID),
			zap.Uint("requestUserServiceID", user.ServiceID),
		)

		return c.Status(http.StatusForbidden).JSON(CancelEmailResponse{
			Message: "You are not allowed to cancel this email",
		})
	}

	// Only allow canceling of pending emails
	if existingEmail.Status != models.PendingStatus {
		log.WarnNoSentry(
			ctx,
			"attempt to delete non-pending email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.String("status", string(existingEmail.Status)),
		)

		return c.Status(http.StatusConflict).JSON(CancelEmailResponse{
			Message: "Can not cancel email that is not waiting to send",
		})
	}

	err = generatedemails.CancelPendingEmail(ctx, existingEmail)
	if err != nil {
		log.Error(ctx, "error deleting generated email in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	result := CancelEmailResponse{
		Message: "Cancelled email successfully",
	}

	return c.Status(http.StatusOK).JSON(result)
}
