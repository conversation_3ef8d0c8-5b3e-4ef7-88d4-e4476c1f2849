package outbox

import (
	"encoding/json"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sfn"
	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/generatedemails"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	UpdateEmailPath struct {
		GeneratedEmailID uint `params:"generatedEmailID" validate:"required"`
	}

	UpdateEmailBody struct {
		Recipients   []string        `json:"recipients" validate:"required,min=1,dive,email"`
		CC           []string        `json:"cc"`
		Subject      *string         `json:"subject,omitempty"`
		Body         *string         `json:"body,omitempty"`
		ScheduleSend models.NullTime `json:"scheduleSend"`
	}

	UpdateEmailResponse struct {
		Message string `json:"message,omitempty"`
	}
)

func UpdateEmail(c *fiber.Ctx) error {
	var path UpdateEmailPath
	var body UpdateEmailBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(UpdateEmailResponse{Message: err.Error()})
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	existingEmail, err := generatedemails.GetEmail(ctx, path.GeneratedEmailID)
	if err != nil {
		log.Error(ctx, "generatedemails query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Authorization check: ensure user belongs to same service as the email
	if existingEmail.ServiceID != user.ServiceID {
		log.WarnNoSentry(
			ctx,
			"unauthorized attempt to update email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.Uint("emailServiceID", existingEmail.ServiceID),
			zap.Uint("requestUserServiceID", user.ServiceID),
		)

		return c.Status(http.StatusForbidden).JSON(UpdateEmailResponse{
			Message: "You are not allowed to update this email",
		})
	}

	// Race condition protection: only allow updates to pending emails
	if existingEmail.Status != models.PendingStatus {
		log.WarnNoSentry(
			ctx,
			"attempt to update non-pending email",
			zap.Uint("generatedEmailID", path.GeneratedEmailID),
			zap.String("status", string(existingEmail.Status)),
		)

		return c.Status(http.StatusConflict).JSON(UpdateEmailResponse{
			Message: "Can not edit email that is not waiting to send",
		})
	}

	existingEmail.Recipients = pq.StringArray(body.Recipients)
	existingEmail.CC = pq.StringArray(body.CC)
	if body.Subject != nil {
		existingEmail.Subject = *body.Subject
	}
	if body.Body != nil {
		existingEmail.AppliedBody = *body.Body
	}

	oldScheduledSend := existingEmail.ScheduleSend
	existingEmail.ScheduleSend = body.ScheduleSend

	err = generatedemails.Update(ctx, existingEmail)
	if err != nil {
		log.Error(ctx, "error updating generated email in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if env.Vars.AppEnv != "dev" && oldScheduledSend.Time != body.ScheduleSend.Time {
		cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
		if err != nil {
			log.Error(ctx, "error creating aws config", zap.Error(err))
		}

		sfnClient := sfn.NewFromConfig(cfg)

		payload := map[string]any{
			"email": map[string]any{
				"generatedEmailID":   existingEmail.ID,
				"senderEmailAddress": user.EmailAddress,
				"emailProvider":      user.EmailProvider,
			},
			"triggerTime": existingEmail.ScheduleSend.Time,
		}

		payloadBytes, err := json.Marshal(payload)
		if err != nil {
			log.Error(ctx, "error marshaling step function payload", zap.Error(err))
		}

		input := &sfn.StartExecutionInput{
			StateMachineArn: aws.String(env.Vars.StateMachineARN),
			Input:           aws.String(string(payloadBytes)),
		}

		_, err = sfnClient.StartExecution(ctx, input)
		if err != nil {
			log.Error(ctx, "error executing step function", zap.Error(err))
		}
	}

	result := UpdateEmailResponse{
		Message: "Email updated",
	}

	return c.Status(http.StatusOK).JSON(result)
}
