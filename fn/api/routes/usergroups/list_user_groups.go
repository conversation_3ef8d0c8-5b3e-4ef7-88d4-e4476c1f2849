package usergroups

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type ListUserGroupsResponse struct {
	UserGroups []UserGroupSummary `json:"userGroups"`
}

type UserGroupSummary struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	ServiceID uint   `json:"serviceId"`
}

func ListUserGroups(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(ctx, "unauthorized access attempt to listUserGroups", zap.String("email", user.EmailAddress))
		return c.SendStatus(http.StatusForbidden)
	}

	itemCount := 50
	if itemCountStr := c.Query("itemCount"); itemCountStr != "" {
		parsed, err := strconv.Atoi(itemCountStr)
		if err != nil {
			log.Warn(ctx, "invalid itemCount query parameter", zap.String("itemCount", itemCountStr), zap.Error(err))
		} else if parsed > 0 {
			itemCount = parsed
		}
	}

	services, err := rds.GetServiceAll(ctx)
	if err != nil {
		log.Error(ctx, "error getting all services", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var allUserGroups []models.UserGroup
	for _, service := range services {
		userGroups, err := userGroupsDB.GetByServiceID(ctx, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting user groups for service", zap.Uint("serviceID", service.ID), zap.Error(err))
			continue
		}
		allUserGroups = append(allUserGroups, userGroups...)
	}

	if itemCount < len(allUserGroups) {
		allUserGroups = allUserGroups[:itemCount]
	}

	userGroupSummaries := make([]UserGroupSummary, len(allUserGroups))
	for i, ug := range allUserGroups {
		userGroupSummaries[i] = UserGroupSummary{
			ID:        ug.ID,
			Name:      ug.Name,
			ServiceID: ug.ServiceID,
		}
	}

	return c.Status(http.StatusOK).JSON(ListUserGroupsResponse{
		UserGroups: userGroupSummaries,
	})
}
