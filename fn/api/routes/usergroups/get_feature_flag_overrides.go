package usergroups

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type (
	GetUserGroupFeatureFlagOverridesPath struct {
		UserGroupID uint `path:"userGroupID" validate:"required"`
	}

	GetUserGroupFeatureFlagOverridesResponse struct {
		UserGroupID          uint                        `json:"userGroupId"`
		FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides"`
	}
)

func GetUserGroupFeatureFlagOverrides(c *fiber.Ctx) error {
	var path GetUserGroupFeatureFlagOverridesPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	// Only allow admin users to access this endpoint
	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(
			ctx,
			"unauthorized access attempt to user group feature flag overrides",
			zap.String("email", user.EmailAddress),
		)
		return c.SendStatus(http.StatusForbidden)
	}

	userGroup, err := userGroupsDB.GetByID(ctx, path.UserGroupID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("user group not found")
		}
		log.Error(ctx, "error getting user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userGroup.FeatureFlagOverrides == nil {
		userGroup.FeatureFlagOverrides = make(models.FeatureFlagOverrides)
	}

	return c.Status(http.StatusOK).JSON(GetUserGroupFeatureFlagOverridesResponse{
		UserGroupID:          userGroup.ID,
		FeatureFlagOverrides: userGroup.FeatureFlagOverrides,
	})
}
