package usergroups

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type SearchUserGroupsQuery struct {
	Name      string `query:"name"`
	ServiceID uint   `query:"serviceID"`
	Limit     int    `query:"limit"`
}

type SearchUserGroupsResponse struct {
	UserGroups []UserGroupSummary `json:"userGroups"`
}

func SearchUserGroups(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(ctx, "unauthorized access attempt to searchUserGroups", zap.String("email", user.EmailAddress))
		return c.SendStatus(http.StatusForbidden)
	}

	var query SearchUserGroupsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 50
	}

	searchQuery := userGroupsDB.SearchUserGroupsQuery{
		Name:      query.Name,
		ServiceID: query.ServiceID,
		Limit:     query.Limit,
	}

	userGroups, err := userGroupsDB.FuzzySearchByName(ctx, searchQuery)
	if err != nil {
		log.Error(ctx, "error searching user groups", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userGroupSummaries := make([]UserGroupSummary, len(userGroups))
	for i, ug := range userGroups {
		userGroupSummaries[i] = UserGroupSummary{
			ID:        ug.ID,
			Name:      ug.Name,
			ServiceID: ug.ServiceID,
		}
	}

	return c.Status(http.StatusOK).JSON(SearchUserGroupsResponse{
		UserGroups: userGroupSummaries,
	})
}
