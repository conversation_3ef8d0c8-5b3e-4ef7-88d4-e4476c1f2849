package usergroups

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
	"github.com/drumkitai/drumkit/fn/api/routes/service"
)

type (
	UpdateUserGroupFeatureFlagOverridesPath struct {
		UserGroupID uint `path:"userGroupID" validate:"required"`
	}

	UpdateUserGroupFeatureFlagOverridesBody struct {
		FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides" validate:"required"`
	}

	UpdateUserGroupFeatureFlagOverridesResponse struct {
		UserGroupID          uint                        `json:"userGroupId"`
		FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides"`
	}
)

func UpdateUserGroupFeatureFlagOverrides(c *fiber.Ctx) error {
	var path UpdateUserGroupFeatureFlagOverridesPath
	var body UpdateUserGroupFeatureFlagOverridesBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path), zap.Any("body", body))
	userID := middleware.UserIDFromContext(c)

	// Only allow admin users to access this endpoint
	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(
			ctx,
			"unauthorized access attempt to update user group feature flag overrides",
			zap.String("email", user.EmailAddress),
		)
		return c.SendStatus(http.StatusForbidden)
	}

	userGroup, err := userGroupsDB.GetByID(ctx, path.UserGroupID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("user group not found")
		}
		log.Error(ctx, "error getting user group", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Validate that all override keys are valid feature flag JSON tag names
	validFlags := service.GetValidFeatureFlags()
	for jsonTagName := range body.FeatureFlagOverrides {
		isValid := false
		for _, validFlag := range validFlags {
			if strings.EqualFold(jsonTagName, validFlag) {
				isValid = true
				break
			}
		}
		if !isValid {
			errorMsg := fmt.Sprintf("invalid feature flag: %s. Valid feature flags are: %s",
				jsonTagName, strings.Join(validFlags, ", "))
			log.Error(ctx, "invalid feature flag name", zap.String("featureFlag", jsonTagName))
			return c.Status(http.StatusBadRequest).SendString(errorMsg)
		}
	}

	userGroup.FeatureFlagOverrides = body.FeatureFlagOverrides

	if err := rds.Update(ctx, &userGroup); err != nil {
		log.Error(ctx, "error updating user group feature flag overrides", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Info(
		ctx,
		"user group feature flag overrides updated",
		zap.Uint("userGroupID", path.UserGroupID),
		zap.Int("overrideCount", len(body.FeatureFlagOverrides)),
	)

	return c.Status(http.StatusOK).JSON(UpdateUserGroupFeatureFlagOverridesResponse{
		UserGroupID:          userGroup.ID,
		FeatureFlagOverrides: userGroup.FeatureFlagOverrides,
	})
}
