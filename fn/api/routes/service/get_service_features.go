package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/config"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type (
	UserConfigurations struct {
		DefaultPriceMargin     *float64                       `json:"defaultPriceMargin"`
		DefaultPriceMarginType *models.DefaultPriceMarginType `json:"defaultPriceMarginType"`
	}

	GetServiceFeaturesPath struct {
		ServiceID uint `json:"service_id"`
	}

	GetServiceFeaturesQuery struct {
		IgnoreOverrides bool `query:"ignoreOverrides"`
	}

	GetServiceFeaturesResponse struct {
		ID                              uint                     `json:"id"`
		TMSIntegrations                 []models.IntegrationCore `json:"tmsIntegrations"`
		QuotingIntegrations             []models.IntegrationCore `json:"quotingIntegrations"`
		SchedulerIntegrations           []models.IntegrationCore `json:"schedulerIntegrations"`
		CarrierVerificationIntegrations []models.IntegrationCore `json:"carrierVerificationIntegrations"`
		Configurations                  UserConfigurations       `json:"configurations"`
		QuickQuoteConfig                *models.QuickQuoteConfig `json:"quickQuoteConfig"`
		models.FeatureFlags
		CarrierQuoteConfig *models.CarrierQuoteConfigOptions `json:"carrierQuoteConfig,omitempty"`
		SchedulerConfig    *models.SchedulerConfig           `json:"schedulerConfig,omitempty"`
		// Technically this is a user-specific value and not a service-specific value,
		// but it's convenient to include here since this API is the first hook that runs when Drumkit sidebar loads
		NeedsInboxReauthorization bool                 `json:"needsInboxReauthorization"`
		EmailProvider             models.EmailProvider `json:"emailProvider"` // gmail or outlook
	}
)

func GetServiceFeatures(c *fiber.Ctx) error {
	var path GetServiceFeaturesPath
	var query GetServiceFeaturesQuery
	if err := api.Parse(c, &path, &query, nil); err != nil {
		log.Error(
			c.UserContext(),
			fmt.Sprintf("error validating service id %d as param for fetching feature flags", path.ServiceID),
			zap.Error(err),
		)

		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path), zap.Any("queryParams", query))

	// Preload for access to QuickQuoteConfig
	service, err := rds.GetServiceWithPreload(ctx, path.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(
				ctx,
				fmt.Sprintf("couldn't find service with id %d to fetch its feature flags", path.ServiceID),
				zap.Error(err),
			)

			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("service with id %d not found", path.ServiceID),
			)
		}

		log.Error(ctx, "serviceDB query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	if query.IgnoreOverrides {
		return c.Status(http.StatusOK).JSON(GetServiceFeaturesResponse{
			ID:           service.ID,
			FeatureFlags: service.FeatureFlags,
		})
	}

	if userServiceID != service.ID {
		return c.SendStatus(http.StatusForbidden)
	}

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userGroups, err := userGroupsDB.GetAllByUserID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "error getting user groups", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Merge feature flag overrides in priority order: User > User Group > Service
	mergedFlags := mergeFeatureFlags(service.FeatureFlags, userGroups, user.FeatureFlagOverrides)

	dbUpdateNeeded := false
	if !service.IsLoadViewEnabled {
		shouldEnable := shouldEnableLoadView(service)
		if shouldEnable != service.IsLoadViewEnabled {
			log.WarnNoSentry(ctx, "load view disabled but load view tabs enabled, correcting")

			service.IsLoadViewEnabled = shouldEnable
			dbUpdateNeeded = true
		}

	}

	if !service.IsQuoteViewEnabled {
		shouldEnable := shouldEnableQuoteView(service)
		if shouldEnable != service.IsQuoteViewEnabled {
			log.WarnNoSentry(ctx, "quote view disabled but quote view tabs enabled, correcting")

			service.IsQuoteViewEnabled = shouldEnable
			dbUpdateNeeded = true
		}
	}

	if dbUpdateNeeded {
		if err := rds.Update(ctx, &service); err != nil {
			log.WarnNoSentry(ctx, "error updating service in DB with corrected flags", zap.Error(err))
		}
	}

	// Optimization; only fetch TMS integrations if needed (e.g. if load building or quote submission is enabled)
	// TODO: Cache in FE
	// Use merged flags to determine which integrations to fetch
	var miniTMSes []models.IntegrationCore

	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
	if err != nil {
		log.Warn(ctx, "error getting TMS integrations", zap.Error(err))
	}
	for _, i := range tmsIntegrations {
		miniTMSes = append(
			miniTMSes,
			models.IntegrationCore{
				ID:           i.ID,
				Name:         i.Name,
				Tenant:       i.Tenant,
				FeatureFlags: i.FeatureFlags,
			},
		)
	}

	miniQuotingIntegrations := []models.IntegrationCore{}
	if mergedFlags.IsQuickQuoteEnabled ||
		mergedFlags.IsCarrierNetworkQuotingEnabled ||
		mergedFlags.IsQuoteLaneHistoryEnabled {
		quotingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting pricing integrations", zap.Error(err))
		}
		for _, i := range quotingIntegrations {
			miniQuotingIntegrations = append(
				miniQuotingIntegrations,
				models.IntegrationCore{
					ID:           i.ID,
					Name:         i.Name,
					FeatureFlags: i.FeatureFlags,
				},
			)
		}
	}

	miniSchedulerIntegrations := []models.IntegrationCore{}
	if mergedFlags.IsAppointmentSchedulingEnabled {
		schedulerIntegrations, err := integrationDB.GetAllSchedulerByServiceIDAndUserID(ctx, userID, service.ID)
		if err != nil {
			log.Warn(ctx, "error getting pricing integrations", zap.Error(err))
		}
		for _, i := range schedulerIntegrations {
			miniSchedulerIntegrations = append(
				miniSchedulerIntegrations,
				models.IntegrationCore{
					ID:           i.ID,
					Name:         i.Name,
					FeatureFlags: i.FeatureFlags,
					Username:     i.Username,
					Tenant:       i.Tenant,
					Note:         i.Note,
				},
			)
		}
	}

	miniCarrierVerificationIntegrations := []models.IntegrationCore{}
	if mergedFlags.IsCarrierVerificationEnabled {
		carrierVerificationIntegration, err := integrationDB.GetCarrierVerificationByServiceID(ctx, service.ID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.WarnNoSentry(ctx, "carrier verification integration not found", zap.Error(err))
			} else {
				log.Warn(ctx, "error getting carrier verification integrations", zap.Error(err))
			}
		} else {
			miniCarrierVerificationIntegrations = append(
				miniCarrierVerificationIntegrations,
				models.IntegrationCore{
					ID:           carrierVerificationIntegration.ID,
					Name:         carrierVerificationIntegration.Name,
					FeatureFlags: carrierVerificationIntegration.FeatureFlags,
					Username:     carrierVerificationIntegration.Username,
					Tenant:       carrierVerificationIntegration.Tenant,
				},
			)
		}
	}

	var carrierQuoteConfig *models.CarrierQuoteConfigOptions
	if mergedFlags.IsCarrierNetworkQuotingEnabled {
		cqc, err := config.GetCarrierQuoteConfigByServiceID(ctx, service.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting CarrierQuoteConfig",
				zap.Uint("serviceID", service.ID),
				zap.Error(err),
			)
		} else {
			carrierQuoteConfig = &cqc.CarrierQuoteConfigOptions
		}
	}

	var schedulerConfig *models.SchedulerConfig
	if service.SCAC != "" {
		schedulerConfig = &models.SchedulerConfig{
			SCAC: service.SCAC,
		}
	} else {
		schedulerConfig = &models.SchedulerConfig{}
	}

	needsInboxReauthorization := false
	emailProvider := getEmailProvider(ctx, user)

	timeNow := time.Now()
	if timeNow.After(user.TokenExpiry) {
		log.Info(
			ctx,
			"token expired, needs inbox reauthorization",
			zap.String("tokenExpiration", user.TokenExpiry.Format(time.RFC3339)),
			zap.String("webhookExpiration", user.WebhookExpiration.Format(time.RFC3339)),
		)
		needsInboxReauthorization = true
	}

	if timeNow.After(user.WebhookExpiration) {
		log.Info(
			ctx,
			"webhook expired, needs inbox reauthorization",
			zap.String("tokenExpiration", user.TokenExpiry.Format(time.RFC3339)),
			zap.String("webhookExpiration", user.WebhookExpiration.Format(time.RFC3339)),
		)
		needsInboxReauthorization = true
	}

	return c.Status(http.StatusOK).JSON(
		GetServiceFeaturesResponse{
			ID:                              service.ID,
			FeatureFlags:                    mergedFlags,
			TMSIntegrations:                 miniTMSes,
			QuotingIntegrations:             miniQuotingIntegrations,
			CarrierVerificationIntegrations: miniCarrierVerificationIntegrations,
			Configurations: UserConfigurations{
				DefaultPriceMargin:     user.DefaultPriceMargin,
				DefaultPriceMarginType: user.DefaultPriceMarginType,
			},
			SchedulerIntegrations:     miniSchedulerIntegrations,
			QuickQuoteConfig:          service.QuickQuoteConfig,
			CarrierQuoteConfig:        carrierQuoteConfig,
			SchedulerConfig:           schedulerConfig,
			NeedsInboxReauthorization: needsInboxReauthorization,
			EmailProvider:             emailProvider,
		},
	)
}

func shouldEnableLoadView(service models.Service) bool {
	return service.IsAppointmentSchedulingEnabled ||
		service.IsTrackAndTraceEnabled ||
		service.IsCarrierVerificationEnabled
}

func shouldEnableQuoteView(service models.Service) bool {
	return service.IsQuickQuoteEnabled ||
		service.IsCarrierNetworkQuotingEnabled ||
		service.IsLoadBuildingEnabled ||
		service.IsTruckListEnabled
}

// NOTE: It's possible for a user to be integrated with both Front and either Gmail or Outlook (e.g. NFI, Trifecta).
// In that case, this function returns the underlying email provider so the user can relink their account when required.
// If the user is integrated *only* with Front, this function will return Front.
// As of Q4 2025, Front does not require user re-authorization as webhooks are set up at an organization level.
// https://www.notion.so/drumkitai/Email-Tokens-Webhooks-2b52b16b087a80dcbfccf776a3e7e0cb
// https://www.notion.so/drumkitai/Front-Guide-1e62b16b087a806f9a35c4e37eb85339
func getEmailProvider(ctx context.Context, user models.User) models.EmailProvider {
	if user.EmailProvider == models.GmailEmailProvider || user.GmailLastHistoryID > 0 {
		return models.GmailEmailProvider
	}

	if user.EmailProvider == models.OutlookEmailProvider || user.OutlookClientState != "" {
		return models.OutlookEmailProvider
	}

	if user.FrontID != "" {
		return models.FrontEmailProvider
	}

	// Should not happen
	log.Warn(ctx, "unknown email provider, failing open but check user/service configuration")

	return models.EmailProvider("")
}
