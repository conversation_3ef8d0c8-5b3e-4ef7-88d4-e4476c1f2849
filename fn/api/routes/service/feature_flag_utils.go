package service

import (
	"reflect"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

// GetValidFeatureFlags returns a list of all valid feature flag JSON field names.
// Exported so it can be used by other packages for validation.
func GetValidFeatureFlags() []string {
	return getValidFeatureFlags()
}

// getValidFeatureFlags returns a list of all valid feature flag JSON field names
func getValidFeatureFlags() []string {
	var flags []string
	featureFlagsType := reflect.TypeOf(models.FeatureFlags{})

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		if field.Type.Kind() == reflect.Bool {
			jsonTag := field.Tag.Get("json")
			if jsonTag != "" && jsonTag != "-" {
				flags = append(flags, jsonTag)
			}
		}
	}

	return flags
}

// findFieldByJSONName finds a field by its JSON tag name (case-insensitive)
func findFieldByJSONName(featureFlags *models.FeatureFlags, jsonName string) (reflect.Value, bool) {
	serviceValue := reflect.ValueOf(featureFlags).Elem()
	featureFlagsType := reflect.TypeOf(*featureFlags)

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		jsonTag := field.Tag.Get("json")

		if jsonTag != "" && jsonTag != "-" && strings.EqualFold(jsonTag, jsonName) {
			return serviceValue.Field(i), true
		}
	}

	return reflect.Value{}, false
}

// mergeFeatureFlags merges feature flag overrides in priority order:
// 1. User overrides (highest priority)
// 2. User Group overrides (middle priority)
// 3. Service flags (lowest priority, fallback)
// If a user is in multiple groups, the first non-null override for each flag is used.
// Returns a new FeatureFlags struct with merged values.
func mergeFeatureFlags(
	serviceFlags models.FeatureFlags,
	userGroups []models.UserGroup,
	userOverrides models.FeatureFlagOverrides,
) models.FeatureFlags {
	// Start with service flags as the base source. Service flags can't be null.
	merged := serviceFlags

	// Build a lookup map: JSON tag name -> field position in struct
	// This allows us to quickly find which struct field corresponds to a JSON key
	// Example: "isQuickQuoteEnabled" -> field index 4
	jsonTagToFieldIndex := buildJSONTagToFieldIndexMap(merged)

	// Priority 1: Apply user group overrides (middle priority)
	applyGroupOverrides(&merged, userGroups, jsonTagToFieldIndex)

	// Priority 2: Apply user-level overrides (highest priority)
	applyUserOverrides(&merged, userOverrides, jsonTagToFieldIndex)

	return merged
}

// buildJSONTagToFieldIndexMap creates a map from JSON tag names to their field positions.
// This is needed because overrides are stored as JSON keys (e.g., "isQuickQuoteEnabled"),
// but we need to find the actual struct field to update.
//
// Example result: {"isQuickQuoteEnabled": 4, "isLoadBuildingEnabled": 5, ...}
func buildJSONTagToFieldIndexMap(flags models.FeatureFlags) map[string]int {
	flagsType := reflect.TypeOf(flags)
	// flagsValue := reflect.ValueOf(&flags).Elem()

	result := make(map[string]int)

	for i := 0; i < flagsType.NumField(); i++ {
		field := flagsType.Field(i)

		// Get the JSON tag (e.g., "isQuickQuoteEnabled" from `json:"isQuickQuoteEnabled"`)
		jsonTag := field.Tag.Get("json")

		if jsonTag != "" && jsonTag != "-" && field.Type.Kind() == reflect.Bool {
			// Store the mapping: JSON tag name -> field index
			result[jsonTag] = i
		}
	}

	return result
}

func applyGroupOverrides(
	merged *models.FeatureFlags,
	userGroups []models.UserGroup,
	jsonTagToFieldIndex map[string]int,
) {
	// Track which flags have been already overridden by a group to avoid double-overriding
	alreadyOverridden := make(map[string]bool)

	mergedValue := reflect.ValueOf(merged).Elem()

	for _, group := range userGroups {
		if group.FeatureFlagOverrides == nil {
			continue
		}

		for jsonTagName, overrideValue := range group.FeatureFlagOverrides {
			if alreadyOverridden[jsonTagName] {
				continue
			}

			if overrideValue != nil {
				applyOverrideToField(mergedValue, jsonTagName, *overrideValue, jsonTagToFieldIndex)
				alreadyOverridden[jsonTagName] = true
			}
		}
	}
}

func applyUserOverrides(
	merged *models.FeatureFlags,
	userOverrides models.FeatureFlagOverrides,
	jsonTagToFieldIndex map[string]int,
) {

	if userOverrides == nil {
		return
	}

	mergedValue := reflect.ValueOf(merged).Elem()

	for jsonTagName, overrideValue := range userOverrides {
		if overrideValue != nil {
			applyOverrideToField(mergedValue, jsonTagName, *overrideValue, jsonTagToFieldIndex)
		}
	}
}

func applyOverrideToField(
	structValue reflect.Value,
	jsonTagName string,
	value bool,
	jsonTagToFieldIndex map[string]int,
) {
	// Look up which field index corresponds to this JSON tag name
	fieldIndex, exists := jsonTagToFieldIndex[jsonTagName]
	if !exists {
		return
	}

	fieldValue := structValue.Field(fieldIndex)

	if !fieldValue.CanSet() || fieldValue.Kind() != reflect.Bool {
		return
	}

	fieldValue.SetBool(value)
}
