package email

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetEmailPath struct {
		ID string `params:"id" validate:"required"`
	}

	GetEmailResponse struct {
		ID                   uint                          `json:"id"`
		ExternalID           string                        `json:"externalID"`
		ThreadID             string                        `json:"threadID"`
		From                 string                        `json:"from"`
		ClassificationMethod models.ClassificationApproach `json:"classificationMethod"`
		Labels               string                        `json:"labels"`
		FreightTrackingIDs   []string                      `json:"freightTrackingIDs"`
		CustomPortalDomain   string                        `json:"customPortalDomain"`
	}

	GetIngestionStatusPath struct {
		ID string `params:"id" validate:"required"`
	}

	GetIngestionStatusResponse struct {
		Status models.SuggestionStatus `json:"status"`
	}
)

func GetEmailByThreadID(c *fiber.Ctx) error {
	return getEmailGeneric(c, "thread_id", emailDB.GetEmailByThreadID)
}

// NOTE: NOT IN USE
// func GetEmail(c *fiber.Ctx) error {
// 	return getEmailGeneric(c, "external_id", emailDB.GetEmailByExternalID)
// }

// Get email by either message ID or thread ID, the only difference being the DB lookup function
func getEmailGeneric(
	c *fiber.Ctx,
	identifier string,
	dbLookupFunc func(context.Context, string) (*models.Email, error),
) error {

	var path GetEmailPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	// NOTE: When ingested, Outlook webhooks replace / with -, and + with _
	// (Gmail IDs are hexadecimal so they're unchanged by this)
	replacedID, err := apiutil.DecodeOutlookID(path.ID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	email, err := dbLookupFunc(ctx, replacedID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("%s %s not found", identifier, replacedID),
			)
		}

		log.Error(ctx, "get email DB error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx, zap.Uint("emailDrumkitID", email.ID))
	claims := middleware.ClaimsFromContext(c)

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusUnauthorized)
	}

	if user.Service.IsDelegatedInboxEnabled {
		// For delegated inboxes we're a bit more permissive, so instead of matching email addresses from the
		// JWT to the Email DB entry we simply match their service IDs instead.
		if *claims.ServiceID != email.ServiceID {
			log.WarnNoSentry(
				ctx,
				"unauthorized: email from token does not match serviceID in DB",
				zap.Uint("dbEmailServiceID", email.ServiceID),
			)

			return c.SendStatus(http.StatusUnauthorized)
		}
	} else {
		if user.ID != email.UserID {
			log.WarnNoSentry(
				ctx,
				"unauthorized: userID from token does not match email's userID",
				zap.Uint("dbEmailUserID", email.UserID),
				zap.String("dbEmailAccount", email.Account),
			)

			return c.SendStatus(http.StatusUnauthorized)
		}
	}

	freightTrackingIDs, err := getAssociatedLoadIDsByEmailProvider(ctx, *email)
	// Fail-open, just return empty list of associated loads
	if err != nil {
		log.Error(ctx, "error getting loads by thread ID", zap.Error(err))
	}

	result := GetEmailResponse{
		ID:                   email.ID,
		ExternalID:           email.ExternalID,
		ThreadID:             email.ThreadID,
		From:                 email.Sender,
		ClassificationMethod: email.ClassificationMethod,
		Labels:               email.Labels,
		FreightTrackingIDs:   freightTrackingIDs,
		CustomPortalDomain:   user.OnPremPortalDomain,
	}

	return c.Status(http.StatusOK).JSON(result)
}

func GetIngestionStatusByThreadID(c *fiber.Ctx) error {
	var path GetIngestionStatusPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		log.Error(c.UserContext(), "error parsing path", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	// Check Redis for thread ID to determine if ingestion is done
	ingestionStatusKey := "ingestion-status:" + path.ID
	result, found, err := redis.GetKey[string](c.UserContext(), ingestionStatusKey)
	if err != nil {
		if errors.Is(err, redis.NilEntry) {
			return c.Status(http.StatusOK).JSON(GetIngestionStatusResponse{Status: models.NotInFlight})
		}

		log.Error(ctx, "error getting ingestion status result", zap.Error(err))
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	if found && result == string(models.InFlight) {
		return c.Status(http.StatusOK).JSON(GetIngestionStatusResponse{Status: models.InFlight})
	}

	return c.Status(http.StatusOK).JSON(GetIngestionStatusResponse{Status: models.NotInFlight})
}

func getAssociatedLoadIDsByEmailProvider(ctx context.Context, email models.Email) ([]string, error) {
	loadIDs := []string{}
	isFront := strings.HasPrefix(email.ExternalID, "msg_") || strings.HasPrefix(email.ExternalID, "com_")

	if !isFront {
		for _, load := range email.Loads {
			loadIDs = append(loadIDs, load.FreightTrackingID)
		}

		return loadIDs, nil
	}

	loads, err := loadDB.GetLoadsByThreadID(ctx, email.ThreadID)
	if err != nil {
		return loadIDs, err
	}

	for _, load := range loads {
		loadIDs = append(loadIDs, load.FreightTrackingID)
	}

	return loadIDs, nil
}
