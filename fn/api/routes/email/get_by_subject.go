package email

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	GetThreadBySubjectPath struct {
		Subject string `params:"subject" validate:"required"`
	}

	GetThreadBySubjectResponse struct {
		ThreadID         string   `json:"threadID,omitempty"`
		ThreadItemID     string   `json:"threadItemID,omitempty"`
		ThreadReferences string   `json:"threadReferences,omitempty"`
		InReplyTo        []string `json:"inReplyTo,omitempty"`
		Recipients       []string `json:"recipients,omitempty"`
	}
)

// normalizeSubject normalizes an email subject by:
// - Removing common prefixes (Re:, RE:, re:, Fwd:, FWD:, fwd:, etc.)
// - Trimming whitespace
// - Converting to lowercase for case-insensitive matching
func normalizeSubject(subject string) string {
	// Remove Re: and Fwd: prefixes (case-insensitive)
	re := regexp.MustCompile(`(?i)^(re|fwd?):\s*`)
	normalized := re.ReplaceAllString(subject, "")

	// Trim whitespace and convert to lowercase
	normalized = strings.TrimSpace(normalized)
	normalized = strings.ToLower(normalized)

	return normalized
}

// GetThreadBySubject searches for an email thread by subject line.
// For Gmail/Outlook: searches the emails database table
// For Front: uses Front API to search conversations
func GetThreadBySubject(c *fiber.Ctx) error {
	var path GetThreadBySubjectPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("subject", path.Subject))
	claims := middleware.ClaimsFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	// URL-decode the subject parameter
	decodedSubject, err := url.QueryUnescape(path.Subject)
	if err != nil {
		log.Warn(ctx, "error URL-decoding subject, using as-is", zap.Error(err))
		decodedSubject = path.Subject
	}

	// Validate subject is not empty after decoding
	decodedSubject = strings.TrimSpace(decodedSubject)
	if decodedSubject == "" {
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"message": "Subject cannot be empty",
		})
	}

	// Get user to determine email provider
	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "user not found", zap.Error(err))
			return c.SendStatus(http.StatusUnauthorized)
		}
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Get service
	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "error getting service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Determine email provider
	emailProvider := getEmailProvider(ctx, user)

	// Normalize subject for matching
	normalizedSubject := normalizeSubject(decodedSubject)

	var response GetThreadBySubjectResponse

	switch emailProvider {
	case models.FrontEmailProvider:
		// Search Front conversations via API
		frontClient, err := frontclient.New(&user)
		if err != nil {
			log.Error(ctx, "error creating Front client", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": "Failed to find thread by subject",
			})
		}

		conversation, err := frontClient.SearchConversationsBySubject(ctx, service, decodedSubject)
		if err != nil {
			// Check if it's a "not found" error
			if strings.Contains(err.Error(), "no conversations found") {
				return c.Status(http.StatusNotFound).JSON(fiber.Map{
					"message": "No thread found with that subject",
				})
			}

			// Check if it's an HTTP 404 error from Front API
			var httpErr errtypes.HTTPResponseError
			if errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusNotFound {
				return c.Status(http.StatusNotFound).JSON(fiber.Map{
					"message": "No thread found with that subject",
				})
			}

			log.Error(ctx, "error searching Front conversations", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": "Failed to find thread by subject",
			})
		}

		// For Front, we can use either conversation ID (threadID) or thread references
		// Front conversations have an ID that can be used as threadID
		response.ThreadID = conversation.ID

		// Extract recipients from Front conversation
		// Front conversations have a recipient field with email address
		if conversation.Recipient.Handle != "" {
			response.Recipients = append(response.Recipients, conversation.Recipient.Handle)
		}

		// Front also supports thread references for Gmail-style threading
		// We'll need to get the first message in the conversation to extract thread references
		// For now, we'll use the conversation ID as the primary identifier
		// The frontend can use threadID for Front conversations

	case models.GmailEmailProvider, models.OutlookEmailProvider:
		// Search emails database
		email, err := emailDB.GetEmailBySubject(ctx, normalizedSubject, userServiceID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(http.StatusNotFound).JSON(fiber.Map{
					"message": "No thread found with that subject",
				})
			}
			log.Error(ctx, "error searching emails by subject", zap.Error(err))
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": "Failed to find thread by subject",
			})
		}

		// Verify user has access to this email
		if service.IsDelegatedInboxEnabled {
			if email.ServiceID != userServiceID {
				log.WarnNoSentry(
					ctx,
					"unauthorized: email from different service",
					zap.Uint("emailServiceID", email.ServiceID),
					zap.Uint("userServiceID", userServiceID),
				)
				return c.SendStatus(http.StatusUnauthorized)
			}
		} else {
			if user.ID != email.UserID {
				log.WarnNoSentry(
					ctx,
					"unauthorized: email from different user",
					zap.Uint("emailUserID", email.UserID),
					zap.Uint("userID", user.ID),
				)
				return c.SendStatus(http.StatusUnauthorized)
			}
		}

		// Collect all email addresses (sender, recipients, CC) for the thread
		recipientsMap := make(map[string]bool)

		// Add sender
		if email.Sender != "" {
			recipientsMap[strings.TrimSpace(email.Sender)] = true
		}

		// Add recipients
		if email.Recipients != "" {
			recipientParts := strings.Split(email.Recipients, ",")
			for _, part := range recipientParts {
				trimmed := strings.TrimSpace(part)
				if trimmed != "" {
					recipientsMap[trimmed] = true
				}
			}
		}

		// Add CC recipients
		if email.CC != "" {
			ccParts := strings.Split(email.CC, ",")
			for _, part := range ccParts {
				trimmed := strings.TrimSpace(part)
				if trimmed != "" {
					recipientsMap[trimmed] = true
				}
			}
		}

		// Convert map to slice
		for emailAddr := range recipientsMap {
			response.Recipients = append(response.Recipients, emailAddr)
		}

		// Set response fields based on email provider
		if emailProvider == models.OutlookEmailProvider {
			// For Outlook, use ThreadID and ExternalID (threadItemID)
			response.ThreadID = email.ThreadID
			response.ThreadItemID = email.ExternalID
		} else {
			// For Gmail, use ThreadReferences and InReplyTo
			response.ThreadID = email.ThreadID
			response.ThreadReferences = email.ThreadReferences
			if email.InReplyTo != "" {
				// InReplyTo can be a single value or comma-separated
				inReplyToParts := strings.Split(email.InReplyTo, ",")
				for _, part := range inReplyToParts {
					trimmed := strings.TrimSpace(part)
					if trimmed != "" {
						response.InReplyTo = append(response.InReplyTo, trimmed)
					}
				}
			}
		}

	default:
		log.Warn(ctx, "unknown email provider", zap.String("provider", string(emailProvider)))
		return c.Status(http.StatusBadRequest).JSON(fiber.Map{
			"message": "Email provider not supported",
		})
	}

	return c.Status(http.StatusOK).JSON(response)
}

// getEmailProvider determines the email provider for a user
func getEmailProvider(ctx context.Context, user models.User) models.EmailProvider {
	if user.EmailProvider == models.GmailEmailProvider || user.GmailLastHistoryID > 0 {
		return models.GmailEmailProvider
	}

	if user.EmailProvider == models.OutlookEmailProvider || user.OutlookClientState != "" {
		return models.OutlookEmailProvider
	}

	if user.FrontID != "" {
		return models.FrontEmailProvider
	}

	log.Warn(ctx, "unknown email provider, defaulting to Gmail")
	return models.GmailEmailProvider
}
