package tmsroutes

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	SyncCustomerQuery struct {
		ExternalTMSID string `query:"externalTMSID" validate:"required"`
		TMSID         uint   `query:"tmsID" validate:"required"`
	}

	SyncCustomerResponse struct {
		Customer models.TMSCustomer `json:"customer"`
		Message  string             `json:"message"`
	}
)

// SyncCustomer syncs a single customer from a TMS by ExternalTMSID
// This endpoint is currently scoped to 3G TMS only but can be extended to other TMS integrations later
func SyncCustomer(c *fiber.Ctx) error {
	var query SyncCustomerQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)

	var tmsIntegration models.Integration
	var err error

	if query.TMSID > 0 {
		tmsIntegration, err = integrationDB.Get(ctx, query.TMSID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warn(
					ctx,
					"trying to sync customer for a service with no active TMS",
					zap.Uint("serviceID", userServiceID),
					zap.Uint("tmsID", query.TMSID),
				)
				return c.SendStatus(http.StatusNotFound)
			}
			log.Error(ctx, "error fetching integration from DB", zap.Error(err))
			return err
		}
	} else {
		// If TMSID is not set, get the first TMS integration for the service
		integrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
		if err != nil {
			log.Error(ctx, "error fetching integrations from DB",
				zap.Error(err), zap.Uint("service_id", userServiceID))
			return err
		}

		switch count := len(integrations); count {
		case 0:
			log.Warn(ctx, "trying to sync customer for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))
			return c.SendStatus(http.StatusNotFound)

		case 1:
			tmsIntegration = integrations[0]

		default:
			// If multiple TMS integrations exist, use the first one
			log.Warn(ctx, "multiple TMS integrations found, using first one",
				zap.Uint("serviceID", userServiceID), zap.Int("countTMS", count))
			tmsIntegration = integrations[0]
		}
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	var customer models.TMSCustomer
	var errTMS error

	switch tmsIntegration.Name {
	case models.ThreeG:
		// Create 3G TMS client directly to access 3G-specific methods
		threegClient, err := threeg.New(ctx, tmsIntegration)
		if err != nil {
			log.Error(ctx, "error creating 3G TMS client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		// Sync the customer from the TMS
		customer, errTMS = threegClient.GetCustomerByTradingPartnerNum(ctx, query.ExternalTMSID)
		if errTMS != nil {
			log.Error(ctx, "error syncing customer from 3G TMS",
				zap.String("external_tms_id", query.ExternalTMSID),
				zap.Error(errTMS))

			// Check if it's a "not found" error
			errStr := errTMS.Error()
			if errors.Is(errTMS, gorm.ErrRecordNotFound) || strings.Contains(strings.ToLower(errStr), "not found") {
				return c.Status(http.StatusNotFound).JSON(
					SyncCustomerResponse{
						Message: fmt.Sprintf(
							"Customer not found with external TMS ID: %s",
							query.ExternalTMSID,
						),
					},
				)
			}

			return c.Status(http.StatusServiceUnavailable).JSON(
				SyncCustomerResponse{
					Message: fmt.Sprintf("Failed to sync customer: %v", errTMS),
				},
			)
		}
	default:
		return c.Status(http.StatusBadRequest).JSON(
			SyncCustomerResponse{
				Message: "Single customer sync is currently only supported for 3G TMS.",
			},
		)
	}

	log.Info(
		ctx,
		"successfully synced customer from %s TMS", zap.String("tms_name", string(tmsIntegration.Name)),
		zap.String("external_tms_id", query.ExternalTMSID),
		zap.String("customer_name", customer.Name),
		zap.Error(errTMS),
	)

	return c.Status(http.StatusOK).JSON(
		SyncCustomerResponse{Customer: customer, Message: "Customer synced successfully"},
	)
}
