package tmsroutes

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsTransportTypeMappingDB "github.com/drumkitai/drumkit/common/rds/tms_transport_type_mapping"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetTransportTypesQuery struct {
		rds.GenericGetQuery
	}

	GetTransportTypesResponse struct {
		TransportTypes []models.TMSEquipmentType `json:"transportTypes"`
		TMSTenant      string                    `json:"tmsTenant"`
		Message        string                    `json:"message,omitempty"`
	}
)

// GetTransportTypes retrieves TMS transport type data.
// It first attempts to fetch transport types from the database unless a refresh is forced.
// If no data exists or a refresh is requested, it fetches from the TMS API directly.
// Returns a list of transport types to populate the transport type dropdown.
func GetTransportTypes(c *fiber.Ctx) error {
	var query GetTransportTypesQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(
			GetTransportTypesResponse{
				Message: err.Error(),
			},
		)
	}

	if query.Limit <= 0 {
		query.Limit = 100
	}

	// Validate required TMSID parameter before database queries
	if query.TMSID == 0 {
		return c.Status(http.StatusBadRequest).JSON(
			GetTransportTypesResponse{
				Message: "TMSID is required",
			},
		)
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var transportTypes []models.TMSEquipmentType
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to fetch transport types for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	// Only 3G TMS supports transport types currently
	if tmsIntegration.Name != models.ThreeG {
		log.Warn(ctx, "GetTransportTypes called for unsupported TMS", zap.String("tms", string(tmsIntegration.Name)))
		// Return empty list for unsupported TMS (like the load/transport-types endpoint)
		return c.Status(http.StatusOK).JSON(
			GetTransportTypesResponse{
				TransportTypes: []models.TMSEquipmentType{},
				TMSTenant:      tmsIntegration.Tenant,
			},
		)
	}

	// If we're not forcing a refresh, then get from DB
	if !query.ForceRefresh {
		mappings, err := tmsTransportTypeMappingDB.GetTransportTypeMappingsByTMSID(ctx, query.GenericGetQuery)
		if err != nil {
			log.WarnNoSentry(ctx, "error getting transport types from DB, falling back to TMS", zap.Error(err))
		} else if len(mappings) > 0 {
			// Convert mappings to TMSEquipmentType format
			transportTypes = make([]models.TMSEquipmentType, 0, len(mappings))
			for _, mapping := range mappings {
				transportTypes = append(transportTypes, models.TMSEquipmentType{
					ID:   fmt.Sprintf("%d", mapping.EquipmentID),
					Name: mapping.EquipmentName,
				})
			}

			endIndex := helpers.Min(len(transportTypes), query.Limit)
			return c.Status(http.StatusOK).JSON(
				GetTransportTypesResponse{
					TransportTypes: transportTypes[0:endIndex],
					TMSTenant:      tmsIntegration.Tenant,
				},
			)
		}
	}

	// Fetch from TMS if force refresh or no data in DB
	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	// Cast to 3G client and sync
	if threeGClient, ok := client.(*threeg.ThreeG); ok {
		transportTypes, err = threeGClient.SyncTransportTypes(ctx)
		if err != nil {
			log.Error(ctx, "failed to sync transport types from 3G TMS", zap.Error(err))
			return c.SendStatus(http.StatusServiceUnavailable)
		}

		if len(transportTypes) == 0 {
			return c.Status(http.StatusOK).JSON(
				GetTransportTypesResponse{
					TransportTypes: []models.TMSEquipmentType{},
					TMSTenant:      tmsIntegration.Tenant,
					Message:        "No transport types found in TMS",
				},
			)
		}

		endIndex := helpers.Min(len(transportTypes), query.Limit)
		return c.Status(http.StatusOK).JSON(
			GetTransportTypesResponse{
				TransportTypes: transportTypes[0:endIndex],
				TMSTenant:      tmsIntegration.Tenant,
			},
		)
	}

	return c.SendStatus(http.StatusInternalServerError)
}
