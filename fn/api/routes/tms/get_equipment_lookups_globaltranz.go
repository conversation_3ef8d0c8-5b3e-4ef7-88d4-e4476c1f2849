package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	GetEquipmentLookupsGlobalTranzQuery struct {
		TMSID uint `json:"tmsID" query:"tmsID"`
	}

	GetEquipmentTypesResponse struct {
		EquipmentTypes []globaltranztms.LookupItem `json:"equipmentTypes"`
		Message        string                      `json:"message,omitempty"`
	}

	GetEquipmentLengthsResponse struct {
		EquipmentLengths []globaltranztms.LookupItem `json:"equipmentLengths"`
		Message          string                      `json:"message,omitempty"`
	}

	GetEquipmentMappingResponse struct {
		EquipmentMapping []globaltranztms.EquipmentMappingItem `json:"equipmentMapping"`
		Message          string                                `json:"message,omitempty"`
	}

	GetAllAccessorialsQuery struct {
		TMSID             uint `json:"tmsID" query:"tmsID"`
		QuoteModeTypeEnum int  `json:"quoteModeTypeEnum" query:"quoteModeTypeEnum"`
	}

	GetAllAccessorialsResponse struct {
		Accessorials []globaltranztms.AccessorialItem `json:"accessorials"`
		Message      string                           `json:"message,omitempty"`
	}

	GetDisplayFieldsResponse struct {
		DisplayFields []globaltranztms.LookupItem `json:"displayFields"`
		Message       string                      `json:"message,omitempty"`
	}
)

// GetEquipmentTypesGlobalTranz fetches equipment types from GlobalTranz
func GetEquipmentTypesGlobalTranz(c *fiber.Ctx) error {
	var query GetEquipmentLookupsGlobalTranzQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get equipment types for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(GetEquipmentTypesResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	equipmentTypes, err := gtClient.GetEquipmentTypes(ctx)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error fetching GlobalTranz equipment types", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(GetEquipmentTypesResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error fetching GlobalTranz equipment types", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetEquipmentTypesResponse{
		EquipmentTypes: equipmentTypes,
	})
}

// GetEquipmentLengthsGlobalTranz fetches equipment lengths from GlobalTranz
func GetEquipmentLengthsGlobalTranz(c *fiber.Ctx) error {
	var query GetEquipmentLookupsGlobalTranzQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get equipment lengths for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(GetEquipmentLengthsResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	equipmentLengths, err := gtClient.GetEquipmentLengths(ctx)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error fetching GlobalTranz equipment lengths", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(GetEquipmentLengthsResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error fetching GlobalTranz equipment lengths", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetEquipmentLengthsResponse{
		EquipmentLengths: equipmentLengths,
	})
}

// GetEquipmentMappingGlobalTranz fetches equipment mapping from GlobalTranz
func GetEquipmentMappingGlobalTranz(c *fiber.Ctx) error {
	var query GetEquipmentLookupsGlobalTranzQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get equipment mapping for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(GetEquipmentMappingResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	equipmentMapping, err := gtClient.GetEquipmentMapping(ctx)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error fetching GlobalTranz equipment mapping", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(GetEquipmentMappingResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error fetching GlobalTranz equipment mapping", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetEquipmentMappingResponse{
		EquipmentMapping: equipmentMapping,
	})
}

// GetAllAccessorialsGlobalTranz fetches all accessorials from GlobalTranz
func GetAllAccessorialsGlobalTranz(c *fiber.Ctx) error {
	var query GetAllAccessorialsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Default quoteModeTypeEnum to 3 if not provided
	quoteModeTypeEnum := query.QuoteModeTypeEnum
	if quoteModeTypeEnum == 0 {
		quoteModeTypeEnum = 3
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get accessorials for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(GetAllAccessorialsResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accessorials, err := gtClient.GetAllAccessorials(ctx, quoteModeTypeEnum)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error fetching GlobalTranz accessorials", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(GetAllAccessorialsResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error fetching GlobalTranz accessorials", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetAllAccessorialsResponse{
		Accessorials: accessorials,
	})
}

// GetDisplayFieldsGlobalTranz fetches display fields from GlobalTranz
func GetDisplayFieldsGlobalTranz(c *fiber.Ctx) error {
	var query GetEquipmentLookupsGlobalTranzQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to get display fields for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(GetDisplayFieldsResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	displayFields, err := gtClient.GetDisplayFields(ctx)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error fetching GlobalTranz display fields", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(GetDisplayFieldsResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error fetching GlobalTranz display fields", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(GetDisplayFieldsResponse{
		DisplayFields: displayFields,
	})
}
