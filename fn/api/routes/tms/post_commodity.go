package tmsroutes

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	CreateCommodityBody struct {
		Commodity CommodityCore   `json:"commodity"`
		TMSID     uint            `json:"tmsID"`
		Options   json.RawMessage `json:"options,omitempty"`
	}

	CommodityCore struct {
		models.TMSCommodity
	}

	CreateCommodityResponse struct {
		Commodity models.TMSCommodity `json:"commodity"`
		Message   string              `json:"message"`
	}
)

// CreateCommodity proxies commodity creation to TMS integrations that support it.
func CreateCommodity(c *fiber.Ctx) error {
	var body CreateCommodityBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Uint("tmsID", body.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, body.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to create commodity for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	newCommodity := models.TMSCommodity{
		TMSIntegrationID:             body.TMSID,
		Commodity:                    body.Commodity.Commodity,
		CommodityDescription:         body.Commodity.CommodityDescription,
		ProductDefaultDetailsID:      body.Commodity.ProductDefaultDetailsID,
		WeightAmount:                 body.Commodity.WeightAmount,
		WeightUnit:                   body.Commodity.WeightUnit,
		WeightUnitID:                 body.Commodity.WeightUnitID,
		Length:                       body.Commodity.Length,
		Width:                        body.Commodity.Width,
		Height:                       body.Commodity.Height,
		DimUnit:                      body.Commodity.DimUnit,
		DimUnitID:                    body.Commodity.DimUnitID,
		HandlingUnitTypeID:           body.Commodity.HandlingUnitTypeID,
		HandlingUnitCount:            body.Commodity.HandlingUnitCount,
		PieceCount:                   body.Commodity.PieceCount,
		FreightClassID:               body.Commodity.FreightClassID,
		FreightClass:                 body.Commodity.FreightClass,
		NMFC:                         body.Commodity.NMFC,
		NMFCNumber:                   body.Commodity.NMFCNumber,
		HazardousMaterial:            body.Commodity.HazardousMaterial,
		HazmatClass:                  body.Commodity.HazmatClass,
		HazmatClassID:                body.Commodity.HazmatClassID,
		HazmatGroup:                  body.Commodity.HazmatGroup,
		HazmatGroupID:                body.Commodity.HazmatGroupID,
		HazmatCode:                   body.Commodity.HazmatCode,
		HazmatPrefixID:               body.Commodity.HazmatPrefixID,
		HazmatChemicalName:           body.Commodity.HazmatChemicalName,
		HazmatEmergencyContactNumber: body.Commodity.HazmatEmergencyContactNumber,
		Stackable:                    body.Commodity.Stackable,
		CarrierType:                  body.Commodity.CarrierType,
		Density:                      body.Commodity.Density,
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	commodityReq := models.CreateCommodityRequest{
		Commodity: newCommodity,
		Options:   body.Options,
	}

	commodity, err := client.CreateCommodity(ctx, commodityReq, nil)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error creating TMS commodity", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(CreateCommodityResponse{Message: ufErr.Error()})
		}

		var notImpl helpers.NotImplementedError
		if errors.As(err, &notImpl) {
			log.Error(
				ctx,
				"CreateCommodity not implemented for TMS",
				zap.String("tmsName", string(tmsIntegration.Name)),
			)
			return c.SendStatus(http.StatusNotImplemented)
		}

		log.Error(ctx, "error creating TMS commodity", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	commodity.TMSIntegrationID = body.TMSID

	return c.Status(http.StatusCreated).JSON(
		CreateCommodityResponse{
			Commodity: commodity,
			Message:   "Successfully created TMS commodity",
		},
	)
}
