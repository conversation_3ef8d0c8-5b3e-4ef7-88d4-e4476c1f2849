package tmsroutes

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCommodityDB "github.com/drumkitai/drumkit/common/rds/tms_commodity"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetCommoditiesQuery struct {
		rds.GenericGetQuery
		CustomerID  *uint `json:"customerID,omitempty" query:"customerID"`   // Optional: filter by customer
		CarrierType *int  `json:"carrierType,omitempty" query:"carrierType"` // Optional: service type for TL
	}

	GetCommoditiesResponse struct {
		CommodityList []models.TMSCommodity `json:"commodityList"`
		TMSTenant     string                `json:"tmsTenant"`
		Message       string                `json:"message"`
	}
)

// GetCommodities retrieves TMS commodity data.
// It first attempts to fetch commodities from the database unless a refresh is forced.
// If no data exists or a refresh is requested, it fetches from the TMS API directly.
// The function supports both synchronous and asynchronous operation modes.

// Returns a list of commodities to initially populate the commodity dropdown.
func GetCommodities(c *fiber.Ctx) error {
	var query GetCommoditiesQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 100
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var commodityList []models.TMSCommodity
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch commodities for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	// If we're not forcing a refresh, then get from DB.
	// If async request, get query.Limit records from DB to return to FE as a placeholder
	// while we asynchronously add the rest to DB
	if !query.ForceRefresh || query.Async {
		// Filter by customer and carrier type if provided
		if query.CustomerID != nil || query.CarrierType != nil {
			commodityList, err = tmsCommodityDB.GetTMSCommoditiesByCustomerIDAndCarrierTypeWithLimit(
				ctx,
				query.TMSID,
				query.CustomerID,
				query.CarrierType,
				query.Limit,
			)
		} else {
			commodityList, err = tmsCommodityDB.GetTMSCommoditiesByTMSID(ctx, query.GenericGetQuery)
		}

		switch {
		case err != nil:
			// Fail-open and try TMS; Gorm will send to Sentry
			log.WarnNoSentry(ctx, "error searching TMS commodities in DB, falling back to TMS", zap.Error(err))

		case len(commodityList) == 0:
			log.Info(ctx, "no commodities found in DB, falling back to TMS")

		case len(commodityList) > 0 && !query.Async:
			return c.Status(http.StatusOK).JSON(
				GetCommoditiesResponse{
					CommodityList: commodityList,
					TMSTenant:     tmsIntegration.Tenant,
				},
			)
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Set optional parameter to change the host name for TMS api call
	var changeHostName bool
	if query.ChangeHostName {
		changeHostName = true
	}

	// Build TMS options
	tmsOpts := []models.TMSOption{
		models.WithChangeHostName(changeHostName),
	}
	// If customerID is provided, only fetch commodities for that customer
	if query.CustomerID != nil {
		tmsOpts = append(tmsOpts, models.WithCustomerID(query.CustomerID))
	}

	if query.Async {
		go sentry.WithHub(ctx, func(ctx context.Context) {
			asyncCtx := log.InheritContext(ctx, context.Background())
			asyncCtx, cancel := context.WithTimeout(asyncCtx, 15*time.Minute)
			defer cancel()

			// Commodities are upserted to db within the GetCommodities function
			// to handle partial progress during large data fetches (10k-100k+ records).
			// This deviates from our usual single-responsibility pattern but is necessary for data reliability
			// Use local variable to avoid data race with main function
			asyncCommodityList, asyncErr := client.GetCommodities(asyncCtx, tmsOpts...)
			// Note: an empty slice of TMSCommodities is not an error, we handle it further down.
			if asyncErr != nil && asyncErr.Error() != tmsCommodityDB.EmptyTMSCommoditiesSlice {
				log.Error(asyncCtx, "async getCommodities from TMS failed", zap.Error(asyncErr))
				return
			}

			asyncErr = integrationDB.SetColumn(
				asyncCtx,
				tmsIntegration.ID,
				integrationDB.LastCommodityUpdatedAt,
				time.Now(),
			)
			if asyncErr != nil {
				log.Warn(
					asyncCtx,
					"failed to set last commodity updated at",
					zap.Error(asyncErr),
					zap.Uint("tmsID", tmsIntegration.ID),
				)
			}

			if len(asyncCommodityList) == 0 {
				log.Info(asyncCtx, "no commodities found in TMS")
				return
			}

			log.Info(asyncCtx, "successfully got commodities from TMS", zap.Int("count", len(asyncCommodityList)))
		})

		// If async request, return query.Limit records from DB to return to FE as a placeholder
		// while we asynchronously add the rest to DB
		// Use commodityList from DB (set at lines 89-98), not from the async goroutine
		endIndex := helpers.Min(len(commodityList), query.Limit)

		return c.Status(http.StatusAccepted).JSON(
			GetCommoditiesResponse{
				CommodityList: commodityList[0:endIndex],
				TMSTenant:     tmsIntegration.Tenant,
				Message:       "Asynchronously fetching more commodities from TMS",
			},
		)
	}

	// Otherwise, fetch synchronously from TMS

	// Commodities are upserted to db within the GetCommodities function
	// to handle partial progress during large data fetches (10k-100k+ records).
	// This deviates from our usual single-responsibility pattern but is necessary for data reliability
	commodityList, err = client.GetCommodities(ctx, tmsOpts...)
	// Note: an empty slice of TMSCommodities is not an error, we handle it further down.
	if err != nil && err.Error() != tmsCommodityDB.EmptyTMSCommoditiesSlice {
		log.Error(ctx, "getCommodities from TMS failed", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	err = integrationDB.SetColumn(ctx, tmsIntegration.ID, integrationDB.LastCommodityUpdatedAt, time.Now())
	if err != nil {
		log.Warn(
			ctx,
			"failed to set last commodity updated at",
			zap.Error(err),
			zap.Uint("tmsID", tmsIntegration.ID),
		)
	}

	if len(commodityList) == 0 {
		log.Info(ctx, "no commodities found in TMS")
		return c.Status(http.StatusNotFound).JSON(
			GetCommoditiesResponse{
				Message: "No new or updated commodities found.",
			},
		)
	}

	endIndex := helpers.Min(len(commodityList), query.Limit)

	return c.Status(http.StatusOK).JSON(
		GetCommoditiesResponse{
			CommodityList: commodityList[0:endIndex],
			TMSTenant:     tmsIntegration.Tenant,
		},
	)
}
