package tmsroutes

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsServiceOptionMappingDB "github.com/drumkitai/drumkit/common/rds/tms_service_option_mapping"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetServiceOptionsQuery struct {
		rds.GenericGetQuery
	}

	GetServiceOptionsResponse struct {
		ServiceOptions []models.TMSEquipmentType `json:"serviceOptions"`
		TMSTenant      string                    `json:"tmsTenant"`
		Message        string                    `json:"message,omitempty"`
	}
)

// GetServiceOptions retrieves TMS service option data.
// It first attempts to fetch service options from the database unless a refresh is forced.
// If no data exists or a refresh is requested, it fetches from the TMS API directly.
// Returns a list of service options to populate the service option dropdown.
func GetServiceOptions(c *fiber.Ctx) error {
	var query GetServiceOptionsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).JSON(
			GetServiceOptionsResponse{
				Message: err.Error(),
			},
		)
	}

	if query.Limit <= 0 {
		query.Limit = 100
	}

	// Validate required TMSID parameter before database queries
	if query.TMSID == 0 {
		return c.Status(http.StatusBadRequest).JSON(
			GetServiceOptionsResponse{
				Message: "TMSID is required",
			},
		)
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var serviceOptions []models.TMSEquipmentType
	var err error

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to fetch service options for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}
		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	// Only 3G TMS supports service options currently
	if tmsIntegration.Name != models.ThreeG {
		log.Warn(ctx, "GetServiceOptions called for unsupported TMS", zap.String("tms", string(tmsIntegration.Name)))
		// Return empty list for unsupported TMS (like the load/service-options endpoint)
		return c.Status(http.StatusOK).JSON(
			GetServiceOptionsResponse{
				ServiceOptions: []models.TMSEquipmentType{},
				TMSTenant:      tmsIntegration.Tenant,
			},
		)
	}

	// If we're not forcing a refresh, then get from DB
	if !query.ForceRefresh {
		mappings, err := tmsServiceOptionMappingDB.GetServiceOptionMappingsByTMSID(ctx, query.GenericGetQuery)
		if err != nil {
			log.WarnNoSentry(ctx, "error getting service options from DB, falling back to TMS", zap.Error(err))
		} else if len(mappings) > 0 {
			// Convert mappings to TMSEquipmentType format
			serviceOptions = make([]models.TMSEquipmentType, 0, len(mappings))
			for _, mapping := range mappings {
				serviceOptions = append(serviceOptions, models.TMSEquipmentType{
					ID:   fmt.Sprintf("%d", mapping.ServiceOptionID),
					Name: mapping.ServiceOptionName,
				})
			}

			endIndex := helpers.Min(len(serviceOptions), query.Limit)
			return c.Status(http.StatusOK).JSON(
				GetServiceOptionsResponse{
					ServiceOptions: serviceOptions[0:endIndex],
					TMSTenant:      tmsIntegration.Tenant,
				},
			)
		}
	}

	// Fetch from TMS if force refresh or no data in DB
	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	// Cast to 3G client and sync
	if threeGClient, ok := client.(*threeg.ThreeG); ok {
		serviceOptions, err = threeGClient.SyncServiceOptions(ctx)
		if err != nil {
			log.Error(ctx, "failed to sync service options from 3G TMS", zap.Error(err))
			return c.SendStatus(http.StatusServiceUnavailable)
		}

		if len(serviceOptions) == 0 {
			return c.Status(http.StatusOK).JSON(
				GetServiceOptionsResponse{
					ServiceOptions: []models.TMSEquipmentType{},
					TMSTenant:      tmsIntegration.Tenant,
					Message:        "No service options found in TMS",
				},
			)
		}

		endIndex := helpers.Min(len(serviceOptions), query.Limit)
		return c.Status(http.StatusOK).JSON(
			GetServiceOptionsResponse{
				ServiceOptions: serviceOptions[0:endIndex],
				TMSTenant:      tmsIntegration.Tenant,
			},
		)
	}

	return c.SendStatus(http.StatusInternalServerError)
}
