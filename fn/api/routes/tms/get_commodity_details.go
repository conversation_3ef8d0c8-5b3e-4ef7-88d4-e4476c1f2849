package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type GetCommodityDetailsResponse struct {
	Commodity models.TMSCommodity `json:"commodity"`
	Message   string              `json:"message"`
}

// GetCommodityDetails fetches full product details for a specific commodity on-demand
func GetCommodityDetails(c *fiber.Ctx) error {
	commodityID := c.Params("id")
	if commodityID == "" {
		return c.Status(http.StatusBadRequest).SendString("commodity ID is required")
	}

	var query struct {
		TMSID      uint  `query:"tmsID"`
		CustomerID *uint `query:"customerID"`
	}
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),
		zap.String("commodityID", commodityID),
		zap.Uint("tmsID", query.TMSID),
	)
	if query.CustomerID != nil {
		ctx = log.With(ctx, zap.Uint("customerID", *query.CustomerID))
	}
	userServiceID := middleware.ServiceIDFromContext(c)

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "trying to fetch commodity details for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	commodity, err := client.GetCommodityDetails(ctx, commodityID, query.CustomerID)
	if err != nil {
		var notImpl helpers.NotImplementedError
		if errors.As(err, &notImpl) {
			log.Error(
				ctx,
				"GetCommodityDetails not implemented for TMS",
				zap.String("tmsName", string(tmsIntegration.Name)),
			)
			return c.SendStatus(http.StatusNotImplemented)
		}

		log.Error(ctx, "error fetching commodity details from TMS", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(
		GetCommodityDetailsResponse{
			Commodity: commodity,
			Message:   "Successfully fetched commodity details",
		},
	)
}
