package tmsroutes

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/sentry"
)

type (
	// NOTE: If limit is not set, it defaults to 100
	GetCustomersQuery struct {
		rds.GenericGetQuery
		Retry bool `query:"retry"`
	}

	GetCustomersResponse struct {
		CustomerList []models.TMSCustomer `json:"customerList"`
		TMSTenant    string               `json:"tmsTenant"`
		Message      string               `json:"message"`
	}
)

type ctxKey string

// GetCustomers retrieves TMS customer data.
// It first attempts to fetch customers from the database unless a refresh is forced.
// If no data exists or a refresh is requested, it fetches from the TMS API directly.
// The function supports both synchronous and asynchronous operation modes.

// Returns a list of customers to initially populate the customer dropdown.
func GetCustomers(c *fiber.Ctx) error {
	var query GetCustomersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 100
	} else if query.Limit > 1000 {
		query.Limit = 1000
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))
	userServiceID := middleware.ServiceIDFromContext(c)
	var customerList []models.TMSCustomer
	var tmsIntegration models.Integration
	var err error

	if query.TMSID > 0 {
		tmsIntegration, err = integrationDB.Get(ctx, query.TMSID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warn(
					ctx,
					"trying to fetch customers for a service with no active TMS",
					zap.Uint("serviceID", userServiceID),
				)

				return c.SendStatus(http.StatusNotFound)
			}

			log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
			return err
		}
	} else {
		// TODO: For backwards-compatibility, deprecate after Vulcan v.27
		// If TMSID is not set, we assume the first TMS integration
		integrations, err := integrationDB.GetTMSListByServiceID(ctx, userServiceID)
		if err != nil {
			log.Error(ctx, "error fetching integrations from DB",
				zap.Error(err), zap.Uint("service_id", userServiceID))
			return err
		}

		switch count := len(integrations); count {
		case 0:
			log.Warn(ctx, "trying to fetch customers for a service with no active TMS",
				zap.Uint("serviceID", userServiceID))

			return c.SendStatus(http.StatusNotFound)

		case 1:
			tmsIntegration = integrations[0]

		default:
			// If this happens, need to update FE to allow user to select TMS
			log.Warn(ctx, "trying to fetch customers for a service with multiple TMS's, using first one",
				zap.Uint("serviceID", userServiceID), zap.Int("countTMS", count))

			tmsIntegration = integrations[0]

		}
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	query.TMSID = tmsIntegration.ID

	// Add retry flag to context for Aljex
	if query.Retry {
		ctx = context.WithValue(ctx, ctxKey("retry"), true)
	}

	// If we're not forcing a refresh, then get from DB.
	// If async request, get query.Limit records from DB to return to FE as a placeholder
	// while we asynchronously add the rest to DB
	if !query.ForceRefresh || query.Async {
		customerList, err = tmsCustomerDB.GetTMSCustomersByTMSID(ctx, query.GenericGetQuery)

		switch {
		case len(customerList) == 0:
			log.Info(ctx, "no customers found in DB, falling back to TMS")

		case err != nil:
			// Fail-open and try TMS; Gorm will send to Sentry
			log.WarnNoSentry(ctx, "error searching TMS customers in DB, falling back to TMS", zap.Error(err))

		case len(customerList) > 0 && !query.Async:
			return c.Status(http.StatusOK).JSON(
				GetCustomersResponse{
					CustomerList: customerList,
					TMSTenant:    tmsIntegration.Tenant,
				},
			)
		}
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if query.Async {
		go sentry.WithHub(ctx, func(ctx context.Context) {
			asyncCtx := log.InheritContext(ctx, context.Background())
			asyncCtx, cancel := context.WithTimeout(asyncCtx, 15*time.Minute)
			defer cancel()

			// Customers are upserted to db in GetCustomers() to handle partial progress
			// during large data fetches (10k-100k+ records). This deviates from our usual
			// single-responsibility pattern but is necessary for data reliability
			customerList, err = client.GetCustomers(asyncCtx)
			// Note: an empty slice of TMSCustomers is not an error, we handle it further down.
			if err != nil && err.Error() != tmsCustomerDB.EmptyTMSCustomersSlice {
				log.Error(asyncCtx, "getCustomers from TMS failed", zap.Error(err))
				return
			}

			err = integrationDB.SetColumn(
				asyncCtx,
				tmsIntegration.ID,
				integrationDB.LastCustomerUpdatedAt,
				time.Now(),
			)
			if err != nil {
				log.Warn(
					asyncCtx,
					"failed to set last customer updated at",
					zap.Error(err),
					zap.Uint("tmsID", tmsIntegration.ID),
				)
			}

			if len(customerList) == 0 {
				log.Info(asyncCtx, "no customers found in TMS")
				return
			}

			log.Info(asyncCtx, "successfully got customers from TMS", zap.Int("count", len(customerList)))
		})

		// If async request, return query.Limit records from DB to return to FE as a placeholder
		// while we asynchronously add the rest to DB
		endIndex := helpers.Min(len(customerList), query.Limit)

		return c.Status(http.StatusAccepted).JSON(
			GetCustomersResponse{
				CustomerList: customerList[0:endIndex],
				TMSTenant:    tmsIntegration.Tenant,
				Message:      "Asynchronously fetching more customers from TMS",
			},
		)
	}

	// Otherwise, fetch synchronously from TMS

	// Customers are upserted to db in GetCustomers() to handle partial progress
	// during large data fetches (10k-100k+ records). This deviates from our usual
	// single-responsibility pattern but is necessary for data reliability
	customerList, err = client.GetCustomers(ctx)
	// Note: an empty slice of TMSCustomers is not an error, we handle it further down.
	if err != nil && err.Error() != tmsCustomerDB.EmptyTMSCustomersSlice {
		log.Error(ctx, "getCustomers from TMS failed", zap.Error(err))
		return c.SendStatus(http.StatusServiceUnavailable)
	}

	err = integrationDB.SetColumn(ctx, tmsIntegration.ID, integrationDB.LastCustomerUpdatedAt, time.Now())
	if err != nil {
		log.Warn(
			ctx,
			"failed to set last customer updated at",
			zap.Error(err),
			zap.Uint("tmsID", tmsIntegration.ID),
		)
	}

	if len(customerList) == 0 {
		log.Info(ctx, "no customers found in TMS")
		return c.Status(http.StatusNotFound).JSON(
			GetCustomersResponse{
				Message: "No new or updated customers found.",
			},
		)
	}

	endIndex := helpers.Min(len(customerList), query.Limit)

	return c.Status(http.StatusOK).JSON(
		GetCustomersResponse{
			CustomerList: customerList[0:endIndex],
			TMSTenant:    tmsIntegration.Tenant,
		},
	)
}

type GetCustomersByServiceIDPath struct {
	ServiceID uint `path:"serviceID" validate:"required"`
}
type CustomerIDAndName struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func GetCustomersByServiceID(c *fiber.Ctx) error {
	var path GetCustomersByServiceIDPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := log.With(c.UserContext(), zap.Uint("serviceID", userServiceID))

	if userServiceID != path.ServiceID {
		log.WarnNoSentry(ctx, "unauthorized: serviceID from token does not match path")
		return c.SendStatus(http.StatusUnauthorized)
	}

	// get tms integrations for the service
	integrations, err := integrationDB.GetTMSListByServiceID(ctx, path.ServiceID)
	if err != nil {
		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// get customers for each integration
	var customerList []CustomerIDAndName
	for _, integration := range integrations {
		query := rds.GenericGetQuery{
			TMSID: integration.ID,
		}
		customers, err := tmsCustomerDB.GetTMSCustomersByTMSID(ctx, query)
		if err != nil {
			log.Error(ctx, "error fetching customers from DB", zap.Error(err))
			continue
		}

		for _, customer := range customers {
			customerList = append(customerList, CustomerIDAndName{
				ID:   customer.ID,
				Name: customer.Name,
			})
		}
	}

	return c.Status(http.StatusOK).JSON(customerList)
}
