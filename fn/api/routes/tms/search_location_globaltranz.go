package tmsroutes

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	SearchLocationGlobalTranzQuery struct {
		TMSID   uint   `json:"tmsID" query:"tmsID"`
		Keyword string `json:"keyword" query:"keyword"`
	}

	SearchLocationGlobalTranzResponse struct {
		Locations []globaltranztms.LocationSearchItem `json:"locations"`
		Message   string                              `json:"message,omitempty"`
	}
)

// SearchLocationGlobalTranz searches for locations using GlobalTranz SearchLocation API
func SearchLocationGlobalTranz(c *fiber.Ctx) error {
	var query SearchLocationGlobalTranzQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("keyword", query.Keyword), zap.Uint("tmsID", query.TMSID))
	userServiceID := middleware.ServiceIDFromContext(c)

	if query.Keyword == "" {
		return c.Status(http.StatusBadRequest).JSON(SearchLocationGlobalTranzResponse{
			Message: "keyword is required",
		})
	}

	tmsIntegration, err := integrationDB.Get(ctx, query.TMSID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(
				ctx,
				"trying to search locations for a service with no active TMS",
				zap.Uint("serviceID", userServiceID),
			)
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error fetching integrations from DB", zap.Error(err))
		return err
	}

	if tmsIntegration.ServiceID != userServiceID {
		log.Infof(
			ctx,
			"user serviceID %d does not match TMS service ID %d",
			userServiceID,
			tmsIntegration.ServiceID,
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	if tmsIntegration.Name != models.GlobalTranzTMS {
		return c.Status(http.StatusBadRequest).JSON(SearchLocationGlobalTranzResponse{
			Message: "this endpoint is only available for GlobalTranz TMS",
		})
	}

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	gtClient, ok := client.(*globaltranztms.GlobalTranz)
	if !ok {
		log.Error(ctx, "failed to cast client to GlobalTranz", zap.String("tmsName", string(tmsIntegration.Name)))
		return c.SendStatus(http.StatusInternalServerError)
	}

	locations, err := gtClient.SearchLocation(ctx, query.Keyword)
	if err != nil {
		var ufErr errtypes.UserFacingError
		if errors.As(err, &ufErr) {
			log.Warn(ctx, "error searching GlobalTranz locations", zap.Error(err))
			return c.Status(http.StatusBadRequest).JSON(SearchLocationGlobalTranzResponse{
				Message: ufErr.Error(),
			})
		}

		log.Error(ctx, "error searching GlobalTranz locations", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(SearchLocationGlobalTranzResponse{
		Locations: locations,
	})
}
