package quoteprivate

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	SearchLTLCustomerStaffBody struct {
		Name          string `json:"name" validate:"required"`
		ExternalTMSID string `json:"externalTMSID" validate:"required"`
	}

	GetLTLCustomerStaffResponse struct {
		CustomerList []models.TMSCustomer `json:"customerList"`
	}
)

// Searches for staff based off the customer in the integration.
func SearchLTLCustomerStaff(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	var body SearchLTLCustomerStaffBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		log.Error(ctx, "invalid request body", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "failed to get user by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var taiIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.TaiPricing {
			taiIntegration = integration
			break
		}
	}

	if taiIntegration.ID == 0 {
		log.Error(ctx, "no TAI pricing integration found for service")
		return c.SendStatus(http.StatusNotFound)
	}

	client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(user.EmailAddress))
	if err != nil {
		log.Error(ctx, "failed to create pricing client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	tmsID, err := strconv.Atoi(body.ExternalTMSID)
	if err != nil {
		log.Error(ctx, "failed to convert externalTMSID from string to int", zap.Error(err))
		return c.SendStatus(http.StatusBadRequest)
	}

	cacheKey := fmt.Sprintf("ltl-customer-staff-search:%d:%s:%d", service.ID, body.Name, tmsID)
	cachedCustomerStaff, found, err := redis.GetKey[[]models.TMSCustomer](ctx, cacheKey)
	if err != nil {
		log.Warn(
			ctx,
			"Error retrieving LTL customer staff from cache",
			zap.String("cacheKey", cacheKey),
			zap.Error(err),
		)
	} else if found {
		log.Infof(ctx, "Cache hit for LTL customer staff search", zap.String("cacheKey", cacheKey))

		return c.Status(http.StatusOK).JSON(
			GetLTLCustomerStaffResponse{
				CustomerList: cachedCustomerStaff,
			},
		)
	}

	customerList, err := client.SearchCustomerStaff(ctx, tmsID, body.Name, true)
	if err != nil {
		log.Error(ctx, "error searching TMS customers in integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(customerList) > 0 {
		cacheExpiration := 1 * time.Hour
		err := redis.SetKey(
			ctx,
			cacheKey,
			customerList,
			cacheExpiration,
		)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to cache LTL customer staff search results",
				zap.String("cacheKey", cacheKey),
				zap.Error(err),
			)
		} else {
			log.Infof(
				ctx,
				"Cached LTL customer staff search results",
				zap.String("cacheKey", cacheKey),
			)
		}
	}

	return c.Status(http.StatusOK).JSON(
		GetLTLCustomerStaffResponse{
			CustomerList: customerList,
		},
	)
}
