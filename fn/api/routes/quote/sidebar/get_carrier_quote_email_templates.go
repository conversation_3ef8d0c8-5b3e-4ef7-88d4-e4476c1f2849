package quoteprivate

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	emailTemplatesHelpers "github.com/drumkitai/drumkit/common/helpers/emailtemplates"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/config"
	"github.com/drumkitai/drumkit/common/rds/emailtemplates"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

type (
	PreparedTemplate struct {
		TemplateType       models.EmailTemplateType       `json:"templateType"`
		TemplateAccessTier models.EmailTemplateAccessTier `json:"templateAccessTier"`
		Subject            string                         `json:"subject"`
		Body               string                         `json:"body"`
		Name               string                         `json:"name"`
		CC                 []string                       `json:"cc"`
	}

	CarrierGroupTemplatesQuery struct {
		models.CarrierQuoteTemplateData
		TemplateType models.EmailTemplateType `json:"templateType"`
	}

	CarrierGroupTemplatesResponse struct {
		CarrierQuoteEmailTemplates []PreparedTemplate `json:"carrierQuoteEmailTemplates"`
	}
)

func GetCarrierGroupTemplates(c *fiber.Ctx) error {
	var query CarrierGroupTemplatesQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		log.Error(ctx, "failed to get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	carrierQuoteConfig, err := config.GetCarrierQuoteConfigByServiceID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get carrier quote config", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString("Could not get carrier quote config")
	}

	if query.TemplateType == models.CarrierQuoteByGroup &&
		(carrierQuoteConfig == nil || !carrierQuoteConfig.IsFindCarrierByGroupEnabled) {
		log.Error(ctx, "carrier quote by group is not enabled", zap.Uint("service_id", user.ServiceID))
		return c.Status(http.StatusBadRequest).SendString("Carrier quote by group is not enabled")
	}

	if query.TemplateType == models.CarrierQuoteByLocation &&
		(carrierQuoteConfig == nil || !carrierQuoteConfig.IsFindCarrierByLocationEnabled) {
		log.Error(ctx, "carrier quote by location is not enabled", zap.Uint("service_id", user.ServiceID))
		return c.Status(http.StatusBadRequest).SendString("Carrier quote by location is not enabled")
	}

	return c.Status(http.StatusOK).JSON(CarrierGroupTemplatesResponse{
		CarrierQuoteEmailTemplates: getCarrierQuoteTemplates(ctx, user, query),
	})
}

func getCarrierQuoteTemplates(
	ctx context.Context,
	user models.User,
	query CarrierGroupTemplatesQuery,
) []PreparedTemplate {

	ctx, metaSpan := otel.StartSpan(ctx, "getCarrierQuoteTemplates", nil)
	defer func() { metaSpan.End(nil) }()

	templateType := query.TemplateType
	carrierQuoteTemplateData := query.CarrierQuoteTemplateData

	userGroupIDs, err := userGroupsDB.GetAllGroupIDsByUserID(ctx, user.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, which just skips checking for user group templates
		log.Error(ctx, "error while getting user group", zap.Error(err))
	}

	carrierQuoteTemplates, err := emailtemplates.GetAllAccessibleEmailTemplatesByTypeForUser(
		ctx,
		user.ID,
		userGroupIDs,
		user.ServiceID,
		templateType,
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// Fail open, will use generic template
		log.Error(ctx, "failed to get email templates", zap.Error(err))
	}

	getTemplatesOrGeneric := func(templateType models.EmailTemplateType) []PreparedTemplate {
		preparedTemplates := []PreparedTemplate{}
		opts := []models.EmailTemplateOption{
			models.WithCarrierQuoteData(carrierQuoteTemplateData),
		}

		for _, t := range carrierQuoteTemplates {
			if t.TemplateType == templateType {
				preparedTemplates = append(
					preparedTemplates,
					prepareTemplate(ctx, templateType, t, opts...),
				)
			}
		}

		if len(preparedTemplates) == 0 {
			genericTemplate := models.GenericEmailTemplates[templateType]
			preparedTemplates = append(
				preparedTemplates,
				prepareTemplate(ctx, templateType, genericTemplate, opts...),
			)
		}

		return preparedTemplates
	}

	return getTemplatesOrGeneric(templateType)
}

func prepareTemplate(
	ctx context.Context,
	tmplType models.EmailTemplateType,
	tmpl models.EmailTemplate,
	opts ...models.EmailTemplateOption,
) PreparedTemplate {

	opts = append(opts, models.WithTemplate(tmpl))

	subject, body, err := models.PrepareEmailTemplate(tmplType, opts...)

	if err != nil {
		log.Error(ctx, "failed to prepare email template", zap.Error(err))
		return PreparedTemplate{
			TemplateType:       tmplType,
			Subject:            tmpl.Subject,
			Body:               tmpl.Body,
			TemplateAccessTier: emailTemplatesHelpers.GetAccessTier(tmpl),
		}
	}

	return PreparedTemplate{
		TemplateType:       tmplType,
		TemplateAccessTier: emailTemplatesHelpers.GetAccessTier(tmpl),
		Subject:            subject,
		Body:               body,
		Name:               tmpl.Name,
		CC:                 tmpl.CC,
	}
}
