package quoteprivate

import (
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	LTLQuickQuoteItem struct {
		PackageType           int      `json:"packageType"`
		FreightClass          string   `json:"freightClass"`
		ClassManuallySelected bool     `json:"classManuallySelected"`
		HandlingUnits         int      `json:"handlingUnits"`
		TotalWeight           int      `json:"totalWeight"`
		CommodityDescription  string   `json:"commodityDescription"`
		QuantityOfGoods       int      `json:"quantityOfGoods"`
		Length                *float32 `json:"length"`
		Width                 *float32 `json:"width"`
		Height                *float32 `json:"height"`
		CubicFeet             *float32 `json:"cubicFeet"`
		CubicMeters           *float32 `json:"cubicMeters"`
		PoundsPerCubicFeet    *float32 `json:"poundsPerCubicFeet"`
	}

	Customer struct {
		Email            string `json:"email"`
		Name             string `json:"name"`
		ExternalTMSID    string `json:"externalTmsId"`
		ExternalID       string `json:"externalId"`
		TMSIntegrationID uint   `json:"tmsIntegrationId"`
		OwnerName        string `json:"ownerName"` // Broker sales rep that manages the customer account
		OwnerID          int    `json:"ownerId"`
	}

	LTLQuickQuoteBody struct {
		// The Email the user was looking at during submission.
		EmailID        uint   `json:"emailId"`
		ThreadID       string `json:"threadId"`
		QuoteRequestID uint   `json:"quoteRequestId"`

		//nolint:lll
		TransportType models.TransportType `json:"transportType" validate:"oneof=VAN REEFER FLATBED 'BOX TRUCK' HOTSHOT SPRINTER DRAYAGE"`

		Customer Customer `json:"customer"`
		// TODO: make this required and use this value once we pull TMS customers from the DB and not the
		// integration
		CustomerExternalTMSID string               `json:"customerExternalTmsId"`
		Stops                 models.Stops         `json:"stops" validate:"min=2"`
		LineItems             []LTLQuickQuoteItem  `json:"lineItems" validate:"min=1"`
		Accessorials          []models.Accessorial `json:"accessorials"`
		WeightUnit            int                  `json:"weightUnit"`
		DimensionUnit         int                  `json:"dimensionUnit"`
		SearchByTariff        bool                 `json:"searchByTariff"`
		Stackable             bool                 `json:"stackable"`
		IsFrontOffice         bool                 `json:"isFrontOffice"`
		ShipmentType          int                  `json:"shipmentType"`
		ActualShipmentType    int                  `json:"actualShipmentType"`
		TrailerType           int                  `json:"trailerType"`
		PCMilerRoutingType    int                  `json:"pcmilerRoutingType"`

		DeclaredValue   string  `json:"declaredValue"`
		DeductibleValue float64 `json:"deductibleValue"`
		LinearFeet      *int    `json:"linearFeet"`
	}
)

func GetLTLQuickQuote(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	var body LTLQuickQuoteBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		log.Error(ctx, "invalid request body", zap.Error(err))
		return c.SendStatus(http.StatusBadRequest)
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "failed to get user by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// TODO: incorporate customer lookups once we backfill the database
	// tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, service.ID)
	// if err != nil {
	// 	log.Error(ctx, "failed to get TMS integration", zap.Error(err))
	// 	return c.SendStatus(http.StatusInternalServerError)
	// }
	//
	// if len(tmsIntegrations) == 0 {
	// 	log.Error(ctx, "no TMS integrations found", zap.Error(err))
	// 	return c.SendStatus(http.StatusInternalServerError)
	// }
	//
	// NOTE: This shouldn't always be the first TMS integration row
	// customer, err := tmsCustomerDB.GetByExternalTMSID(ctx, tmsIntegrations[0].ID, body.CustomerExternalTMSID)
	// if err != nil {
	// 	log.Error(ctx, "failed to get TMS customer", zap.Error(err))
	// 	return c.SendStatus(http.StatusInternalServerError)
	// }

	pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var taiIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.TaiPricing {
			taiIntegration = integration
			break
		}
	}

	if taiIntegration.ID == 0 {
		log.Error(ctx, "no TAI pricing integration found for service")
		return c.SendStatus(http.StatusNotFound)
	}

	client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(user.EmailAddress))
	if err != nil {
		log.Error(ctx, "failed to create pricing client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(body.Stops) < 2 {
		log.Error(ctx, "ltl quick quote requires at least 2 stops")
		return c.SendStatus(http.StatusBadRequest)
	}

	pickup := body.Stops[0]
	dropoff := body.Stops[len(body.Stops)-1]

	customerOrganizationID, err := strconv.Atoi(body.Customer.ExternalTMSID)
	if err != nil {
		log.Error(ctx, "ltl quick quote requires the external tms id", zap.Error(err))
		return c.SendStatus(http.StatusBadRequest)
	}

	// TODO: incorporate caching
	mileage, err := client.GetMileageByZipCodes(
		ctx,
		customerOrganizationID,
		body.PCMilerRoutingType,
		pickup.Address,
		dropoff.Address,
	)
	if err != nil {
		log.Warn(ctx, "failed to compute mileage", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	accIDs := extractAccessorialIDs(body.Accessorials)

	var items []pricing.LTLQuoteLineItem
	for _, it := range body.LineItems {
		qty := it.QuantityOfGoods

		if qty == 0 {
			qty = 1
		}

		cf := formatToTwoDecimals(it.CubicFeet)
		cm := formatToTwoDecimals(it.CubicMeters)
		pcf := formatToTwoDecimals(it.PoundsPerCubicFeet)

		item := pricing.LTLQuoteLineItem{
			PackageType:                      it.PackageType,
			FreightClass:                     strings.TrimSpace(it.FreightClass),
			ClassManuallySelected:            it.ClassManuallySelected,
			CubicFeet:                        cf,
			CubicMeters:                      cm,
			PoundsPerCubicFeet:               pcf,
			DimensionalWeight:                nil,
			ChargeableWeight:                 nil,
			EstimatedFreightClass:            strings.TrimSpace(it.FreightClass),
			ShowEstimatedFreightClassMessage: false,
			CubicFeetChanged:                 false,
			HandlingUnits:                    it.HandlingUnits,
			TotalWeight:                      it.TotalWeight,
			CommodityDescription:             it.CommodityDescription,
			QuantityOfGoods:                  qty,
		}

		items = append(items, item)
	}

	pickupFields, dropoffFields := buildStopTimeFields(pickup, dropoff)

	customer := body.Customer
	brokerID := customer.TMSIntegrationID
	if brokerID > math.MaxInt {
		log.Error(
			ctx,
			"TMSIntegrationID overflows int",
			zap.Uint("tmsIntegrationID", customer.TMSIntegrationID),
		)

		return c.SendStatus(http.StatusBadRequest)
	}

	req := pricing.LTLQuoteRequest{
		ShipmentID:         0,
		OriginCity:         pickup.Address.City,
		OriginState:        pickup.Address.State,
		OriginZip:          pickup.Address.Zip,
		OriginCountry:      pickup.Address.Country,
		LineItems:          items,
		BrokerID:           int(brokerID),
		CustomerStaffID:    customer.OwnerID,
		DestinationCity:    dropoff.Address.City,
		DestinationState:   dropoff.Address.State,
		DestinationZip:     dropoff.Address.Zip,
		DestinationCountry: dropoff.Address.Country,
		WeightUnit:         body.WeightUnit,
		DimensionUnit:      body.DimensionUnit,
		SearchByTariff:     body.SearchByTariff,
		Stackable:          body.Stackable,
		ShipmentType:       body.ShipmentType,
		ActualShipmentType: body.ActualShipmentType,
		// TODO: This can be one of many values but Tai's website doesn't show a selection either and defaults
		// to 40.
		// TrailerType:                  body.TrailerType,
		TrailerType:                  40,
		PickupReadyDateTime:          pickupFields.Ready,
		PickupCloseDateTime:          pickupFields.Close,
		PickupAppointmentBeginTime:   pickupFields.ApptBegin,
		PickupAppointmentEndTime:     pickupFields.ApptEnd,
		DeliveryReadyDateTime:        &dropoffFields.Ready,
		DeliveryCloseDateTime:        &dropoffFields.Close,
		DeliveryAppointmentBeginTime: dropoffFields.ApptBegin,
		DeliveryAppointmentEndTime:   dropoffFields.ApptEnd,
		Mileage:                      mileage,
		Accessorials:                 accIDs,
		IsFrontOffice:                body.IsFrontOffice,
		DeclaredValue:                body.DeclaredValue,
		DeductibleValue:              body.DeductibleValue,
		LinearFeet:                   body.LinearFeet,
	}

	quotes, err := client.GetLTLQuickQuotes(ctx, req)
	if err != nil {
		log.Error(ctx, "GetLTLQuickQuotes failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	for i := range quotes {
		quoteRecord := &quotes[i]
		rateTargetCost := quoteRecord.TotalCost
		rateData := quote.RateData{
			Distance: mileage,
		}

		created := quote.CreateQuoteRecord(
			ctx,
			service,
			user.ID,
			body.EmailID,
			body.ThreadID,
			body.QuoteRequestID,
			body.Stops,
			body.TransportType,
			pickup.ReadyTime.Time,
			dropoff.ReadyTime.Time,
			rateData,
			rateTargetCost,
			1.07*rateTargetCost,
			1.10*rateTargetCost,
			models.TaiSource,
			"",
			nil,
		)
		if created != nil {
			quoteRecord.ID = created.ID
			quoteRecord.QuoteRequestID = created.QuoteRequestID
		}
	}

	return c.Status(http.StatusOK).JSON(quotes)
}

func extractAccessorialIDs(accessorials models.Accessorials) []int {
	if len(accessorials) == 0 {
		return []int{}
	}

	ids := make([]int, 0, len(accessorials))

	for _, a := range accessorials {
		id := a.ExternalID
		ids = append(ids, id)
	}

	return ids
}

type StopTimeFields struct {
	Ready     string
	Close     string
	ApptBegin *string
	ApptEnd   *string
}

// buildStopTimeFields extracts time-related fields (ready, close, and appointment windows) from the pickup and dropoff
// stops and converts them into RFC3339 strings.
func buildStopTimeFields(pickup, dropoff models.Stop) (StopTimeFields, StopTimeFields) {
	var pickupFields, dropoffFields StopTimeFields

	pickupFields.Ready, _ = formatStopTime(pickup.ReadyTime)
	pickupFields.Close, _ = formatStopTime(pickup.ReadyEndTime)
	if s, ok := formatStopTime(pickup.ApptStartTime); ok {
		pickupFields.ApptBegin = &s
	}
	if s, ok := formatStopTime(pickup.ApptEndTime); ok {
		pickupFields.ApptEnd = &s
	}

	dropoffFields.Ready, _ = formatStopTime(dropoff.ReadyTime)
	dropoffFields.Close, _ = formatStopTime(dropoff.MustDeliver)
	if s, ok := formatStopTime(dropoff.ApptStartTime); ok {
		dropoffFields.ApptBegin = &s
	}
	if s, ok := formatStopTime(dropoff.ApptEndTime); ok {
		dropoffFields.ApptEnd = &s
	}

	return pickupFields, dropoffFields
}

// formatStopTime converts a valid models.NullTime into an RFC3339 string.
// It returns the formatted value and a boolean indicating if it's valid (true) or empty/invalid (false).
func formatStopTime(t models.NullTime) (string, bool) {
	if !t.Valid {
		return "", false
	}

	s := t.Time.Format(time.RFC3339)
	if s == "0001-01-01T00:00:00Z" {
		return "", false
	}

	return s, true
}

// formatToTwoDecimals converts optional float32 to a 2-decimal string.
// If nil, NaN, or negative, it returns "0.00".
func formatToTwoDecimals(v *float32) string {
	if v == nil {
		return "0.00"
	}

	if *v < 0 || (fmt.Sprintf("%f", *v) == "NaN") {
		return "0.00"
	}

	return fmt.Sprintf("%.2f", *v)
}
