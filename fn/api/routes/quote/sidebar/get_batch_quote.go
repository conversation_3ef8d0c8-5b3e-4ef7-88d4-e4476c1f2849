package quoteprivate

import (
	"context"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	batchQuoteDB "github.com/drumkitai/drumkit/common/rds/batch_quote"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
	quoteutil "github.com/drumkitai/drumkit/fn/api/routes/quote/util"
)

type BatchQuoteStatus string

const (
	BatchQuoteStatusSuccess BatchQuoteStatus = "success"
	BatchQuoteStatusError   BatchQuoteStatus = "error"
)

type (
	BatchQuotePrivateBody struct {
		QuickQuotes []QuickQuotePrivateBody `json:"quickQuotes"`
		// Test parameter to purposefully fail the last quote
		TestFailLastQuote bool `json:"testFailLastQuote,omitempty"`
	}

	BatchQuoteResult struct {
		QuoteRequestID uint             `json:"quoteRequestId"`
		Status         BatchQuoteStatus `json:"status"`
		Error          string           `json:"error,omitempty"`

		Quotes                  []Quote                         `json:"quotes,omitempty"`
		Stops                   []Stop                          `json:"stops,omitempty"`
		SelectedRateName        quote.SelectedRateName          `json:"selectedRateName,omitempty"`
		IsZipCodeLookup         bool                            `json:"isZipCodeLookup,omitempty"`
		InputtedTransportType   models.TransportType            `json:"inputtedTransportType,omitempty"`
		SubmittedTransportType  models.TransportType            `json:"submittedTransportType,omitempty"`
		Configuration           Configuration                   `json:"configuration,omitempty"`
		QuoteReplyDraftTemplate QuickQuoteEmailTemplateResponse `json:"quoteReplyDraftTemplate,omitempty"`
	}

	BatchQuotePrivateResponse struct {
		BatchQuoteID   uint                    `json:"batchQuoteId"`
		Results        []BatchQuoteResult      `json:"results"`
		ProcessedCount int                     `json:"processedCount"`
		ErrorCount     int                     `json:"errorCount"`
		TotalQuotes    int                     `json:"totalQuotes"`
		Errors         models.BatchQuoteErrors `json:"errors"`
	}
)

func GetBatchQuote(c *fiber.Ctx) error {
	var body BatchQuotePrivateBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	if len(body.QuickQuotes) == 0 {
		return c.Status(http.StatusBadRequest).SendString("No quote requests provided")
	}

	service, err := rds.GetServiceWithPreload(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userFound, err := userDB.GetByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Extract quote request IDs from the request
	quoteRequestIDs := make([]uint, len(body.QuickQuotes))
	for i, quickQuote := range body.QuickQuotes {
		quoteRequestIDs[i] = quickQuote.QuoteRequestID
	}

	// Search for existing batch quote that contains these quote request IDs
	var batchQuote *models.BatchQuote
	existingBatchQuotes, err := batchQuoteDB.GetBatchQuotesByQuoteRequestIDs(ctx, quoteRequestIDs)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error(ctx, "could not search for existing batch quote", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(existingBatchQuotes) > 0 {
		// Use the most recent batch quote
		batchQuote = existingBatchQuotes[0]
		batchQuote.Status = models.BatchQuoteStatusSubmitted

		if err := batchQuoteDB.UpdateBatchQuote(ctx, batchQuote); err != nil {
			log.Error(ctx, "could not update existing batch quote", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		log.Info(
			ctx,
			"updated existing batch quote status to submitted",
			zap.Uint("batchQuoteID", batchQuote.ID),
			zap.Int("quoteCount", len(body.QuickQuotes)),
		)
	} else {
		batchQuote = &models.BatchQuote{
			UserID:             userFound.ID,
			ServiceID:          service.ID,
			Status:             models.BatchQuoteStatusSubmitted,
			SourceCategory:     models.QuotingPortalSourceCategory,
			TotalQuoteRequests: len(body.QuickQuotes),
		}

		if err := batchQuoteDB.CreateBatchQuote(ctx, batchQuote); err != nil {
			log.Error(ctx, "could not create batch quote", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		log.Info(
			ctx,
			"created new batch quote",
			zap.Uint("batchQuoteID", batchQuote.ID),
			zap.Int("quoteCount", len(body.QuickQuotes)),
		)
	}

	var results []BatchQuoteResult
	var processedCount int
	var errorCount int
	batchErrors := models.BatchQuoteErrors{
		QuoteRequestErrors: make(map[uint][]string),
		QuickQuoteErrors:   make(map[uint][]string),
	}

	var totalConfidence float64
	var quickQuoteCount int
	var totalQuotes int
	var processedQuoteRequestIDs []uint

	for i, quickQuoteBody := range body.QuickQuotes {
		result := BatchQuoteResult{
			QuoteRequestID: quickQuoteBody.QuoteRequestID,
		}

		// Artificially fail the last quote if test parameter is enabled
		if handleTestFailure(body.TestFailLastQuote, i, len(body.QuickQuotes)) {
			result = createTestFailureResult(ctx, quickQuoteBody.QuoteRequestID, i, &batchErrors)
			errorCount++
		} else {
			resp, err := processIndividualQuote(ctx, &quickQuoteBody, service, userFound)
			if err != nil {
				errorCount++
				result.Status = BatchQuoteStatusError
				result.Error = err.Error()
				batchErrors.QuoteRequestErrors[quickQuoteBody.QuoteRequestID] = append(
					batchErrors.QuoteRequestErrors[quickQuoteBody.QuoteRequestID],
					err.Error(),
				)
				log.Error(
					ctx,
					"failed to process quote",
					zap.Int("index", i),
					zap.Uint("quoteRequestID", quickQuoteBody.QuoteRequestID),
					zap.Error(err),
				)
			} else {
				processedCount++
				result.Status = BatchQuoteStatusSuccess

				batchQuotes := make([]Quote, len(resp.Quotes))
				for j, q := range resp.Quotes {
					batchQuotes[j] = Quote{
						ID:       q.ID,
						Source:   q.Source,
						Type:     q.Type,
						Rates:    q.Rates,
						Distance: q.Distance,
						Metadata: q.Metadata,
					}
				}

				result.Quotes = batchQuotes
				result.Stops = resp.Stops
				result.SelectedRateName = resp.SelectedRateName
				result.IsZipCodeLookup = resp.IsZipCodeLookup
				result.InputtedTransportType = resp.InputtedTransportType
				result.SubmittedTransportType = resp.SubmittedTransportType
				result.Configuration = resp.Configuration
				result.QuoteReplyDraftTemplate = resp.QuoteReplyDraftTemplate

				processedQuoteRequestIDs = append(processedQuoteRequestIDs, quickQuoteBody.QuoteRequestID)
				totalQuotes += len(resp.Quotes)

				// Calculate confidence for batch average
				for _, quote := range resp.Quotes {
					if conf, ok := quote.Metadata["confidenceLevel"].(float64); ok {
						totalConfidence += conf
						quickQuoteCount++
					}
				}
			}
		}

		results = append(results, result)
	}

	if len(processedQuoteRequestIDs) > 0 {
		// For existing batch quotes, we already have associations, so just update summary
		if len(existingBatchQuotes) > 0 {
			// Update batch quote summary fields only
			averageConfidence := 0.0
			if quickQuoteCount > 0 {
				averageConfidence = totalConfidence / float64(quickQuoteCount)
			}

			if err := batchQuoteDB.UpdateBatchQuoteWithOptions(
				ctx,
				batchQuote.ID,
				batchQuoteDB.UpdateBatchQuoteOptions{
					AverageConfidence: &averageConfidence,
					ProcessingErrors:  &batchErrors,
				},
			); err != nil {
				log.Warn(ctx, "failed to update batch quote summary", zap.Error(err))
			}
		} else {
			// For new batch quotes, create associations and update summary in one transaction
			averageConfidence := 0.0
			if quickQuoteCount > 0 {
				averageConfidence = totalConfidence / float64(quickQuoteCount)
			}

			if err := batchQuoteDB.UpdateBatchQuoteWithOptions(
				ctx,
				batchQuote.ID,
				batchQuoteDB.UpdateBatchQuoteOptions{
					QuoteRequestIDs:     processedQuoteRequestIDs,
					UpdateAssociations:  true,
					AppendQuoteRequests: true,
					AverageConfidence:   &averageConfidence,
					ProcessingErrors:    &batchErrors,
					UpdateSummaryFields: true,
				},
			); err != nil {
				log.Warn(
					ctx,
					"failed to update batch quote with associations and summary",
					zap.Error(err),
					zap.Uint("batchQuoteID", batchQuote.ID),
				)
			}
		}
	}

	response := BatchQuotePrivateResponse{
		BatchQuoteID:   batchQuote.ID,
		Results:        results,
		ProcessedCount: processedCount,
		ErrorCount:     errorCount,
		TotalQuotes:    totalQuotes,
		Errors:         batchErrors,
	}

	return c.Status(http.StatusOK).JSON(response)
}

func processIndividualQuote(
	ctx context.Context,
	body *QuickQuotePrivateBody,
	service models.Service,
	user models.User,
) (*QuickQuotePrivateResponse, error) {

	customer, pickupDate, deliveryDate, err := quoteutil.PrepareCustomerAndDates(
		ctx,
		service,
		body.CustomerName,
		body.PickupDate,
		body.DeliveryDate,
	)
	if err != nil {
		return nil, err
	}

	body.PickupDate = pickupDate
	body.DeliveryDate = deliveryDate

	resp, err := GetQuickQuotes(ctx, body, service, user, customer)
	if err != nil {
		return nil, err
	}

	// Optionally include lane rate from service (e.g., Trident-backed DAT) into quotes list
	if service.IsGetLaneRateFromServiceEnabled {
		serviceQuotes, svcErr := GetServiceQuotes(
			ctx,
			service,
			user,
			body.EmailID,
			body.ThreadID,
			body.QuoteRequestID,
			body.NewStops,
			body.TransportType,
			body.PickupDate,
			body.DeliveryDate,
		)
		if svcErr != nil {
			log.Warn(ctx, "failed to fetch service lane rate for batch quote", zap.Error(svcErr))
		} else if len(serviceQuotes) > 0 {
			resp.Quotes = append(resp.Quotes, serviceQuotes...)
		}
	}

	// Update the quote request status to InFlight
	pickupLocation := models.Address{
		City:  resp.Stops[0].City,
		State: resp.Stops[0].State,
		Zip:   resp.Stops[0].Zip,
	}

	dropoffLocation := models.Address{
		City:  resp.Stops[1].City,
		State: resp.Stops[1].State,
		Zip:   resp.Stops[1].Zip,
	}

	appliedFields := models.QuoteLoadInfo{
		CustomerID:       customer.ID,
		TransportType:    resp.InputtedTransportType,
		PickupLocation:   pickupLocation,
		PickupDate:       models.NullTime{Time: body.PickupDate, Valid: true},
		DeliveryLocation: dropoffLocation,
		DeliveryDate:     models.NullTime{Time: body.DeliveryDate, Valid: true},
		Stops:            body.NewStops,
	}

	err = quoteRequestDB.UpdateQuoteRequestStatus(
		ctx,
		body.QuoteRequestID,
		&appliedFields,
		&user,
		models.InFlight,
		nil,
		body.SelectedQuickQuoteID,
		nil,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to update quote request status",
			zap.Uint("quoteRequestID", body.QuoteRequestID),
			zap.Error(err),
		)
	}

	resp.QuoteRequestID = body.QuoteRequestID

	var lowConfidenceThreshold int
	if service.QuickQuoteConfig != nil {
		lowConfidenceThreshold = service.QuickQuoteConfig.LowConfidenceThreshold
	}

	showLowConfWarning := showLowConfidenceWarning(lowConfidenceThreshold, resp.Quotes)
	if showLowConfWarning && !service.QuickQuoteConfig.OmitUnderLowThreshold {
		resp.Configuration.BelowThresholdMessage = service.QuickQuoteConfig.BelowThresholdMessage
	}

	quoteReplyTemplate := getQuoteReplyTemplate(ctx, user)

	resp.QuoteReplyDraftTemplate = QuickQuoteEmailTemplateResponse{
		Subject: quoteReplyTemplate.Subject,
		Body:    quoteReplyTemplate.Body,
	}

	return resp, nil
}

// handleTestFailure checks if this quote should be artificially failed for testing
// Returns true if the quote should fail, false otherwise
func handleTestFailure(testFailLastQuote bool, currentIndex int, totalQuotes int) bool {
	return testFailLastQuote && currentIndex == totalQuotes-1
}

// createTestFailureResult creates a BatchQuoteResult for an artificially failed quote
func createTestFailureResult(
	ctx context.Context,
	quoteRequestID uint,
	index int,
	batchErrors *models.BatchQuoteErrors,
) BatchQuoteResult {

	errorMsg := "Artificially failed for testing purposes"

	batchErrors.QuoteRequestErrors[quoteRequestID] = append(
		batchErrors.QuoteRequestErrors[quoteRequestID],
		errorMsg,
	)

	log.Info(
		ctx,
		"artificially failed quote for testing",
		zap.Int("index", index),
		zap.Uint("quoteRequestID", quoteRequestID),
	)

	return BatchQuoteResult{
		QuoteRequestID: quoteRequestID,
		Status:         BatchQuoteStatusError,
		Error:          errorMsg,
	}
}
