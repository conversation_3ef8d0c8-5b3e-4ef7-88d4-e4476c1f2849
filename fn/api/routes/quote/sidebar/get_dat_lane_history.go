package quoteprivate

import (
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing/dat"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	DATLaneHistoryRequestQuery struct {
		OriginCity       string               `json:"originCity"`
		OriginState      string               `json:"originState"`
		DestinationCity  string               `json:"destinationCity"`
		DestinationState string               `json:"destinationState"`
		Equipment        models.TransportType `json:"equipment"`
		FromYear         int                  `json:"fromYear"`
		FromMonth        int                  `json:"fromMonth"`
		ToYear           int                  `json:"toYear"`
		ToMonth          int                  `json:"toMonth"`
	}

	DATLaneHistoryResponse struct {
		// HotShot and Box Truck are proxied with Flatbed and Van, respectively.
		// Use the following to inform users of any proxies used to complete request.
		InputtedTransportType  models.TransportType  `json:"inputtedTransportType"`
		SubmittedTransportType models.TransportType  `json:"submittedTransportType"`
		Rates                  []dat.LaneHistoryRate `json:"rates"`
	}

	DATLaneHistoryError string
)

// Map external error messages to FE user-facing messages
var (
	LaneHistoryFeatureFlagNotEnabled  DATLaneHistoryError = "laneHistoryFeatureFlagNotEnabled"
	LaneHistoryDATIntegrationNotFound DATLaneHistoryError = "laneHistoryDatIntegrationNotFound"
	LaneHistoryGenericDATError        DATLaneHistoryError = "laneHistoryGenericDATError"
	LaneHistoryNoData                 DATLaneHistoryError = "laneHistoryNoData"
	LaneHistoryUnsupportedLaneType    DATLaneHistoryError = "laneHistoryUnsupportedLaneType"

	DATLaneHistoryErrors = map[DATLaneHistoryError]string{
		LaneHistoryFeatureFlagNotEnabled:  "Your service does not allow for DAT lane history",
		LaneHistoryDATIntegrationNotFound: "No DAT integration found for this service",
		LaneHistoryGenericDATError:        "Error retrieving lane history, please verify your inputs and try again",
		LaneHistoryNoData:                 "No historical rate data available for this lane",
		LaneHistoryUnsupportedLaneType:    "DAT failed to validate lane, please verify your inputs and try again",
	}
)

func GetDATLaneHistory(c *fiber.Ctx) error {
	var query DATLaneHistoryRequestQuery
	ctx := log.With(c.UserContext())

	if err := api.Parse(c, nil, &query, nil); err != nil {
		log.Error(ctx, "failed to parse request query", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	ctx = log.With(ctx, zap.Any("request", query))

	log.Debug(ctx, "received DAT lane history request", zap.Any("request", query))

	user, err := rds.GetUserByEmail(c.UserContext(), email)
	if err != nil {
		log.Error(ctx, "failed to get user by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsOnDemandDATLaneHistoryEnabled {
		log.Error(ctx, "on-demand DAT lane history is not enabled for this service")
		return c.Status(http.StatusUnauthorized).
			SendString(DATLaneHistoryErrors[LaneHistoryFeatureFlagNotEnabled])
	}

	pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var datIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.DAT {
			datIntegration = integration
			break
		}
	}

	if datIntegration.ID == 0 {
		log.Error(ctx, "no DAT integration found for service")
		return c.Status(http.StatusNotFound).SendString(DATLaneHistoryErrors[LaneHistoryDATIntegrationNotFound])
	}

	datClient, err := dat.New(ctx, datIntegration, user.DATEmailAddress)
	if err != nil {
		log.Error(ctx, "failed to create DAT client", zap.Error(err))
		return c.Status(http.StatusInternalServerError).SendString(DATLaneHistoryErrors[LaneHistoryGenericDATError])
	}

	submittedTransportType := models.TransportType(mapTransportType(query.Equipment))

	req := &dat.GetLaneHistoryRequest{
		Origin: dat.InputLocation{
			City:            query.OriginCity,
			StateOrProvince: query.OriginState,
		},
		Destination: dat.InputLocation{
			City:            query.DestinationCity,
			StateOrProvince: query.DestinationState,
		},
		RateType:  dat.SpotRateType,
		Equipment: submittedTransportType,
		FromYearMonth: dat.YearMonth{
			Year:  query.FromYear,
			Month: query.FromMonth,
		},
		ToYearMonth: dat.YearMonth{
			Year:  query.ToYear,
			Month: query.ToMonth,
		},
	}

	resp, err := datClient.GetLaneHistory(ctx, req)
	if err != nil {
		log.Error(ctx, "failed to get DAT lane history", zap.Error(err))

		originalErrMessage := strings.ToLower(err.Error())
		errMessage := DATLaneHistoryErrors[LaneHistoryGenericDATError]

		if strings.Contains(originalErrMessage, "unsupported lane type specified") {
			errMessage = DATLaneHistoryErrors[LaneHistoryNoData]
		}

		return c.Status(http.StatusBadRequest).SendString(errMessage)
	}

	if len(resp.RateResponse.Response.Rates) == 0 {
		log.Error(ctx, "DAT lane history response is empty")
		return c.Status(http.StatusNotFound).SendString(DATLaneHistoryErrors[LaneHistoryNoData])
	}

	log.Info(
		ctx,
		"successfully retrieved DAT lane history",
		zap.Int("ratesCount", len(resp.RateResponse.Response.Rates)),
	)

	historicalRatesWithFuel := make([]dat.LaneHistoryRate, 0)
	for _, rate := range resp.RateResponse.Response.Rates {
		historicalRatesWithFuel = append(historicalRatesWithFuel, includeFuelForHistoricalRates(rate))
	}

	return c.Status(http.StatusOK).JSON(DATLaneHistoryResponse{
		InputtedTransportType:  query.Equipment,
		SubmittedTransportType: submittedTransportType,
		Rates:                  historicalRatesWithFuel,
	})
}

func includeFuelForHistoricalRates(rateData dat.LaneHistoryRate) dat.LaneHistoryRate {
	updatedRate := rateData

	// Per-mile rates
	updatedRate.PerMile.RateUSD = rateData.PerMile.RateUSD + rateData.AverageFuelSurchargePerMileUsd
	updatedRate.PerMile.LowUSD = rateData.PerMile.LowUSD + rateData.AverageFuelSurchargePerMileUsd
	updatedRate.PerMile.HighUSD = rateData.PerMile.HighUSD + rateData.AverageFuelSurchargePerMileUsd

	// Per-trip rates
	updatedRate.PerTrip.RateUSD = rateData.PerTrip.RateUSD + rateData.AverageFuelSurchargePerTripUsd
	updatedRate.PerTrip.LowUSD = rateData.PerTrip.LowUSD + rateData.AverageFuelSurchargePerTripUsd
	updatedRate.PerTrip.HighUSD = rateData.PerTrip.HighUSD + rateData.AverageFuelSurchargePerTripUsd

	return updatedRate
}
