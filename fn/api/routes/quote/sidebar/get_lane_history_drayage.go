package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/hardfinhq/go-date"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

// searchDrayageLoadsByTierFromRDS searches for drayage loads using the Stops table
// where stop 0 (pickup) and stop 2 (return to port) are at the same location.
func searchDrayageLoadsByTierFromRDS(
	ctx context.Context,
	tier LaneTier,
	body LaneHistoryRequestBody,
	userServiceID uint,
	searchFrom,
	searchTo time.Time,
	quoteMileDistance float64,
	tmsIntegrationName models.IntegrationName,
	numLookbackDays int,
) (*SourceHistory, error) {

	log.Info(
		ctx,
		"starting drayage tier search using Stops table",
		zap.String("tier", string(tier)),
		zap.Time("searchFrom", searchFrom),
		zap.Time("searchTo", searchTo),
	)

	var loads []models.Load
	db := rds.WithContextReader(ctx)

	// Convert time.Time to UTC first, then to date.Date for proper date-only comparison
	fromDate := date.Date{
		Year:  searchFrom.UTC().Year(),
		Month: searchFrom.UTC().Month(),
		Day:   searchFrom.UTC().Day(),
	}

	toDate := date.Date{
		Year:  searchTo.UTC().Year(),
		Month: searchTo.UTC().Month(),
		Day:   searchTo.UTC().Day(),
	}

	// Query loads using stop filters via EXISTS subqueries
	// Drayage pattern expects exactly 3 stops (0, 1, 2) where 0 and 2 share the same location.
	query := db.Table("loads").
		Select("loads.id, loads.created_at, loads.pickup_date, loads.carrier_name, " +
			"loads.freight_tracking_id, loads.external_tms_id, " +
			"loads.ratedata_carrier_line_haul_charge, " +
			"loads.ratedata_carrier_cost, loads.ratedata_carrier_cost_currency, " +
			"loads.specifications_total_distance, loads.specifications_transport_type, " +
			"loads.specifications_order_type, " +
			"loads.pickup_city, loads.pickup_state, loads.pickup_zip_prefix, " +
			"loads.consignee_city, loads.consignee_state, loads.consignee_zip_prefix")

	// Base query conditions that apply to all tiers
	query = query.Where(
		"loads.service_id = ? "+
			"AND loads.pickup_date IS NOT NULL "+
			"AND loads.pickup_date BETWEEN ? AND ? "+
			"AND loads.ratedata_carrier_cost IS NOT NULL ",
		userServiceID, fromDate, toDate)

	// Handle transport type matching using strict equality for index usage
	query = query.Where(
		"upper(loads.specifications_transport_type) = ?",
		"DRAYAGE",
	)

	// Drayage supports 2-stop and 3-stop patterns:
	// - 2 stops: 0 -> 1 (no stop 2)
	// - 3 stops: 0 -> 1 -> 2, where stop 0 and stop 2 share location
	query = query.Where(
		"(NOT EXISTS (" +
			"SELECT 1 FROM stops " +
			"WHERE load_id = loads.id " +
			"AND stop_number = 2" +
			")) OR EXISTS (" +
			"SELECT 1 FROM stops first_stop " +
			"JOIN stops last_stop ON last_stop.load_id = first_stop.load_id " +
			"AND last_stop.stop_number = 2 " +
			"WHERE first_stop.load_id = loads.id " +
			"AND first_stop.stop_number = 0 " +
			"AND lower(trim(last_stop.city)) = lower(trim(first_stop.city)) " +
			"AND lower(trim(last_stop.state)) = lower(trim(first_stop.state))" +
			")",
	)

	// Ensure stop 1 exists (consignee) for drayage pattern
	query = query.Where(
		"EXISTS (" +
			"SELECT 1 FROM stops " +
			"WHERE load_id = loads.id " +
			"AND stop_number = 1" +
			")",
	)

	// Ensure there are no stops beyond stop 2 (exactly 2 or 3 stops)
	query = query.Where(
		"NOT EXISTS (" +
			"SELECT 1 FROM stops " +
			"WHERE load_id = loads.id " +
			"AND stop_number > 2" +
			")",
	)

	// Add tier-specific conditions for drayage
	// Match origin to first stop (stop 0) and destination to consignee stop (stop 1)
	switch tier {
	case _3DigitZipLaneTier:
		if len(body.OriginZip) < 3 || len(body.DestinationZip) < 3 {
			log.Info(
				ctx,
				"insufficient zip for 3-digit zip tier drayage",
				zap.String("originZip", body.OriginZip),
				zap.String("destZip", body.DestinationZip),
			)
			return nil, errors.New("insufficient zip provided for 3-digit zip tier drayage")
		}
		originPrefix := body.OriginZip[:3]
		destPrefix := body.DestinationZip[:3]
		// Match origin to first stop, destination to consignee stop
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops first_stop "+
				"WHERE first_stop.load_id = loads.id "+
				"AND first_stop.stop_number = 0 "+
				"AND left(trim(first_stop.zip), 3) = ?"+
				")",
			originPrefix,
		)
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops consignee_stop "+
				"WHERE consignee_stop.load_id = loads.id "+
				"AND consignee_stop.stop_number = 1 "+
				"AND left(trim(consignee_stop.zip), 3) = ?"+
				")",
			destPrefix,
		)

	case CityLaneTier:
		if helpers.IsBlank(body.OriginCity) || helpers.IsBlank(body.OriginState) ||
			helpers.IsBlank(body.DestinationCity) || helpers.IsBlank(body.DestinationState) {
			log.Info(
				ctx,
				"insufficient city/state data for city tier drayage",
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
				zap.String("destCity", body.DestinationCity),
				zap.String("destState", body.DestinationState),
			)
			return nil, errors.New("insufficient city/state provided for city/state tier drayage")
		}

		// Match origin to first stop, destination to consignee stop
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops first_stop "+
				"WHERE first_stop.load_id = loads.id "+
				"AND first_stop.stop_number = 0 "+
				"AND lower(trim(first_stop.city)) = lower(trim(?)) "+
				"AND lower(trim(first_stop.state)) = lower(trim(?))"+
				")",
			body.OriginCity, body.OriginState,
		)
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops consignee_stop "+
				"WHERE consignee_stop.load_id = loads.id "+
				"AND consignee_stop.stop_number = 1 "+
				"AND lower(trim(consignee_stop.city)) = lower(trim(?)) "+
				"AND lower(trim(consignee_stop.state)) = lower(trim(?))"+
				")",
			body.DestinationCity, body.DestinationState,
		)

	case StateLaneTier:
		if helpers.IsBlank(body.OriginState) || helpers.IsBlank(body.DestinationState) {
			log.Info(
				ctx,
				"insufficient state data for state tier drayage",
				zap.String("originState", body.OriginState),
				zap.String("destState", body.DestinationState),
			)
			return nil, errors.New("insufficient state provided for state tier drayage")
		}

		// Match origin state to first stop, destination state to consignee stop
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops first_stop "+
				"WHERE first_stop.load_id = loads.id "+
				"AND first_stop.stop_number = 0 "+
				"AND lower(trim(first_stop.state)) = lower(trim(?))"+
				")",
			body.OriginState,
		)
		query = query.Where(
			"EXISTS ("+
				"SELECT 1 FROM stops consignee_stop "+
				"WHERE consignee_stop.load_id = loads.id "+
				"AND consignee_stop.stop_number = 1 "+
				"AND lower(trim(consignee_stop.state)) = lower(trim(?))"+
				")",
			body.DestinationState,
		)
	}

	// Order by created_at descending to get the most recent loads first
	query = query.Order("created_at DESC")

	if err := query.Preload("Stops").Find(&loads).Error; err != nil {
		log.Error(
			ctx,
			"error querying drayage loads for tier",
			zap.String("tier", string(tier)),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error querying drayage loads for tier %s: %w", tier, err)
	}

	// McLeod can have LTL loads that show up with "TL" mode, we need to filter them out by order type
	if tmsIntegrationName == models.McleodEnterprise || tmsIntegrationName == models.Mcleod {
		loads = filterOutLTLOrderTypeLoads(loads)
	}

	if len(loads) == 0 {
		log.Info(
			ctx,
			"no matching drayage loads found for tier",
			zap.String("tier", string(tier)),
			zap.String("transportType", string(body.TransportType)),
		)
		return nil, nil
	}

	log.Info(
		ctx,
		"found matching drayage loads for tier",
		zap.String("tier", string(tier)),
		zap.Int("loadCount", len(loads)),
	)

	// Process the loads into the expected format using percentile calculations
	weekDataMap := apiutil.CalculateWeekDataPercentile(ctx, loads, quoteMileDistance, numLookbackDays)
	orderedData := apiutil.OrderWeekDataPercentile(weekDataMap, numLookbackDays/7)
	calculatedQuote := apiutil.CalculateQuotePercentile(weekDataMap, quoteMileDistance)

	return &SourceHistory{
		Source:                tmsIntegrationName,
		Timeframe:             fmt.Sprintf("%d-day", numLookbackDays),
		LaneTier:              tier,
		InputtedTransportType: body.TransportType,
		ProxiedTransportType:  body.TransportType,
		CalculatedQuote:       calculatedQuote,
		Weeks:                 orderedData,
		IsPercentile:          true,
		HistoryLoads:          getHistoryLoads(ctx, loads),
	}, nil
}
