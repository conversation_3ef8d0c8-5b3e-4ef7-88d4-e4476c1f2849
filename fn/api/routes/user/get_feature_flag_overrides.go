package user

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type GetUserFeatureFlagOverridesPath struct {
	UserID uint `params:"userID" validate:"required"`
}

type GetUserFeatureFlagOverridesResponse struct {
	UserID               uint                        `json:"userId"`
	FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides"`
}

func GetUserFeatureFlagOverrides(c *fiber.Ctx) error {
	ctx := c.UserContext()
	authenticatedUserID := middleware.UserIDFromContext(c)

	authenticatedUser, err := rds.GetUserByID(ctx, authenticatedUserID)
	if err != nil {
		log.Error(ctx, "error getting authenticated user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(authenticatedUser.EmailAddress) {
		log.Warn(
			ctx,
			"unauthorized access attempt to user feature flag overrides",
			zap.String("email", authenticatedUser.EmailAddress),
		)
		return c.SendStatus(http.StatusForbidden)
	}

	var path GetUserFeatureFlagOverridesPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		log.Error(
			ctx,
			fmt.Sprintf("error validating user id %d as param for fetching feature flags overrides", path.UserID),
			zap.Error(err),
		)

		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	user, err := rds.GetUserByID(ctx, path.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(
				ctx,
				fmt.Sprintf("couldn't find user with id %d to fetch its feature flags overrides", path.UserID),
				zap.Error(err),
			)

			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("user with id %d not found", path.UserID),
			)
		}

		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Initialize empty map if nil
	if user.FeatureFlagOverrides == nil {
		user.FeatureFlagOverrides = make(models.FeatureFlagOverrides)
	}

	return c.Status(http.StatusOK).JSON(GetUserFeatureFlagOverridesResponse{
		UserID:               user.ID,
		FeatureFlagOverrides: user.FeatureFlagOverrides,
	})
}
