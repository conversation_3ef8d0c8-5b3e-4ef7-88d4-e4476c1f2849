package user

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/fn/api/routes/service"
)

type (
	UpdateUserFeatureFlagOverridesPath struct {
		UserID uint `params:"userID" validate:"required"`
	}

	UpdateUserFeatureFlagOverridesBody struct {
		FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides" validate:"required"`
	}

	UpdateUserFeatureFlagOverridesResponse struct {
		UserID               uint                        `json:"userId"`
		FeatureFlagOverrides models.FeatureFlagOverrides `json:"featureFlagOverrides"`
	}
)

func UpdateUserFeatureFlagOverrides(c *fiber.Ctx) error {
	ctx := c.UserContext()
	authenticatedUserID := middleware.UserIDFromContext(c)

	authenticatedUser, err := rds.GetUserByID(ctx, authenticatedUserID)
	if err != nil {
		log.Error(ctx, "error getting authenticated user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(authenticatedUser.EmailAddress) {
		log.Warn(
			ctx,
			"unauthorized access attempt to update user feature flag overrides",
			zap.String("email", authenticatedUser.EmailAddress),
		)
		return c.SendStatus(http.StatusForbidden)
	}

	var path UpdateUserFeatureFlagOverridesPath
	var body UpdateUserFeatureFlagOverridesBody
	if err := api.Parse(c, &path, nil, &body); err != nil {
		log.Error(
			ctx,
			fmt.Sprintf("error validating request for updating feature flags overrides for user %d", path.UserID),
			zap.Error(err),
		)

		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx = log.With(ctx, zap.Any("pathParams", path), zap.Any("body", body))

	user, err := rds.GetUserByID(ctx, path.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(
				ctx,
				fmt.Sprintf("couldn't find user with id %d to update its feature flags overrides", path.UserID),
				zap.Error(err),
			)

			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("user with id %d not found", path.UserID),
			)
		}

		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Validate that all override keys are valid feature flag JSON tag names
	validFlags := service.GetValidFeatureFlags()
	for jsonTagName := range body.FeatureFlagOverrides {
		isValid := false
		for _, validFlag := range validFlags {
			if strings.EqualFold(jsonTagName, validFlag) {
				isValid = true
				break
			}
		}
		if !isValid {
			errorMsg := fmt.Sprintf("invalid feature flag: %s. Valid feature flags are: %s",
				jsonTagName, strings.Join(validFlags, ", "))
			log.Error(ctx, "invalid feature flag name", zap.String("featureFlag", jsonTagName))
			return c.Status(http.StatusBadRequest).SendString(errorMsg)
		}
	}

	user.FeatureFlagOverrides = body.FeatureFlagOverrides

	if err := rds.Update(ctx, &user); err != nil {
		log.Error(ctx, "error updating user feature flag overrides", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	log.Info(
		ctx,
		"user feature flag overrides updated",
		zap.Uint("userID", path.UserID),
		zap.Int("overrideCount", len(body.FeatureFlagOverrides)),
	)

	return c.Status(http.StatusOK).JSON(UpdateUserFeatureFlagOverridesResponse{
		UserID:               user.ID,
		FeatureFlagOverrides: user.FeatureFlagOverrides,
	})
}
