package user

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type SearchUsersQuery struct {
	Name      string `query:"name"`
	Email     string `query:"email"`
	ServiceID uint   `query:"serviceID"`
	Limit     int    `query:"limit"`
}

type SearchUsersResponse struct {
	Users []Summary `json:"users"`
}

func SearchUsers(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(ctx, "unauthorized access attempt to searchUsers", zap.String("email", user.EmailAddress))
		return c.SendStatus(http.StatusForbidden)
	}

	var query SearchUsersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if query.Limit <= 0 {
		query.Limit = 50
	}

	searchQuery := userDB.SearchUsersQuery{
		Name:      query.Name,
		Email:     query.Email,
		ServiceID: query.ServiceID,
		Limit:     query.Limit,
	}

	users, err := userDB.FuzzySearchByNameOrEmail(ctx, searchQuery)
	if err != nil {
		log.Error(ctx, "error searching users", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	userSummaries := make([]Summary, len(users))
	for i, u := range users {
		userSummaries[i] = Summary{
			ID:           u.ID,
			EmailAddress: u.EmailAddress,
			Name:         u.Name,
			ServiceID:    u.ServiceID,
		}
	}

	return c.Status(http.StatusOK).JSON(SearchUsersResponse{
		Users: userSummaries,
	})
}
