package user

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

type ListUsersResponse struct {
	Users []Summary `json:"users"`
}

type Summary struct {
	ID           uint   `json:"id"`
	EmailAddress string `json:"emailAddress"`
	Name         string `json:"name"`
	ServiceID    uint   `json:"serviceId"`
}

func ListUsers(c *fiber.Ctx) error {
	ctx := c.UserContext()
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "error getting user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Only allow admin users to access this endpoint
	if !perms.IsAdmin(user.EmailAddress) {
		log.Warn(ctx, "unauthorized access attempt to listUsers", zap.String("email", user.EmailAddress))
		return c.SendStatus(http.StatusForbidden)
	}

	itemCount := 50
	if itemCountStr := c.Query("itemCount"); itemCountStr != "" {
		parsed, err := strconv.Atoi(itemCountStr)
		if err != nil {
			log.Warn(ctx, "invalid itemCount query parameter", zap.String("itemCount", itemCountStr), zap.Error(err))
		} else if parsed > 0 {
			itemCount = parsed
		}
	}

	users, err := rds.GetAllUsers(ctx)
	if err != nil {
		log.Error(ctx, "error getting all users", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if itemCount < len(users) {
		users = users[:itemCount]
	}

	userSummaries := make([]Summary, len(users))
	for i, u := range users {
		userSummaries[i] = Summary{
			ID:           u.ID,
			EmailAddress: u.EmailAddress,
			Name:         u.Name,
			ServiceID:    u.ServiceID,
		}
	}

	return c.Status(http.StatusOK).JSON(ListUsersResponse{
		Users: userSummaries,
	})
}
