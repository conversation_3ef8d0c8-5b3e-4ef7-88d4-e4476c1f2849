package apptutil

// TODO: support multi-stop appt scheduling
// GetStopTypeAndNumber returns the string representation and stop number for a given StopType. And is used in
// the 4 ways appts can be created: new request appt email, copy request appt email, submit appt, and confirm appt.
func GetStopTypeAndNumber(stopType StopType) (string, int) {
	if stopType == DropoffStopType {
		return string(DropoffStopType), 1
	}

	return string(PickupStopType), 0
}
