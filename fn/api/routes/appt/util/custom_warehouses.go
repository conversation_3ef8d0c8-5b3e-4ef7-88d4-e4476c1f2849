package apptutil

import (
	"context"
	"crypto/sha256"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

// Custom warehouses are created by Drumkit when a scheduling integration/portal doesn't have or expose it's own
// native warehouses/locations.
// We create custom warehouses so that we can record customer x warehouse x scheduling integraiton usage (to provide
// smart suggestion for scheduling integrations/logins).
//nolint:lll
// See Notion: https://www.notion.so/drumkitai/Scheduler-Smart-Suggesting-Portals-Logins-2732b16b087a80bd846aff4f494f78d8?source=copy_link

type StopType string

const (
	PickupStopType  StopType = "pickup"
	DropoffStopType StopType = "dropoff"
)

// LoadToCustomWarehouse builds a custom warehouse object from load information based on stop type.
func LoadToCustomWarehouse(
	load *models.Load,
	stopType StopType,
	source models.WarehouseSource,
) models.Warehouse {
	warehouseName := helpers.Ternary(
		stopType == PickupStopType, load.Pickup.Name, load.Consignee.Name,
	)
	addressLine1 := helpers.Ternary(
		stopType == PickupStopType, load.Pickup.AddressLine1, load.Consignee.AddressLine1,
	)
	addressLine2 := helpers.Ternary(
		stopType == PickupStopType,
		load.Pickup.City+", "+load.Pickup.State+" "+load.Pickup.Zipcode,
		load.Consignee.City+", "+load.Consignee.State+" "+load.Consignee.Zipcode,
	)
	warehouseFullAddress := addressLine1 + " " + addressLine2
	warehouseFullIdentifier := warehouseName + " " + warehouseFullAddress
	warehouseTimezone := helpers.Ternary(
		stopType == PickupStopType, load.Pickup.Timezone, load.Consignee.Timezone,
	)

	// Deterministic external warehouse ID based on source + normalized address
	normalizedAddress := strings.ToLower(strings.TrimSpace(warehouseFullAddress))
	idSeed := string(source) + "|" + normalizedAddress
	deterministicID := fmt.Sprintf("%x", sha256.Sum256([]byte(idSeed)))

	return models.Warehouse{
		WarehouseID:             deterministicID,
		WarehouseName:           warehouseName,
		WarehouseAddressLine1:   addressLine1,
		WarehouseAddressLine2:   addressLine2,
		WarehouseFullAddress:    warehouseFullAddress,
		WarehouseTimezone:       warehouseTimezone,
		WarehouseFullIdentifier: warehouseFullIdentifier,
		Source:                  source,
	}
}

// CreateCustomWarehouseFromLoad creates a custom warehouse in the database from load information.
func CreateCustomWarehouseFromLoad(
	ctx context.Context,
	load *models.Load,
	stopType StopType,
	source string,
) (*models.Warehouse, error) {
	supportType, err := models.GetWarehouseSupportType(models.WarehouseSource(source))
	if err != nil {
		return nil, fmt.Errorf("failed to determine warehouse support type: %w", err)
	}

	if supportType != models.CustomWarehouseSupportType {
		return nil, fmt.Errorf("source %s does not support custom warehouses", source)
	}

	warehouse := LoadToCustomWarehouse(load, stopType, models.WarehouseSource(source))

	if err := warehouseDB.Upsert(ctx, &warehouse); err != nil {
		return nil, fmt.Errorf("failed to upsert custom warehouse: %w", err)
	}

	log.Info(
		ctx,
		"created custom warehouse from load",
		zap.Any("warehouse", warehouse),
		zap.String("source", source),
		zap.String("stopType", string(stopType)),
	)

	return &warehouse, nil
}

func GetWarehouseIDForUsage(
	warehouse,
	existingWarehouse,
	newWarehouse *models.Warehouse,
) uint {
	if warehouse != nil {
		return warehouse.ID
	}
	if existingWarehouse != nil {
		return existingWarehouse.ID
	}
	if newWarehouse != nil {
		return newWarehouse.ID
	}

	return 0
}

func GetTMSIntegrationID(
	load *models.Load,
	tmsIntegration *models.Integration,
) uint {
	if load != nil {
		return load.TMSID
	}
	if tmsIntegration != nil {
		return tmsIntegration.ID
	}

	return 0
}
