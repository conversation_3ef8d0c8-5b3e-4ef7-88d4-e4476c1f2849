package apptutil

import (
	"errors"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

// SetApptDatesFromLoad sets the Appointment Date and StartTime from the load based on the stop type.
func SetApptDatesFromLoad(appt *models.Appointment, load *models.Load, stopType StopType) error {
	if appt == nil || load == nil || stopType == "" {
		return errors.New("appointment or load cannot be nil")
	}

	var apptDate time.Time
	switch stopType {
	case PickupStopType:
		apptDate = models.FallbackTime(load.Pickup.ApptStartTime, load.Pickup.ReadyTime)
	case DropoffStopType:
		apptDate = models.FallbackTime(load.Consignee.ApptStartTime, load.Consignee.MustDeliver)
	}
	appt.Date = apptDate.Format(time.DateOnly)

	switch stopType {
	case PickupStopType:
		appt.StartTime = load.Pickup.ApptStartTime.Time
	case DropoffStopType:
		appt.StartTime = load.Consignee.ApptStartTime.Time
	}
	if appt.StartTime.IsZero() {
		appt.StartTime = apptDate
	}

	return nil
}
