package appt

import (
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apptutil "github.com/drumkitai/drumkit/fn/api/routes/appt/util"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	SendRequestApptEmailBody struct {
		LoadID          uint              `json:"loadID" validate:"required"`
		Recipients      []string          `json:"recipients" validate:"required,dive,email"`
		CC              []string          `json:"cc" validate:"dive,email"`
		BCC             []string          `json:"bcc" validate:"dive,email"`
		Subject         string            `json:"subject"`
		EmailBody       string            `json:"emailBody" validate:"required,min=10"`
		EmailTemplateID uint              `json:"emailTemplateId,omitempty"`
		ScheduleSend    *time.Time        `json:"scheduleSend"` // Optional: if nil/omitted, sends immediately
		StopType        apptutil.StopType `json:"stopType" validate:"required,oneof=pickup dropoff"`

		// Threading fields for replies
		ReplyInThread bool `json:"replyInThread"`

		// Outlook/Front specific threading fields:
		// - ThreadID: Conversation ID (fallback if ThreadItemID not provided)
		// - ThreadItemID: Message ID to reply to (preferred)
		ThreadID     string `json:"threadID"`
		ThreadItemID string `json:"threadItemID"`

		// Gmail-specific threading fields (RFC standard):
		// - ThreadReferences: RFC "References" header (chain of message IDs)
		// - InReplyTo: RFC "In-Reply-To" header (previous message ID)
		ThreadReferences string   `json:"threadReferences"`
		InReplyTo        []string `json:"inReplyTo"`
	}

	SendRequestApptEmailResponse struct {
		Message string `json:"message"`
	}
)

// SendRequestApptEmail handles sending appointment request emails to carriers.
//
// Supports two modes:
//  1. Immediate send: omit `scheduleSend` or provide a past/current time
//  2. Scheduled send: provide a future timestamp in `scheduleSend`
func SendRequestApptEmail(c *fiber.Ctx) error {
	var body SendRequestApptEmailBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).JSON(SendRequestApptEmailResponse{
			Message: err.Error(),
		})
	}

	ctx := c.UserContext()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	// Validate that dev users can't send to external emails
	if err := emails.ValidateDevUserRecipients(
		user.EmailAddress,
		body.Recipients,
		body.CC,
		body.BCC,
	); err != nil {
		log.Warn(ctx, "dev user attempted to send email to external address", zap.Error(err))
		return c.Status(http.StatusBadRequest).JSON(SendRequestApptEmailResponse{
			Message: err.Error(),
		})
	}

	// Validate Subject
	if !body.ReplyInThread && body.Subject == "" {
		return c.Status(http.StatusBadRequest).JSON(SendRequestApptEmailResponse{
			Message: "Subject is required to send new emails",
		})
	}

	// Validate threading fields if this is a reply
	if body.ReplyInThread {
		// For Outlook/Front: need ThreadID or ThreadItemID (message ID / conversation ID)
		// For Gmail: need ThreadReferences and/or InReplyTo (RFC headers)
		hasOutlookOrFrontThreading := !helpers.IsBlank(body.ThreadID) || !helpers.IsBlank(body.ThreadItemID)
		hasGmailThreading := !helpers.IsBlank(body.ThreadReferences) || len(body.InReplyTo) > 0

		if !hasOutlookOrFrontThreading && !hasGmailThreading {
			log.Error(
				ctx,
				"required threading fields not provided for reply mode",
				zap.Bool("replyInThread", body.ReplyInThread),
			)
			return c.Status(http.StatusBadRequest).JSON(SendRequestApptEmailResponse{
				Message: "Error sending email",
			})
		}

		log.Info(
			ctx,
			"sending reply in existing email thread",
			zap.String("threadID", body.ThreadID),
			zap.String("threadItemID", body.ThreadItemID),
			zap.String("threadReferences", body.ThreadReferences),
			zap.Strings("inReplyTo", body.InReplyTo),
		)
	} else {
		log.Info(ctx, "sending brand new email")
	}

	service, err := rds.GetServiceByID(ctx, middleware.ServiceIDFromContext(c))
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	load, err := loadDB.GetLoadByID(ctx, body.LoadID)
	if err != nil {
		return apiutil.HandleDBError(c, err, true, "load %d not found", body.LoadID)
	}

	if user.EmailSignature == "" {
		log.WarnNoSentry(ctx, "user without signature trying to generate scheduling email")
	}

	ctx = log.With(ctx, zap.String("freightTrackingID", load.FreightTrackingID))

	// Get TMS integration for customer association
	tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, load.FreightTrackingID)
	if err != nil {
		log.Warn(ctx, "failed to get TMS integration", zap.Error(err))
		// Continue without TMS integration - customer association will still work with name fallback
	}

	// Determine schedule time: if not provided or in the past, send immediately
	var scheduleTime models.NullTime
	if body.ScheduleSend != nil {
		scheduleTime = models.NullTime{
			Time:  *body.ScheduleSend,
			Valid: true,
		}
		// If scheduled time is in the past, treat as immediate send
		if body.ScheduleSend.Before(time.Now()) {
			log.Info(ctx, "scheduled time is in the past, sending immediately")
			scheduleTime = models.NullTime{
				Time:  time.Now(),
				Valid: true,
			}
		}
	} else {
		// No schedule time provided, send immediately
		log.Info(ctx, "no schedule time provided, sending immediately")
		scheduleTime = models.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
	}

	stopType, stopNumber := apptutil.GetStopTypeAndNumber(body.StopType)

	// Create appointment record first so we can associate the generated email with it
	appt := models.Appointment{
		Account:           user.EmailAddress,
		UserID:            user.ID,
		ServiceID:         user.ServiceID,
		FreightTrackingID: load.FreightTrackingID,
		LoadID:            load.ID,
		EmailBody:         body.EmailBody,
		EmailTemplateID:   body.EmailTemplateID,
		Source:            models.EmailRequest,
		StopType:          stopType,
		StopNumber:        stopNumber,
	}

	appt.EmailType = models.EmailAppointmentTypeNew
	if body.ReplyInThread {
		appt.EmailType = models.EmailAppointmentTypeReply
	}

	// Set appt.Date and appt.StartTime from load based on stop type
	err = apptutil.SetApptDatesFromLoad(&appt, &load, body.StopType)
	if err != nil {
		log.Warn(ctx, "failed to set appointment dates from load", zap.Error(err))
		// Fail open on failure to set appointment dates (main action for route is successful email send)
	}

	_, err = associateCustomerWithAppt(ctx, &appt, tmsIntegration)
	if err != nil {
		log.Error(ctx, "failed to associate customer with appointment", zap.Error(err))
		// Fail open on failure to associate customer with appointment (main action for route is successful email send)
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		log.Error(ctx, "failed to create appointment", zap.Error(err))
		// Fail open on failure to create appointment (main action for route is successful email send)
	}

	// TODO: Ensure warehouse exists and create custom warehouse if needed, similar to confirm_appt.go.
	// This should check if warehouse exists, get it from DB if not, and create a custom warehouse from load
	// TODO: Email based warehouse association usage instead of by schedule integration.

	// Create the generated email and associate it with the appointment
	generatedEmail := &models.GeneratedEmail{
		FreightTrackingID: load.FreightTrackingID,
		UserID:            user.ID,
		User:              user,
		TriggeredByUserID: user.ID,
		ServiceID:         service.ID,
		Service:           service,
		Loads:             []models.Load{load},
		Recipients:        pq.StringArray(body.Recipients),
		CC:                pq.StringArray(body.CC),
		BCC:               pq.StringArray(body.BCC),
		Subject:           body.Subject,
		AppliedBody:       body.EmailBody,
		Status:            models.PendingStatus,
		ScheduleSend:      scheduleTime,
	}

	// Set threading fields if this is a reply
	if body.ReplyInThread {
		// Outlook: Use ThreadItemID (message ID) if provided, otherwise ThreadID (conversation ID)
		// Gmail: ThreadID will be automatically assigned by Gmail API
		if body.ThreadItemID != "" {
			generatedEmail.ThreadID = body.ThreadItemID
			log.Info(ctx, "using ThreadItemID for reply", zap.String("threadItemID", body.ThreadItemID))
		} else if body.ThreadID != "" {
			generatedEmail.ThreadID = body.ThreadID
			log.Info(ctx, "using ThreadID for reply", zap.String("threadID", body.ThreadID))
		}

		// Gmail RFC threading headers (ignored by Outlook)
		generatedEmail.ThreadReferences = body.ThreadReferences
		if len(body.InReplyTo) > 0 {
			generatedEmail.InReplyTo = pq.StringArray(body.InReplyTo)
		}
	}

	// Associate with appointment if it was successfully created
	if appt.ID != 0 {
		generatedEmail.AppointmentID = &appt.ID
	}

	if err = genEmailDB.BatchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{generatedEmail}); err != nil {
		log.Error(ctx, "error storing generated email", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(SendRequestApptEmailResponse{
			Message: "There was an issue saving the email. Please try again.",
		})
	}

	errs := apiutil.SendEmail(ctx, user, []*models.GeneratedEmail{generatedEmail})
	if len(errs) > 0 {
		for _, err := range errs {
			log.Error(ctx, "error sending email", zap.Error(err))
		}
		return c.Status(http.StatusInternalServerError).JSON(SendRequestApptEmailResponse{
			Message: "There was an issue sending the email. Please try again.",
		})
	}

	log.Info(
		ctx,
		"email sent successfully",
		zap.Uint("generatedEmailID", generatedEmail.ID),
		zap.Uint("appointmentID", appt.ID),
	)

	return c.Status(http.StatusCreated).JSON(SendRequestApptEmailResponse{
		Message: "Email sent successfully",
	})
}
