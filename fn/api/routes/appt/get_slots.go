package appt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/retalix"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	GetOpenSlotsQuery struct {
		FreightTrackingID string `json:"freightTrackingID"`
		LoadTypeID        string `json:"loadTypeID" validate:"required"`
		TrailerType       string `json:"trailerType"`
		DockID            string `json:"dockID"`
		models.GetOpenSlotsRequest
		Source          string `json:"source"` // TODO: limit type to scheduling integrations
		AppointmentDate string `json:"appointmentDate,omitempty"`
		ProIDFieldName  string `json:"proIdFieldName,omitempty"`
		IntegrationID   uint   `json:"integrationID"`
	}

	GetOpenSlotsResponse struct {
		GetOpenSlotsQuery
		Warehouse models.Warehouse `json:"warehouse"`
		LoadType  models.LoadType  `json:"loadType"`
		Slots     []models.Slot    `json:"slots"`
	}

	specificErrorResponse struct {
		status  int
		message string
	}
)

const browserbaseSessionTTL = 3 * time.Minute

func GetOpenSlots(c *fiber.Ctx) error {
	var query GetOpenSlotsQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if rawFormData := c.Query("formData"); rawFormData != "" {
		formDataJSON := rawFormData
		var formData map[string]any
		if err := json.Unmarshal([]byte(formDataJSON), &formData); err != nil {
			log.Warn(c.UserContext(), "failed to parse formData parameter", zap.Error(err))
		} else {
			query.FormData = formData
		}
	}

	ctx := log.With(c.UserContext(), zap.Any("requestQuery", query))

	// TODO: remove this when the frontend passes in a nonempty source
	if query.Source == "" {
		query.Source = string(models.OpendockSource)
	}

	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		userServiceID,
		query.Source,
		query.IntegrationID,
	)
	if err != nil {
		log.Error(ctx, "error fetching integration id for warehouse", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(
		ctx,
		zap.String("schedulingUsername", integration.Username),
		zap.Uint("integrationID", integration.ID),
	)

	// Attempt to reuse an existing browserbase session for Selenium-based scheduling flows.
	// If the request doesn't already have a session ID, try to get it from cache.
	if query.BrowserbaseSessionID == nil || *query.BrowserbaseSessionID == "" {
		session, found, err := redis.GetSchedulingBrowserbaseSession(ctx, userID, integration.ID)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get scheduling browserbase session", zap.Error(err))
			// Continue without cached session
		}
		if found && session != nil && session.BrowserbaseSessionID != "" {
			// Propagate existing browserbase session ID into the open slots request.
			cachedID := session.BrowserbaseSessionID
			query.BrowserbaseSessionID = &cachedID

			// Update LastUsedAt + refresh TTL to reflect active usage.
			session.LastUsedAt = time.Now().UTC()
			if err := redis.SetSchedulingBrowserbaseSession(
				ctx,
				userID,
				integration.ID,
				*session,
				browserbaseSessionTTL,
			); err != nil {
				log.WarnNoSentry(ctx, "failed to update scheduling browserbase session timestamp", zap.Error(err))
			}
		}
	}

	resp, err := getSlots(ctx, &query, query.WarehouseID, integration)
	if err != nil {
		// Check for user-facing errors first (these include cleaned Retalix errors)
		var userFacingErr errtypes.UserFacingError
		if errors.As(err, &userFacingErr) {
			log.Warn(ctx, "user-facing getSlots error", zap.Error(err))

			return c.Status(http.StatusNotFound).JSON(fiber.Map{
				"message": userFacingErr.Error(),
			})
		}

		// Check for specific errors by checking string
		if specificErr := handleSpecificErrors(ctx, err); specificErr != nil {
			return c.Status(specificErr.status).JSON(fiber.Map{
				"message": specificErr.message,
			})
		}

		log.Error(ctx, "getSlots failed", zap.Error(err))

		var cyclopsErr *models.CyclopsError
		if errors.As(err, &cyclopsErr) {
			return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
				"message": cyclopsErr.Message,
				"errors":  cyclopsErr.Errors,
			})
		}

		return c.Status(http.StatusInternalServerError).JSON(fiber.Map{
			"message": "Live appointments are not available at this time",
		})
	}

	// Persist/refresh browserbase session ID if returned from scheduling flow.
	if resp != nil && resp.BrowserbaseSessionID != nil && *resp.BrowserbaseSessionID != "" {
		// Always preserve existing CreatedAt when updating, default to now for new sessions
		createdAt := time.Now().UTC()
		existingSession, found, err := redis.GetSchedulingBrowserbaseSession(
			ctx, userID, integration.ID,
		)
		if err == nil && found && existingSession != nil {
			createdAt = existingSession.CreatedAt
		}

		session := redis.SchedulingBrowserbaseSession{
			BrowserbaseSessionID: *resp.BrowserbaseSessionID,
			CreatedAt:            createdAt,
			LastUsedAt:           time.Now().UTC(),
		}

		// Use a TTL slightly below the expected browserbase session lifetime (e.g. 25 minutes).
		if err := redis.SetSchedulingBrowserbaseSession(
			ctx,
			userID,
			integration.ID,
			session,
			browserbaseSessionTTL,
		); err != nil {
			log.WarnNoSentry(ctx, "failed to cache scheduling browserbase session", zap.Error(err))
		}
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getSlots(
	ctx context.Context,
	query *GetOpenSlotsQuery,
	warehouseID string,
	integration models.Integration,
) (*GetOpenSlotsResponse, error) {

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to %s: %w", query.Source, err)
	}

	var warehouse models.Warehouse
	var loadType models.LoadType
	var slots []models.Slot

	switch integration.Name {
	case models.E2open:
		if query.FreightTrackingID == "" {
			return nil, errors.New("PRO ID is required for E2open")
		}

		// Set appointmentDate in the request if provided
		if query.AppointmentDate != "" {
			query.GetOpenSlotsRequest.AppointmentDate = query.AppointmentDate
		}

		// Propagate selected PRO ID field name into the request for Cyclops
		if query.ProIDFieldName != "" {
			query.GetOpenSlotsRequest.ProIDFieldName = query.ProIDFieldName
		}

		slots, err = client.GetOpenSlots(ctx, query.FreightTrackingID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		key := fmt.Sprintf("%s-%s-%s", models.E2openSource, query.FreightTrackingID, query.RequestType)

		whName, ok, err := redis.GetKey[string](ctx, key)
		if err == nil && ok && whName != "" {
			warehouse = models.Warehouse{
				WarehouseID:   whName,
				WarehouseName: whName,
				Source:        models.E2openSource,
			}
		} else {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis - falling back to db",
				zap.String("source", string(models.E2openSource)),
				zap.String("freightTrackingID", query.FreightTrackingID),
				zap.Error(err),
			)

			// NOTE: E2open warehouses have no IDs so we use freightTrackingID for storing and lookups
			wh, err := warehouseDB.GetWarehouseByIDAndSource(
				ctx,
				integration.ServiceID,
				models.E2openSource,
				query.FreightTrackingID,
			)
			if err != nil {
				return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
			}

			if err := redis.SetKey(ctx, key, wh.WarehouseName, 24*time.Hour); err != nil {
				log.WarnNoSentry(
					ctx,
					"error setting warehouse in redis",
					zap.String("source", string(models.E2openSource)),
					zap.String("freightTrackingID", query.FreightTrackingID),
					zap.String("name", wh.WarehouseName),
					zap.Error(err),
				)
			}
		}

	case models.Manhattan:
		tenant := ""

		if integration.Tenant != "" {
			tenant = integration.Tenant
		}

		query.Tenant = tenant

		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		// Check if slots are empty before accessing the first element
		if len(slots) == 0 {
			return nil, errors.New("no available slots found")
		}

		// For Manhattan, warehouse info is embedded in slots and upserted during GetOpenSlots
		// Try to get warehouse from slots first, then fall back to Redis/DB lookup
		if len(slots) > 0 && slots[0].Dock.ID != "" {
			// Warehouse info is in the slot's dock ID (set in convertToSlots)
			warehouseID := slots[0].Dock.ID

			key := fmt.Sprintf("%s-%s", models.ManhattanSource, warehouseID)

			// Always try to get full warehouse info from DB to get timezone
			wh, err := warehouseDB.GetWarehouseByIDAndSource(
				ctx,
				integration.ServiceID,
				models.ManhattanSource,
				warehouseID,
			)
			if err != nil {
				// Don't fail the request if warehouse lookup fails
				// The warehouse was just upserted in GetOpenSlots, use basic info
				log.WarnNoSentry(
					ctx,
					"warehouse lookup failed, using basic info from slots",
					zap.String("source", string(models.ManhattanSource)),
					zap.String("warehouseID", warehouseID),
					zap.Error(err),
				)

				// Try to get warehouse name from Redis as fallback
				whName, ok, redisErr := redis.GetKey[string](ctx, key)
				if redisErr == nil && ok && whName != "" {
					warehouse = models.Warehouse{
						WarehouseID:   warehouseID,
						WarehouseName: whName,
						Source:        models.ManhattanSource,
					}
				} else {
					warehouse = models.Warehouse{
						WarehouseID:   warehouseID,
						WarehouseName: warehouseID,
						Source:        models.ManhattanSource,
					}
				}
			} else {
				warehouse = *wh

				// Cache warehouse name for next time
				if err := redis.SetKey(ctx, key, wh.WarehouseName, 24*time.Hour); err != nil {
					log.WarnNoSentry(
						ctx,
						"error setting warehouse in redis",
						zap.String("source", string(models.ManhattanSource)),
						zap.String("name", wh.WarehouseName),
						zap.Error(err),
					)
				}
			}
		} else {
			// Fallback: create minimal warehouse info
			log.WarnNoSentry(
				ctx,
				"no warehouse info available in slots",
				zap.String("source", string(models.ManhattanSource)),
			)
			warehouse = models.Warehouse{
				Source: models.ManhattanSource,
			}
		}

	case models.OneNetwork:
		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

		key := fmt.Sprintf("%s-%s-%s", models.OneNetworkSource, query.LoadTypeID, query.RequestType)

		warehouse, _, err = redis.GetKey[models.Warehouse](ctx, key)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting warehouse in redis",
				zap.String("source", string(models.OneNetworkSource)),
				zap.String("poNum", query.LoadTypeID),
				zap.Error(err),
			)
		}

	case models.Opendock:
		warehouse, err = client.GetWarehouse(ctx, warehouseID)
		if err != nil {
			return nil, fmt.Errorf("%s.GetWarehouse failed: %w", query.Source, err)
		}

		loadTypes, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{
			WarehouseID: warehouseID,
			Search:      `{"allowCarrierScheduling":true}`,
		})
		if err != nil {
			return nil, fmt.Errorf("%s.GetLoadTypes failed: %w", query.Source, err)
		}

		// Find the loadType with the matching name
		for _, lt := range loadTypes {
			if strings.EqualFold(lt.ID, query.LoadTypeID) {
				loadType = lt
				break
			}
		}

		if loadType.ID == "" {
			return nil, fmt.Errorf("loadType '%s' not found for warehouse %s", query.LoadTypeID, warehouseID)
		}

		query.IncludeStartTimes = true
		log.Info(ctx, "get open slots request object", zap.Any("query", query.GetOpenSlotsRequest))

		slots, err = client.GetOpenSlots(ctx, loadType.ID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.C3Reservations:
		if integration.Tenant != "" {
			query.Tenant = integration.Tenant
		}

		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}

	case models.Retalix:
		// Validate Retalix-specific requirements
		if len(query.PONumbers) == 0 {
			return nil, errors.New("PO numbers are required for Retalix")
		}

		if query.Warehouse.WarehouseID == "" {
			return nil, errors.New("warehouse ID is required for Retalix")
		}

		if query.Warehouse.WarehouseName == "" {
			return nil, errors.New("warehouse name is required for Retalix")
		}

		// Retalix warehouses are available in the database after onboarding flow
		wh, err := warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			integration.ServiceID,
			models.RetalixSource,
			query.Warehouse.WarehouseID,
		)
		if err != nil {
			return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
		}

		warehouse = *wh

		// Retalix doesn't need warehouse/loadtype calls
		slots, err = client.GetOpenSlots(ctx, "", query.GetOpenSlotsRequest)
		if err != nil {
			// Apply Retalix error cleaning to provide user-friendly error messages
			cleanedErr := retalix.CleanRetalixError(err)
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, cleanedErr)
		}

	case models.YardView:
		// Load warehouse from DB to get timezone for proper time parsing
		wh, err := warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			integration.ServiceID,
			models.YardViewSource,
			query.WarehouseID,
		)
		if err == nil && wh != nil {
			warehouse = *wh
			query.Warehouse = wh
		} else if err != nil {
			log.Error(
				ctx,
				"error fetching warehouse from db",
				zap.String("source", string(models.YardViewSource)),
				zap.String("warehouseID", query.WarehouseID),
				zap.Error(err),
			)
			return nil, fmt.Errorf("warehouseDB.GetWarehouseByIDAndSource failed: %w", err)
		}

		slots, err = client.GetOpenSlots(ctx, query.LoadTypeID, query.GetOpenSlotsRequest)
		if err != nil {
			return nil, fmt.Errorf("%s.GetOpenSlots failed: %w", integration.Name, err)
		}
		// Yardview warehouses (Target and ALlentown) have shifts (likely others do as well).
		// You do not schedule appointments during an active shift, so we should filter those slots out.
		// Ex: It is 3pm during the 2pm-8pm shift, none of the slots 2:00, 3:00, 4:00, ..., 8:00 are considered valid.
		slots = FilterSlotsByCurrentShift(ctx, warehouse, slots)

		// Check if there are no valid start and end times in array of slots
		if len(slots[0].StartTimes) == 0 || len(slots[len(slots)-1].StartTimes) == 0 {
			return nil, errors.New("no start and end times found")
		}
		slotsStartDate := slots[0].StartTimes[0]          // First slot start time
		slotsEndDate := slots[len(slots)-1].StartTimes[0] // Last slot start time

		// Get appointments in the date range of slots to filter out slots that have 0 capacity
		appts, err := client.GetAppointments(
			ctx,
			models.GetAppointmentsRequest{
				StartDate:                slotsStartDate,
				EndDate:                  slotsEndDate,
				WarehouseID:              warehouseID,
				Warehouse:                &warehouse,
				Status:                   models.AppointmentStatusPending,
				ErrorOnEmptyAppointments: false, // It's ok if there are no appointments found
			},
		)
		if err != nil {
			return nil, fmt.Errorf("%s.GetAppointments failed: %w", integration.Name, err)
		}

		slots = FilterSlotsByAppointments(ctx, slots, appts)

	default:
		return nil, fmt.Errorf("unsupported integration: %s", integration.Name)
	}

	// Filter slots by dock if specified
	excludedIntegrations := map[models.IntegrationName]bool{
		models.C3Reservations: true,
		models.E2open:         true,
		models.OneNetwork:     true,
		models.YardView:       true,
	}

	slotsResponse := make([]models.Slot, 0)
	if query.DockID != "" && !excludedIntegrations[integration.Name] {
		for _, s := range slots {
			if s.Dock.ID == query.DockID {
				slotsResponse = append(slotsResponse, s)
			}
		}
	} else {
		slotsResponse = slots
	}

	// Warehouse info is now only available at the top level of the response

	// Filter out past time slots
	slotsResponse = filterSlotsByPastTime(ctx, warehouse, slotsResponse)

	return &GetOpenSlotsResponse{
		GetOpenSlotsQuery: *query,
		Warehouse:         warehouse,
		LoadType:          loadType,
		Slots:             slotsResponse,
	}, nil
}

func handleSpecificErrors(ctx context.Context, err error) *specificErrorResponse {
	errStr := strings.ToLower(err.Error())

	if strings.Contains(errStr, "not found") || strings.Contains(errStr, "404") {
		log.Warn(ctx, "getSlots failed with not found error", zap.String("err", errStr))

		return &specificErrorResponse{
			status:  http.StatusNotFound,
			message: err.Error(),
		}
	}

	if strings.Contains(errStr, "not supported") {
		log.Warn(ctx, "getSlots failed", zap.String("err", errStr))

		return &specificErrorResponse{
			status:  http.StatusUnprocessableEntity,
			message: err.Error(),
		}
	}

	if strings.Contains(errStr, "no available appointment dates found") {
		log.WarnNoSentry(
			ctx,
			"getSlots failed - no available appointment dates found",
			zap.String("err", errStr),
		)

		return &specificErrorResponse{
			status:  http.StatusUnprocessableEntity,
			message: err.Error(),
		}
	}

	// Check for Cyclops connection errors
	if strings.Contains(errStr, "10.0.0.39:8000") {
		log.Warn(ctx, "getSlots failed - unable to connect to cyclops", zap.String("err", errStr))

		return &specificErrorResponse{
			status:  http.StatusInternalServerError,
			message: "There was an issue processing your request. Please try again later.",
		}
	}

	return nil
}

// filterSlotsByPastTime removes slot start times that are in the past.
// Properly handles warehouse timezone for accurate past time filtering.
func filterSlotsByPastTime(
	ctx context.Context,
	warehouse models.Warehouse,
	slots []models.Slot,
) []models.Slot {
	now, loc := getWarehouseTimeNowAndLocation(ctx, warehouse)

	filtered := make([]models.Slot, 0, len(slots))
	for _, s := range slots {
		newTimes := make([]time.Time, 0, len(s.StartTimes))
		for _, t := range s.StartTimes {
			// Re-interpret slot time in the warehouse timezone (keep wall clock the same)
			slotTime := t
			if loc != nil {
				slotTime = time.Date(
					t.Year(), t.Month(), t.Day(),
					t.Hour(), t.Minute(), t.Second(), t.Nanosecond(),
					loc,
				)
			}

			// Filter past times - comparison in consistent timezone
			if slotTime.After(now) {
				newTimes = append(newTimes, slotTime)
			}
		}

		if len(newTimes) > 0 {
			s.StartTimes = newTimes
			filtered = append(filtered, s)
		}
	}

	return filtered
}

// getWarehouseTimeNowAndLocation returns the current time in the warehouse's timezone if available,
// and the location, otherwise returns server time with appropriate logging.
func getWarehouseTimeNowAndLocation(ctx context.Context, warehouse models.Warehouse) (time.Time, *time.Location) {
	if warehouse.WarehouseTimezone == "" {
		now := time.Now()
		log.Debug(
			ctx,
			"No warehouse timezone available, using server time",
			zap.String("warehouseID", warehouse.WarehouseID),
			zap.String("currentTime", now.Format("2006-01-02 15:04:05 MST")),
		)
		return now, nil
	}

	loc, locErr := time.LoadLocation(warehouse.WarehouseTimezone)
	if locErr != nil {
		log.WarnNoSentry(
			ctx,
			"Invalid warehouse timezone, falling back to server time",
			zap.String("warehouseTimezone", warehouse.WarehouseTimezone),
			zap.String("warehouseID", warehouse.WarehouseID),
			zap.Error(locErr),
		)
		return time.Now(), nil
	}

	return time.Now().In(loc), loc
}

// FilterSlotsByCurrentShift removes slot start times that occur during the warehouse's current
// shift window (same local date and within the current shift's HH:MM window).
func FilterSlotsByCurrentShift(
	ctx context.Context,
	warehouse models.Warehouse,
	slots []models.Slot,
) []models.Slot {
	schedule := warehouse.Settings.Shifts.Schedule
	if len(schedule) == 0 {
		// No shift schedule, skip filtering
		return slots
	}

	now, loc := getWarehouseTimeNowAndLocation(ctx, warehouse)

	// Find if now is inside any shift window (using simple time-of-day comparison)
	var currentShift models.ShiftWindow
	nowInShift := false
	for _, shiftWindow := range schedule {
		if isTimeOfDayInShift(now, shiftWindow) {
			nowInShift = true
			currentShift = shiftWindow
			log.Info(
				ctx,
				"found current shift",
				zap.String("shiftStart", shiftWindow.Start),
				zap.String("shiftEnd", shiftWindow.End),
				zap.String("shiftName", shiftWindow.Name),
			)
			break
		}
	}

	if !nowInShift {
		log.Info(ctx, "not currently in any shift, skipping shift filtering")
		return slots
	}

	filtered := make([]models.Slot, 0, len(slots))
	// Compute local midnight using warehouse timezone
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Location already resolved via getWarehouseTimeNow
	for _, s := range slots {
		newTimes := make([]time.Time, 0, len(s.StartTimes))
		for _, t := range s.StartTimes {
			// Re-interpret slot time in the warehouse timezone (keep wall clock the same)
			slotTime := t
			if loc != nil {
				slotTime = time.Date(
					t.Year(), t.Month(), t.Day(),
					t.Hour(), t.Minute(), t.Second(), t.Nanosecond(),
					loc,
				)
			}

			// Only filter times that are on the same calendar day AND within the current shift time window
			slotDate := time.Date(slotTime.Year(), slotTime.Month(), slotTime.Day(), 0, 0, 0, 0, slotTime.Location())
			isSameDay := slotDate.Equal(nowDate)
			inCurrentShift := isTimeOfDayInShift(slotTime, currentShift)

			if isSameDay && inCurrentShift {
				continue // Skip this time as it's in current shift on the same day
			}

			newTimes = append(newTimes, t)
		}

		if len(newTimes) > 0 {
			s.StartTimes = newTimes
			filtered = append(filtered, s)
		}
	}

	return filtered
}

// FilterSlotsByAppointments removes slots that have 0 capacity
// We subtract 1 from capacity for each appointment that is in the slot
func FilterSlotsByAppointments(ctx context.Context, slots []models.Slot, appts []models.Appointment) []models.Slot {
	filtered := make([]models.Slot, 0, len(slots))

	// Build a lookup of appointments by normalized start time to achieve O(n + m) vs double for loop pass (n^2)
	// Normalize to UTC nanoseconds to avoid monotonic clock and location issues.
	apptCountByStartTime := make(map[int64]int, len(appts))
	for _, appt := range appts {
		key := appt.StartTime.UTC().UnixNano()
		apptCountByStartTime[key]++
	}

	// For each slot, subtract the number of appointments that start at the slot's start time
	for _, slot := range slots {
		if len(slot.StartTimes) > 0 {
			key := slot.StartTimes[0].UTC().UnixNano()
			if used := apptCountByStartTime[key]; used > 0 {
				slot.Capacity -= used
				log.Debug(
					ctx,
					"subtracting appointments from slot capacity",
					zap.Any("slot", slot),
					zap.Int("appointments_at_time", used),
					zap.Int("capacity", slot.Capacity),
				)
			}
		}
		// Add slot to filtered if it has capacity left
		if slot.Capacity > 0 {
			filtered = append(filtered, slot)
		}
	}

	return filtered
}

// isTimeOfDayInShift checks if the time-of-day of t falls within the given shift window.
// Handles windows that cross midnight (e.g., 22:00-04:00).
func isTimeOfDayInShift(t time.Time, shift models.ShiftWindow) bool {
	startH, startM, ok := parseHHMM(shift.Start)
	if !ok {
		return false
	}
	endH, endM, ok := parseHHMM(shift.End)
	if !ok {
		return false
	}

	minutes := t.Hour()*60 + t.Minute()
	startMin := startH*60 + startM
	endMin := endH*60 + endM

	if endMin > startMin {
		return minutes >= startMin && minutes <= endMin
	}

	// Crosses midnight
	return minutes >= startMin || minutes <= endMin
}

// parseHHMM parses a time string in the format HH:MM.
func parseHHMM(s string) (int, int, bool) {
	if len(s) != 5 || s[2] != ':' {
		return 0, 0, false
	}
	h1 := int(s[0] - '0')
	h2 := int(s[1] - '0')
	m1 := int(s[3] - '0')
	m2 := int(s[4] - '0')
	hour := h1*10 + h2
	minute := m1*10 + m2
	if hour < 0 || hour > 23 || minute < 0 || minute > 59 {
		return 0, 0, false
	}
	return hour, minute, true
}
