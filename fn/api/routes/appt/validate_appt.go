package appt

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	ValidateApptBody struct {
		PONumbers       []string               `json:"poNumbers,omitempty"`
		Source          models.WarehouseSource `json:"source" validate:"required"`
		WarehouseID     string                 `json:"warehouseId"`
		RequestType     models.RequestType     `json:"requestType"`
		StopType        string                 `json:"stopType"`
		AppointmentType string                 `json:"appointmentType"`
		Company         string                 `json:"company"`
		Operation       string                 `json:"operation"`
		City            string                 `json:"city"`
		State           string                 `json:"state"`
		FacilityName    string                 `json:"facilityName,omitempty"`
		ZipCode         string                 `json:"zipCode"`
		Country         string                 `json:"country"`
		DepotValue      string                 `json:"depotValue"`
		Uom             string                 `json:"uom"`
		QtyCount        int                    `json:"qtyCount"`
		AppointmentDate string                 `json:"appointmentDate,omitempty"`
		IntegrationID   uint                   `json:"integrationID"`
		FetchCompanies  bool                   `json:"fetchCompanies,omitempty"`
		FetchOperations bool                   `json:"fetchOperations,omitempty"`
		FetchFormFields bool                   `json:"fetchFormFields,omitempty"`
		ProIDFieldName  string                 `json:"proIdFieldName,omitempty"`
		HardRefresh     bool                   `json:"hardRefresh,omitempty"`
		FlowType        string                 `json:"flowType,omitempty"`
		// Optional browserbase session ID for Selenium-based scheduling flows (via Cyclops).
		BrowserbaseSessionID *string `json:"browserbaseSessionId,omitempty"`
	}

	ValidateApptResponse struct {
		ValidatedPONumbers         []models.ValidatedPONumber `json:"validatedPONumbers"`
		Companies                  []CompanyInfo              `json:"companies,omitempty"`
		Operations                 []OperationInfo            `json:"operations,omitempty"`
		RequiresCompanySelection   bool                       `json:"requiresCompanySelection,omitempty"`
		RequiresOperationSelection bool                       `json:"requiresOperationSelection,omitempty"`
		ProIDFields                []ProIDField               `json:"proIdFields,omitempty"`
		DateFields                 []DateField                `json:"dateFields,omitempty"`
	}

	CompanyInfo struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	}

	OperationInfo struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	}

	ProIDField struct {
		Label     string `json:"label"`
		FieldName string `json:"fieldName"`
	}

	DateField struct {
		Label     string `json:"label"`
		FieldName string `json:"fieldName"`
		Exists    bool   `json:"exists"`
	}
)

// NOTE: Only supports E2open, Manhattan, Costco and Retalix now
func ValidateAppt(c *fiber.Ctx) error {
	var body ValidateApptBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("requestBody", body))
	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		userServiceID,
		string(body.Source),
		body.IntegrationID,
	)
	if err != nil {
		log.Error(ctx, "failed to get scheduler integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Attempt to reuse an existing browserbase session for Selenium-based scheduling flows.
	// If the request doesn't already have a session ID, try to get it from cache.
	if body.BrowserbaseSessionID == nil || *body.BrowserbaseSessionID == "" {
		session, found, err := redis.GetSchedulingBrowserbaseSession(ctx, userID, integration.ID)
		if err != nil {
			log.WarnNoSentry(ctx, "failed to get scheduling browserbase session", zap.Error(err))
		}
		if found && session != nil && session.BrowserbaseSessionID != "" {
			cachedID := session.BrowserbaseSessionID
			body.BrowserbaseSessionID = &cachedID

			// Update LastUsedAt + refresh TTL to reflect active usage
			session.LastUsedAt = time.Now().UTC()
			if err := redis.SetSchedulingBrowserbaseSession(
				ctx,
				userID,
				integration.ID,
				*session,
				browserbaseSessionTTL,
			); err != nil {
				log.WarnNoSentry(ctx, "failed to update scheduling browserbase session timestamp", zap.Error(err))
			}
		}
	}

	var warehouse *models.Warehouse

	if body.Source == models.RetalixSource {
		warehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			body.Source,
			body.WarehouseID,
		)
		if err != nil {
			log.Error(
				ctx,
				"error getting warehouse name",
				zap.Error(err),
				zap.String("warehouseID", body.WarehouseID),
			)
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	var validatedAppt ValidateApptResponse

	// Handle E2open 3rd party dynamic company/operation/form fields flow
	if body.Source == models.E2openSource && (body.RequestType == "3rd_party" || body.AppointmentType == "3rd_party") {
		if body.FetchCompanies || body.FetchOperations || body.FetchFormFields {
			// Handle hard refresh - delete all cached data for this user
			if body.HardRefresh && body.FetchCompanies {
				log.Info(ctx, "hard refresh requested, clearing all cached data",
					zap.Uint("integrationID", body.IntegrationID))

				if err := redis.ClearE2openCache(ctx, body.IntegrationID); err != nil {
					log.Warn(ctx, "failed to clear cache during hard refresh", zap.Error(err))
					// Continue anyway - will fetch from Cyclops
				} else {
					log.Info(ctx, "successfully cleared cache for hard refresh")
				}
			}

			// Try to get data from cache first (will be empty if hard refresh)
			cacheHit, cacheErr := tryGetE2openCachedData(ctx, body, &validatedAppt)
			if cacheErr != nil {
				// Check if this is a "not supported" error (business logic, not cache error)
				if strings.Contains(cacheErr.Error(), "not supported") {
					log.Info(ctx, "returning cached 'not supported' error")
					return c.Status(http.StatusInternalServerError).SendString(cacheErr.Error())
				}
				// Otherwise it's a cache error - fall back to API
				log.Warn(ctx, "error checking cache, falling back to API", zap.Error(cacheErr))
			} else if cacheHit {
				// Log what was fetched from cache
				logCacheHit(ctx, body, validatedAppt)
				return c.Status(http.StatusOK).JSON(validatedAppt)
			}

			// Cache miss or error - call Cyclops API
			log.Info(ctx, "cache miss, calling Cyclops API")
			result, err := validatePONumbers(ctx, integration, body, warehouse)
			if err != nil {
				log.Error(ctx, "failed to fetch company/operation data", zap.Error(err))

				// Check if error is "stop type not supported" and cache it
				errMsg := err.Error()
				isStopTypeError := body.FetchFormFields &&
					strings.Contains(errMsg, "Stop type") &&
					strings.Contains(errMsg, "not supported")

				if isStopTypeError {
					stopType := body.StopType
					isPickupOrDropoff := string(body.RequestType) == "pickup" ||
						string(body.RequestType) == "dropoff"
					if stopType == "" && isPickupOrDropoff {
						stopType = string(body.RequestType)
					}

					log.Info(
						ctx,
						"caching stop type not supported",
						zap.String("company", body.Company),
						zap.String("operation", body.Operation),
						zap.String("stopType", stopType),
					)

					if cacheErr := redis.SetE2openStopTypeNotSupported(
						ctx,
						body.IntegrationID,
						body.Company,
						body.Operation,
						stopType,
					); cacheErr != nil {
						log.Warn(ctx, "failed to cache stop type not supported", zap.Error(cacheErr))
					}
				}

				return c.Status(http.StatusInternalServerError).SendString(err.Error())
			}

			// Decode the metadata from the special marker ValidatedPONumber
			if len(result) > 0 && result[0].PONumber == "__METADATA__" {
				var metadata map[string]any
				if err := json.Unmarshal([]byte(result[0].Error), &metadata); err != nil {
					log.Error(ctx, "failed to decode metadata", zap.Error(err), zap.String("raw_data", result[0].Error))
					return c.Status(http.StatusInternalServerError).SendString("Failed to decode response metadata")
				}

				// Extract companies if present
				if companies, ok := metadata["companies"].([]any); ok && companies != nil {
					for _, comp := range companies {
						compMap, ok := comp.(map[string]any)
						if !ok {
							continue
						}
						name, nameOk := compMap["name"].(string)
						id, idOk := compMap["id"].(string)
						if !nameOk || !idOk {
							log.Warn(ctx, "invalid company data", zap.Any("company", compMap))
							continue
						}
						validatedAppt.Companies = append(validatedAppt.Companies, CompanyInfo{
							Name: name,
							ID:   id,
						})
					}
					if len(validatedAppt.Companies) > 0 {
						log.Info(
							ctx,
							"extracted companies from Cyclops",
							zap.Int("count",
								len(validatedAppt.Companies)),
						)
					}
				}

				// Extract operations if present
				if operations, ok := metadata["operations"].([]any); ok && operations != nil {
					for _, op := range operations {
						opMap, ok := op.(map[string]any)
						if !ok {
							continue
						}
						name, nameOk := opMap["name"].(string)
						id, idOk := opMap["id"].(string)
						if !nameOk || !idOk {
							continue
						}
						validatedAppt.Operations = append(validatedAppt.Operations, OperationInfo{
							Name: name,
							ID:   id,
						})
					}
					if len(validatedAppt.Operations) > 0 {
						log.Info(
							ctx,
							"extracted operations from Cyclops",
							zap.Int("count", len(validatedAppt.Operations)),
						)
					}
				}

				// Extract selection flags
				if req, ok := metadata["requiresCompanySelection"].(bool); ok {
					validatedAppt.RequiresCompanySelection = req
				}
				if req, ok := metadata["requiresOperationSelection"].(bool); ok {
					validatedAppt.RequiresOperationSelection = req
				}

				// Extract PRO ID fields if present
				if proIDFields, ok := metadata["proIdFields"].([]any); ok && proIDFields != nil {
					for _, field := range proIDFields {
						fieldMap, ok := field.(map[string]any)
						if !ok {
							continue
						}
						label, labelOk := fieldMap["label"].(string)
						fieldName, fieldNameOk := fieldMap["fieldName"].(string)
						if !labelOk || !fieldNameOk {
							continue
						}
						validatedAppt.ProIDFields = append(validatedAppt.ProIDFields, ProIDField{
							Label:     label,
							FieldName: fieldName,
						})
					}
					if len(validatedAppt.ProIDFields) > 0 {
						log.Info(
							ctx,
							"extracted PRO ID fields from Cyclops",
							zap.Int("count", len(validatedAppt.ProIDFields)),
						)
					}
				}

				// Extract date fields if present
				if dateFields, ok := metadata["dateFields"].([]any); ok && dateFields != nil {
					for _, field := range dateFields {
						fieldMap, ok := field.(map[string]any)
						if !ok {
							continue
						}
						label, labelOk := fieldMap["label"].(string)
						fieldName, fieldNameOk := fieldMap["fieldName"].(string)
						exists, existsOk := fieldMap["exists"].(bool)
						if !labelOk || !fieldNameOk || !existsOk {
							continue
						}
						validatedAppt.DateFields = append(validatedAppt.DateFields, DateField{
							Label:     label,
							FieldName: fieldName,
							Exists:    exists,
						})
					}
					if len(validatedAppt.DateFields) > 0 {
						log.Info(
							ctx,
							"extracted date fields from Cyclops",
							zap.Int("count", len(validatedAppt.DateFields)),
						)
					}
				}

				// Cache the fetched data
				if err := cacheE2openData(ctx, body, validatedAppt, metadata); err != nil {
					log.Warn(ctx, "failed to cache E2open data", zap.Error(err))
					// Don't fail the request if caching fails
				}
			}

			return c.Status(http.StatusOK).JSON(validatedAppt)
		}
	}

	if len(body.PONumbers) > 0 {
		validatedAppt.ValidatedPONumbers, err = validatePONumbers(
			ctx,
			integration,
			body,
			warehouse,
		)
		if err != nil {
			log.Error(ctx, "failed to validate PO numbers", zap.Error(err))
			return err
		}
	} else {
		log.WarnNoSentry(ctx, "empty list of PO numbers, skipping validation")
	}

	return c.Status(http.StatusOK).JSON(validatedAppt)
}

func validatePONumbers(
	ctx context.Context,
	integration models.Integration,
	body ValidateApptBody,
	warehouse *models.Warehouse,
) ([]models.ValidatedPONumber, error) {

	requestType := body.RequestType
	poNumbers := body.PONumbers

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		if body.Source == models.RetalixSource {
			log.Warn(ctx, "failed to build scheduling client", zap.Error(err))
		} else {
			log.Error(ctx, "failed to build scheduling client", zap.Error(err))
		}

		return nil, fmt.Errorf("failed to build scheduling client: %w", err)
	}

	var wh models.Warehouse
	if warehouse != nil {
		wh = *warehouse
	}

	var opts []models.SchedulingOption

	if body.AppointmentType != "" {
		opts = append(opts, models.WithAppointmentType(body.AppointmentType))
	}

	if requestType != "" {
		opts = append(opts, models.WithRequestType(requestType))
	}

	// Handle stop type - check explicit StopType field first, then fallback to requestType
	stopType := body.StopType
	if stopType == "" && (string(body.RequestType) == "pickup" || string(body.RequestType) == "dropoff") {
		stopType = string(body.RequestType)
	}
	if stopType != "" {
		opts = append(opts, models.WithSchedulingStopType(stopType))
	}

	if body.Company != "" {
		opts = append(opts, models.WithCompany(body.Company))
	}

	if body.Operation != "" {
		opts = append(opts, models.WithOperation(body.Operation))
	}

	if body.City != "" {
		opts = append(opts, models.WithCity(body.City))
	}

	if body.State != "" {
		opts = append(opts, models.WithState(body.State))
	}

	if body.ZipCode != "" {
		opts = append(opts, models.WithZipCode(body.ZipCode))
	}

	if body.Country != "" {
		opts = append(opts, models.WithCountry(body.Country))
	}

	if body.DepotValue != "" {
		opts = append(opts, models.WithDepotValue(body.DepotValue))
	}

	if body.Uom != "" {
		opts = append(opts, models.WithUom(body.Uom))
	}

	if body.QtyCount != 0 {
		opts = append(opts, models.WithQtyCount(body.QtyCount))
	}

	if body.AppointmentDate != "" {
		opts = append(opts, models.WithAppointmentDate(body.AppointmentDate))
	}

	if integration.Tenant != "" {
		opts = append(opts, models.WithTenant(integration.Tenant))
	}

	if body.FacilityName != "" {
		opts = append(opts, models.WithFacilityName(body.FacilityName))
	}

	if body.FetchCompanies {
		opts = append(opts, models.WithFetchCompanies(true))
	}

	if body.FetchOperations {
		opts = append(opts, models.WithFetchOperations(true))
	}

	if body.FetchFormFields {
		opts = append(opts, models.WithFetchFormFields(true))
	}

	if body.ProIDFieldName != "" {
		opts = append(opts, models.WithProIDFieldName(body.ProIDFieldName))
	}

	if body.FlowType != "" {
		opts = append(opts, models.WithFlowType(body.FlowType))
	}

	// Propagate browserbase session ID for Selenium-based flows (via Cyclops).
	if body.BrowserbaseSessionID != nil && *body.BrowserbaseSessionID != "" {
		opts = append(opts, models.WithBrowserbaseSessionID(*body.BrowserbaseSessionID))
	}

	validatedPOs, err := client.ValidateAppointment(ctx, poNumbers, wh, opts...)
	if err != nil {
		// Return context-aware error message based on what's being fetched
		if body.FetchCompanies {
			return nil, fmt.Errorf("failed to fetch companies: %w", err)
		}
		if body.FetchOperations {
			return nil, fmt.Errorf("failed to fetch operations: %w", err)
		}
		if body.FetchFormFields {
			return nil, fmt.Errorf("failed to fetch form fields: %w", err)
		}
		// Default for actual PO validation
		return nil, fmt.Errorf("error validating PO numbers: %w", err)
	}

	return validatedPOs, nil
}

// tryGetE2openCachedData attempts to retrieve cached data for E2open 3rd party requests
// Returns true if cache hit, false if cache miss
func tryGetE2openCachedData(
	ctx context.Context,
	body ValidateApptBody,
	response *ValidateApptResponse,
) (bool, error) {
	integrationID := body.IntegrationID

	// Case 1: Fetching companies
	if body.FetchCompanies {
		log.Info(ctx, "checking cache for companies", zap.Uint("integrationID", integrationID))

		// Check if user has direct access (no companies)
		directAccess, err := redis.GetE2openDirectAccess(ctx, integrationID)
		if err != nil {
			return false, err
		}
		if directAccess {
			log.Info(ctx, "cache hit: user has direct access")
			response.RequiresCompanySelection = false
			response.RequiresOperationSelection = false
			return true, nil
		}

		// Check for cached companies
		companies, found, err := redis.GetE2openCachedCompanies(ctx, integrationID)
		if err != nil {
			return false, err
		}
		if found {
			log.Info(ctx, "cache hit: companies found", zap.Int("count", len(companies)))
			for _, comp := range companies {
				response.Companies = append(response.Companies, CompanyInfo{
					Name: comp.Name,
					ID:   comp.ID,
				})
			}
			response.RequiresCompanySelection = len(companies) > 0
			return true, nil
		}

		return false, nil
	}

	// Case 2: Fetching operations for a company
	if body.FetchOperations && body.Company != "" {
		log.Info(ctx, "checking cache for operations",
			zap.Uint("integrationID", integrationID),
			zap.String("company", body.Company))

		operations, found, err := redis.GetE2openCachedOperations(ctx, integrationID, body.Company)
		if err != nil {
			return false, err
		}
		if found {
			log.Info(ctx, "cache hit: operations found", zap.Int("count", len(operations)))
			for _, op := range operations {
				response.Operations = append(response.Operations, OperationInfo{
					Name: op.Name,
					ID:   op.ID,
				})
			}
			response.RequiresOperationSelection = len(operations) > 0
			return true, nil
		}

		return false, nil
	}

	// Case 3: Fetching form fields
	if body.FetchFormFields {
		// Determine stopType from body
		stopType := body.StopType
		if stopType == "" && (string(body.RequestType) == "pickup" || string(body.RequestType) == "dropoff") {
			stopType = string(body.RequestType)
		}

		if stopType == "" {
			log.Warn(
				ctx,
				"stopType missing for form fields cache lookup",
				zap.Uint("integrationID", integrationID),
				zap.String("company", body.Company),
				zap.String("operation", body.Operation),
				zap.String("requestType", string(body.RequestType)),
			)
			return false, errors.New("stopType is required for form fields caching")
		}

		log.Info(ctx, "checking cache for form fields",
			zap.Uint("integrationID", integrationID),
			zap.String("company", body.Company),
			zap.String("operation", body.Operation),
			zap.String("stopType", stopType))

		// Check if this stopType is cached as not supported
		notSupported, err := redis.IsE2openStopTypeNotSupported(
			ctx,
			integrationID,
			body.Company,
			body.Operation,
			stopType,
		)
		if err == nil && notSupported {
			log.Info(ctx, "cache hit: stop type not supported",
				zap.String("stopType", stopType))
			// Return error - this will be caught by the caller and returned to FE
			// Using the same error format as Cyclops returns
			return false, fmt.Errorf(
				"stop type '%s' is not supported by this company. "+
					"the required toggle button is not present on the page",
				stopType,
			)
		}

		var formFields *redis.E2openFormFields
		var found bool

		// Check direct access form fields first
		if body.Company == "" {
			formFields, found, err = redis.GetE2openDirectFormFields(ctx, integrationID, stopType)
		} else {
			formFields, found, err = redis.GetE2openCachedFormFields(
				ctx,
				integrationID,
				body.Company,
				body.Operation,
				stopType,
			)
		}

		if err != nil {
			return false, err
		}
		if found && formFields != nil {
			log.Info(ctx, "cache hit: form fields found",
				zap.Int("proIdFields", len(formFields.ProIDFields)),
				zap.Int("dateFields", len(formFields.DateFields)))

			for _, field := range formFields.ProIDFields {
				response.ProIDFields = append(response.ProIDFields, ProIDField{
					Label:     field.Label,
					FieldName: field.FieldName,
				})
			}

			for _, field := range formFields.DateFields {
				response.DateFields = append(response.DateFields, DateField{
					Label:     field.Label,
					FieldName: field.FieldName,
					Exists:    field.Exists,
				})
			}

			return true, nil
		}

		return false, nil
	}

	return false, nil
}

// cacheE2openData stores the fetched E2open data in Redis cache
func cacheE2openData(
	ctx context.Context,
	body ValidateApptBody,
	response ValidateApptResponse,
	metadata map[string]any,
) error {
	integrationID := body.IntegrationID

	// Case 1: Caching companies
	if body.FetchCompanies {
		log.Info(ctx, "caching companies", zap.Uint("integrationID", integrationID))

		// Check if this is direct access (no companies)
		requiresCompanySelection, _ := metadata["requiresCompanySelection"].(bool)
		if !requiresCompanySelection {
			return redis.SetE2openDirectAccess(ctx, integrationID)
		}

		// Cache companies
		if len(response.Companies) > 0 {
			companies := make([]redis.E2openCompanyInfo, len(response.Companies))
			for i, comp := range response.Companies {
				companies[i] = redis.E2openCompanyInfo{
					Name: comp.Name,
					ID:   comp.ID,
				}
			}
			return redis.SetE2openCachedCompanies(ctx, integrationID, companies)
		}

		return nil
	}

	// Case 2: Caching operations
	if body.FetchOperations && body.Company != "" {
		log.Info(ctx, "caching operations",
			zap.Uint("integrationID", integrationID),
			zap.String("company", body.Company))

		if len(response.Operations) == 0 {
			// No operations for this company
			return redis.SetE2openNoOperations(ctx, integrationID, body.Company)
		}

		operations := make([]redis.E2openOperationInfo, len(response.Operations))
		for i, op := range response.Operations {
			operations[i] = redis.E2openOperationInfo{
				Name: op.Name,
				ID:   op.ID,
			}
		}
		return redis.SetE2openCachedOperations(ctx, integrationID, body.Company, operations)
	}

	// Case 3: Caching form fields
	if body.FetchFormFields {
		// Determine stopType from body
		stopType := body.StopType
		if stopType == "" && (string(body.RequestType) == "pickup" || string(body.RequestType) == "dropoff") {
			stopType = string(body.RequestType)
		}

		if stopType == "" {
			log.Warn(
				ctx,
				"stopType missing for form fields cache write",
				zap.Uint("integrationID", integrationID),
				zap.String("company", body.Company),
				zap.String("operation", body.Operation),
				zap.String("requestType", string(body.RequestType)),
			)
			return errors.New("stopType is required for form fields caching")
		}

		log.Info(
			ctx,
			"caching form fields",
			zap.Uint("integrationID", integrationID),
			zap.String("company", body.Company),
			zap.String("operation", body.Operation),
			zap.String("stopType", stopType))

		formFields := redis.E2openFormFields{
			ProIDFields: make([]redis.E2openProIDField, len(response.ProIDFields)),
			DateFields:  make([]redis.E2openDateField, len(response.DateFields)),
		}

		for i, field := range response.ProIDFields {
			formFields.ProIDFields[i] = redis.E2openProIDField{
				Label:     field.Label,
				FieldName: field.FieldName,
			}
		}

		for i, field := range response.DateFields {
			formFields.DateFields[i] = redis.E2openDateField{
				Label:     field.Label,
				FieldName: field.FieldName,
				Exists:    field.Exists,
			}
		}

		// Check if this is direct access (no company)
		if body.Company == "" {
			return redis.SetE2openDirectFormFields(ctx, integrationID, stopType, formFields)
		}

		return redis.SetE2openCachedFormFields(
			ctx,
			integrationID,
			body.Company,
			body.Operation,
			stopType,
			formFields,
		)
	}

	return nil
}

// logCacheHit logs what data was returned from cache
func logCacheHit(ctx context.Context, body ValidateApptBody, response ValidateApptResponse) {
	if body.FetchCompanies {
		log.Info(
			ctx, "returning companies from cache",
			zap.Int("count", len(response.Companies)),
			zap.Bool("requiresCompanySelection", response.RequiresCompanySelection),
		)
		return
	}
	if body.FetchOperations {
		log.Info(
			ctx, "returning operations from cache",
			zap.Int("count", len(response.Operations)),
			zap.Bool("requiresOperationSelection", response.RequiresOperationSelection),
		)
		return
	}
	if body.FetchFormFields {
		log.Info(
			ctx, "returning form fields from cache",
			zap.Int("proIdFields", len(response.ProIDFields)),
			zap.Int("dateFields", len(response.DateFields)),
		)
	}
}
