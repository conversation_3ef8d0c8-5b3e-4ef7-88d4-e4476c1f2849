package appt

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
)

type GetApptsForLoadQuery struct {
	FreightTrackingID string `query:"freightTrackingID" validate:"required"`
}

type AppointmentWithTenant struct {
	models.Appointment
	Tenant string `json:"tenant"`
}

type GetApptsForLoadResponse struct {
	Appointments []AppointmentWithTenant `json:"appointments"`
}

// GetApptsForLoad fetches appointments scheduled by drumkit for a specific load
// It searches by freight tracking ID and includes tenant information from the integration
func GetApptsForLoad(c *fiber.Ctx) error {
	var query GetApptsForLoadQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := log.With(
		c.UserContext(),
		zap.Uint("serviceID", userServiceID),
		zap.String("freightTrackingID", query.FreightTrackingID),
	)

	// Get appointments by freight tracking ID and service ID with integration preloaded
	appointments, err := apptDB.GetApptsByFreightTrackingIDAndServiceID(
		ctx,
		query.FreightTrackingID,
		userServiceID,
	)
	if err != nil {
		log.Error(ctx, "error fetching appointments", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Transform appointments to include tenant information
	appointmentsWithTenant := make([]AppointmentWithTenant, len(appointments))
	for i, appt := range appointments {
		appointmentsWithTenant[i] = AppointmentWithTenant{
			Appointment: appt,
			Tenant:      appt.Integration.Tenant,
		}
	}

	log.Info(ctx, "successfully fetched appointments", zap.Int("count", len(appointments)))

	return c.Status(http.StatusOK).JSON(GetApptsForLoadResponse{
		Appointments: appointmentsWithTenant,
	})
}
