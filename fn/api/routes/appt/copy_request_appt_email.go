package appt

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apptutil "github.com/drumkitai/drumkit/fn/api/routes/appt/util"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	CopyRequestApptEmailBody struct {
		ID                uint                     `json:"id"`
		TemplateType      models.EmailTemplateType `json:"templateType"`
		Subject           string                   `json:"subject"`
		Body              string                   `json:"body"`
		FreightTrackingID string                   `json:"freightTrackingID"`
		StopType          apptutil.StopType        `json:"stopType" validate:"required,oneof=pickup dropoff"`
	}

	CopyRequestApptEmailResponse struct {
		TemplateID uint `json:"templateID"`
	}
)

// Creates an appointment in db as a side effect of user copy action (for email scheduling request appt email)
func CopyRequestApptEmail(c *fiber.Ctx) error {
	var body CopyRequestApptEmailBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	ctx, metaSpan := otel.StartSpan(ctx, "CopyRequestApptEmail", nil)
	defer func() { metaSpan.End(nil) }()

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	ctx = log.With(ctx, zap.Uint("userID", user.ID), zap.Uint("serviceID", user.ServiceID))

	log.Info(ctx, "saving record of copy request appointment email", zap.Any("requestBody", body))

	// fetch load from FreightTrackingID
	load, err := loadDB.GetLoadByFreightIDAndService(ctx, user.ServiceID, body.FreightTrackingID)
	var loadID uint
	if err == nil {
		loadID = load.ID
	}

	stopType, stopNumber := apptutil.GetStopTypeAndNumber(body.StopType)

	tmsIntegration, err := getTMSIntegration(ctx, user.ServiceID, body.FreightTrackingID)
	if err != nil {
		log.Warn(ctx, "failed to get TMS integration", zap.Error(err))
		// Continue without TMS integration - customer association will still work with name fallback
	}

	appt := models.Appointment{
		Account:           user.EmailAddress,
		UserID:            user.ID,
		ServiceID:         user.ServiceID,
		FreightTrackingID: body.FreightTrackingID,
		LoadID:            loadID,
		EmailBody:         body.Body,
		EmailTemplateID:   body.ID,
		Source:            models.EmailRequest,
		EmailType:         models.EmailAppointmentTypeCopy,
		StopType:          stopType,
		StopNumber:        stopNumber,
	}

	existingAppt, err := apptDB.GetCopyRequestEmailAppointment(
		ctx,
		user.ServiceID,
		user.EmailAddress,
		body.FreightTrackingID,
		models.StopType(stopType),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return apiutil.HandleDBError(c, err, false, "failed to query appointment")
	}

	if existingAppt.ID != 0 {
		existingAppt.EmailBody = appt.EmailBody
		existingAppt.EmailTemplateID = appt.EmailTemplateID
		existingAppt.EmailType = models.EmailAppointmentTypeCopy

		// Associate customer with appointment before updating
		_, err = associateCustomerWithAppt(ctx, &existingAppt, tmsIntegration)
		if err != nil {
			log.Warn(ctx, "failed to associate customer with appointment", zap.Error(err))
			// Continue with update even if customer association fails
		}

		if err := apptDB.Update(ctx, existingAppt); err != nil {
			return apiutil.HandleDBError(c, err, false, "failed to update appointment")
		}

		return c.Status(http.StatusOK).JSON(CopyRequestApptEmailResponse{TemplateID: body.ID})
	}

	// Associate customer with appointment before creating
	_, err = associateCustomerWithAppt(ctx, &appt, tmsIntegration)
	if err != nil {
		log.Warn(ctx, "failed to associate customer with appointment", zap.Error(err))
		// Continue with creation even if customer association fails
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		return apiutil.HandleDBError(c, err, false, "failed to create appointment")
	}

	return c.Status(http.StatusOK).JSON(CopyRequestApptEmailResponse{TemplateID: body.ID})
}
