package appt

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/retalix"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	apptDB "github.com/drumkitai/drumkit/common/rds/appt"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
	apptutil "github.com/drumkitai/drumkit/fn/api/routes/appt/util"
)

type (
	SubmitApptBody struct {
		LoadID          uint                   `json:"loadID"`
		PONumbers       []string               `json:"poNumbers" validate:"required"`
		Source          models.WarehouseSource `json:"source" validate:"required"`
		WarehouseID     string                 `json:"warehouseId" validate:"required"`
		LumperRequested bool                   `json:"lumperRequested"`
		RequestedDate   time.Time              `json:"requestedDate"`
		Note            string                 `json:"note"`
		TimePreference  models.TimePreference  `json:"timePreference"`
		RequestType     models.RequestType     `json:"requestType"`
		IntegrationID   uint                   `json:"integrationID"`
		FlowType        string                 `json:"flowType,omitempty"`
	}

	SubmitApptResponse struct {
		models.Appointment

		Message string `json:"message"`
	}
)

// SubmitAppt handles appointment requests for some timeframe e.g. this day before noon pending an approval.
// NOTE: Only supports E2open, Manhattan, and Retalix now
func SubmitAppt(c *fiber.Ctx) error {
	var body SubmitApptBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	email := middleware.ClaimsFromContext(c).Email
	userID := middleware.UserIDFromContext(c)
	userServiceID := middleware.ServiceIDFromContext(c)

	ctx := log.With(
		c.UserContext(),
		zap.String("emailAddress", email),
		zap.Uint("userID", userID),
		zap.Uint("serviceID", userServiceID),
		zap.String("integration", string(body.Source)),
		zap.Any("requestBody", body),
	)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "GetUserByID failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	integration, err := integrationDB.GetSchedulerByServiceUserIDsAndName(
		ctx,
		userID,
		userServiceID,
		string(body.Source),
		body.IntegrationID,
	)
	if err != nil {
		log.Error(ctx, "failed to get scheduler integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var warehouse *models.Warehouse

	if body.Source == models.RetalixSource {
		warehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			body.Source,
			body.WarehouseID,
		)
		if err != nil {
			log.Error(
				ctx,
				"error getting warehouse name",
				zap.Error(err),
				zap.String("warehouseID", body.WarehouseID),
			)

			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	// Determine stop type and number based on request type
	// Convert models.RequestType to StopType (they share the same string values)
	stopType, stopNumber := apptutil.GetStopTypeAndNumber(apptutil.StopType(body.RequestType))

	appt := models.Appointment{
		Account:             email,
		UserID:              user.ID,
		ServiceID:           user.ServiceID,
		Source:              models.IntegrationName(body.Source),
		ExternalWarehouseID: body.WarehouseID,
		Notes:               body.Note,
		PONums:              fmt.Sprint(body.PONumbers),
		IntegrationID:       integration.ID,
		StopType:            stopType,
		StopNumber:          stopNumber,
		// FreightTrackingID is set in getLoadForAppt()
	}

	if warehouse != nil {
		appt.WarehouseID = warehouse.ID
	}

	load, err := getLoadForAppt(ctx, body.LoadID, body.PONumbers, user.ServiceID, &appt)
	if err != nil {
		log.Warn(ctx, "failed to get load for appointment", zap.Error(err))
		// Continue without load - appointment can still be created
	}

	// Associate customer info (sets CustomerName and optionally TMSCustomerID)
	if _, assocErr := associateCustomerWithAppt(ctx, &appt, nil); assocErr != nil {
		log.Warn(ctx, "failed to associate customer with appointment", zap.Error(assocErr))
	}

	// Ensure WarehouseID is populated BEFORE persisting appointment by creating/finding warehouse if applicable
	var existingWarehouse, newWarehouse *models.Warehouse
	if warehouse != nil {
		existingWarehouse = warehouse
	} else if body.WarehouseID != "" {
		existingWarehouse, err = warehouseDB.GetWarehouseByIDAndSource(
			ctx,
			userServiceID,
			body.Source,
			body.WarehouseID,
		)
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "error while trying to get existing warehouse", zap.Error(err))
		}
	}

	// If no wh exists and we have a load, attempt to create a custom warehouse if scheduling integration is supported
	if existingWarehouse == nil && load != nil {
		stopType := apptutil.PickupStopType
		if body.RequestType == models.RequestTypeDropoff {
			stopType = apptutil.DropoffStopType
		}

		createdWarehouse, err := apptutil.CreateCustomWarehouseFromLoad(ctx, load, stopType, string(body.Source))
		if err != nil {
			log.Warn(ctx, "failed to create custom warehouse from load", zap.Error(err))
		} else {
			newWarehouse = createdWarehouse
		}

	}

	if appt.WarehouseID == 0 {
		switch {
		case warehouse != nil:
			appt.WarehouseID = warehouse.ID
		case existingWarehouse != nil:
			appt.WarehouseID = existingWarehouse.ID
			warehouse = existingWarehouse
		case newWarehouse != nil:
			appt.WarehouseID = newWarehouse.ID
			warehouse = newWarehouse
		}
	}

	if appt.ExternalWarehouseID == "" && newWarehouse != nil {
		appt.ExternalWarehouseID = newWarehouse.WarehouseID
	}

	// Build options only when date or time preference provided
	var opts []models.SchedulingOption

	if !body.RequestedDate.IsZero() {
		opts = append(opts, models.WithRequestedDate(body.RequestedDate))
		appt.Date = body.RequestedDate.Format(time.DateOnly)
	}

	if body.TimePreference != "" {
		opts = append(opts, models.WithTimePreference(body.TimePreference))
		appt.TimePreference = string(body.TimePreference)
	}

	if body.RequestType != "" {
		opts = append(opts, models.WithRequestType(body.RequestType))
	}

	// For Retalix: Extract pickup zipcode from load and set origin required flag
	// This is needed for certain Retalix warehouses that require pickup zipcode in the order list
	if body.Source == models.RetalixSource && load != nil && load.Pickup.Zipcode != "" {
		normalizedZipCode := helpers.NormalizeZipCode(load.Pickup.Zipcode)
		if normalizedZipCode != "" {
			opts = append(opts, models.WithZipCode(normalizedZipCode))
			// TODO: Set IsOriginRequired from FE when ready
			// For now, we'll set it manually - FE will send this flag later
			opts = append(opts, models.WithIsOriginRequired(true))
		}
	}

	if integration.Tenant != "" {
		opts = append(opts, models.WithTenant(integration.Tenant))
	}

	if body.FlowType != "" {
		opts = append(opts, models.WithFlowType(body.FlowType))
	}

	err = submitAppt(ctx, integration, body.PONumbers, warehouse, body.LumperRequested, body.Note, opts...)
	if err != nil {
		log.Error(ctx, "failed to submit appointment", zap.Error(err))

		var retalixErr *retalix.Error
		if errors.As(err, &retalixErr) {
			if retalixErr.Type == retalix.ValidationError {
				// Special return case for specific error message
				if retalixErr.Message == "request for review appointment type selection not available" {
					return c.Status(http.StatusInternalServerError).JSON(&SubmitApptResponse{
						Message: "Live appointments are not supported at this time.",
					})
				}

				return c.Status(http.StatusInternalServerError).JSON(&SubmitApptResponse{
					Message: retalixErr.Message,
				})
			}
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	if err := apptDB.Create(ctx, &appt); err != nil {
		log.Error(ctx, "failed to create appt record in DB", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(&SubmitApptResponse{
			Message: "Failed to save appointment",
		})
	}

	warehouseIDForUsage := appt.WarehouseID
	if warehouseIDForUsage == 0 {
		warehouseIDForUsage = apptutil.GetWarehouseIDForUsage(warehouse, existingWarehouse, newWarehouse)
	}

	if warehouseIDForUsage != 0 && load != nil {
		tmsIntegrationID := load.TMSID

		go recordCustomerIntegrationUsage(
			log.InheritContext(ctx, context.Background()),
			&appt,
			load,
			warehouseIDForUsage,
			integration.ID,
			tmsIntegrationID,
		)
	} else {
		log.Warn(ctx, "not enough information to record customer integration usage", zap.Any("warehouse", warehouse))
	}

	return c.SendStatus(http.StatusOK)
}

// getLoadForAppt attempts to get a load for the appointment by LoadID or PO number.
// Updates appt.LoadID and appt.FreightTrackingID when a load is found.
// Returns the load if found, or nil if no load could be found.
// Returns an error only if all database operations fail (non-gorm errors).
// If GetLoadByID fails with a non-gorm error, we still attempt the PO number fallback.
func getLoadForAppt(
	ctx context.Context,
	loadID uint,
	poNumbers []string,
	serviceID uint,
	appt *models.Appointment,
) (*models.Load, error) {
	var resolvedLoad *models.Load
	var getLoadByIDErr error

	if loadID != 0 {
		loadByID, err := loadDB.GetLoadByID(ctx, loadID)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				// Non-gorm error - log but continue to fallback
				getLoadByIDErr = err
				log.WarnNoSentry(ctx, "failed to get load by ID, will attempt PO number fallback", zap.Error(err))
			}
		} else {
			appt.LoadID = loadByID.ID
			if appt.FreightTrackingID == "" {
				appt.FreightTrackingID = loadByID.FreightTrackingID
			}
			resolvedLoad = &loadByID
		}
	}

	// Fallback: Try to find load by PO number
	if resolvedLoad == nil && appt.LoadID == 0 && len(poNumbers) > 0 {
		load, loadErr := loadDB.GetLoadByPONum(ctx, poNumbers[0], serviceID)
		if load != nil && loadErr == nil {
			appt.LoadID = load.ID
			appt.FreightTrackingID = load.FreightTrackingID
			resolvedLoad = load
		} else if loadErr != nil {
			if errors.Is(loadErr, gorm.ErrRecordNotFound) {
				// Not found is expected, return nil if we also had a non-gorm error from GetLoadByID
				if getLoadByIDErr != nil {
					return nil, fmt.Errorf("failed to get load by ID: %w", getLoadByIDErr)
				}
			} else {
				// Non-gorm error on fallback - return error (or combine with GetLoadByID error if present)
				if getLoadByIDErr != nil {
					return nil, fmt.Errorf("failed to get load by ID and PO number: %w, %w", getLoadByIDErr, loadErr)
				}
				return nil, fmt.Errorf("failed to get load by PO number: %w", loadErr)
			}
		}
	}

	// If we got here with a GetLoadByID error but no resolved load, return that error
	if resolvedLoad == nil && getLoadByIDErr != nil {
		return nil, fmt.Errorf("failed to get load by ID: %w", getLoadByIDErr)
	}

	return resolvedLoad, nil
}

func submitAppt(
	ctx context.Context,
	integration models.Integration,
	poNumbers []string,
	warehouse *models.Warehouse,
	lumperRequested bool,
	note string,
	opts ...models.SchedulingOption,
) error {

	client, err := scheduling.GetCachedClient(ctx, integration)
	if err != nil {
		log.Error(ctx, "error getting scheduling client", zap.Error(err))
		return fmt.Errorf("error getting scheduling client: %w", err)
	}

	err = client.SubmitAppointment(ctx, poNumbers, *warehouse, lumperRequested, note, opts...)
	if err != nil {
		return fmt.Errorf("error submitting appointment: %w", err)
	}

	log.Info(ctx, "appointment created successfully")

	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	requestType := options.RequestType

	if requestType == "" {
		log.Error(ctx, "request type is required")
		return nil
	}

	load, err := loadDB.GetLoadByWarehouseID(
		ctx,
		integration.ServiceID,
		warehouse.ID,
		requestType,
	)
	if err != nil {
		log.Error(ctx, "failed to get load for warehouse-address association", zap.Error(err))
		return nil
	}

	err = warehouseDB.CreateWarehouseAddress(
		ctx,
		*warehouse,
		load,
		requestType,
	)
	if err != nil {
		log.Error(ctx, "failed to create warehouse address association", zap.Error(err))
		return nil
	}

	return nil
}
