# DB
APP_ENV=dev
DB_USER=postgres
DB_HOST=localhost
DB_NAME=drumkit_dev # beacon_dev if you're old
DB_PASSWORD=postgres
# Use only is specifically testing Sentry, otherwise disable
# SENTRY_DSN=https://<EMAIL>/4505756382658560 

# Redis
REDIS_URL=redis://localhost:6379

# Email API creds
GOOGLE_CLIENT_ID="968651685013-i120oufqf06lonr2lj3ahh92il7j67qo.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="See 1Password"
MICROSOFT_CLIENT_ID=7ef2023f-908c-4cd7-99ba-866a75fa15d0
MICROSOFT_CLIENT_SECRET="See 1Password"
MICROSOFT_WEBHOOK_URL="{ngrok generated URL}/inboxWebhook"

# Tracing
AXIOM_ORG_ID=axle-xqz9
AXIOM_TOKEN="See 1Password"
# Use only is specifically testing Sentry, otherwise disable
# AXIOM_TRACE_DATASET=beacon-otel-dev

# Misc
DISABLE_RATE_LIMIT=false # false recommended
DEBUG=true
