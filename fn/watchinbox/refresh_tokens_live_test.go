package main

import (
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/watchinbox/env"
)

// TestLiveRefreshTokens tests the refreshTokens function with a real user in the database.
// Run with:
//
//	LIVE_TEST=true \
//	  TEST_ENCRYPTED_ACCESS_TOKEN="<encrypted-token>" \
//	  TEST_ENCRYPTED_REFRESH_TOKEN="<encrypted-token>" \
//	  TEST_EMAIL_PROVIDER="gmail" \
//	  TEST_EMAIL_ADDRESS="<EMAIL>" \
//	  [TEST_TENANT_ID="<tenant-id>"] \  # Required for Outlook
//	  go test -v ./fn/watchinbox -run TestLiveRefreshTokens
//
// or with .env file.
// Tests must run sequentially
func TestLiveRefreshTokens(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveRefreshTokens: run with LIVE_TEST=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	ctx = log.NewFromEnv(ctx)

	// Load environment variables
	err := env.Load(ctx)
	require.NoError(t, err, "failed to load environment variables")

	// Validate required environment variables

	// Read test parameters from environment variables
	encryptedAccessToken := os.Getenv("TEST_ENCRYPTED_ACCESS_TOKEN")
	encryptedRefreshToken := os.Getenv("TEST_ENCRYPTED_REFRESH_TOKEN")
	emailProvider := os.Getenv("TEST_EMAIL_PROVIDER")
	emailAddress := os.Getenv("TEST_EMAIL_ADDRESS")
	tenantID := os.Getenv("TEST_TENANT_ID")

	// Validate required environment variables
	require.NotEmpty(t, env.Vars.GoogleClientID, "GOOGLE_CLIENT_ID is required")
	require.NotEmpty(t, env.Vars.GoogleClientSecret, "GOOGLE_CLIENT_SECRET is required")
	require.NotEmpty(t, env.Vars.MicrosoftClientID, "MICROSOFT_CLIENT_ID is required")
	require.NotEmpty(t, env.Vars.MicrosoftClientSecret, "MICROSOFT_CLIENT_SECRET is required")

	require.NotEmpty(t, encryptedAccessToken, "TEST_ENCRYPTED_ACCESS_TOKEN environment variable is required")
	require.NotEmpty(t, encryptedRefreshToken, "TEST_ENCRYPTED_REFRESH_TOKEN environment variable is required")
	require.NotEmpty(t, emailProvider, "TEST_EMAIL_PROVIDER environment variable is required (gmail or outlook)")
	require.NotEmpty(t, emailAddress, "TEST_EMAIL_ADDRESS environment variable is required")

	var provider models.EmailProvider
	switch emailProvider {
	case "gmail":
		provider = models.GmailEmailProvider
	case "outlook":
		provider = models.OutlookEmailProvider
		require.NotEmpty(t, tenantID, "TEST_TENANT_ID environment variable is required for Outlook")
	default:
		t.Fatalf("invalid email provider: %s (must be 'gmail' or 'outlook')", emailProvider)
	}

	// Create a test service
	service := rds.CreateTestService(ctx, t)

	// Create test user with encrypted tokens
	oldTokenExpiry := time.Now().Add(30 * time.Minute)
	user := models.User{
		Model:                 gorm.Model{},
		ServiceID:             service.ID,
		EmailAddress:          emailAddress,
		EmailProvider:         provider,
		EncryptedAccessToken:  encryptedAccessToken,
		EncryptedRefreshToken: encryptedRefreshToken,
		TokenExpiry:           oldTokenExpiry,
		WebhookExpiration:     time.Now().Add(24 * time.Hour),
	}

	if provider == models.OutlookEmailProvider {
		user.TenantID = tenantID
	}

	err = rds.WithContext(ctx).Create(&user).Error
	require.NoError(t, err, "failed to create test user")
	require.NotZero(t, user.ID, "user ID should be set after creation")

	// Verify we can decrypt the tokens (basic sanity check)
	var encryptionKey []byte
	if env.Vars.AppEnv == "prod" || env.Vars.AppEnv == "staging" {
		encryptionKey = crypto.AESKey
	}
	// In dev, encryption key might be nil (uses secrets manager)

	_, err = crypto.DecryptAESGCM(ctx, user.EncryptedAccessToken, &encryptionKey)
	require.NoError(t, err, "failed to decrypt access token - check encryption key")

	_, err = crypto.DecryptAESGCM(ctx, user.EncryptedRefreshToken, &encryptionKey)
	require.NoError(t, err, "failed to decrypt refresh token - check encryption key")

	t.Run("TokenExpiryWithinThreshold", func(t *testing.T) {
		// Store old token expiry for comparison
		oldExpiry := user.TokenExpiry
		oldAccessToken := user.EncryptedAccessToken
		oldRefreshToken := user.EncryptedRefreshToken

		// Call refreshTokens
		err = refreshTokens(ctx, &user)
		require.NoError(t, err, "refreshTokens should succeed")

		// Reload user from database to get updated tokens
		updatedUser, err := userDB.GetByID(ctx, user.ID)
		require.NoError(t, err, "failed to reload user from database")

		// Verify token expiry changed
		assert.False(
			t,
			updatedUser.TokenExpiry.Equal(oldExpiry),
			"token expiry should have changed: old=%v, new=%v",
			oldExpiry.Format(time.RFC3339),
			updatedUser.TokenExpiry.Format(time.RFC3339),
		)

		// Verify encrypted tokens changed
		assert.NotEqual(
			t,
			oldAccessToken,
			updatedUser.EncryptedAccessToken,
			"encrypted access token should have changed",
		)

		assert.NotEqual(
			t,
			oldRefreshToken,
			updatedUser.EncryptedRefreshToken,
			"encrypted refresh token should have changed",
		)

		// Verify we can decrypt the new tokens
		newAccessToken, err := crypto.DecryptAESGCM(ctx, updatedUser.EncryptedAccessToken, &encryptionKey)
		require.NoError(t, err, "failed to decrypt new access token")
		assert.NotEmpty(t, newAccessToken, "new access token should not be empty")

		newRefreshToken, err := crypto.DecryptAESGCM(ctx, updatedUser.EncryptedRefreshToken, &encryptionKey)
		require.NoError(t, err, "failed to decrypt new refresh token")
		assert.NotEmpty(t, newRefreshToken, "new refresh token should not be empty")

		t.Logf(
			"Successfully refreshed tokens: old expiry=%v, new expiry=%v",
			oldExpiry.Format(time.RFC3339),
			updatedUser.TokenExpiry.Format(time.RFC3339),
		)
	})

	t.Run("TokenExpired", func(t *testing.T) {
		// Store old token expiry for comparison
		user.TokenExpiry = time.Now().Add(-1 * time.Hour)

		// Store old values for comparison
		oldTokenExpiry := user.TokenExpiry
		oldAccessToken := user.EncryptedAccessToken
		oldRefreshToken := user.EncryptedRefreshToken

		err = rds.UpdateUser(ctx, user)
		require.NoError(t, err, "failed to update user in database with expired token")

		// Call refreshTokens
		result, err := refreshAllUsers(ctx)
		require.NoError(t, err, "refreshTokens should succeed")
		require.NotNil(t, result)
		require.Equal(t, http.StatusOK, result.StatusCode)

		// Reload user from database to get updated tokens
		updatedUser, err := userDB.GetByID(ctx, user.ID)
		require.NoError(t, err, "failed to reload user from database")

		// Verify token expiry is after the old token expiry
		assert.True(
			t,
			updatedUser.TokenExpiry.After(time.Now()),
			"token expiry should be after the current time: new=%v",
			updatedUser.TokenExpiry.Format(time.RFC3339),
		)

		assert.False(
			t,
			updatedUser.TokenExpiry.Equal(oldTokenExpiry),
			"token expiry should NOT be the same as the old token expiry: old=%v, new=%v",
			oldTokenExpiry.Format(time.RFC3339),
			updatedUser.TokenExpiry.Format(time.RFC3339),
		)

		// Verify encrypted tokens changed
		assert.NotEqual(
			t,
			oldAccessToken,
			updatedUser.EncryptedAccessToken,
			"encrypted access token should have changed",
		)

		assert.NotEqual(
			t,
			oldRefreshToken,
			updatedUser.EncryptedRefreshToken,
			"encrypted refresh token should have changed",
		)

	})

	t.Run("NoRefreshNeeded", func(t *testing.T) {
		curUser, err := userDB.GetByID(ctx, user.ID)
		require.NoError(t, err, "failed to get current user from database")

		oldUpdatedAt := curUser.UpdatedAt
		oldTokenExpiry := curUser.TokenExpiry
		oldAccessToken := curUser.EncryptedAccessToken
		oldRefreshToken := curUser.EncryptedRefreshToken
		oldWebhookExpiration := curUser.WebhookExpiration

		// Call refreshTokens
		result, err := refreshAllUsers(ctx)
		require.NoError(t, err, "refreshTokens should succeed")
		require.NotNil(t, result)
		require.Equal(t, http.StatusOK, result.StatusCode)

		updatedUser, err := userDB.GetByID(ctx, user.ID)
		require.NoError(t, err, "failed to reload user from database")

		// Verify user is the same
		assert.Equal(
			t,
			oldUpdatedAt,
			updatedUser.UpdatedAt,
			"UpdatedAt should be the same",
		)
		assert.Equal(
			t,
			oldTokenExpiry,
			updatedUser.TokenExpiry,
			"token expiry should be the same",
		)
		assert.Equal(
			t,
			oldWebhookExpiration,
			updatedUser.WebhookExpiration,
			"webhook expiration should be the same",
		)
		assert.Equal(
			t,
			oldAccessToken,
			updatedUser.EncryptedAccessToken,
			"encrypted access token should be the same",
		)
		assert.Equal(
			t,
			oldRefreshToken,
			updatedUser.EncryptedRefreshToken,
			"encrypted refresh token should be the same",
		)
	})
}
