package main

import (
	"context"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/models"
)

var (
	originalGetAllGmailAndOutlookFunc func(context.Context) ([]models.User, error)
	originalRewatchInboxFunc          func(context.Context, *models.User) error
	originalRefreshTokensFunc         func(context.Context, *models.User) error
	originalUpdateUserFunc            func(context.Context, models.User) error
	originalUpdateOnPremUserFunc      func(context.Context, models.OnPremUser) error
)

func setupMocks() {
	originalGetAllGmailAndOutlookFunc = getAllGmailAndOutlookFunc
	originalRewatchInboxFunc = rewatchInboxFunc
	originalRefreshTokensFunc = refreshTokensFunc
	originalUpdateUserFunc = updateUserFunc
	originalUpdateOnPremUserFunc = oauth.UpdateOnPremUserFunc
}

func teardownMocks() {
	getAllGmailAndOutlookFunc = originalGetAllGmailAndOutlookFunc
	rewatchInboxFunc = originalRewatchInboxFunc
	refreshTokensFunc = originalRefreshTokensFunc
	updateUserFunc = originalUpdateUserFunc
	oauth.UpdateOnPremUserFunc = originalUpdateOnPremUserFunc
}

func TestRefreshAllUsers_Success_NoUsers(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{}, nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
}

func TestRefreshAllUsers_Success_NoRefreshNeeded(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringLater := time.Now().Add(1 * time.Hour)

	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringLater,
	}

	getAllGmailAndOutlookFunc = func(context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	var rewatchCalled bool
	var refreshTokensCalled bool

	rewatchInboxFunc = func(context.Context, *models.User) error {
		rewatchCalled = true
		return nil
	}

	refreshTokensFunc = func(context.Context, *models.User) error {
		refreshTokensCalled = true
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.False(t, rewatchCalled, "rewatchInbox should not be called")
	assert.False(t, refreshTokensCalled, "refreshTokens should not be called")
}
func TestRefreshAllUsers_Success_WebhookExpiringSoon(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	expiringSoon := time.Now().Add(12 * time.Hour)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: expiringSoon,
		TokenExpiry:       time.Now().Add(1 * time.Hour),
	}

	var rewatchCalled bool
	var rewatchUserID uint

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	rewatchInboxFunc = func(_ context.Context, u *models.User) error {
		rewatchCalled = true
		rewatchUserID = u.ID
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.True(t, rewatchCalled, "rewatchInbox should be called")
	assert.Equal(t, user.ID, rewatchUserID)
}

func TestRefreshAllUsers_Success_TokenExpiringSoon(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
	}

	var refreshCalled bool
	var refreshUserID uint

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	refreshTokensFunc = func(_ context.Context, u *models.User) error {
		refreshCalled = true
		refreshUserID = u.ID
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.True(t, refreshCalled, "refreshTokens should be called")
	assert.Equal(t, user.ID, refreshUserID)
}

func TestRefreshAllUsers_Success_MultipleUsers(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringSoon := time.Now().Add(12 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringLater := time.Now().Add(1 * time.Hour)

	users := []models.User{
		{
			Model:             gorm.Model{ID: 1},
			EmailAddress:      "<EMAIL>",
			EmailProvider:     models.GmailEmailProvider,
			WebhookExpiration: webhookExpiringSoon,
			TokenExpiry:       tokenExpiringLater,
		},
		{
			Model:             gorm.Model{ID: 2},
			EmailAddress:      "<EMAIL>",
			EmailProvider:     models.OutlookEmailProvider,
			WebhookExpiration: webhookExpiringLater,
			TokenExpiry:       tokenExpiringSoon,
		},
		{
			Model:             gorm.Model{ID: 3},
			EmailAddress:      "<EMAIL>",
			EmailProvider:     models.GmailEmailProvider,
			WebhookExpiration: webhookExpiringLater,
			TokenExpiry:       tokenExpiringLater,
		},
	}

	var rewatchCount int
	var refreshCount int

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return users, nil
	}

	rewatchInboxFunc = func(_ context.Context, _ *models.User) error {
		rewatchCount++
		return nil
	}

	refreshTokensFunc = func(_ context.Context, _ *models.User) error {
		refreshCount++
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.Equal(t, 1, rewatchCount, "rewatchInbox should be called once for user1")
	assert.Equal(t, 1, refreshCount, "refreshTokens should be called once for user2")
}

func TestRefreshAllUsers_Error_GetAllGmailAndOutlookFails(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	expectedErr := errors.New("database error")
	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return nil, expectedErr
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "users lookup failed")
}

func TestRefreshAllUsers_Error_RewatchInboxFails(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	expiringSoon := time.Now().Add(12 * time.Hour)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: expiringSoon,
		TokenExpiry:       time.Now().Add(1 * time.Hour),
	}

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	rewatchInboxFunc = func(_ context.Context, _ *models.User) error {
		return errors.New("rewatch failed")
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
}

func TestRefreshAllUsers_Error_RefreshTokensFails(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
	}

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	refreshTokensFunc = func(_ context.Context, _ *models.User) error {
		return errors.New("refresh failed")
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
}

func TestRefreshAllUsers_RefreshTokens_CallsUpdateUserFunc(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
	}

	var updateUserCalled bool
	var updatedUser models.User

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	refreshTokensFunc = func(ctx context.Context, u *models.User) error {
		u.TokenExpiry = time.Now().Add(1 * time.Hour)
		err := oauth.UpdateUserFunc(ctx, *u)
		return err
	}

	oauth.UpdateUserFunc = func(_ context.Context, u models.User) error {
		updateUserCalled = true
		updatedUser = u
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.True(t, updateUserCalled, "UpdateUserFunc should be called")
	assert.Equal(t, user.ID, updatedUser.ID)
	assert.Greater(t, updatedUser.TokenExpiry, tokenExpiringSoon)
}

func TestRefreshAllUsers_RefreshTokens_OnPremUser_CallsUpdateOnPremUserFunc(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	onPremUser := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
		IsOnPrem:          true,
	}

	var updateOnPremUserCalled bool
	var updatedOnPremUser models.OnPremUser

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{onPremUser}, nil
	}

	refreshTokensFunc = func(ctx context.Context, u *models.User) error {
		u.TokenExpiry = time.Now().Add(1 * time.Hour)
		err := oauth.UpdateOnPremUserFunc(ctx, models.OnPremUser{Model: u.Model})
		return err
	}

	oauth.UpdateOnPremUserFunc = func(_ context.Context, u models.OnPremUser) error {
		updateOnPremUserCalled = true
		updatedOnPremUser = u
		return nil
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.True(t, updateOnPremUserCalled, "UpdateOnPremUserFunc should be called")
	assert.Equal(t, onPremUser.ID, updatedOnPremUser.ID)
}

func TestRefreshAllUsers_RefreshTokens_UpdateUserFuncError(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	user := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
	}

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{user}, nil
	}

	refreshTokensFunc = func(_ context.Context, _ *models.User) error {
		return nil
	}

	expectedErr := errors.New("update user failed")
	oauth.UpdateUserFunc = func(_ context.Context, _ models.User) error {
		return expectedErr
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
}

func TestRefreshAllUsers_RefreshTokens_UpdateOnPremUserFuncError(t *testing.T) {
	setupMocks()
	defer teardownMocks()

	webhookExpiringLater := time.Now().Add(48 * time.Hour)
	tokenExpiringSoon := time.Now().Add(5 * time.Minute)
	onPremUser := models.User{
		Model:             gorm.Model{ID: 1},
		EmailAddress:      "<EMAIL>",
		EmailProvider:     models.GmailEmailProvider,
		WebhookExpiration: webhookExpiringLater,
		TokenExpiry:       tokenExpiringSoon,
		IsOnPrem:          true,
	}

	getAllGmailAndOutlookFunc = func(_ context.Context) ([]models.User, error) {
		return []models.User{onPremUser}, nil
	}

	refreshTokensFunc = func(_ context.Context, _ *models.User) error {
		return nil
	}

	expectedErr := errors.New("update onprem user failed")
	oauth.UpdateOnPremUserFunc = func(_ context.Context, _ models.OnPremUser) error {
		return expectedErr
	}

	ctx := context.Background()
	result, err := refreshAllUsers(ctx)

	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, http.StatusOK, result.StatusCode)
}
