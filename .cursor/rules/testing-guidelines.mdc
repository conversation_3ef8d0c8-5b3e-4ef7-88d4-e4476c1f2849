---
description: "Testing guidelines for Go code, especially Redis-related tests"
globs: ["**/*_test.go", "**/testdata/**"]
alwaysApply: true
---

# Testing Guidelines

## Redis Testing

### Mocking Redis:
- Use `redismock` for unit tests
- When mocking `<PERSON><PERSON><PERSON>[T]()`, return JSON-encoded values: `mock.SetVal(`"value"`)` not `mock.SetVal("value")`
- For integration tests that need Redis disabled, use `DISABLE_RATE_LIMIT=true` environment variable

### Example Test Setup:
```go
func TestMain(m *testing.M) {
    // Disable rate limiting for tests to avoid Redis connection issues
    os.Setenv("DISABLE_RATE_LIMIT", "true")
    exitCode := m.Run()
    os.Unsetenv("DISABLE_RATE_LIMIT")
    os.Exit(exitCode)
}
```

### Redis Mock Examples:
```go
// ✅ Correct - JSON-encoded value for <PERSON><PERSON><PERSON>[string]
mock.ExpectGet("key").SetVal(`"myvalue"`)

// ❌ Wrong - raw string will cause JSON unmarshal error
mock.ExpectGet("key").SetVal("myvalue")
```

## Database Testing

### Live Database Tests (TEST_LIVE_DB):
- **Location**: Tests that use `TEST_LIVE_DB=true` should ideally **only** exist in `common/rds/` or `common/models/`
- **Rationale**: Our CI workflow runs sequential database tests conditionally based on changes to these directories to optimize test execution time
- **When adding new LIVE_DB tests**: Place them in the appropriate `common/rds/` or `common/models/` subdirectory
- **Exceptions**: If LIVE_DB tests are absolutely necessary elsewhere, document why and update `.github/workflows/golang-test.yml` to include that path in the conditional check

### Example:
```go
// ✅ Correct location: common/rds/load/query_test.go
func TestLoadQuery(t *testing.T) {
    if os.Getenv("TEST_LIVE_DB") != "true" {
        t.Skip("Skipping live DB test")
    }
    // test implementation
}

// ❌ Avoid: fn/api/routes/appt/handler_test.go
// (unless there's a compelling reason and CI workflow is updated)
```

## General Testing Best Practices

- Write meaningful test names that describe the scenario
- Use table-driven tests for multiple similar test cases
- Test both success and error paths
- Use `testify/assert` and `testify/require` appropriately
- Mock external dependencies
- Keep tests focused and independent